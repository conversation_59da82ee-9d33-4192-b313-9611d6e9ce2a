package cn.jdl.oms.express.horz.ext.coupon;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.coupon.ICouponOccupyExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.coupon.CouponFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.coupon.CouponInfoRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.coupon.CouponInfoResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.coupon.CouponInfoTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.coupon.CouponUseRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.coupon.CouponUseTranslator;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AdministrativeRegionEnum;
import cn.jdl.oms.express.domain.vo.Customs;
import cn.jdl.oms.express.domain.vo.Ticket;
import cn.jdl.oms.express.domain.vo.modify.ChangedPropertyDelegate;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.CouponExecuteTypeEnum;
import cn.jdl.oms.express.shared.common.dict.CouponStatusEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;

/**
 * 修改优惠券占用扩展点
 */
@Extension(code = ExpressOrderProduct.CODE)
public class ModifyCouponOccupyExtension implements ICouponOccupyExtension {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ModifyCouponOccupyExtension.class);

    @Resource
    private CouponFacade couponFacade;

    @Resource
    private CouponUseTranslator couponUseTranslator;

    @Resource
    private CouponInfoTranslator couponInfoTranslator;

    @Override
    public void execute(ExpressOrderContext expressOrderContext) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            //校验未释放的老券是否可用
            verifyCoupon(expressOrderContext);
            ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
            if (null == orderModel.getPromotion()) {
                return;
            }
            List<Ticket> tickets = orderModel.getPromotion().getTickets();
            if (null == tickets || tickets.size() == 0) {
                return;
            }
            //占用新券
            for (Ticket ticket : tickets) {
                if (OperateTypeEnum.INSERT == ticket.getOperateType()) {
                    //目前只支持一张券的占用及释放
                    CouponInfoRequest request = new CouponInfoRequest();
                    request.setTraceId(orderModel.traceId());
                    request.setCouponNo(ticket.getTicketNo());
                    request.setOrderCreatorNo(orderModel.getOperator());
                    request.setCouponSource(ticket.getTicketSource());
                    request.setPlatformOrderNo(orderModel.getRefOrderInfoDelegate().getEnquiryOrderNo());
                    //流向
                    request.setStartFlowDirection(getStartFlowDirectionName(orderModel));
                    request.setEndFlowDirection(getEndFlowDirectionName(orderModel));
                    //寄件人手机号
                    if (null != orderModel.getConsignor()
                            && null != orderModel.getConsignor().getConsignorMobile()) {
                        request.setFromUserTel(orderModel.getConsignor().getConsignorMobile());
                    } else if (null != orderModel.getOrderSnapshot()
                            && null != orderModel.getOrderSnapshot().getConsignor()
                            && null != orderModel.getOrderSnapshot().getConsignor().getConsignorMobile()) {
                        request.setFromUserTel(orderModel.getOrderSnapshot().getConsignor().getConsignorMobile());
                    }

                    //收件人手机号
                    if (null != orderModel.getConsignee()
                            && null != orderModel.getConsignee().getConsigneeMobile()) {
                        request.setToUserTel(orderModel.getConsignee().getConsigneeMobile());
                    } else if (null != orderModel.getOrderSnapshot()
                            && null != orderModel.getOrderSnapshot().getConsignee()
                            && null != orderModel.getOrderSnapshot().getConsignee().getConsigneeMobile()) {
                        request.setToUserTel(orderModel.getOrderSnapshot().getConsignee().getConsigneeMobile());
                    }
                    CouponInfoResponse couponInfoResponse = couponFacade.queryInfo(request);

                    CouponUseRequest couponUseRequest = couponUseTranslator.toCouponUseRequest(orderModel, couponInfoResponse, orderModel.getOperator(), ticket);
                    couponUseRequest.setUse(true);
                    couponUseRequest.setUseAmount(ticket.getTicketUseAmount().getAmount());
                    couponUseRequest.setCouponExecuteType(CouponExecuteTypeEnum.USE.getCode());
                    //流向
                    couponUseRequest.setStartFlowDirection(getStartFlowDirection(orderModel.getOrderSnapshot()));
                    couponUseRequest.setEndFlowDirection(getEndFlowDirection(orderModel.getOrderSnapshot()));
                    boolean rest = couponFacade.useCoupon(couponUseRequest);
                    //占用成功，回写优惠券信息落库
                    if (rest) {
                        couponInfoTranslator.resetTicket(ticket, couponInfoResponse);
                    }
                } else if (OperateTypeEnum.DELETE == ticket.getOperateType()) {
                    ticket.setCouponStatus(CouponStatusEnum.ALLOW_ROLLBACK_YES.getCode());
                }
            }

        } catch (InfrastructureException infrastructureException) {
            LOGGER.error("修改优惠券占用扩展点执行异常", infrastructureException);
            throw infrastructureException;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("修改优惠券占用扩展点执行异常", exception);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.COUPON_OCCUPY_FAIL, exception);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 判断地址、支付方式、产品等信息是否发生变更，校验未释放的老券是否可用
     */
    private void verifyCoupon(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        List<Ticket> oldTickets = orderModel.getOrderSnapshot().getPromotion().getTickets();
        //判断地址、支付方式、产品等信息是否发生变更，校验未释放的老券是否可用
        ChangedPropertyDelegate changedPropertyDelegate = expressOrderContext.getChangedPropertyDelegate();
        if ((changedPropertyDelegate.consignorAddressHaveChange()
                || changedPropertyDelegate.productHaveChange()
                || changedPropertyDelegate.settlementTypeHaveChange()
                || changedPropertyDelegate.consignorOrConsigneeMobileHaveChange()//收寄件人手机号发生变化-亲情卡需求接入
        ) && CollectionUtils.isNotEmpty(oldTickets)) {
            for (Ticket oldTicket : oldTickets) {
                //未释放的优惠券
                if (!CouponStatusEnum.ALLOW_ROLLBACK_YES.getCode().equals(oldTicket.getCouponStatus()) && !CouponStatusEnum.DELETE_PENDING.getCode().equals(oldTicket.getCouponStatus())) {
                    Ticket ticket = orderModel.getPromotion().ofTicket(oldTicket.getTicketNo());
                    //若只修改地址、产品等信息，新单里不存在优惠券信息，则会为空。
                    if (ticket == null) {
                        ticket = oldTicket;
                    }
                    if (OperateTypeEnum.DELETE != ticket.getOperateType()) {
                        CouponInfoRequest request = new CouponInfoRequest();
                        request.setTraceId(orderModel.traceId());
                        request.setCouponNo(ticket.getTicketNo());
                        request.setOrderCreatorNo(orderModel.getOrderSnapshot().getOperator());
                        //流向
                        request.setStartFlowDirection(getStartFlowDirectionName(orderModel));
                        request.setEndFlowDirection(getEndFlowDirectionName(orderModel));
                        //寄件人手机号
                        if (null != orderModel.getConsignor()
                                && null != orderModel.getConsignor().getConsignorMobile()) {
                            request.setFromUserTel(orderModel.getConsignor().getConsignorMobile());
                        } else if (null != orderModel.getOrderSnapshot()
                                && null != orderModel.getOrderSnapshot().getConsignor()
                                && null != orderModel.getOrderSnapshot().getConsignor().getConsignorMobile()) {
                            request.setFromUserTel(orderModel.getOrderSnapshot().getConsignor().getConsignorMobile());
                        }

                        //收件人手机号
                        if (null != orderModel.getConsignee()
                                && null != orderModel.getConsignee().getConsigneeMobile()) {
                            request.setToUserTel(orderModel.getConsignee().getConsigneeMobile());
                        } else if (null != orderModel.getOrderSnapshot()
                                && null != orderModel.getOrderSnapshot().getConsignee()
                                && null != orderModel.getOrderSnapshot().getConsignee().getConsigneeMobile()) {
                            request.setToUserTel(orderModel.getOrderSnapshot().getConsignee().getConsigneeMobile());
                        }
                        CouponInfoResponse couponInfoResponse = couponFacade.queryInfo(request);

                        CouponUseRequest couponUseRequest = couponUseTranslator.toCouponUseRequest(orderModel, couponInfoResponse, orderModel.getOrderSnapshot().getOperator(),ticket);
                        //流向
                        couponUseRequest.setStartFlowDirection(getStartFlowDirectionName(orderModel));
                        couponUseRequest.setEndFlowDirection(getEndFlowDirectionName(orderModel));
                        boolean verifyResult = couponFacade.verifyCoupon(couponUseRequest);
                        if (!verifyResult) {
                            throw new DomainAbilityException(UnifiedErrorSpec.BasisOrder.COUPON_VERIFY_FAIL).withCustom("优惠券校验为不可用");
                        } else {
                            //回填订单信息，补全批次号等信息
                            couponInfoTranslator.resetTicket(ticket, couponInfoResponse);
                        }
                    }
                }
            }
        }
    }


    /**
     * 获取始发流向
     * @param orderModel
     * @return
     */
    private String getStartFlowDirectionName(ExpressOrderModel orderModel) {
        Customs snapShotCustoms = orderModel.getOrderSnapshot() != null ? orderModel.getOrderSnapshot().getCustoms() : null;
        Customs customs = orderModel.getCustoms();
        if (snapShotCustoms == null && customs == null) {
            return null;
        }
        AdministrativeRegionEnum startFlowDirection = null;

        if (customs != null && customs.getStartFlowDirection() != null) {
            startFlowDirection = customs.getStartFlowDirection();
        } else if (snapShotCustoms != null && snapShotCustoms.getStartFlowDirection() != null) {
            startFlowDirection = snapShotCustoms.getStartFlowDirection();
        }
        if (startFlowDirection != null) {
            return startFlowDirection.name();
        } else {
            return null;
        }
    }


    /**
     * 获取目的流向
     * @param orderModel
     * @return
     */
    private String getEndFlowDirectionName(ExpressOrderModel orderModel) {
        Customs snapShotCustoms = orderModel.getOrderSnapshot() != null ? orderModel.getOrderSnapshot().getCustoms() : null;
        Customs customs = orderModel.getCustoms();
        if (snapShotCustoms == null && customs == null) {
            return null;
        }
        AdministrativeRegionEnum endFlowDirection = null;

        if (customs != null && customs.getEndFlowDirection() != null) {
            endFlowDirection = customs.getEndFlowDirection();
        } else if (snapShotCustoms != null && snapShotCustoms.getEndFlowDirection() != null) {
            endFlowDirection = snapShotCustoms.getEndFlowDirection();
        }
        if (endFlowDirection != null) {
            return endFlowDirection.name();
        } else {
            return null;
        }
    }

    /**
     * 获取始发流向
     * @param orderModel
     * @return
     */
    private String getStartFlowDirection(ExpressOrderModel orderModel){
        if(null == orderModel.getCustoms()){
            return null;
        }
        if(null == orderModel.getCustoms().getStartFlowDirection()){
            return null;
        }
        return orderModel.getCustoms().getStartFlowDirection().name();
    }

    /**
     * 获取目的流向
     * @param orderModel
     * @return
     */
    private String getEndFlowDirection(ExpressOrderModel orderModel){
        if(null == orderModel.getCustoms()){
            return null;
        }
        if(null == orderModel.getCustoms().getEndFlowDirection()){
            return null;
        }
        return orderModel.getCustoms().getEndFlowDirection().name();
    }
}
