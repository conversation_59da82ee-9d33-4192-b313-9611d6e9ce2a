package cn.jdl.oms.express.horz.ext.presort;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.PresortExtend;
import cn.jdl.oms.express.domain.bo.PresortResult;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.presort.IPresortExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.presort.PresortFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.presort.PresortBaseSiteFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.presort.PresortFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.presort.PresortFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.presort.PresortFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.presort.PresortRpcResultEnum;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.SystemCallerUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.WarehouseModeUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ShipmentUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.SupplyChainDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PickupTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.vo.Shipment;
import cn.jdl.oms.express.domain.vo.modify.ChangedProperty;
import cn.jdl.oms.express.domain.vo.modify.ChangedPropertyDelegate;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description 水平扩展：修改-预分拣校验
 * @Copyright &copy;2022 JDL.CN All Right Reserved
 * <AUTHOR> liu
 * @date 2022/5/13 2:56 PM
 * @version 1.0
 */
@Extension(code = ExpressOrderProduct.CODE)
public class ModifyPresortExtension implements IPresortExtension {
    /** Log */
    private static final Logger LOGGER = LoggerFactory.getLogger(ModifyPresortExtension.class);

    /** 预分拣接口 */
    @Resource
    private PresortFacade presortFacade;

    /** 预分拣请求转换 */
    @Resource
    private PresortFacadeTranslator presortFacadeTranslator;

    /**
     * 预分拣场景 - 揽收
     */
    private static final Integer SCENE_TYPE_COLLECT = 1;

    /**
     * 预分拣场景 - 派送
     */
    private static final Integer SCENE_TYPE_DELIVER = 2;

    //605且站点id为-136时，属于疫情限售逻辑，此场景会由路由或预分拣返回指定话术，话术内容在overAreaReason
    private static final Integer PRESORT_SPECIAL_RESULT_STATUS = -136;

    /**
     * 非超区
     */
    private static final String PRESORT_RESULT_SUCCESS = "1";

    //邮政限制揽收二级超区码
    private final static String PRESORT_DAWK_DELIVERY_LIMIT = "-253";

    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        ExpressOrderModel orderModel = context.getOrderModel();

        try {
            LOGGER.info("修改订单预分拣扩展点执行开始");

            // C2B港澳关务数据的修改不涉及预分拣
            if (orderModel.isOnlyModifyCustoms()) {
                LOGGER.info("C2B港澳关务数据的修改不涉及预分拣--跳过预分拣");
                return;
            }

            ChangedPropertyDelegate changedPropertyDelegate = context.getChangedPropertyDelegate();
            // 前提条件：修改收寄件地址、产品、增值服务、站点信息、结算方式
            if (!ifNeedPresort(changedPropertyDelegate)) {
                return;
            }

            // 保存预分拣结果
            PresortResult presortResult = new PresortResult();
            PresortExtend presortExtend = new PresortExtend();
            List<PresortFacadeRequest> requestList = new ArrayList<>(2);
            ExpressOrderModel snapshot = orderModel.getOrderSnapshot();
            if (orderModel.getShipment() == null
                    || (orderModel.getShipment().getPickupType() == null
                    && StringUtils.isBlank(orderModel.getShipment().getStartStationNo()))) {
                if (PickupTypeEnum.SELF_DELIVERY == snapshot.getShipment().getPickupType()
                        || PickupTypeEnum.ON_SITE_PICK_CREATE_ORDER == snapshot.getShipment().getPickupType()) {
                    orderModel.complement().complementPickupType(this, snapshot.getShipment().getPickupType());
                    orderModel.complement().complementStartStationInfo(this, snapshot.getShipment().getStartStationNo(), snapshot.getShipment().getStartStationName());
                }
            }

            if (orderModel.getShipment() == null
                    || (orderModel.getShipment().getDeliveryType() == null
                    && StringUtils.isBlank(orderModel.getShipment().getEndStationNo()))) {
                if (DeliveryTypeEnum.SELF_PICKUP == snapshot.getShipment().getDeliveryType()) {
                    orderModel.complement().complementDeliveryType(this, snapshot.getShipment().getDeliveryType());
                    orderModel.complement().complementEndStationInfo(this, snapshot.getShipment().getEndStationNo(), snapshot.getShipment().getEndStationName());
                }
            }

            // 构建预分拣请求
            this.buildRequestList(context, requestList);

            Map<String, PresortFacadeResponse> presortFacadeResponseMap = null;
            if (!requestList.isEmpty()) {
                LOGGER.info("修改订单-订单号:{}，开始调用预分拣计算", orderModel.orderNo());
                presortFacadeResponseMap = presortFacade.batchComputePresort(requestList);
                if (null == presortFacadeResponseMap || presortFacadeResponseMap.size() != requestList.size()) {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                            .withCustom("修改订单派送站点预分拣计算失败, 订单号: " + orderModel.orderNo());
                }
                // 派送预分拣实际网络类型是否强B
                boolean isDeliveryTransportNetModeB = ShipmentUtil.isDeliveryTransportNetModeB(presortFacadeResponseMap.get(SCENE_TYPE_DELIVER.toString()));

                presortFacadeResponseMap.forEach((sceneType, response) -> {
                    checkPresortFacadeResult(orderModel, presortExtend, changedPropertyDelegate, response, sceneType, presortResult, isDeliveryTransportNetModeB);
                });
            }

            // 如果揽收/派送站点有变化，则补充到上下文订单模型中
            if (presortExtend.getStartStation() != null || presortExtend.getEndStation() != null) {
                presortFacadeTranslator.complementPresort(orderModel, presortExtend);
            }
            context.setPresortResult(presortResult);

            // 根据补全营业厅标示补全营业厅编码、营业厅名称
            presortFacadeTranslator.complementBusinessHall(context);

            // 增值产品后置处理
            presortFacadeTranslator.postProcessAddOnProduct(context, presortFacadeResponseMap);

        } catch (BusinessDomainException businessDomainException) {
            Profiler.functionError(callerInfo);
            LOGGER.error("修改预分拣扩展点执行异常, traceId={}", orderModel.traceId(), businessDomainException);
            throw businessDomainException;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("修改预分拣扩展点执行异常, traceId={}", orderModel.traceId(), exception);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL, exception).withCustom("预分拣信息校验失败");
        } finally {
            Profiler.registerInfoEnd(callerInfo);
            LOGGER.info("修改订单,预分拣扩展点执行结束");
        }
    }

    /**
     * 功能: 是否需要做预分拣
     * 以下几种情况需要做预分拣，否则直接跳过 -- 修改收寄件地址、产品、增值服务、站点信息、结算方式
     */
    private boolean ifNeedPresort(ChangedPropertyDelegate changedPropertyDelegate) {
        // 收件人地址是否发生变更
        return changedPropertyDelegate.consigneeAddressHaveChange()
                // 发件人地址是否发生变更
                || changedPropertyDelegate.consignorAddressHaveChange()
                // 产品信息是否发生变更
                || changedPropertyDelegate.productHaveChange()
                // 结算方式是否发生变更
                || changedPropertyDelegate.settlementTypeHaveChange()
                // 起始站点信息是否发生变更
                || changedPropertyDelegate.startStationHaveChange()
                // 目的站点信息是否发生变更
                || changedPropertyDelegate.endStationHaveChange();
    }

    /**
     * 构建预分拣的请求列表
     * 区分来源：客服系统&其他系统
     * @param context      上下文
     * @param requestList  请求列表[返回作入参]
     */
    private void buildRequestList(ExpressOrderContext context, List<PresortFacadeRequest> requestList) {
        ExpressOrderModel orderModel = context.getOrderModel();
        // 仓配的零担且强B不做揽收派送预分拣计算
        if (SystemCallerUtil.snapIsSupplyOFC(orderModel)
                && WarehouseModeUtil.flag(orderModel)
                && ShipmentUtil.isDeliveryTransportNetModeB(orderModel)) {
            // 【快运+冷链b2b+合同物流】仓配的零担且强B不做预分拣计算。此扩展点当前只有快运零担（非整车直达）使用，因此不判断零担
            LOGGER.info("仓配订单，仓配的零担且强B不做预分拣计算");
            return;
        }

        if (SystemCallerEnum.CSS.getCode().equals(orderModel.getChannel().getSystemCaller().getCode())) {
            // 客服系统可以同时修改揽收和派送的地址，但是不会修改站点信息，所以需要通过判断地址是否修改来构建请求
            ChangedPropertyDelegate changedPropertyDelegate = context.getChangedPropertyDelegate();
            // 快运不允许改寄件人地址，所以不涉及：订单标识orderSign.supplyChainDelivery=1，且为接货仓模式（deliveryPattern=1）不做揽收预分拣计算
            if (changedPropertyDelegate.consignorAddressHaveChange()) {
                PresortFacadeRequest startPresortFacadeRequest = presortFacadeTranslator.toPresortModifyFacadeRequest(orderModel, SCENE_TYPE_COLLECT);
                LOGGER.info("预分拣校验[揽收]入参:{}", JSONUtils.beanToJSONDefault(startPresortFacadeRequest));
                requestList.add(startPresortFacadeRequest);
            }
            if (changedPropertyDelegate.consigneeAddressHaveChange()) {
                // fixme 仓配逆向单暂时没有修改流程，不考虑当前单为逆向单且快照是原单
                if (SystemCallerUtil.snapIsSupplyOFC(orderModel)
                        && WarehouseModeUtil.flag(orderModel)
                        && ShipmentUtil.isDeliveryTransportNetModeB(orderModel)) {
                    // 【快运+冷链b2b+合同物流】仓配的零担且强B不做派送预分拣计算。此扩展点当前只有快运零担（非整车直达）使用，因此不判断零担
                    LOGGER.info("仓配订单，仓配的零担且强B不做派送预分拣计算");
                } else {
                    PresortFacadeRequest endPresortFacadeRequest = presortFacadeTranslator.toPresortModifyFacadeRequest(orderModel, SCENE_TYPE_DELIVER);
                    LOGGER.info("预分拣校验[派送]入参:{}", JSONUtils.beanToJSONDefault(endPresortFacadeRequest));
                    requestList.add(endPresortFacadeRequest);
                }
            }
        } else {
             if (orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp()) {
                LOGGER.info("订单号：{},揽收后修改，跳过揽收预分拣", orderModel.orderNo());
            } else if (StringUtils.isNotBlank(orderModel.getShipment().getStartStationNo())) {
                boolean result = this.checkPresortBaseSiteFacade(orderModel, orderModel.getShipment().getStartStationNo(), true);
                if (!result) {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                            .withCustom("揽收站点校验失败");
                }
            } else {
                 if (ModifySceneRuleUtil.isPickupTransferStation(context.getOrderModel())) {
                     LOGGER.info("揽收转站、跨站截单策略,不重新计算预分拣,只补全站点信息");
                     return;
                 }
                if (SupplyChainDeliveryOrderSignUtil.flag(orderModel)
                        && WarehouseModeUtil.flag(orderModel)
                        && BatrixSwitch.applyByBoolean(BatrixSwitchKey.SUPPLY_CHAIN_DELIVERY_SKIP_START_STATION_PRESORT)) {
                    LOGGER.info("订单标识orderSign.supplyChainDelivery=1，且为接货仓模式（deliveryPattern=1）不做揽收预分拣计算");
                } else {
                    // 构建请求查询预分拣接口获取揽收站点
                    PresortFacadeRequest startPresortFacadeRequest = presortFacadeTranslator.toPresortModifyFacadeRequest(orderModel, SCENE_TYPE_COLLECT);
                    LOGGER.info("预分拣校验[揽收]入参:{}", JSONUtils.beanToJSONDefault(startPresortFacadeRequest));
                    requestList.add(startPresortFacadeRequest);
                }
            }

            if (StringUtils.isNotBlank(orderModel.getShipment().getEndStationNo())) {
                boolean result = this.checkPresortBaseSiteFacade(orderModel, orderModel.getShipment().getEndStationNo(), false);
                if (!result) {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                            .withCustom("派送站点校验失败");
                }
            } else {
                if (ModifySceneRuleUtil.isPickupTransferStation(context.getOrderModel())) {
                    LOGGER.info("揽收转站、跨站截单策略,不重新计算预分拣,只补全站点信息");
                    return;
                }
                // fixme 仓配逆向单暂时没有修改流程，不考虑当前单为逆向单且快照是原单
                if (SystemCallerUtil.snapIsSupplyOFC(orderModel)
                        && WarehouseModeUtil.flag(orderModel)
                        && ShipmentUtil.isDeliveryTransportNetModeB(orderModel)) {
                    // 【快运+冷链b2b+合同物流】仓配的零担且强B不做派送预分拣计算。此扩展点当前只有快运零担（非整车直达）使用，因此不判断零担
                    LOGGER.info("仓配订单，仓配的零担且强B不做派送预分拣计算");
                } else {
                    // 构建请求查询预分拣接口获取派送站点
                    PresortFacadeRequest endPresortFacadeRequest = presortFacadeTranslator.toPresortModifyFacadeRequest(orderModel, SCENE_TYPE_DELIVER);
                    LOGGER.info("预分拣校验[派送]入参:{}", JSONUtils.beanToJSONDefault(endPresortFacadeRequest));
                    requestList.add(endPresortFacadeRequest);
                }
            }
        }

    }

    /**
     * 调用青龙进行站点校验
     * @param orderModel 订单模型
     * @param stationNo  站点号
     * @return 校验结果
     */
    private boolean checkPresortBaseSiteFacade(ExpressOrderModel orderModel, String stationNo, boolean start) {
        if (StringUtils.isBlank(stationNo)) {
            LOGGER.info("修改订单-订单号:{}未指定{}站点，需要调用预分拣计算", orderModel.orderNo(), start ? "揽收" : "派送");
            return false;
        }

        PresortBaseSiteFacadeResponse presortBaseSiteFacadeResponse =
                presortFacade.getBaseSiteBySiteId(presortFacadeTranslator.toPresortBaseSiteFacadeRequest(stationNo));
        if (null == presortBaseSiteFacadeResponse
                || !stationNo.equals(presortBaseSiteFacadeResponse.getSiteCode())) {
            // 修改的时候，站点信息不为空，校验不通过直接修改失败
            // 注：下单的时候，此处若是校验不通过会通过预分拣去寻找正确的站点信息并赋值
            String errMsg = "订单号:" + orderModel.orderNo() + (start ? "揽收" : "派送") + "站点:" + stationNo + "不为空，且调用青龙基础资料校验未通过，修改失败";
            LOGGER.error(errMsg);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                    .withCustom(errMsg);
        }

        LOGGER.info("订单号:{} {}站点:{}调用青龙基础资料校验通过", orderModel.orderNo(), start ? "揽收" : "派送", stationNo);
        presortFacadeTranslator.complementShipmentStationInfo(orderModel, presortBaseSiteFacadeResponse, start);
        return true;
    }

    /**
     * 校验预分拣计算结果
     */
    private void checkPresortFacadeResult(ExpressOrderModel orderModel,
                                          PresortExtend presortExtend,
                                          ChangedPropertyDelegate changedPropertyDelegate,
                                          PresortFacadeResponse presortFacadeResponse,
                                          String sceneType,
                                          PresortResult presortResult,
                                          boolean isDeliveryTransportNetModeB) {
        boolean startStation = (SCENE_TYPE_COLLECT.toString().equals(sceneType));
        String orderNo = orderModel.orderNo();
        // 配送要求 - 快照
        Shipment snapshotShipment = orderModel.getOrderSnapshot().getShipment();

        if (PresortRpcResultEnum.PRESORT_RPC_RESULT_200.getCode().equals(presortFacadeResponse.getResultCode())) {
            // 订单标识为仓配订单，派送信息交付模式为1，派送预分拣实际网络类型为强B，不处理揽收预分拣
            if (startStation
                    && SupplyChainDeliveryOrderSignUtil.flag(orderModel)
                    && WarehouseModeUtil.flag(orderModel)
                    && isDeliveryTransportNetModeB
                    && BatrixSwitch.applyByBoolean(BatrixSwitchKey.PRESORT_SKIP_START_STATION_SWITCH)) {
                    LOGGER.info("订单标识为仓配订单，派送信息交付模式为1，派送预分拣实际网络类型为强B，不处理揽收预分拣");
                    return;
            }

            LOGGER.info("订单号:{}订单修改,{}站点预分拣计算成功siteId:{}", orderNo, startStation ? "揽收" : "派送", presortFacadeResponse.getSiteId());
            computePresort(orderModel, presortResult, presortExtend, Integer.parseInt(sceneType), presortFacadeResponse);

            // 如果站点地址修改，则设置到修改项中
            String snapStationNo = startStation ? snapshotShipment.getStartStationNo() : snapshotShipment.getEndStationNo();
            if (!String.valueOf(presortFacadeResponse.getSiteId()).equals(snapStationNo)) {
                ChangedProperty changedProperty = new ChangedProperty();
                changedProperty.setOperateType(OperateTypeEnum.UPDATE);
                // 揽收派送分别赋值
                if (startStation) {
                    changedProperty.setProperty(ModifyItemConfigEnum.START_STATION_NO.getField());
                    changedProperty.setItemCode(ModifyItemConfigEnum.START_STATION_NO.getCode());
                    changedProperty.setPropertyDesc(ModifyItemConfigEnum.START_STATION_NO.getDesc());
                    changedProperty.setOriginValue(snapshotShipment.getStartStationNo());
                } else {
                    changedProperty.setProperty(ModifyItemConfigEnum.END_STATION_NO.getField());
                    changedProperty.setItemCode(ModifyItemConfigEnum.END_STATION_NO.getCode());
                    changedProperty.setPropertyDesc(ModifyItemConfigEnum.END_STATION_NO.getDesc());
                    changedProperty.setOriginValue(snapshotShipment.getEndStationNo());
                }
                changedProperty.setModifyValue(String.valueOf(presortFacadeResponse.getSiteId()));
                changedPropertyDelegate.addChangedProperties(changedProperty);
            }
        } else if (PresortRpcResultEnum.PRESORT_RPC_RESULT_605.getCode().equals(presortFacadeResponse.getResultCode())) {
            LOGGER.info("订单:{}{}站点预分拣计算超区resultMsg:{}", orderNo, startStation ? "揽收" : "派送", presortFacadeResponse.getResultMessage());
            String secondLevelOverAreaCode = null;
            if (presortFacadeResponse.getPresortRpcDto() != null) {
                secondLevelOverAreaCode = presortFacadeResponse.getPresortRpcDto().getSecondLevelOverAreaCode() != null ?
                        String.valueOf(presortFacadeResponse.getPresortRpcDto().getSecondLevelOverAreaCode()) : null;
            }
            if (presortFacadeResponse.getSiteId() != null && PRESORT_SPECIAL_RESULT_STATUS.equals(presortFacadeResponse.getSiteId())) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                        .withSubCode(PresortRpcResultEnum.PRESORT_RPC_RESULT_605.getCode() + "" + PRESORT_SPECIAL_RESULT_STATUS)
                        .withSubMessage(presortFacadeResponse.getOverAreaReason())
                        .withCustom(String.format("预分拣信息校验失败,修改订单%s站点预分拣计算失败 %s", startStation ? "揽收" : "派送", presortFacadeResponse.getResultMessage()));
            } else if (presortFacadeResponse.getPresortRpcDto() != null && PRESORT_DAWK_DELIVERY_LIMIT.equals(secondLevelOverAreaCode)) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                        .withCustom(String.format("预分拣信息校验失败,修改订单揽收站点预分拣计算失败 %s", presortFacadeResponse.getResultMessage()))
                        .withSubCode(PresortRpcResultEnum.PRESORT_RPC_RESULT_605.getCode() + "_" + PRESORT_DAWK_DELIVERY_LIMIT)
                        .putExt(OrderConstants.SPECIAL_LIMIT_INFO, JSONUtils.beanToJSONDefault(presortFacadeResponse.getSpecialLimitInfoDTO()))
                        .withSubMessage(presortFacadeResponse.getResultMessage());
            }else {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                        .withCustom(String.format("预分拣信息校验失败,修改订单%s站点预分拣计算失败 %s", startStation ? "揽收" : "派送", presortFacadeResponse.getResultMessage()))
                        .withSubCode(presortFacadeResponse.getResultCode())
                        .withSubMessage(presortFacadeResponse.getResultMessage());
            }
        } else {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                    .withCustom(String.format("订单%s,修改订单%s站点预分拣计算失败 %s", orderNo, startStation ? "揽收" : "派送", presortFacadeResponse.getResultMessage()))
                    .withSubCode(presortFacadeResponse.getResultCode())
                    .withSubMessage(presortFacadeResponse.getResultMessage());
        }
    }

    /**
     * 预分拣计算
     * 跟接单不同，只处理PRESORT_RPC_RESULT_200
     */
    private void computePresort(ExpressOrderModel orderModel,
                                PresortResult presortResult,
                                PresortExtend presortExtend,
                                int sceneType,
                                PresortFacadeResponse presortFacadeResponse) {
        //补齐预分拣扩展信息，需要下发给OFC
        if (SCENE_TYPE_COLLECT == sceneType) {
            presortFacadeTranslator.complementStartStation(orderModel, presortFacadeResponse, presortExtend);
        } else if (SCENE_TYPE_DELIVER == sceneType) {
            presortFacadeTranslator.complementEndStation(orderModel, presortFacadeResponse, presortExtend);

            // 补全接驳站点
            if(null != presortFacadeResponse.getPresortRpcDto() && null != presortFacadeResponse.getPresortRpcDto().getTransferPointId()){
                presortFacadeTranslator.complementEndTransferStation(orderModel, String.valueOf(presortFacadeResponse.getPresortRpcDto().getTransferPointId()));
                presortResult.setEndTransferStationNo(String.valueOf(presortFacadeResponse.getPresortRpcDto().getTransferPointId()));
            } else {
                //置为空
                presortFacadeTranslator.complementEndTransferStation(orderModel, "");
                presortResult.setEndTransferStationNo("");
            }
        }

        Integer resultCode = presortFacadeResponse.getResultCode();
        Integer siteId = presortFacadeResponse.getSiteId();
        Integer siteType = presortFacadeResponse.getSiteType();
        String siteName = presortFacadeResponse.getSiteName();

        if (PresortRpcResultEnum.PRESORT_RPC_RESULT_200.getCode().equals(resultCode)) {
            LOGGER.info("订单揽收站点预分拣计算成功siteId:{}", siteId);

            if (SCENE_TYPE_COLLECT == sceneType) {
                presortResult.setStartStationNo(siteId.toString());
                presortResult.setStartStationName(siteName);
                if (siteType != null) {
                    presortResult.setStartStationType(siteType.toString());
                }
                presortResult.setStartStationPresortResultType(PRESORT_RESULT_SUCCESS);
                if (presortFacadeResponse.getPresortRpcDto() != null) {
                    //揽收路区
                    presortResult.setStartRoadArea(presortFacadeResponse.getPresortRpcDto().getRoad());
                    PresortFacadeResponse.PresortRpcDto presortRpcDto = presortFacadeResponse.getPresortRpcDto();
                    //始发站点AOI路区
                    presortResult.setStartAoiCode(presortRpcDto.getAoiCode());
                    //始发站点物流模式
                    presortResult.setStartDeliveryType((presortRpcDto.getDeliveryType() == null) ? null : presortRpcDto.getDeliveryType().toString());
                    //始发站点超区编码
                    presortResult.setStartOverAreaCode((presortRpcDto.getSecondLevelOverAreaCode() == null) ? null : presortRpcDto.getSecondLevelOverAreaCode().toString());
                    //始发站点超区原因
                    presortResult.setStartOverAreaReason(presortRpcDto.getSecondLevelOverAreaValue());
                }
                presortExtend.getStartStation().setPresortResultType(PRESORT_RESULT_SUCCESS);
                presortFacadeTranslator.complementShipmentStartStationInfo(orderModel, siteId.toString(), siteType, siteName);
            } else if (SCENE_TYPE_DELIVER == sceneType) {
                presortResult.setEndStationNo(siteId.toString());
                presortResult.setEndStationName(siteName);
                if (siteType != null) {
                    presortResult.setEndStationType(siteType.toString());
                }
                if (presortFacadeResponse.getPresortRpcDto() != null) {
                    //派送路区
                    presortResult.setEndRoadArea(presortFacadeResponse.getPresortRpcDto().getRoad());
                    PresortFacadeResponse.PresortRpcDto presortRpcDto = presortFacadeResponse.getPresortRpcDto();
                    //目的站点AOI路区
                    presortResult.setEndAoiCode(presortRpcDto.getAoiCode());
                    //目的站点物流模式
                    presortResult.setEndDeliveryType((presortRpcDto.getDeliveryType() == null) ? null : presortRpcDto.getDeliveryType().toString());
                    //目的站点超区编码
                    presortResult.setEndOverAreaCode((presortRpcDto.getSecondLevelOverAreaCode() == null) ? null : presortRpcDto.getSecondLevelOverAreaCode().toString());
                    //目的站点超区原因
                    presortResult.setEndOverAreaReason(presortRpcDto.getSecondLevelOverAreaValue());
                }
                presortResult.setEndStationPresortResultType(PRESORT_RESULT_SUCCESS);
                presortExtend.getEndStation().setPresortResultType(PRESORT_RESULT_SUCCESS);
                presortFacadeTranslator.complementShipmentEndStationInfo(orderModel, siteId.toString(), siteType, siteName);
            }
        }
    }
}


