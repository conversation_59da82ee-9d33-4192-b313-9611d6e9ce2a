package cn.jdl.oms.express.horz.ext.init;

import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.MoneyInfo;
import cn.jdl.oms.core.model.ReaddressRecordDetailInfo;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.converter.ChannelMapper;
import cn.jdl.oms.express.domain.converter.ConsigneeMapper;
import cn.jdl.oms.express.domain.converter.CostMapper;
import cn.jdl.oms.express.domain.converter.FinanceMapper;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.converter.ProductMapper;
import cn.jdl.oms.express.domain.converter.ShipmentMapper;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.init.IInitExtension;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ReaddressSettlementTypeUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.ContextInfoEnum;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.InitiatorTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ModifyRecordTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductReaddressModeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.util.ReceiptCurrencyUtil;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.record.ModifyRecord;
import cn.jdl.oms.express.domain.vo.record.ModifyRecordDelegate;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.FinanceConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.OrderNoCreateUtil;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 改址一单到底，改址记录初始化
 */
@Extension(code = ExpressOrderProduct.CODE)
public class ReaddressInitExtension implements IInitExtension {
    /** Log */
    private static final Logger LOGGER = LoggerFactory.getLogger(ReaddressInitExtension.class);
    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("改址一单到底-改址记录初始化-扩展能力-执行开始!");
            //1 查询订单快照 获取订单已经存在的改址记录 size
            ExpressOrderModel orderModel = context.getOrderModel();//修改出入参转换
            ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();//订单快照数据
            if(null == orderModel.getFinance().getSettlementType()){//补全结算方式
                orderModel.getFinance().setSettlementType(orderSnapshot.getFinance().getSettlementType());
            }
            if(null == orderModel.getFinance().getPaymentStage()){//补全支付环节
                orderModel.getFinance().setPaymentStage(orderSnapshot.getFinance().getPaymentStage());
            }

            if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.READDRESS_JF_VALID_SWITCH)) {
                // 校验 改址一单到底不支持寄付后款
                if (SettlementTypeEnum.CASH_ON_PICK == orderModel.getFinance().getSettlementType()
                        && PaymentStageEnum.CASHONDELIVERY == orderModel.getFinance().getPaymentStage()) {
                    LOGGER.info("改址一单到底，不支持寄付后款改址: {}", orderModel.orderNo());
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("改址一单到底，不支持寄付后款改址");
                }
            }

            String productReaddressMode = null == orderModel.getProductDelegate().getProductReaddressMode()
                    ? ProductReaddressModeEnum.THROUGH_ORDER.getCode()
                    : orderModel.getProductDelegate().getProductReaddressMode();
            ProductReaddressModeEnum productReaddressModeEnum = ProductReaddressModeEnum.of(productReaddressMode);

            String modifyRecordType = ModifyRecordTypeEnum.READDRESS.getCode();// 默认改址记录
            if (ProductReaddressModeEnum.INTERCEPTION_THROUGH_ORDER == productReaddressModeEnum){
                modifyRecordType = ModifyRecordTypeEnum.INTERCEPT.getCode();// 拦截记录
            } else if (ProductReaddressModeEnum.REJECTION_ORDER == productReaddressModeEnum) {
                modifyRecordType = ModifyRecordTypeEnum.REJECT.getCode();// 拒收记录
            }

            //2.1 null || size = 0  初始化改址记录 0-原始记录；1第一条改址记录
            //2.2 size > 0 初始化本次改址记录，序号原子+1
            ModifyRecordDelegate modifyRecordDelegate = orderSnapshot.getModifyRecordDelegate();
            List<ModifyRecord> modifyRecordList = new ArrayList<>();
            List<ModifyRecord> enabledModifySortRecords = modifyRecordDelegate.getEnabledModifySortRecords();
            if(modifyRecordDelegate.isNotEmpty() && CollectionUtils.isNotEmpty(enabledModifySortRecords)){
                //历史有效改址记录
                modifyRecordList.addAll(enabledModifySortRecords);
                //新增最新改址记录 序号，
                modifyRecordList.add(initNewModifyRecord(context, orderSnapshot,orderModel,modifyRecordList.size(),false, modifyRecordType));
            } else {
                //无有效改址记录[超时未支付的清掉]
                firstReaddress(context, modifyRecordList,orderSnapshot,orderModel, modifyRecordType);
                //第一次改址打标
                context.putExtMaps(ContextInfoEnum.READDRESS_1ORDER_2END_FIRST.getCode(), OrderConstants.YES_VAL);
            }

            // 新增 先款修改标识 置于上下文 用于后续流程判断
            if ((null != orderModel.getFinance() && PaymentStageEnum.ONLINEPAYMENT == orderModel.getFinance().getPaymentStage())
                    || ReaddressSettlementTypeUtil.isTaoTianOriginalJiFu(orderModel)) {
                context.putExtMaps(ContextInfoEnum.PAY_STAGE_ONLINE_PAYMENT.getCode(), OrderConstants.YES_VAL);
            }

            // 同城同站处理逻辑，判断同城同站打skip标识，不询价不写账  加业务开关控制 港澳存在同城同站改址收费场景，不跳过
            if (orderModel.isSameSiteReaddress() && !orderSnapshot.isHKMO()) {
                // 原单未支付，且改址改原单结算方式为先款，先款合并支付，同城同站，重新询价写账，不跳过
                Map<String, String> orderNewFinanceExt = orderModel.getFinance().getExtendProps();//新单财务域扩展信息
                // 指定原单的支付环节
                String originPaymentStage = MapUtils.isNotEmpty(orderNewFinanceExt) ? orderNewFinanceExt.get(AttachmentKeyEnum.ORIGIN_PAYMENT_STAGE.getKey()) : null;
                // 指定原单的支付环节
                Integer originPaymentStageIntVal = StringUtils.isNotBlank(originPaymentStage) ? Integer.parseInt(originPaymentStage) : null;
                // 改址的支付环节
                PaymentStageEnum paymentStage = orderModel.getFinance().getPaymentStage();

                if (PaymentStatusEnum.COMPLETE_PAYMENT != orderSnapshot.getFinance().getPaymentStatus()
                        && PaymentStageEnum.ONLINEPAYMENT.getCode().equals(originPaymentStageIntVal)
                        && PaymentStageEnum.ONLINEPAYMENT == paymentStage) {
                    LOGGER.info("原单未支付，且改址改原单结算方式为先款，先款合并支付，同城同站，重新询价写账，不跳过");
                } else {
                    if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.READDRESS_SAME_SITE_SKIP_ENQUIRY_SWITCH)) {
                        LOGGER.info("改址一单到底-同城同站-跳过询价写台账-开关开启动true：打跳过标识");
                        Map<String, String> map = new HashMap<>();
                        map.put(FinanceConstants.SKIP_ENQUIRY_ORDER_BANK, OrderConstants.YES_VAL);
                        orderModel.complement().complementFinanceInfoExtendProps(this, map);
                    } else {
                        LOGGER.info("改址一单到底-同城同站-跳过询价写台账-开关关闭false：不跳过");
                    }
                }
            }

            // 跳过计费写账标识
            if(null != orderModel.getFinance() && MapUtils.isNotEmpty(orderModel.getFinance().getExtendProps())
                    && StringUtils.isNotBlank(orderModel.getFinance().getExtendProps().get(FinanceConstants.SKIP_ENQUIRY_ORDER_BANK))
                    && OrderConstants.YES_VAL.equals(orderModel.getFinance().getExtendProps().get(FinanceConstants.SKIP_ENQUIRY_ORDER_BANK))){
                CurrencyCodeEnum currencyCodeEnum = getCurrencyCodeEnum(orderSnapshot);
                context.putExtMaps(ContextInfoEnum.PAY_STAGE_ONLINE_PAYMENT.getCode(), OrderConstants.NO_VAL);
                context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY.getCode(), OrderConstants.NO_VAL);
                context.putExtMaps(ContextInfoEnum.READDRESS_NEED_REFUND.getCode(), OrderConstants.NO_VAL);
                context.putExtMaps(ContextInfoEnum.SKIP_ENQUIRY_ORDER_BANK.getCode(), OrderConstants.YES_VAL);
                context.putExtMaps(ContextInfoEnum.READDRESS_NEED_PAY_MONEY.getCode(), defMoneyZero(currencyCodeEnum));
                ModifyRecord modifyRecord = modifyRecordList.get(modifyRecordList.size() - 1);
                modifyRecord.setModifyRecordStatus(ModifyRecordDelegate.MODIFY_RECORD_STATUS_ENABLE);
                ReaddressRecordDetailInfo modifyRecordDetail = (ReaddressRecordDetailInfo) modifyRecord.getModifyRecordDetail();
                modifyRecordDetail.getFinance().setPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT.getPaymentStatus());//已支付
                modifyRecordDetail.getFinance().setEnquiryStatus(EnquiryStatusEnum.INQUIRED.getCode());//已询价
                modifyRecordDetail.getFinance().setPendingMoney(defMoneyZero(currencyCodeEnum));//待支付金额0
                ModifyRecord preModifyRecord = modifyRecordList.get(modifyRecordList.size() - 2);
                ReaddressRecordDetailInfo preModifyRecordDetail = (ReaddressRecordDetailInfo) preModifyRecord.getModifyRecordDetail();
                modifyRecordDetail.getFinance().setDiscountAmount(preModifyRecordDetail.getFinance().getDiscountAmount());//上次询价后的折后金额
                //如果第一次改址，将快照记录改址状态置为生效
                if (OrderConstants.YES_VAL.equals(context.getExtInfo(ContextInfoEnum.READDRESS_1ORDER_2END_FIRST.getCode()))) {
                    preModifyRecord.setModifyRecordStatus(ModifyRecordDelegate.MODIFY_RECORD_STATUS_ENABLE);
                }
            }

            //2.3 初次改址，初始化校验
            // 支付成功校验 [一单到底：只限制首次改址，改后先款，改前寄付现结，若当前支付状态不是已支付，不允许改址]
            if (OrderConstants.YES_VAL.equals(context.getExtInfo(ContextInfoEnum.READDRESS_1ORDER_2END_FIRST.getCode()))
                    && OrderConstants.YES_VAL.equals(context.getExtInfo(ContextInfoEnum.PAY_STAGE_ONLINE_PAYMENT.getCode()))
                    && SettlementTypeEnum.CASH_ON_PICK == orderSnapshot.getFinance().getSettlementType()
                    && PaymentStatusEnum.COMPLETE_PAYMENT != orderSnapshot.getFinance().getPaymentStatus()){
                LOGGER.error("订单:{}初次改址，先款改址，原单为寄付现结，订单未支付成功，不允许改址操作",orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("订单尚未支付，不能发起改址，若确认已支付，则联系工作人员处理");
            }

            //3 封装所有的记录信息到一个list 放在上下文
            modifyRecordDelegate.setModifyRecords(modifyRecordList);
            context.setModifyRecordDelegate(modifyRecordDelegate);

            //4 后款，改址次数+1
            if (!OrderConstants.YES_VAL.equals(context.getExtInfo(ContextInfoEnum.PAY_STAGE_ONLINE_PAYMENT.getCode()))) {
                String readdressTimes = Optional.ofNullable(orderModel.getOrderSnapshot().getAttachment(OrderConstants.READDRESS_TIMES)).orElse("0");
                int readdressTimesVal = 0;
                if(StringUtils.isNumeric(readdressTimes)){
                    readdressTimesVal = Integer.parseInt(readdressTimes);
                }
                orderModel.putAttachment(OrderConstants.READDRESS_TIMES,readdressTimesVal + 1);
            }
            LOGGER.info("改址一单到底-改址记录初始化-扩展能力-执行结束!");
        } catch (BusinessDomainException e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("改址一单到底-改址记录初始化-扩展能力-业务异常", e);
            throw e;
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("改址一单到底-改址记录初始化-扩展能力-执行异常", e);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("改址一单到底-改址记录初始化-扩展能力-执行异常");
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 初始化原始记录
     * @param orderSnapshot
     * @return
     */
    private ModifyRecord initOriginModifyRecord(ExpressOrderContext context,
                                                ExpressOrderModel orderSnapshot,
                                                ExpressOrderModel orderModel,
                                                String modifyRecordType){
        ModifyRecord originModifyRecord = new ModifyRecord();
        originModifyRecord.setOrderNo(orderSnapshot.orderNo());
        //第一条为原单记录，改址记录号设置为原单号，供终端进行台账合并查询
        originModifyRecord.setModifyRecordNo(orderSnapshot.getCustomOrderNo());
        originModifyRecord.setModifyRecordStatus(ModifyRecordDelegate.MODIFY_RECORD_STATUS_DISABLE);
        //后款订单不需要同步询价 不需要支付 初始化完成直接置为生效
        if (!OrderConstants.YES_VAL.equals(context.getExtInfo(ContextInfoEnum.PAY_STAGE_ONLINE_PAYMENT.getCode()))) {
            originModifyRecord.setModifyRecordStatus(ModifyRecordDelegate.MODIFY_RECORD_STATUS_ENABLE);
        }
        originModifyRecord.setModifyRecordSequence(0);
        originModifyRecord.setModifyRecordType(modifyRecordType);
        originModifyRecord.setOperator(orderSnapshot.getOperator());
        originModifyRecord.setOperateTime(orderSnapshot.getOperateTime());

        // 初始化扩展字段
        Map<String, String> extendProps = originModifyRecord.getExtendProps();
        if (orderSnapshot.isHKMO()) {
            if (MapUtils.isEmpty(extendProps)) {
                extendProps = new HashMap<>();
            }
            extendProps.put(OrderConstants.HKMO_FLAG, OrderConstants.YES_VAL);
            originModifyRecord.setExtendProps(extendProps);
        }

        ReaddressRecordDetailInfo readdressRecordDetailInfo = new ReaddressRecordDetailInfo();
        FinanceInfo financeInfo = FinanceMapper.INSTANCE.toFinanceInfo(orderSnapshot.getFinance());

        if(SettlementTypeEnum.CASH_ON_DELIVERY==orderSnapshot.getFinance().getSettlementType()){
            //如果原单是到付现结，需要根据修改后的结算方式，更新第一条改址记录。目的是处理：到付-》寄付，后款-》先款。
            //月结不需要处理，月结-》寄付/到付，后款-》先款的场景，台账金额都是记录到当前的改址记录里
            Map<String, String> financeExt = orderModel.getFinance().getExtendProps();//新单财务域扩展信息
            // 原单的结算方式
            String orderSettlementType = MapUtils.isNotEmpty(financeExt) ? financeExt.get(AttachmentKeyEnum.ORIGIN_SETTLEMENT_TYPE.getKey()) : null;
            if (StringUtils.isNotBlank(orderSettlementType)) {
                financeInfo.setSettlementType(Integer.valueOf(orderSettlementType));
            }
            // 原单的支付环节
            String orderPaymentStage = MapUtils.isNotEmpty(financeExt) ? financeExt.get(AttachmentKeyEnum.ORIGIN_PAYMENT_STAGE.getKey()) : null;
            if (StringUtils.isNotBlank(orderPaymentStage)) {
                financeInfo.setPaymentStage(Integer.valueOf(orderPaymentStage));
            }
        }

        CurrencyCodeEnum currencyCodeEnum = getCurrencyCodeEnum(orderSnapshot);
        financeInfo.setReceivableDifferenceAmount(defMoneyZero(currencyCodeEnum));
        financeInfo.setRefundedAmount(defMoneyZero(currencyCodeEnum));
        if(null == financeInfo.getDiscountAmount() || null == financeInfo.getDiscountAmount().getAmount()){
            financeInfo.setOriginalReceivedAmount(defMoneyZero(currencyCodeEnum));
            financeInfo.setPendingMoney(defMoneyZero(currencyCodeEnum));
            financeInfo.setReceivableDifferenceAmount(defMoneyZero(currencyCodeEnum));
        } else {
            financeInfo.setOriginalReceivedAmount(MoneyMapper.INSTANCE.toMoneyInfo(financeInfo.getDiscountAmount().getAmount(),financeInfo.getDiscountAmount().getCurrencyCode()));
            financeInfo.setPendingMoney(MoneyMapper.INSTANCE.toMoneyInfo(financeInfo.getDiscountAmount().getAmount(),financeInfo.getDiscountAmount().getCurrencyCode()));
            financeInfo.setReceivableDifferenceAmount(MoneyMapper.INSTANCE.toMoneyInfo(financeInfo.getDiscountAmount().getAmount(),financeInfo.getDiscountAmount().getCurrencyCode()));//从原单退款时是通过差额判断可退金额
        }
        //financeInfo.setPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT.getPaymentStatus());
        readdressRecordDetailInfo.setFinance(financeInfo);
        //readdressRecordDetailInfo.setShipment(orderSnapshot.getShipment());
        readdressRecordDetailInfo.setOldConsignee(ConsigneeMapper.INSTANCE.toConsigneeInfo(orderSnapshot.getConsignee()));
        readdressRecordDetailInfo.setProductInfos(ProductMapper.INSTANCE.toProductInfos(orderSnapshot.getProductDelegate().getProductList()));
        originModifyRecord.setModifyRecordDetail(readdressRecordDetailInfo);
        readdressRecordDetailInfo.setInitiatorType(InitiatorTypeEnum.SYSTEM.getCode());
        return originModifyRecord;
    }

    /**
     * 第一次改址
     * @param modifyRecordList
     * @param orderSnapshot
     * @param orderModel
     */
    private void firstReaddress(ExpressOrderContext context,
                                List<ModifyRecord> modifyRecordList,
                                ExpressOrderModel orderSnapshot,
                                ExpressOrderModel orderModel,
                                String modifyRecordType) {
        // 用快照数据初始化第一次改址记录
        modifyRecordList.add(initOriginModifyRecord(context, orderSnapshot, orderModel, modifyRecordType));
        modifyRecordList.add(initNewModifyRecord(context, orderSnapshot,orderModel,1,true, modifyRecordType));
    }

    /**
     * 多次改址
     * @param orderSnapshot
     * @param order
     * @param recordSequence 序号
     * @param firstModify 是否第一次改址
     * @return
     */
    private ModifyRecord initNewModifyRecord(ExpressOrderContext context,
                                             ExpressOrderModel orderSnapshot,
                                             ExpressOrderModel order,
                                             Integer recordSequence,
                                             boolean firstModify,
                                             String modifyRecordType){
        ModifyRecord newModifyRecord = new ModifyRecord();
        newModifyRecord.setOrderNo(orderSnapshot.orderNo());
        newModifyRecord.setOperator(order.getOperator());
        newModifyRecord.setModifyRecordSequence(recordSequence);
        newModifyRecord.setOperateTime(order.getOperateTime());
        newModifyRecord.setModifyRecordStatus(ModifyRecordDelegate.MODIFY_RECORD_STATUS_DISABLE);
        //后款订单不需要同步询价 不需要支付 初始化完成直接置为生效
        if (!OrderConstants.YES_VAL.equals(context.getExtInfo(ContextInfoEnum.PAY_STAGE_ONLINE_PAYMENT.getCode()))) {
            newModifyRecord.setModifyRecordStatus(ModifyRecordDelegate.MODIFY_RECORD_STATUS_ENABLE);
        }
        newModifyRecord.setModifyRecordNo(getReaddressModifyRecordNo(orderSnapshot,firstModify,1));
        if (order.isFreight() || UnitedB2CUtil.isUnitedFreightB2C(order)) {
            newModifyRecord.setModifyRecordNo(getFreightModifyRecordNo(orderSnapshot, firstModify, 1));
        }
        newModifyRecord.setModifyRecordType(modifyRecordType);
        Map<String, String> extendProps = newModifyRecord.getExtendProps();
        if (order.isHKMO()) {
            if (MapUtils.isEmpty(extendProps)) {
                extendProps = new HashMap<>();
            }
            extendProps.put(OrderConstants.HKMO_FLAG, OrderConstants.YES_VAL);
            newModifyRecord.setExtendProps(extendProps);
        }

        ReaddressRecordDetailInfo readdressRecordDetailInfo = new ReaddressRecordDetailInfo();
        readdressRecordDetailInfo.setChannel(ChannelMapper.INSTANCE.toChannelInfo(order.getChannel()));
        readdressRecordDetailInfo.setRecordSequence(recordSequence);
        FinanceInfo financeInfo = FinanceMapper.INSTANCE.toFinanceInfo(order.getFinance());
        // 更新修改记录里的询价状态 和 支付状态
        CurrencyCodeEnum currencyCodeEnum = getCurrencyCodeEnum(orderSnapshot);
        financeInfo.setReceivableDifferenceAmount(defMoneyZero(currencyCodeEnum));
        financeInfo.setDiscountAmount(defMoneyZero(currencyCodeEnum));
        financeInfo.setRefundedAmount(defMoneyZero(currencyCodeEnum));
        financeInfo.setOriginalReceivedAmount(defMoneyZero(currencyCodeEnum));
        financeInfo.setEnquiryStatus(EnquiryStatusEnum.WAITE_ENQUIRY.getCode());
        financeInfo.setPaymentStatus(PaymentStatusEnum.NO_PAYMENT.getStatus());
        List<CostInfo> attachFees = order.getFinance().nonDeletedAttachFees();
        financeInfo.setAttachFees(CostMapper.INSTANCE.toCostInfos(attachFees));
        readdressRecordDetailInfo.setFinance(financeInfo);
        readdressRecordDetailInfo.setShipment(ShipmentMapper.INSTANCE.toShipmentInfo(order.getShipment()));
        readdressRecordDetailInfo.setNewConsignee(ConsigneeMapper.INSTANCE.toConsigneeInfo(order.getConsignee()));
        readdressRecordDetailInfo.setOldConsignee(ConsigneeMapper.INSTANCE.toConsigneeInfo(orderSnapshot.getConsignee()));
        List<Product> products = order.getProductDelegate().nonDeletedProducts();
        readdressRecordDetailInfo.setProductInfos(ProductMapper.INSTANCE.toProductInfos(products));
        newModifyRecord.setModifyRecordDetail(readdressRecordDetailInfo);
        Map<String,String> ext = new HashMap<>();
        //改址多方支付标识，用于做线上兼容逻辑
        ext.put(AttachmentKeyEnum.READDRESS_MULTI_PAY.getKey(), OrderConstants.YES_VAL);
        readdressRecordDetailInfo.setExtendProps(ext);
        readdressRecordDetailInfo.setInitiatorType(order.getInitiatorType().getCode());
        return newModifyRecord;
    }

    private static MoneyInfo defMoneyZero(CurrencyCodeEnum currencyCodeEnum){
        MoneyInfo moneyInfo = new MoneyInfo();
        moneyInfo.setAmount(BigDecimal.ZERO);
        moneyInfo.setCurrencyCode(currencyCodeEnum.getCode());
        return moneyInfo;
    }


    /**
     * 获取改址记录号
     * @param orderSnapshot 订单原始数据
     * @param firstModify 是否第一次改址
     * @param offSet 偏移量，第一次改址时生成两笔记录 新纪录需要偏移1
     * GZ_EO订单号_写序号
     * @return
     */
    private String getReaddressModifyRecordNo(ExpressOrderModel orderSnapshot, boolean firstModify, Integer offSet){
        String modifyInfoSequence = Optional.ofNullable(orderSnapshot.getAttachment(OrderConstants.MODIFY_INFO_SEQUENCE)).orElse("0");
        int modifyInfoSequenceVal = 0;
        if(StringUtils.isNumeric(modifyInfoSequence)){
            modifyInfoSequenceVal = Integer.parseInt(modifyInfoSequence);
        }
        int addValue = firstModify ? modifyInfoSequenceVal + 1 + offSet : modifyInfoSequenceVal + 1;
        return OrderNoCreateUtil.MODIFY_RECORD_NO_PREFIX + addValue + OrderNoCreateUtil.MODIFY_RECORD_NO_PREFIX +  orderSnapshot.getCustomOrderNo();
    }

    /**
     * 获取快运变更记录号
     * @param orderSnapshot 订单原始数据
     * @param firstModify 是否第一次改址
     * @param offSet 偏移量，第一次改址时生成两笔记录 新纪录需要偏移1
     * GZ_EO订单号_写序号
     * @return
     */
    private String getFreightModifyRecordNo(ExpressOrderModel orderSnapshot, boolean firstModify, Integer offSet){
        String modifyInfoSequence = Optional.ofNullable(orderSnapshot.getAttachment(OrderConstants.MODIFY_INFO_SEQUENCE)).orElse("0");
        int modifyInfoSequenceVal = 0;
        if(StringUtils.isNumeric(modifyInfoSequence)){
            modifyInfoSequenceVal = Integer.parseInt(modifyInfoSequence);
        }
        int addValue = firstModify ? modifyInfoSequenceVal + 1 + offSet : modifyInfoSequenceVal + 1;
        return OrderNoCreateUtil.MODIFY_RECORD_NO_PREFIX + addValue + OrderNoCreateUtil.MODIFY_RECORD_NO_PREFIX +  orderSnapshot.getCustomOrderNo();
    }

    /**
     * 从快照获取币种
     */
    private CurrencyCodeEnum getCurrencyCodeEnum(ExpressOrderModel orderSnapshot) {
        CurrencyCodeEnum currencyCodeEnum = CurrencyCodeEnum.CNY;
        if(orderSnapshot.isHKMO() || orderSnapshot.isIntl()){
            if(null != orderSnapshot.getConsignor()
                    && null != orderSnapshot.getConsignor().getAddress()
                    && StringUtils.isNotBlank(orderSnapshot.getConsignor().getAddress().getRegionNo())
                    && null != orderSnapshot.getConsignee()
                    && null != orderSnapshot.getConsignee().getAddress()
                    && StringUtils.isNotBlank(orderSnapshot.getConsignee().getAddress().getRegionNo())
            ){
                String startRegionNo = orderSnapshot.getConsignor().getAddress().getRegionNo();
                String endRegionNo = orderSnapshot.getConsignee().getAddress().getRegionNo();
                if(null != orderSnapshot.getFinance() && null != orderSnapshot.getFinance().getSettlementType()){
                    //换汇币种
                    // fixme 当前港澳改址改派只有同城改址，原单结算方式和改址结算方式不同不影响换汇币种；后续放开非同城改址需要考虑传改址结算方式
                    return ReceiptCurrencyUtil.getCurrency(startRegionNo, endRegionNo, orderSnapshot.getFinance().getSettlementType().getCode());
                }
            }
        }
        return currencyCodeEnum;
    }
}
