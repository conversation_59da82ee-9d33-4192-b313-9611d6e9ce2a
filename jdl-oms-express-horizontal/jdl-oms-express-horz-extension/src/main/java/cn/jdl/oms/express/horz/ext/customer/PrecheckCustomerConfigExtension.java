package cn.jdl.oms.express.horz.ext.customer;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CustomerConfig;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.customer.ICustomerConfigExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.TraderOperateStateEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.dict.UnifiedSubErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2024/3/14 13:40:22
 * @Description 前置校验客户配置信息校验
 * @Version 1.0
 */
@Extension(code = ExpressOrderProduct.CODE)
public class PrecheckCustomerConfigExtension implements ICustomerConfigExtension {

    private static final Logger LOGGER = LoggerFactory.getLogger(PrecheckCustomerConfigExtension.class);

    /**
     * 客户配置信息
     */
    @Resource
    private CustomerConfigFacade customerConfigFacade;

    @Override
    public void execute(ExpressOrderContext expressOrderContext) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("前置校验客户配置信息校验开始：");
            ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
            if (OrderTypeEnum.RETURN_ORDER == orderModel.getOrderType()) {
                LOGGER.info("客户配置信息校验，逆向单不校验客户信息");
                return;
            }
            CustomerConfig customerConfig = customerConfigFacade.getCustomerConfig(expressOrderContext);
            // 青龙业主号在商家工作台中无记录，校验失败
            if (customerConfig == null) {
                LOGGER.error("客户配置信息校验失败，未查到相关客户信息");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0007.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0007.desc());
            }
            // 青龙业主号有记录时，商家状态异常，校验失败
            if (!TraderOperateStateEnum.normal.getCode().equals(customerConfig.getTraderOperateState())) {
                LOGGER.error("青龙业主号:{}状态异常，TraderOperateState:{}"
                        , orderModel.getCustomer().getAccountNo()
                        , customerConfig.getTraderOperateState());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withCustom("客户配置信息校验失败,商家青龙业主号状态异常")
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.desc());
            }
            if (expressOrderContext.isReaddressScene()) {
                //校验trader_sign 171, 商家是否开通允许收件人改址
                consigneeReaddressCheck(orderModel, customerConfig);
            }
        } catch (InfrastructureException infrastructureException) {
            LOGGER.error("前置校验客户配置信息校验扩展点执行异常, {}", infrastructureException.fullMessage());
            throw infrastructureException;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("前置校验客户配置信息校验扩展点执行异常", exception);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL, exception);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }
}
