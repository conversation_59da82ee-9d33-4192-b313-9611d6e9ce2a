package cn.jdl.oms.express.horz.ext.white;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.white.IWhiteExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.StationTypeEnum;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.drools.IDroolsRuleService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.drools.dto.DroolsRuleDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.drools.dto.DroolsRuleTranslator;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisClient;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductAttrEnum;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.InitiatorTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.modify.ChangedProperty;
import cn.jdl.oms.express.domain.vo.modify.ChangedPropertyDelegate;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.ModifySceneRuleConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.ShipmentConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyMarkEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.ReaddressMarkEnum;
import cn.jdl.oms.express.shared.common.dict.SystemSubCallerEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.shared.common.utils.ModifyMarkUtil;
import com.alibaba.fastjson.JSONArray;
import com.jd.matrix.sdk.annotation.Extension;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName ModifyWhiteExtension
 * @Description 京喜达修改白名单校验
 * <AUTHOR>
 * @Date 2021/4/3 9:58 下午
 * @ModifyDate 2021/4/3 9:58 下午
 * @Version 1.0
 */
@Extension(code = ExpressOrderProduct.CODE)
public class ModifyWhiteExtension implements IWhiteExtension {
    private static final Logger LOGGER = LoggerFactory.getLogger(ModifyWhiteExtension.class);

    private static final String CODE_ALL = "ALL";

    /**
     * 白名单的前缀
     */
    private static final String WHITE_PREFIX = "DOO:WHITE";

    @Resource
    private IDroolsRuleService droolsRuleService;

    @Resource
    private IRedisClient redisClient;

    @Resource
    private DroolsRuleTranslator droolsRuleTranslator;

    @Resource
    private CustomerFacade customerFacade;

    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    @Override
    public void execute(ExpressOrderContext expressOrderContext) throws AbilityExtensionException {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        ExpressOrderModel snapshot = orderModel.getOrderSnapshot();

        ChangedPropertyDelegate changedPropertyDelegate = expressOrderContext.getChangedPropertyDelegate();

        String modifyMark = null;

        if (orderModel.getOrderSnapshot().getExtendProps() != null) {
            modifyMark = orderModel.getOrderSnapshot().getExtendProps().get(AttachmentKeyEnum.MODIFY_MARK.getKey());
        }

        if (StringUtils.isBlank(modifyMark)) {
            modifyMark = ModifyMarkUtil.getInitMark();
        }

        String majorProductNo = orderModel.getOrderSnapshot().getProductDelegate().getMajorProductNo();
        if (BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.MODIFY_ORDER_BLACK_MAIN_PRODUCT_LIST, majorProductNo)) {
            LOGGER.error("主产品不允许修改：{}", majorProductNo);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("主产品不允许修改");
        }

        // TODO 重构产品维度的校验代码
        // TODO 是否也可以用上面逻辑 校验 当前单和快照的主产品不允许修改
        if (changedPropertyDelegate.productHaveChange()) {
            String modifiedMainProduct = orderModel.getProductDelegate().getMajorProductNo();
            if (StringUtils.isNotBlank(modifiedMainProduct)
                    && BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.MODIFY_ORDER_BLACK_MAIN_PRODUCT_LIST, modifiedMainProduct)) {
                LOGGER.error("修改后主产品不允许修改：{}", majorProductNo);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("修改后主产品不允许修改");
            }
        }

        // 如果是港澳订单
        if (orderModel.getOrderSnapshot().isHKMO()) {
            // 订单类型
            String orderType = (null != orderModel.getOrderType()) ? orderModel.getOrderType().getCode() : null;
            // 修改策略为"仅修改报关信息"
            if (orderModel.isOnlyModifyCustoms()) {
                // 如果修改的内容不在白名单中则不允许修改
                validateOnlyModifyFields(changedPropertyDelegate, orderModel);
                // 如果删除字段不在白名单中则不允许修改
                validateOnlyModifyClearFields(orderModel);
            } else if (OrderTypeEnum.RETURN_ORDER.getCode().equals(orderType)) {
                // 如果是逆向单, 则修改策略必填 "仅修改报关信息"
                LOGGER.error("港澳逆向单修改时修改策略必填--仅修改报关信息, orderNo: {} ", expressOrderContext.getOrderModel().orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("港澳逆向单修改时修改策略必填--仅修改报关信息");
            }
        }

        // 如果是国际C2C
        if (orderModel.getOrderSnapshot().isIntlC2C()) {
            // 国际C2C业务退单不允许修改
            String orderType = (null != orderModel.getOrderType()) ? orderModel.getOrderType().getCode() : null;
            if (OrderTypeEnum.RETURN_ORDER.getCode().equals(orderType)) {
                LOGGER.error("国际C2C业务退单不允许修改, orderNo: {} ", expressOrderContext.getOrderModel().orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("国际C2C业务退单不允许修改");
            }

            // 修改策略为"仅修改报关信息"
            if (orderModel.isOnlyModifyCustoms()) {
                // 白名单目前国际C2C和港澳共用一个
                // 如果修改的内容不在白名单中则不允许修改
                validateOnlyModifyFields(changedPropertyDelegate, orderModel);
                // 如果删除字段不在白名单中则不允许修改
                validateOnlyModifyClearFields(orderModel);
            }
        }

        // 删除黑名单能力点，并将原有内容（ModifyBlackExtension）迁移到白名单中
        // 1.通过扩展字段的属性判断修改的策略编码
        String sceneRuleVal = ModifySceneRuleUtil.getModifySceneRule(orderModel);

        // 2.改址单仅支持 仅修改收件人联系方式 修改联系方式
        if (OrderTypeEnum.READDRESS == orderModel.getOrderSnapshot().getOrderType()) {
            if (!ModifySceneRuleUtil.isOnlyModifyConsigneeContactInformation(sceneRuleVal)
                    && !ModifySceneRuleUtil.isModifyContactInformation(sceneRuleVal)
                    && !ModifySceneRuleUtil.isCarbonEmissionCalculation(sceneRuleVal)) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.MODIFY_BLANK_VALIDATE_FAIL)
                        .withCustom("改址单修改策略仅支持仅修改收件人信息、修改联系方式");
            }
        }
        // 3.仅修改收件人联系方式（姓名、电话、手机）
        onlyModifyConsigneeContactInformation(orderModel, sceneRuleVal);

        // 4.修改联系方式
        modifyContactInformation(orderModel, sceneRuleVal);

        // 策略为修改运营模式（modifyOpMode）时，校验修改字段是否在白名单中
        if(ModifySceneRuleConstants.MODIFY_OP_MODE.equals(sceneRuleVal)) {
            validateModifyOpMode(changedPropertyDelegate, orderModel);
        }

        // 支付方式（1、微信代扣，2、白条代扣）只支持寄付现结，不支持到付现结和寄付月结
        if (orderModel.getFinance() != null) {
            PaymentTypeEnum payment = null;
            SettlementTypeEnum settlementType = null;
            // 额外判断当前支付方式是否被清空
            if (!isPaymentCleared(orderModel)) {
                if (orderModel.getFinance().getPayment() != null) {
                    payment = orderModel.getFinance().getPayment();
                } else if (orderModel.getOrderSnapshot() != null && orderModel.getOrderSnapshot().getFinance() != null) {
                    payment = orderModel.getOrderSnapshot().getFinance().getPayment();
                }
                if (orderModel.getFinance().getSettlementType() != null) {
                    settlementType = orderModel.getFinance().getSettlementType();
                }else if (orderModel.getOrderSnapshot() != null && orderModel.getOrderSnapshot().getFinance() != null) {
                    settlementType = orderModel.getOrderSnapshot().getFinance().getSettlementType();
                }
                if ((PaymentTypeEnum.WECHAT_WITHHOLDING == payment || PaymentTypeEnum.BAITIAO_WITHHOLDING == payment)
                        && SettlementTypeEnum.CASH_ON_PICK != settlementType) {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("支付方式为微信代扣或白条代扣时，只支持寄付现结");
                }
            }
        }

        // 揽收方式：自送  揽收站点类型：站点子类型（编号：56），待支付状态自助寄件柜订单不支持修改；
        if(snapshot.getShipment()!= null
                && snapshot.getShipment().getStartStationType() != null
                && StationTypeEnum.ZIJIGUI_TYPE.getType().equals(orderModel.getShipment().getStartStationType())) {
            if(snapshot.getFinance() != null && PaymentStatusEnum.WAITING_FOR_PAYMENT == snapshot.getFinance().getPaymentStatus()) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("待支付状态自助寄件柜订单不支持修改");
            }
        }

        // 订单中心修改的systemsubcaller=5 便民点（5 乐加APP），结算方式不允许修改为：月结
        if(changedPropertyDelegate.settlementTypeHaveChange()
                && (SettlementTypeEnum.MONTHLY_PAYMENT == orderModel.getFinance().getSettlementType()
                || SettlementTypeEnum.MONTHLY_PAYMENT_DELIVERY == orderModel.getFinance().getSettlementType())) {
            if(SystemSubCallerEnum.BIAN_MIN.getCode().equals(orderModel.getChannel().getSystemSubCaller())) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("便民点订单，结算方式不允许修改为月结");
            }
        }

        // 获取修改策略
        String modifySceneRule = ModifySceneRuleUtil.getModifySceneRule(orderModel);

        // 仅修改收件人联系方式（姓名、电话、手机）
        if (ModifySceneRuleUtil.isOnlyModifyConsigneeContactInformation(modifySceneRule)) {
            // 校验仅修改收件人联系方式
            validateOnlyModifyConsigneeContactInformation(modifySceneRule, orderModel, changedPropertyDelegate);
        }

        // 修改联系方式
        if (ModifySceneRuleUtil.isModifyContactInformation(modifySceneRule)) {
            validateModifyContactInformation(modifySceneRule, changedPropertyDelegate, orderModel);
        }

        //产品信息发生变更
        if (changedPropertyDelegate.productHaveChange()) {
            String productNo = null;
            //TODO 是否放扩展点
            if (orderModel.getOrderBusinessIdentity().getBusinessUnit().equals(BusinessUnitEnum.CN_JDL_C2C.businessUnit())) {
                productNo = AddOnProductEnum.JDL_COD_TOC.getCode();
            }
            Product product = orderModel.getProductDelegate().ofProductNo(productNo);
            //代收货款不能从无改到有,不能从小改大
            if (product != null && product.getOperateType() != null) {
                if (product.getOperateType().getCode().intValue() != OperateTypeEnum.DELETE.getCode().intValue()) {
                    BigDecimal originCod = new BigDecimal(0);
                    if (product.getOperateType().getCode().intValue() == OperateTypeEnum.UPDATE.getCode().intValue()) {
                        Product originProduct = orderModel.getOrderSnapshot().getProductDelegate().ofProductNo(AddOnProductEnum.JDL_COD_TOC.getCode());
                        originCod = new BigDecimal(originProduct.getProductAttrs().get(AddOnProductAttrEnum.COD.getCode()));
                    }

                    BigDecimal modifyCod = new BigDecimal(product.getProductAttrs().get(AddOnProductAttrEnum.COD.getCode()));
                    //TODO 增加代收货款比对开关，后续可能去掉
                    if (!originCod.equals(modifyCod)) {
                        LOGGER.info("代收货款发生变更");
                        // 揽收后校验：不能从小改大
                        // PRD链接：终端切百川 https://joyspace.jd.com/pages/Z3Bmj6DR0rKuBnYMXnSh
                        if(orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp()) {
                            if (modifyCod.compareTo(originCod) > 0) {
                                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改后代收货款金额大于修改前代收货款金额，不支持修改");
                            }
                        }
                        //是否修改过
                        String originSign = ModifyMarkUtil.getPositionMark(modifyMark, ModifyMarkEnum.COD.getPosition());
                        if (orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp() && ModifyMarkEnum.COD.getSign().equals(originSign)) {
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("订单修改过代收货款，不支持修改");
                        }
                        // 更新COD标位
                        modifyMark = updateCodMark(orderModel, modifyMark);
                    }
                } else {
                    //是否修改过
                    String originSign = ModifyMarkUtil.getPositionMark(modifyMark, ModifyMarkEnum.COD.getPosition());
                    if (orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp() && ModifyMarkEnum.COD.getSign().equals(originSign)) {
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("订单修改过代收货款，不允许删除");
                    }
                    //更新COD标位
                    modifyMark = updateCodMark(orderModel, modifyMark);
                }
            }
        }

        // 校验必须删除的产品是否删除（不放上述changedPropertyDelegate.productHaveChange()内原因：卡控必须删除但是没修改增值产品）
        validateProductMustDelete(orderModel, orderModel.getOrderSnapshot());

        //无接触收货方式或指定地点发生修改
        if (changedPropertyDelegate.contactlessTypeHaveChange()) {
            if (!orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp()) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("无接触收货方式必须是在订单揽收后才可以设置");
            }

            //是否修改过无接触收货
            String originSign = ModifyMarkUtil.getPositionMark(modifyMark, ModifyMarkEnum.CONTACTLESS.getPosition());
            if (ModifyMarkEnum.CONTACTLESS.getSign().equals(originSign)) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("无接触收货方式已经发生过修改，不支持再次修改");
            }

            //是否修改过派送方式
            originSign = ModifyMarkUtil.getPositionMark(modifyMark, ModifyMarkEnum.DELIVERY_TYPE.getPosition());
            if (ModifyMarkEnum.DELIVERY_TYPE.getSign().equals(originSign)) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("派送方式已经发生过修改，不支持再次修改");
            }

            //更新标位
            modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.CONTACTLESS.getPosition(), ModifyMarkEnum.CONTACTLESS.getSign());
        }

        //派送方式发生修改
        if (changedPropertyDelegate.deliveryTypeHaveChange() && orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp()) {
            //是否修改过派送方式
            String originSign = ModifyMarkUtil.getPositionMark(modifyMark, ModifyMarkEnum.DELIVERY_TYPE.getPosition());
            if (ModifyMarkEnum.DELIVERY_TYPE.getSign().equals(originSign)) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("派送方式已经发生过修改，不支持再次修改");
            }
            //更新标位
            modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.DELIVERY_TYPE.getPosition(), ModifyMarkEnum.DELIVERY_TYPE.getSign());
        }

        //结算方式发生变更，且改为到付
        if (changedPropertyDelegate.settlementTypeHaveChange()
                && SettlementTypeEnum.CASH_ON_DELIVERY == orderModel.getFinance().getSettlementType()) {
            //根据下单人Pin调到付黑名单校验接口
            LOGGER.error("修改到付黑名单pin校验，pin:{}", orderModel.getOrderSnapshot().getOperator());
            boolean pinBlack = customerFacade.userPinBlack(orderModel.getOrderSnapshot().getOperator());
            if (pinBlack) {
                LOGGER.error("修改到付黑名单pin:{},为黑名单", orderModel.getOrderSnapshot().getOperator());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("该订单用户已被设置为到付黑名单");
            }
        }

        //收件人地址发生修改,并且是揽收后 港澳同城改址以上游传改址产品为准
        if (orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp()
                && (changedPropertyDelegate.consigneeAddressHaveChange() || isHKMOReaddress(orderModel))) {
            if (orderModel.isReaddress1Order2End()) {
                // 原单月结 不允许收件人发起改址
                if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.MONTH_SETTLE_READDRESS_VALID_SWITCH)) {
                    if (SettlementTypeEnum.MONTHLY_PAYMENT == orderModel.getOrderSnapshot().getFinance().getSettlementType()
                            && InitiatorTypeEnum.CONSIGNEE == orderModel.getInitiatorType()
                            && !orderModel.isCashOnDelivery()
                            && !orderModel.isSameSiteReaddress()){
                        LOGGER.error("原单月结收件人只能到付改址");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("原单月结收件人只能到付改址");
                    }
                } else {
                    LOGGER.info("月结改址校验开关关闭");
                }

                int readdressLimit = 1;
                if (SystemCallerEnum.CSS == orderModel.getChannel().getSystemCaller() || SystemCallerEnum.JDL_ISC == orderModel.getChannel().getSystemCaller()) {
                    readdressLimit = OrderConstants.READDRESS_TIMES_MAX;
                }
                String readdressTimes = Optional.ofNullable(orderModel.getOrderSnapshot().getAttachment(OrderConstants.READDRESS_TIMES)).orElse("0");
                int readdressTimesVal = Integer.parseInt(readdressTimes);
                if (readdressLimit <= readdressTimesVal) {
                    LOGGER.error("地址修改次数已达上线，{}，不允许再次发起改址", readdressLimit);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.MODIFY_VALIDATE_FAIL_CONSIGNEE).withCustom("地址修改次数已达上线，不允许再次发起改址");
                }

                if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.READDRESS_JF_VALID_SWITCH)) {
                    // 校验 改址一单到底不支持寄付后款
                    if (SettlementTypeEnum.CASH_ON_PICK == orderModel.getFinance().getSettlementType()
                            && PaymentStageEnum.CASHONDELIVERY == orderModel.getFinance().getPaymentStage()) {
                        LOGGER.info("改址一单到底，不支持寄付后款改址: {}", orderModel.orderNo());
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom("改址一单到底，不支持寄付后款改址");
                    }
                }

            } else if (!ModifySceneRuleUtil.isCssCustomsAudit(orderModel)) {
                // 判断标位
                String originSign = ModifyMarkUtil.getPositionMark(modifyMark, ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getPosition());
                if (ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getSign().equals(originSign)) {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("地址已经修改，不支持再次修改");
                }
                //更新标位 TODO ModifyMarkEnum -> ReaddressMarkEnum
                modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getPosition(), ReaddressMarkEnum.RESULT_SUCCESS.getCode());
            }
        }

        //将更新后的标位放入上下文中
        changedPropertyDelegate.setModifyMark(modifyMark);

        //更新订单信息上的modifyMark
        orderModel.getExtendProps().put(AttachmentKeyEnum.MODIFY_MARK.getKey(), modifyMark);

        if (expressUccConfigCenter.isDroolsRuleSwitch()) {
            //TODO 增加降级开关：总开关和渠道开关
            //为修改项编码，map<规则编码,Map<条件编码,条件集合>>是规则条件编码及对应编码的结果集合
            Map<String, Map<String, Map<String, Set<String>>>> whileListMap = getWhiteList(orderModel, expressOrderContext.getChangedPropertyDelegate().getChangedProperties());
            if (whileListMap == null || whileListMap.isEmpty()) {
                //TODO 后续考虑缓存时效的情况
                LOGGER.warn("系统来源{}未设置白名单规则", orderModel.getChannel().getSystemCaller());
                return;
                //throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("");
            }

            DroolsRuleDto droolsRuleDto = droolsRuleTranslator.toDroolsRuleDto(orderModel);

            //修改项规则映射
            for (String key : whileListMap.keySet()) {
                Map<String, Map<String, Set<String>>> ruleDictSetMap = whileListMap.get(key);
                //规则编码
                boolean filter = droolsRuleService.executeWhiteRule(ruleDictSetMap, droolsRuleDto);
                if (filter) {
                    //TODO 增加业务报警
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改项" + key + ":不准修改");
                }
            }
        } else {
            LOGGER.info("droolsRule 开关关闭");
        }

        //todo readdress1order2endV2 一单到底-路由运营模式允许通过配送域扩展字段修改
        //改址一单到底引入
        //下单时带了路由运营模式，然后发生改址，改址时会通过订单存储的运营模式调用产品中心，产品中心调用路由
        //结果：因为地址变更导致原有运营模式不适配，路由校验失败，产品校验失败，改址失败拒单
        //fixme 产品层面是不是可以考虑下单不下运营模式，按路由返回的确定模式去运营
        if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.C2C_READDRESS_OPERATION_MODE_DELETE_SWITCH)) {
            LOGGER.info("C2C改址一单到底-产品路由策略降级,开关：开");
            if(StringUtils.isNotBlank(orderModel.getShipment().getExtendProps(ShipmentConstants.OPERATION_MODE))){
                if(null == orderModel.getProductDelegate().getMainProduct()){
                    orderModel.getProductDelegate().addMainProduct((Product) orderModel.getOrderSnapshot().getProductDelegate().getMainProduct());
                }
                Map<String,String> appendExt = new HashMap<>();
                appendExt.put(ShipmentConstants.OPERATION_MODE, orderModel.getShipment().getExtendProps(ShipmentConstants.OPERATION_MODE));
                appendExt.put(AttachmentKeyEnum.OPERATE_TYPE.getKey(), String.valueOf(OperateTypeEnum.UPDATE.getCode()));
                orderModel.getProductDelegate().appendMainProductExt(appendExt);
            } else {
                if(orderModel.isReaddress1Order2End()){
                    if(null == orderModel.getProductDelegate().getMainProduct()){
                        orderModel.getProductDelegate().addMainProduct((Product) orderModel.getOrderSnapshot().getProductDelegate().getMainProduct());
                        Map<String,String> appendExt = new HashMap<>();
                        appendExt.put(AttachmentKeyEnum.OPERATE_TYPE.getKey(), String.valueOf(OperateTypeEnum.UPDATE.getCode()));
                        orderModel.getProductDelegate().appendMainProductExt(appendExt);
                    }
                    orderModel.getProductDelegate().removeMainProductExt(ShipmentConstants.OPERATION_MODE);
                }
            }
        } else {
            LOGGER.info("C2C改址一单到底-产品路由策略降级,开关：关");
        }
    }

    /**
     * 更新COD标位
     *
     * @param orderModel
     * @param modifyMark
     * @return
     */
    private String updateCodMark(ExpressOrderModel orderModel, String modifyMark) {
        LOGGER.info("修改白名单扩展点-允许快递C2C-COD修改两次开关开启");
        if (orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp()) {
            if (orderModel.isC2CUnit()) {
                // 记录cod修改次数并更新标位
                modifyMark = recordCodModifyTimesAndUpdateModifyMark(orderModel, modifyMark);
            } else {
                //更新标位
                modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.COD.getPosition(), ModifyMarkEnum.COD.getSign());
            }
        }
        return modifyMark;
    }

    /**
     * 记录COD修改次数并更新修改标记
     *
     * @param orderModel
     * @param modifyMark
     * @return
     */
    private String recordCodModifyTimesAndUpdateModifyMark(ExpressOrderModel orderModel, String modifyMark) {

        // 取历史快照cod修改次数
        String codModifyTimes = orderModel.getOrderSnapshot().getAttachment(OrderConstants.COD_MODIFY_TIMES);

        if (StringUtils.isBlank(codModifyTimes)) {
            LOGGER.info("订单{}，第一次修改COD", orderModel.orderNo());
            // 记录COD修改次数
            orderModel.getExtendProps().put(OrderConstants.COD_MODIFY_TIMES, "1");
        } else {
            //
            Integer codModifyTimesInt = Integer.valueOf(codModifyTimes);
            codModifyTimesInt++;
            LOGGER.info("订单{}，第[{}]次修改COD", orderModel.orderNo(), codModifyTimesInt);
            // 记录COD修改次数
            orderModel.getExtendProps().put(OrderConstants.COD_MODIFY_TIMES, String.valueOf(codModifyTimesInt));
            if (codModifyTimesInt >= OrderConstants.COD_MAX_MODIFY_TIMES) {
                LOGGER.info("订单{}，第[{}]次修改COD，更新标位", orderModel.orderNo(), codModifyTimesInt);
                //更新标位
                modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.COD.getPosition(), ModifyMarkEnum.COD.getSign());
            }
        }

        return modifyMark;
    }

    /**
     * 策略为修改运营模式（modifyOpMode）时，校验修改字段是否在白名单中
     *
     * @param changedPropertyDelegate
     * @param orderModel
     */
    private void validateModifyOpMode(ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        Map<String, ChangedProperty> changedPropertyMap = changedPropertyDelegate.getChangedPropertyMap();
        if (changedPropertyMap == null) {
            return;
        }
        //去除白名单卡控
        /*for (String code : changedPropertyMap.keySet()) {
            // 如果修改内容不在白名单中则不允许修改
            if (!isInModifyOpMode(code)) {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("策略为修改运营模式时 ");
                if (null != changedPropertyMap.get(code)) {
                    stringBuilder.append(changedPropertyMap.get(code).getPropertyDesc());
                }
                stringBuilder.append(" 修改内容不在白名单中");
                LOGGER.info("{}，orderNo:{}", stringBuilder.toString(), orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(stringBuilder.toString());
            }
        }*/
    }

    /**
     * 修改运营模式（在产品对象扩展字段中）、运输方式（配送信息）和预计送达时间（配送信息）
     *
     * @param code
     */
    private boolean isInModifyOpMode(String code) {
        if (StringUtils.isBlank(code)) {
            return false;
        }
        HashSet<String> modifyOpModeWhiteSet = new HashSet<>();
        modifyOpModeWhiteSet.add(ModifyItemConfigEnum.PRODUCT_NO.getCode());
        modifyOpModeWhiteSet.add(ModifyItemConfigEnum.TRANSPORT_TYPE.getCode());
        modifyOpModeWhiteSet.add(ModifyItemConfigEnum.PLAN_DELIVERY_TIME.getCode());
        return modifyOpModeWhiteSet.contains(code);
    }

    /**
     * 额外判断当前支付方式是否被清空
     * @param orderModel
     */
    private boolean isPaymentCleared(ExpressOrderModel orderModel) {
        if (orderModel.getClearFields() == null) {
            return false;
        }
        return orderModel.getClearFields().contains(ModifyItemConfigEnum.PAYMENT.getCode());
    }

    /**
     * @param
     * @return
     * @throws
     * @throws
     * @Description 获取白名单策略规则 TODO 后期需要优化数据缓存结构
     * <AUTHOR>
     * @createDate 2021/4/3 11:58 下午
     * @lastModify
     */
    private Map<String, Map<String, Map<String, Set<String>>>> getWhiteList(ExpressOrderModel
                                                                                    expressOrderModel, List<ChangedProperty> changedProperties) {

        //修改来源
        String modifySource = expressOrderModel.getChannel().getSystemCaller().getCode();
        StringBuilder sb = new StringBuilder();

        sb.append(WHITE_PREFIX).append(":")
                .append(expressOrderModel.requestProfile().getTenantId()).append(":")
                .append(expressOrderModel.getOrderBusinessIdentity().getBusinessUnit()).append(":")
                .append(modifySource);

        if (CollectionUtils.isEmpty(changedProperties)) {
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单修改修改项为空");
        }
        String whiteRedisKey = sb.toString();
        String whiteJsonValue = redisClient.get(whiteRedisKey);
        LOGGER.info("获取白名单key={},value={}", whiteRedisKey, whiteJsonValue);

        if (StringUtils.isBlank(whiteJsonValue)) {
            LOGGER.warn("该修改来源未未配置修改项白名单策略");
            /*throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("该修改来源未未配置修改项白名单策略");*/
        }

        //修改项集合
        Set<String> itemCodeSet = changedProperties.stream().filter(Objects::nonNull)
                .filter(e -> StringUtils.isNotEmpty(e.getItemCode()))
                .map(ChangedProperty::getItemCode)
                .collect(Collectors.toSet());

        LOGGER.info("itemCodeSet={}", JSONUtils.beanToJSONDefault(itemCodeSet));

        Map<String, Map<String, Map<String, JSONArray>>> targetSet = JSONUtils.jsonToBean(whiteJsonValue, Map.class);

        if (targetSet == null) {
            return null;
        }
        //key为修改项编码，map<规则编码,Map<条件编码,条件集合>>是规则条件编码及对应编码的结果集合
        final Map<String, Map<String, Map<String, Set<String>>>> whileListMap = new HashMap<>();

        itemCodeSet.forEach(modifyItem -> {
            if (targetSet.containsKey(modifyItem)) {
                //规则编码Map<规则编码,Map<条件编码,条件集合>
                Map<String, Map<String, JSONArray>> ruleDictSetMap = targetSet.get(modifyItem);
                //map<规则编码,Map<条件编码,条件数值集合>>
                Map<String, Map<String, Set<String>>> resultMap = new HashMap<>();
                //遍历处理修改项下的条件集合
                //key是规则编码
                for (String key : ruleDictSetMap.keySet()) {
                    Map<String, JSONArray> dictSetMap = ruleDictSetMap.get(key);
                    Map<String, Set<String>> dictCodeMapSet = new HashMap<>();
                    //条件组合
                    for (String dictCode : dictSetMap.keySet()) {
                        Set<String> valueSet = new HashSet<>(dictSetMap.get(dictCode).toJavaList(String.class));
                        dictCodeMapSet.put(dictCode, valueSet);

                    }
                    resultMap.put(key, dictCodeMapSet);
                }
                //修改项规则编码
                whileListMap.put(modifyItem, resultMap);

            } else {
                throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(modifyItem + "修改项未配置白名单策略");
            }


        });

        return whileListMap;
    }

    /**
     * 校验修改字段是否在白名单中
     * @param changedPropertyDelegate
     * @param orderModel
     */
    private void validateOnlyModifyFields(ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        Map<String, ChangedProperty> changedPropertyMap = changedPropertyDelegate.getChangedPropertyMap();
        if (changedPropertyMap == null) {
            return;
        }
        for (String code: changedPropertyMap.keySet()) {
            // 如果修改内容不在白名单中则不允许修改
            if (!expressUccConfigCenter.isInExpressHKMModifyWhite(code)) {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("在策略为仅修改报关数据时 ");
                if (null != changedPropertyMap.get(code)) {
                    stringBuilder.append(changedPropertyMap.get(code).getPropertyDesc());
                }
                stringBuilder.append(" 修改内容不在白名单中");
                LOGGER.info("{}，orderNo:{}", stringBuilder.toString(), orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(stringBuilder.toString());
            }
        }
    }

    /**
     * 校验删除字段是否在白名单中
     * @param orderModel
     */
    private void validateOnlyModifyClearFields(ExpressOrderModel orderModel) {
        List<String> clearFields = orderModel.getClearFields();
        if (CollectionUtils.isEmpty(clearFields)) {
            return;
        }
        for (String code: clearFields) {
            // 如果删除字段不在白名单中则不允许修改
            if (!expressUccConfigCenter.isInExpressHKMModifyWhite(code)) {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("在策略为仅修改报关数据时 ");
                stringBuilder.append(code);
                stringBuilder.append(" 修改内容不在白名单中");
                LOGGER.info("{}，orderNo:{}", stringBuilder.toString(), orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(stringBuilder.toString());
            }
        }
    }

    /**
     * 校验仅修改收件人联系方式
     */
    private void validateOnlyModifyConsigneeContactInformation(String modifySceneRule, ExpressOrderModel orderModel, ChangedPropertyDelegate changedPropertyDelegate) {
        // 修改黑名单
        modifyBlacklistModifyContactInformation(modifySceneRule, changedPropertyDelegate, orderModel);

        // 不允许未修改
        if (!changedPropertyDelegate.consigneeContactInformationHaveChange()) {
            LOGGER.info("修改策略为仅修改收件人联系方式，收件人姓名电话手机未发生变化，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为仅修改收件人联系方式，收件人姓名电话手机未发生变化");
        }

        // 不允许修改收件人其他信息
        if (changedPropertyDelegate.consigneeHaveChangeIgnoreContactInformation()) {
            LOGGER.info("修改策略为仅修改收件人联系方式，仅允许修改姓名电话手机，不允许修改其他收件信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为仅修改收件人联系方式，仅允许修改姓名电话手机，不允许修改其他收件信息");
        }
    }

    /**
     * 修改黑名单：校验仅修改收件人联系方式
     */
    private void modifyBlacklistModifyContactInformation(String modifySceneRule, ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        String modifySceneRuleDesc = null;
        if (ModifySceneRuleUtil.isOnlyModifyConsigneeContactInformation(modifySceneRule)) {
            modifySceneRuleDesc = "仅修改收件人联系方式";
        } else {
            modifySceneRuleDesc = "修改联系方式";
        }

        // 仅修改收件人联系方式，不允许修改发货信息
        if (ModifySceneRuleUtil.isOnlyModifyConsigneeContactInformation(modifySceneRule)) {
            if (changedPropertyDelegate.consignorHaveChange()) {
                LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改发货信息，orderNo:{}", orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改发货信息");
            }
        }
        if (changedPropertyDelegate.cargoHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改货品信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改货品信息");
        }
        if (changedPropertyDelegate.goodsHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改商品信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改商品信息");
        }
        if (changedPropertyDelegate.financeHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改交易费用信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改交易费用信息");
        }
        /*if (changedPropertyDelegate.shipmentHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改配送信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改配送信息");
        }*/
        if (changedPropertyDelegate.promotionHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改营销信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改营销信息");
        }
        if (changedPropertyDelegate.agreementInfosHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改协议信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改协议信息");
        }
        if (changedPropertyDelegate.productHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改产品信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改产品信息");
        }
    }

    /**
     * 修改联系方式（姓名、电话、手机）
     */
    private void validateModifyContactInformation(String modifySceneRule, ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        // 修改黑名单
        modifyBlacklistModifyContactInformation(modifySceneRule, changedPropertyDelegate, orderModel);

        // 发件人、收件人姓名电话手机不允许未修改
        if (!changedPropertyDelegate.consignorContactInformationHaveChange() &&
                !changedPropertyDelegate.consigneeContactInformationHaveChange()) {
            LOGGER.info("修改策略为修改联系方式，发件人、收件人姓名电话手机未发生变化，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为修改联系方式，发件人、收件人姓名电话手机未发生变化");
        }

        // 不允许修改发件人、收件人其他信息
        if (changedPropertyDelegate.consignorHaveChangeIgnoreContactInformation()
                || changedPropertyDelegate.consigneeHaveChangeIgnoreContactInformation()) {
            LOGGER.info("修改策略为修改联系方式，仅允许修改发件人、收件人姓名电话手机，不允许修改其他发货信息、收货信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为修改联系方式，仅允许修改发件人、收件人姓名电话手机，不允许修改其他发货信息、收货信息");
        }
    }

    /**
     * 订单状态校验：仅修改收件人联系方式
     */
    private void onlyModifyConsigneeContactInformation(ExpressOrderModel expressOrderModel, String sceneRuleVal) {
        if (!ModifySceneRuleUtil.isOnlyModifyConsigneeContactInformation(sceneRuleVal)) {
            return;
        }
        String orderNo = expressOrderModel.orderNo();
        OrderStatusEnum currentOrderStatus = expressOrderModel.getOrderSnapshot().getOrderStatus().getOrderStatus();
        if (currentOrderStatus == OrderStatusEnum.CUSTOMER_SIGNED
                || currentOrderStatus == OrderStatusEnum.PARTIAL_SIGNED
                || currentOrderStatus == OrderStatusEnum.CUSTOMER_REJECTED) {
            if (currentOrderStatus == OrderStatusEnum.CUSTOMER_REJECTED && expressOrderModel.isRejectionOrder()) {
                LOGGER.info("拒收一单到底，拒收状态允许修改收件人信息");
            } else {
                LOGGER.info("妥投和拒收状态下不支持修改收件人信息: {}", orderNo);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("妥投和拒收状态下不支持修改收件人信息");
            }
        }
    }

    /**
     * 订单状态校验：修改联系方式
     */
    private void modifyContactInformation(ExpressOrderModel expressOrderModel, String sceneRuleVal) {
        if (!ModifySceneRuleUtil.isModifyContactInformation(sceneRuleVal)) {
            return;
        }
        String orderNo = expressOrderModel.orderNo();
        OrderStatusEnum currentOrderStatus = expressOrderModel.getOrderSnapshot().getOrderStatus().getOrderStatus();
        if (currentOrderStatus == OrderStatusEnum.CUSTOMER_SIGNED
                || currentOrderStatus == OrderStatusEnum.PARTIAL_SIGNED
                || currentOrderStatus == OrderStatusEnum.CUSTOMER_REJECTED) {
            LOGGER.info("妥投和拒收状态下不支持修改联系方式: {}", orderNo);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("妥投和拒收状态下不支持修改联系方式");
        }
    }

    /**
     * 校验必须删除的产品是否删除
     */
    private void validateProductMustDelete(ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        // 拒收一单到底，如果原单有代收货款，拒收一单修改必须删除（validateProductInsertOrDelete已卡控不能新增）
        if (orderModel.isRejectionOrder()
                && orderSnapshot != null
                && orderSnapshot.getProductDelegate() != null
                && orderSnapshot.getProductDelegate().getCodProduct() != null) {
            Product codProductModify = orderModel.getProductDelegate() != null ? orderModel.getProductDelegate().getCodProduct() : null;
            if (codProductModify == null || OperateTypeEnum.DELETE != codProductModify.getOperateType()) {
                String forbidDesc = "拒收一单到底，不允许存在代收货款，必须删除已有的增值服务" +  orderSnapshot.getProductDelegate().getCodProduct().getProductNo();
                LOGGER.error(forbidDesc);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(forbidDesc);
            }
        }
    }

    /**
     * 判断是否港澳改址修改请求
     * 港澳同城改址以上游传改址产品为准
     * 快照是港澳订单 并且 当前单有改址增值服务
     */
    private boolean isHKMOReaddress(ExpressOrderModel orderModel) {
        return orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().isHKMO()
                && orderModel.getProductDelegate() != null
                && orderModel.getProductDelegate().hasReaddressProduct();
    }
}
