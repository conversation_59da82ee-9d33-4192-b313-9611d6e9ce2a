package cn.jdl.oms.express.horz.infrs.acl.rpc.customer;

import cn.jdl.oms.express.domain.infrs.acl.rpc.RpcResult;
import cn.jdl.oms.express.domain.infrs.acl.rpc.customer.ICustomerConfigService;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.ldop.basic.api.BasicTraderAPI;
import com.jd.ldop.basic.api.BasicTraderNewAPI;
import com.jd.ldop.basic.api.BasicTraderShipAddressAPI;
import com.jd.ldop.basic.dto.BasicTraderExtendInfoDTO;
import com.jd.ldop.basic.dto.BasicTraderInfoDTO;
import com.jd.ldop.basic.dto.BasicTraderInfoFusionDTO;
import com.jd.ldop.basic.dto.BasicTraderNeccesaryInfoDTO;
import com.jd.ldop.basic.dto.BasicTraderShipAddressQueryDTO;
import com.jd.ldop.basic.dto.ResponseDTO;
import com.jd.ldop.basic.dto.serviceItem.AccountProductInfoDTO;
import com.jd.ldop.basic.dto.serviceItem.ServiceItemQuery;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 客户配置信息校验
 */
@Service
public class CustomerConfigService implements ICustomerConfigService {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(CustomerConfigService.class);

    /**
     * 商家基础资料API
     */
    @Resource
    private BasicTraderAPI basicTraderAPI;

    /**
     * 商家多地址发货查询API
     */
    @Resource
    private BasicTraderShipAddressAPI basicTraderShipAddressAPI;


    @Resource
    private BasicTraderNewAPI basicTraderNewAPI;

    /**
     * 根据商家编码（青龙业主号）查询商家信息
     * https://cf.jd.com/pages/viewpage.action?pageId=********
     * @param deliveryFulfillmentNo
     * @return
     */
    @Override
    public RpcResult<ResponseDTO<BasicTraderInfoDTO>> getBaseTraderByCode(String deliveryFulfillmentNo) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".getBaseTraderByCode"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("根据商家编号查询商家信息入参:{}", deliveryFulfillmentNo);
            ResponseDTO<BasicTraderInfoDTO> result = basicTraderAPI.getBaseTraderByCode(deliveryFulfillmentNo);
            LOGGER.info("根据商家编号查询商家信息出参:{}", JSONUtils.beanToJSONDefault(result));
            if (result.isSuccess() && result.getResult() != null) {
                return RpcResult.ofSuccess(result);
            }else{
                return RpcResult.ofUnexpectedResult(result);
            }
        } catch (Exception e) {
            LOGGER.error("根据商家编号查询商家信息异常", e);
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }


    /**
     * 125根据商家编码（青龙业主号）、产品名称和模式名称查询商家信息
     **/
    @Override
    public RpcResult<ResponseDTO<AccountProductInfoDTO>> getNewBaseTraderByCode(ServiceItemQuery serviceItemQuery) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".getNewBaseTraderByCode"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("根据商家编号查询新商家信息接口入参:{}", JSONUtils.beanToJSONDefault(serviceItemQuery));
            ResponseDTO<AccountProductInfoDTO> result = basicTraderNewAPI.getServiceItemByCodeAndCondition(serviceItemQuery);
            LOGGER.info("根据商家编号查询新商家信息出参:{}", JSONUtils.beanToJSONDefault(result));
            if (result.isSuccess() && result.getResult() != null) {
                return RpcResult.ofSuccess(result);
            }else{
                return RpcResult.ofUnexpectedResult(result);
            }
        } catch (Exception e) {
            LOGGER.error("根据商家编号查询新商家信息异常", e);
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    @Override
    public RpcResult<ResponseDTO<BasicTraderExtendInfoDTO>> getBaseTraderExtendByCode(String deliveryFulfillmentNo) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".getBaseTraderExtendByCode"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("查询商家扩展信息入参:{}", deliveryFulfillmentNo);
            ResponseDTO<BasicTraderExtendInfoDTO> responseDTO = basicTraderAPI.getBaseTraderExtendByCode(deliveryFulfillmentNo);
            LOGGER.info("查询商家扩展信息结果:{}", JSONUtils.beanToJSONDefault(responseDTO));
            if (responseDTO.isSuccess() && responseDTO.getResult() != null) {
                return RpcResult.ofSuccess(responseDTO);
            } else {
                return RpcResult.ofUnexpectedResult(responseDTO);
            }
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("查询商家扩展信息接口异常", e);
            throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR, e).withCustom("查询商家扩展信息接口异常");
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    @Override
    public RpcResult<ResponseDTO<BasicTraderNeccesaryInfoDTO>> getBaseTraderNeccesaryInfoById(Integer traderId) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".getBaseTraderNeccesaryInfoById"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("查询商家必要信息入参: traderId = {}", traderId);
            ResponseDTO<BasicTraderNeccesaryInfoDTO> responseDTO = basicTraderAPI.getBaseTraderNeccesaryInfoById(traderId);
            LOGGER.info("查询商家必要信息结果:{}", JSONUtils.beanToJSONDefault(responseDTO));
            if (responseDTO.isSuccess() && responseDTO.getResult() != null) {
                return RpcResult.ofSuccess(responseDTO);
            } else {
                return RpcResult.ofUnexpectedResult(responseDTO);
            }
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("查询商家必要信息接口异常", e);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 根据商家编码（青龙业主号）查询商家渠道信息
     *
     * @param deliveryFulfillmentNo
     * @return
     */
    @Override
    public RpcResult<ResponseDTO<BasicTraderInfoFusionDTO>> getBasicTraderFusionInfo(String deliveryFulfillmentNo, String appName) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".getBasicTraderFusionInfo"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("根据商家编号查询商家渠道信息入参:{},appName:{}", deliveryFulfillmentNo, appName);
            ResponseDTO<BasicTraderInfoFusionDTO> result = basicTraderAPI.getBasicTraderFusionInfo(deliveryFulfillmentNo, appName);
            LOGGER.info("根据商家编号查询商家渠道信息出参:{}", JSONUtils.beanToJSONDefault(result));
            if (result.isSuccess() && result.getResult() != null) {
                return RpcResult.ofSuccess(result);
            } else {
                return RpcResult.ofUnexpectedResult(result);
            }
        } catch (Exception e) {
            LOGGER.error("根据商家编号查询商家渠道信息异常", e);
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 根据商家编码查询多地址发货。https://joyspace.jd.com/pages/zr64oUn3HhVJwzexRxkB
     *
     * @param traderCode 商家编码
     * @return 多地址发货列表
     */
    @Override
    public RpcResult<ResponseDTO<BasicTraderShipAddressQueryDTO>> getTraderShipAddressListByTraderCode(String traderCode) {

        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".getTraderShipAddressListByTraderCode"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("根据商家编码查询多地址发货入参traderCode:{}", traderCode);
            ResponseDTO<BasicTraderShipAddressQueryDTO> result = basicTraderShipAddressAPI.getTraderShipAddressListByTraderCode(traderCode);
            LOGGER.info("根据商家编码查询多地址发货出参:{}", JSONUtils.beanToJSONDefault(result));
            if (result.isSuccess() && result.getResult() != null) {
                return RpcResult.ofSuccess(result);
            } else {
                return RpcResult.ofUnexpectedResult(result);
            }
        } catch (Exception e) {
            LOGGER.error("根据商家编码查询多地址发货异常", e);
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }

    }

}
