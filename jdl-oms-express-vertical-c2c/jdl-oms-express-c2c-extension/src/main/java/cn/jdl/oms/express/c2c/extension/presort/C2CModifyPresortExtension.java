package cn.jdl.oms.express.c2c.extension.presort;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.PresortExtend;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.presort.IPresortExtension;
import cn.jdl.oms.express.domain.infrs.acl.baseHandler.presort.StationBaseHandler;
import cn.jdl.oms.express.domain.infrs.acl.facade.presort.PresortFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.site.OwnSendCabinetFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.presort.PresortBaseSiteFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.presort.PresortFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.presort.PresortFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.presort.PresortFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.presort.PresortRpcResultEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.site.OwnSendCabinetRequest;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PickupTypeEnum;
import cn.jdl.oms.express.domain.vo.Shipment;
import cn.jdl.oms.express.domain.vo.modify.ChangedProperty;
import cn.jdl.oms.express.domain.vo.modify.ChangedPropertyDelegate;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Extension(code = ExpressOrderProduct.CODE)
public class C2CModifyPresortExtension implements IPresortExtension {
    private static final Logger LOGGER = LoggerFactory.getLogger(C2CModifyPresortExtension.class);

    @Resource
    private PresortFacade presortFacade;

    @Resource
    private PresortFacadeTranslator presortFacadeTranslator;
    @Resource
    private OwnSendCabinetFacade ownSendCabinetFacade;

    @Resource
    private StationBaseHandler stationBaseHandler;

    // 预分拣场景 - 揽收
    private final static Integer SCENE_TYPE_COLLECT = 1;

    // 预分拣场景 - 派送
    private final static Integer SCENE_TYPE_DELIVER = 2;

    //605且站点id为-136时，属于疫情限售逻辑，此场景会由路由或预分拣返回指定话术，话术内容在overAreaReason
    private final static Integer PRESORT_SPECIAL_RESULT_STATUS = -136;

    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        ExpressOrderModel orderModel = context.getOrderModel();
        if (null == orderModel) {
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL).withCustom("订单中心纯配领域订单模型为空");
        }

        try {
            // C2C修改运营模式不涉及预分拣
            if (ModifySceneRuleUtil.isModifyOpMode(orderModel)) {
                LOGGER.info("C2C修改策略为：修改运营模式不涉及预分拣--跳过预分拣");
                return;
            }

            // C2C港澳关务数据的修改不涉及预分拣
            if (context.getOrderModel().isOnlyModifyCustoms()) {
                LOGGER.info("C2C港澳关务数据的修改不涉及预分拣--跳过预分拣");
                return;
            }

            LOGGER.info("修改订单预分拣C2C扩展点执行开始");
            ChangedPropertyDelegate changedPropertyDelegate = context.getChangedPropertyDelegate();
            // 前提条件：修改收寄件地址、产品、增值服务、站点信息、结算方式
            if (!ifNeedPresort(changedPropertyDelegate)) {
                // 不计算预分拣但是货品信息有修改，深度合作自提柜需求校验货品数量、重量、最长边
                if (changedPropertyDelegate.cargoHaveChange()) {
                    stationBaseHandler.validateHKSelfPickupLocker(orderModel);
                }
                return;
            }

            // 保存预分拣结果
            PresortExtend presortExtend = new PresortExtend();
            String orderNo = orderModel.orderNo();

            // 揽收站点 & 配送站点
            String startStationNo = orderModel.getShipment().getStartStationNo();
            String endStationNo = orderModel.getShipment().getEndStationNo();

            // 配送要求 - 快照
            Shipment snapshotShipment = orderModel.getOrderSnapshot().getShipment();

            //https://joyspace.jd.com/pages/DkhoQR1lkqeClM9MNuCv
            //修改入参中不含始发站点：
            //若原单的揽收方式为自送，且始发站点不为空（可以同上校验站点有效性逻辑），则不再执行预分拣获取站点逻辑；
            if(null == startStationNo && PickupTypeEnum.SELF_DELIVERY == snapshotShipment.getPickupType() && StringUtils.isNotBlank(snapshotShipment.getStartStationNo())){
                startStationNo = snapshotShipment.getStartStationNo();
            }
            //修改入参中不含目的站点：
            //若原单的派送方式为自提，且目的站点不为空（可以同上校验站点有效性），则不再执行预分拣获取站点逻辑；
            if(null == endStationNo && DeliveryTypeEnum.SELF_PICKUP == snapshotShipment.getDeliveryType() && StringUtils.isNotBlank(snapshotShipment.getEndStationNo())){
                endStationNo = snapshotShipment.getEndStationNo();
            }


            LOGGER.info("订单号：{},订单修改揽收站点预分拣计算开始", orderNo);
            // 是否计算揽派标志
            boolean startStationCalc = false;
            boolean endStationCalc = false;

            // 揽收站点不为空「一般自送，扫描小哥二维码下单的时候不为空」
            if (orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp()) {
                LOGGER.info("订单号：{},揽收后修改，跳过揽收预分拣", orderModel.orderNo());
                // TODO 是否需要判断和揽收预分拣相关字段，如果传了清空，防止数据错误
            } else if (StringUtils.isNotBlank(startStationNo)) {
                PresortBaseSiteFacadeResponse presortBaseSiteFacadeResponse = presortFacade.getBaseSiteBySiteId(presortFacadeTranslator.toPresortBaseSiteFacadeRequest(startStationNo));

                if (null != presortBaseSiteFacadeResponse
                        && startStationNo.equals(presortBaseSiteFacadeResponse.getSiteCode())) {
                    LOGGER.info("订单号:{}揽收站点:{}调用青龙基础资料校验通过", orderNo, startStationNo);
                    presortFacadeTranslator.complementShipmentStartStationInfo(orderModel, startStationNo, presortBaseSiteFacadeResponse.getSiteType(), presortBaseSiteFacadeResponse.getSiteName());
                } else {
                    // 修改的时候，站点信息不为空，校验不通过直接修改失败
                    // 注：下单的时候，此处若是校验不通过会通过预分拣去寻找正确的站点信息并赋值
                    LOGGER.error("订单号:{}揽收站点:{}不为空，且调用青龙基础资料校验未通过，修改失败", orderNo, startStationNo);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                            .withCustom(String.format("揽收站点:%s不为空，且调用青龙基础资料校验未通过，修改失败", startStationNo));
                }
            } else {
                LOGGER.info("揽收站点为空执行揽收预分拣校验");
                startStationCalc = true;

            }

            // 派送站点不为空「一般自取的时候不为空」
            LOGGER.info("订单号：{},订单修改派件站点预分拣计算开始", orderNo);
            if (StringUtils.isNotBlank(endStationNo)) {
                PresortBaseSiteFacadeResponse presortBaseSiteFacadeResponse = presortFacade.getBaseSiteBySiteId(presortFacadeTranslator.toPresortBaseSiteFacadeRequest(endStationNo));
                if (null != presortBaseSiteFacadeResponse
                        && endStationNo.equals(presortBaseSiteFacadeResponse.getSiteCode())) {
                    LOGGER.info("订单号:{}派件站点:{}调用青龙基础资料校验通过", orderNo, endStationNo);
                    // 香港深度合作自提柜校验：目的站点是特殊自提柜时，卡控包裹数量、重量、最长边
                    stationBaseHandler.validateHKSelfPickupLocker(orderModel, presortBaseSiteFacadeResponse);
                    presortFacadeTranslator.complementShipmentEndStationInfo(orderModel, endStationNo, presortBaseSiteFacadeResponse.getSiteType(), presortBaseSiteFacadeResponse.getSiteName());
                } else {
                    // 修改的时候，站点信息不为空，校验不通过直接修改失败
                    // 注：下单的时候，此处若是校验不通过会通过预分拣去寻找正确的站点信息并赋值
                    LOGGER.error("订单号:{}派件站点:{}不为空，且调用青龙基础资料校验未通过，修改失败", orderNo, endStationNo);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                            .withCustom(String.format("派件站点:%s不为空，且调用青龙基础资料校验未通过，修改失败", endStationNo));
                }
            } else {
                LOGGER.info("派送站点为空执行揽收预分拣校验");
                endStationCalc = true;
            }

            if (ModifySceneRuleUtil.isPickupTransferStation(context.getOrderModel())) {
                LOGGER.info("揽收转站、跨站截单策略,不重新计算预分拣,只补全站点信息");
                return;
            }

            List<PresortFacadeRequest> requestList = new ArrayList<>(2);
            if(startStationCalc){
                PresortFacadeRequest startPresortFacadeRequest = presortFacadeTranslator.toPresortModifyFacadeRequest(orderModel, SCENE_TYPE_COLLECT);
                requestList.add(startPresortFacadeRequest);
            }

            if(endStationCalc){
                PresortFacadeRequest endPresortFacadeRequest = presortFacadeTranslator.toPresortModifyFacadeRequest(orderModel, SCENE_TYPE_DELIVER);
                requestList.add(endPresortFacadeRequest);
            }

            if (requestList.size() > 0) {
                Map<String, PresortFacadeResponse> presortFacadeResponseMap = presortFacade.batchComputePresort(requestList);
                if(MapUtils.isEmpty(presortFacadeResponseMap)){
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                            .withCustom(String.format("订单%s,修改订单派送站点预分拣计算失败", orderNo));
                }
                if (startStationCalc) {
                    PresortFacadeResponse presortFacadeResponse = presortFacadeResponseMap.get(SCENE_TYPE_COLLECT.toString());
                    if (PresortRpcResultEnum.PRESORT_RPC_RESULT_200.getCode().equals(presortFacadeResponse.getResultCode())) {
                        LOGGER.info("订单号:{}订单修改,揽收站点预分拣计算成功siteId:{}", orderNo, presortFacadeResponse.getSiteId());
                        presortFacadeTranslator.complementStartStation(orderModel, presortFacadeResponse, presortExtend);
                        presortFacadeTranslator.complementShipmentStartStationInfo(orderModel, String.valueOf(presortFacadeResponse.getSiteId()), presortFacadeResponse.getSiteType(), presortFacadeResponse.getSiteName(), presortFacadeResponse.getSiteThirdType(), true);

                        // 如果揽收地址修改，则设置到修改项中
                        if (!String.valueOf(presortFacadeResponse.getSiteId()).equals(snapshotShipment.getStartStationNo())) {
                            ChangedProperty changedProperty = getChangedProperty(snapshotShipment, presortFacadeResponse);
                            changedPropertyDelegate.addChangedProperties(changedProperty);
                        }
                    } else if (PresortRpcResultEnum.PRESORT_RPC_RESULT_605.getCode().equals(presortFacadeResponse.getResultCode())) {
                        LOGGER.info("订单:{}揽收站点预分拣计算超区resultMsg:{}", orderNo, presortFacadeResponse.getResultMessage());
                        if (presortFacadeResponse.getSiteId() != null && PRESORT_SPECIAL_RESULT_STATUS.equals(presortFacadeResponse.getSiteId())) {
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                                    .withSubCode(PresortRpcResultEnum.PRESORT_RPC_RESULT_605.getCode() + "" + PRESORT_SPECIAL_RESULT_STATUS)
                                    .withSubMessage(presortFacadeResponse.getOverAreaReason())
                                    .withCustom(String.format("预分拣信息校验失败,修改订单揽收站点预分拣计算失败 %s", presortFacadeResponse.getResultMessage()));
                        }else {
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                                    .withCustom(String.format("预分拣信息校验失败,修改订单揽收站点预分拣计算失败 %s", presortFacadeResponse.getResultMessage()))
                                    .withSubCode(presortFacadeResponse.getResultCode())
                                    .withSubMessage(presortFacadeResponse.getResultMessage());
                        }
                    } else if (PresortRpcResultEnum.PRESORT_RPC_RESULT_614.getCode().equals(presortFacadeResponse.getResultCode())) {
                        // 需要补全预分拣结果扩展字段
                        presortFacadeTranslator.complementStartStation(orderModel, presortFacadeResponse, presortExtend);
                        presortFacadeTranslator.clearShipmentStartStationInfo(orderModel);
                        orderModel.getClearFields().add(ModifyItemConfigEnum.START_STATION_TYPE.getCode());
                        ChangedProperty changedProperty = getChangedProperty(snapshotShipment, presortFacadeResponse);
                        changedPropertyDelegate.addChangedProperties(changedProperty);
                        LOGGER.info("订单:{}揽收站点地址不详需要外呼，resultMsg:{}", orderNo, presortFacadeResponse.getResultMessage());
                    } else {
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                                .withCustom(String.format("预分拣信息校验失败,修改订单揽收站点预分拣计算失败 %s", presortFacadeResponse.getResultMessage()))
                                .withSubCode(presortFacadeResponse.getResultCode())
                                .withSubMessage(presortFacadeResponse.getResultMessage());
                    }
                }

                if (endStationCalc) {
                    PresortFacadeResponse presortFacadeResponse = presortFacadeResponseMap.get(SCENE_TYPE_DELIVER.toString());

                    if (PresortRpcResultEnum.PRESORT_RPC_RESULT_200.getCode().equals(presortFacadeResponse.getResultCode())) {
                        LOGGER.info("修改订单-订单号:{}派件站点预分拣计算成功siteId:{}", orderNo, presortFacadeResponse.getSiteId());
                        presortFacadeTranslator.complementEndStation(orderModel, presortFacadeResponse, presortExtend);
                        presortFacadeTranslator.complementShipmentEndStationInfo(orderModel, String.valueOf(presortFacadeResponse.getSiteId()), presortFacadeResponse.getSiteType(), presortFacadeResponse.getSiteName(), presortFacadeResponse.getSiteThirdType(), true);

                        // 补全接驳站点
                        if(null != presortFacadeResponse.getPresortRpcDto() && null != presortFacadeResponse.getPresortRpcDto().getTransferPointId()){
                            presortFacadeTranslator.complementEndTransferStation(orderModel, String.valueOf(presortFacadeResponse.getPresortRpcDto().getTransferPointId()));
                        } else {
                            //置为空
                            presortFacadeTranslator.complementEndTransferStation(orderModel, "");
                        }

                        // 如果派送地址修改，则设置到修改项中
                        if (!String.valueOf(presortFacadeResponse.getSiteId()).equals(snapshotShipment.getEndStationNo())) {
                            ChangedProperty changedProperty = new ChangedProperty();
                            changedProperty.setOperateType(OperateTypeEnum.UPDATE);
                            changedProperty.setProperty(ModifyItemConfigEnum.END_STATION_NO.getField());
                            changedProperty.setItemCode(ModifyItemConfigEnum.END_STATION_NO.getCode());
                            changedProperty.setPropertyDesc(ModifyItemConfigEnum.END_STATION_NO.getDesc());
                            changedProperty.setOriginValue(snapshotShipment.getEndStationNo());
                            changedProperty.setModifyValue(String.valueOf(presortFacadeResponse.getSiteId()));
                            changedPropertyDelegate.addChangedProperties(changedProperty);
                        }
                    } else if (PresortRpcResultEnum.PRESORT_RPC_RESULT_605.getCode().equals(presortFacadeResponse.getResultCode())) {
                        LOGGER.info("订单:{}派件站点预分拣计算超区resultMsg:{}", orderNo, presortFacadeResponse.getResultMessage());
                        if (presortFacadeResponse.getSiteId() != null && PRESORT_SPECIAL_RESULT_STATUS.equals(presortFacadeResponse.getSiteId())) {
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                                    .withSubCode(PresortRpcResultEnum.PRESORT_RPC_RESULT_605.getCode() + "" + PRESORT_SPECIAL_RESULT_STATUS)
                                    .withSubMessage(presortFacadeResponse.getOverAreaReason())
                                    .withCustom(String.format("预分拣信息校验失败,修改订单派件站点预分拣计算失败 %s", presortFacadeResponse.getResultMessage()));
                        }else {
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                                    .withCustom(String.format("预分拣信息校验失败,修改订单揽收站点预分拣计算失败 %s", presortFacadeResponse.getResultMessage()))
                                    .withSubCode(presortFacadeResponse.getResultCode())
                                    .withSubMessage(presortFacadeResponse.getResultMessage());
                        }
                    } else {
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                                .withCustom(String.format("预分拣信息校验失败,修改订单派件站点预分拣计算失败 %s", presortFacadeResponse.getResultMessage()))
                                .withSubCode(presortFacadeResponse.getResultCode())
                                .withSubMessage(presortFacadeResponse.getResultMessage());
                    }
                }
            }

            // 如果揽收/派送站点有变化，则补充到上下文订单模型中
            if (presortExtend.getStartStation() != null
                    || presortExtend.getEndStation() != null) {
                presortFacadeTranslator.complementPresort(orderModel, presortExtend);
            }

            DeliveryTypeEnum deliveryType = orderModel.getShipment().getDeliveryType() != null ? orderModel.getShipment().getDeliveryType() : snapshotShipment.getDeliveryType();

            // 揽收后，派送方式是送货上门时,站点若发生变化则不允许修改
            if (orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp()
                    && DeliveryTypeEnum.TO_DOOR == deliveryType
                    && StringUtils.isNotBlank(orderModel.getShipment().getEndStationNo())
                    && !snapshotShipment.getEndStationNo().equals(orderModel.getShipment().getEndStationNo())
                    && !orderModel.isReaddress1Order2End()) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                        .withCustom(String.format("订单状态为%s,且派送方式为送货上门，此时订单配送站点不允许发生变化。", orderModel.getOrderSnapshot().getOrderStatus().getOrderStatus().getDesc()));
            }

            //自寄柜校验
            if (orderModel.getShipment().getStartStationType() != null && 56 == orderModel.getShipment().getStartStationType()) {
                List<String> stationCodeList = new ArrayList<>();
                stationCodeList.add(orderModel.getShipment().getStartStationNo());
                OwnSendCabinetRequest ownSendCabinetRequest = new OwnSendCabinetRequest();
                ownSendCabinetRequest.setStationCodeList(stationCodeList);
                if (!ownSendCabinetFacade.getOwnSendCabinet(orderModel.requestProfile(), ownSendCabinetRequest)) {
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.OWN_SEND_CABINET_CODE_FAIL).withCustom("预分拣信息校验失败,自寄柜校验失败");
                }
            }
            LOGGER.info("修改订单,预分拣C2C扩展点执行结束");
        } catch (BusinessDomainException businessDomainException) {
            LOGGER.error("修改预分拣C2C扩展点执行异常, traceId={}", orderModel.traceId(), businessDomainException);
            throw businessDomainException;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("修改预分拣C2C扩展点执行异常, traceId={}", orderModel.traceId(), exception);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL, exception).withCustom("预分拣信息校验失败");
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    private static ChangedProperty getChangedProperty(Shipment snapshotShipment, PresortFacadeResponse presortFacadeResponse) {
        ChangedProperty changedProperty = new ChangedProperty();
        changedProperty.setOperateType(OperateTypeEnum.UPDATE);
        changedProperty.setProperty(ModifyItemConfigEnum.START_STATION_NO.getField());
        changedProperty.setItemCode(ModifyItemConfigEnum.START_STATION_NO.getCode());
        changedProperty.setPropertyDesc(ModifyItemConfigEnum.START_STATION_NO.getDesc());
        changedProperty.setOriginValue(snapshotShipment.getStartStationNo());
        if (presortFacadeResponse.getSiteId() != null) {
            changedProperty.setModifyValue(String.valueOf(presortFacadeResponse.getSiteId()));
        } else {
            changedProperty.setModifyValue("");
        }
        return changedProperty;
    }

    /**
     * 功能: 是否需要做预分拣
     *
     * @param: ChangedPropertyDelegate 更改的属性集合
     * @return: boolean
     * @throw:
     * @description: 以下几种情况需要做预分拣，否则直接跳过
     * 修改收寄件地址、产品、增值服务、站点信息、结算方式、温层
     * @author: liufarui
     * @date: 2021/5/19 3:50 下午
     */
    private boolean ifNeedPresort(ChangedPropertyDelegate changedPropertyDelegate) {
        // 收件人地址是否发生变更
        return changedPropertyDelegate.consigneeAddressHaveChange()
                // 发件人地址是否发生变更
                || changedPropertyDelegate.consignorAddressHaveChange()
                // 产品信息是否发生变更
                || changedPropertyDelegate.productHaveChange()
                // 结算方式是否发生变更
                || changedPropertyDelegate.settlementTypeHaveChange()
                // 税金结算方式是否发生变更
                || changedPropertyDelegate.taxSettlementTypeHaveChange()
                // 起始站点信息是否发生变更
                || changedPropertyDelegate.startStationHaveChange()
                // 目的站点信息是否发生变更
                || changedPropertyDelegate.endStationHaveChange()
                // 温层是否发生变更
                || changedPropertyDelegate.warmLayerHaveChange();
    }
}
