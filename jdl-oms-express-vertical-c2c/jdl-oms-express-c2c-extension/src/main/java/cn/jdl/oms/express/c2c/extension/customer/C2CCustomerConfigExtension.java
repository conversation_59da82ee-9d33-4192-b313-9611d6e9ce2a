package cn.jdl.oms.express.c2c.extension.customer;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CustomerConfig;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.customer.ICustomerConfigExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.BasicTraderResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.DeptResponse;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedBusinessIdentityUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.TraderOperateStateEnum;
import cn.jdl.oms.express.shared.common.dict.TraderSignEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.dict.UnifiedSubErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.TraderSignUtils;
import com.jd.matrix.sdk.annotation.Extension;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * 客户配置信息校验扩展点实现
 */
@Extension(code = ExpressOrderProduct.CODE)
public class C2CCustomerConfigExtension implements ICustomerConfigExtension {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(C2CCustomerConfigExtension.class);

    @Resource
    private CustomerConfigFacade customerConfigFacade;

    /**
     * 事业部信息
     */
    @Resource
    private CustomerFacade customerFacade;

    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 客户配置信息校验扩展
     *
     * @param expressOrderContext
     * @throws AbilityExtensionException
     */
    @Override
    public void execute(ExpressOrderContext expressOrderContext) throws AbilityExtensionException {
        ExpressOrderModel expressOrderModel = expressOrderContext.getOrderModel();

        // C2C修改运营模式不涉及客户配置信息校验
        if (ModifySceneRuleUtil.isModifyOpMode(expressOrderModel)) {
            LOGGER.info("C2C修改策略为：修改运营模式不涉及客户配置信息校验--跳过客户配置信息校验");
            return;
        }

        //校验履约账号
        checkBasicTraderInfo(expressOrderContext);

        // 补全事业部信息，目前只补融合身份
        complementEbuInfo(expressOrderContext);

        //若结算方式不是月结，则不进行商家配置校验
        if (expressOrderModel.getFinance() == null || SettlementTypeEnum.MONTHLY_PAYMENT != expressOrderModel.getFinance().getSettlementType()) {
            LOGGER.info("结算方式不是月结，则不进行商家配置校验");
            return;
        }

        //逆向单月结，ucc校验开关关闭，不进行商家配置校验
        if(OrderTypeEnum.RETURN_ORDER == expressOrderModel.getOrderType()
                && !expressUccConfigCenter.isReverseMonthSettlementCustomerConfigSwitch()){
            LOGGER.info("逆向单月结，ucc校验开关关闭，不进行商家配置校验");
            return;
        }

        //结算账户不为空,则通过结算账号校验账户是否开通月结，否则校验配送履约账号是否开通月结
        if (StringUtils.isNotBlank(expressOrderModel.getFinance().getSettlementAccountNo())) {
            LOGGER.info("有结算账号，SettlementAccountNo={}", expressOrderModel.getFinance().getSettlementAccountNo());

            BasicTraderResponse basicTraderResponse = customerConfigFacade.getCustomerConfig(expressOrderModel.getFinance().getSettlementAccountNo());
            //青龙业主号校验
            if (!TraderOperateStateEnum.normal.getCode().equals(basicTraderResponse.getTraderOperateState())) {
                LOGGER.error("青龙业主号:{}状态异常，TraderOperateState:{}"
                        , expressOrderModel.getFinance().getSettlementAccountNo()
                        , basicTraderResponse.getTraderOperateState());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL)
                        .withCustom("客户配置信息校验失败,商家青龙业主号状态异常")
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.desc());
            }
            if (OrderTypeEnum.DELIVERY.equals(expressOrderModel.getOrderType()) && !SettlementTypeEnum.MONTHLY_PAYMENT.getTraderSignCode().contains(TraderSignUtils.getTraderSignCode(basicTraderResponse.getTraderSign(), TraderSignEnum.SETTLEMENT.getCode()))) {
                LOGGER.error("结算方式校验失败,商家结算账户未开通月结结算方式");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL).withCustom("客户配置信息校验失败,结算方式校验失败")
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.desc());
            }
            //月结结算账号类型校验 商家账号子类型102101,102102
            //放开校验
            //BP产品：申玉晓 订单产品：王维金
           /* if (102101 != basicTraderResponse.getSubTraderMold() && 102102 != basicTraderResponse.getSubTraderMold()) {
                LOGGER.error("结算账号校验失败,商家账号子类型不匹配");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL).withCustom("客户配置信息校验失败,商家账号子类型不匹配");
            }*/
        } else {
            CustomerConfig customerConfig = expressOrderContext.getCustomerConfig();
            //青龙业主号校验
            if (!TraderOperateStateEnum.normal.getCode().equals(customerConfig.getTraderOperateState())) {
                LOGGER.error("青龙业主号:{}状态异常，TraderOperateState:{}"
                        , expressOrderModel.getCustomer().getAccountNo()
                        , customerConfig.getTraderOperateState());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL)
                        .withCustom("客户配置信息校验失败,商家青龙业主号状态异常")
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.desc());
            }

            if (OrderTypeEnum.DELIVERY.equals(expressOrderModel.getOrderType()) && !SettlementTypeEnum.MONTHLY_PAYMENT.getTraderSignCode().contains(TraderSignUtils.getTraderSignCode(customerConfig.getTraderSign(), TraderSignEnum.SETTLEMENT.getCode()))) {
                LOGGER.error("结算方式校验失败,商家结算账户未开通月结结算方式");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL).withCustom("客户配置信息校验失败,结算方式校验失败")
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.desc());
            }
        }

        // 校验收件人是否允许改址
        if (expressOrderContext.isReaddressScene()) {
            consigneeReaddressCheck(expressOrderModel, expressOrderContext.getCustomerConfig());
        }
    }

    /**
     * 补全事业部信息，目前只补融合身份
     *
     * @param expressOrderContext
     */
    private void complementEbuInfo(ExpressOrderContext expressOrderContext) {

        // 只在融合身份、接单场景 下补全
        if (UnitedBusinessIdentityUtil.isUnitedIdentity(expressOrderContext)
                && BusinessSceneEnum.CREATE.getCode().equals(expressOrderContext.getOrderModel().getOrderBusinessIdentity().getBusinessScene())) {

            // accountName2为空 且 accountNo2非空
            if (StringUtils.isBlank(expressOrderContext.getOrderModel().getCustomer().getAccountName2())
                    && StringUtils.isNotBlank(expressOrderContext.getOrderModel().getCustomer().getAccountNo2())) {

                //获取事业部信息
                DeptResponse deptInfo = customerFacade.getDept(expressOrderContext.getOrderModel().getCustomer().getAccountNo2());
                expressOrderContext.getOrderModel().getComplementModel().complementAccountName2(this, deptInfo.getDeptName());
            }
        }

    }

    /**
     * 商家基础资料校验
     *
     * @param expressOrderContext
     */
    private void checkBasicTraderInfo(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel expressOrderModel = expressOrderContext.getOrderModel();

        //根据履约账号获取账号配置信息
        BasicTraderResponse basicTraderResponse = customerConfigFacade.getCustomerConfig(expressOrderModel.getCustomer().getAccountNo());

        // 履约账号校验
        if (basicTraderResponse == null) {
            LOGGER.error("客户配置信息校验失败，未查到相关客户信息");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL).withCustom("客户配置信息校验失败，未查到相关客户信息");
        }

        // 补全账号id
        expressOrderModel.getComplementModel().complementAccountId(this, basicTraderResponse.getCustomerId());
        // 补全账号名称
        expressOrderModel.getComplementModel().complementAccountName(this, basicTraderResponse.getCustomerName());

        //将客户信息放入上下文
        CustomerConfig customerConfig = new CustomerConfig();
        customerConfig.setTraderOperateState(basicTraderResponse.getTraderOperateState());
        customerConfig.setTraderSign(basicTraderResponse.getTraderSign());
        customerConfig.setCustomerId(basicTraderResponse.getCustomerId());
        customerConfig.setCustomerName(basicTraderResponse.getCustomerName());
        customerConfig.setSignedCompany(basicTraderResponse.getSignedCompany());
        customerConfig.setSignedOrg(basicTraderResponse.getSignedOrg());
        expressOrderContext.setCustomerConfig(customerConfig);

    }

}
