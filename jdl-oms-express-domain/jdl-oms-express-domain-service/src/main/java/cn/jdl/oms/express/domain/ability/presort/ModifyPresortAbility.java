package cn.jdl.oms.express.domain.ability.presort;

import cn.jdl.batrix.core.flow.domain.BDomainFlowNode;
import cn.jdl.oms.express.domain.ability.AbstractDomainAbility;
import cn.jdl.oms.express.domain.annotation.AbilityScene;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.presort.IPresortExtension;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.shared.common.constant.DomainConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import com.jd.matrix.core.SimpleReducer;
import com.jd.matrix.core.annotation.DomainAbility;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 0.0.1
 * @description:
 * @create 2021-03-29 20:32
 **/
@DomainAbility(name = "纯配领域能力-改单预分活动能力", parent = DomainConstants.EXPRESS_ORDER_DOMIAN_CODE)
@AbilityScene(businessScenes = BusinessSceneEnum.MODIFY, isDefault = false)
public class ModifyPresortAbility extends AbstractDomainAbility<ExpressOrderContext, IPresortExtension> {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ModifyPresortAbility.class);

    /**
     * 功能：改单预分拣活动能力
     *
     * @param context         1
     * @param bDomainFlowNode 2
     * @return void
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/30 19:58
     */
    @Override
    public void execute(ExpressOrderContext context, BDomainFlowNode bDomainFlowNode) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("纯配领域能力-改单预分活动能力执行开始");
            if (ModifySceneRuleUtil.isNoTaskFinishCollect(context.getOrderModel())
                || ModifySceneRuleUtil.isModifyQuantity(context.getOrderModel())
                || ModifySceneRuleUtil.isModifyOpMode(context.getOrderModel())) {
                LOGGER.info("纯配领域能力-无任务揽收、修改包裹数、修改运营模式、跳过预分拣校验,改单预分活动能力执行结束");
                return;
            }
            IPresortExtension extension = this.getMiddleExtensionFast(IPresortExtension.class,
                    context.getOrderModel(),
                    SimpleReducer.listCollectOf(Objects::nonNull), bDomainFlowNode);

            /*判断改单接口是否需要校验预分拣，修改收寄件地址、产品、增值服务、站点信息、结算方式*/
            //收件人四级地址  --  快照 比较
            // 产品 ：产品集合有值 就是修改， 新增、修改、删除（标识）
            //站点：开始结束
            //结算方式：结算方式
            extension.execute(context);
            LOGGER.info("纯配领域能力-改单预分活动能力执行结束");
        } catch (AbilityExtensionException e) {
            LOGGER.info("纯配领域能力-改单预分活动能力执行异常: {}", e.fullMessage());
            throw e;
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.info("纯配领域能力-改单预分活动能力执行异常: ", e);
            throw new DomainAbilityException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL, e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    @Override
    public IPresortExtension getDefaultExtension() {
        return null;
    }
}
