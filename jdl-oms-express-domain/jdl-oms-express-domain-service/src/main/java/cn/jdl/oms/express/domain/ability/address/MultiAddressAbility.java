package cn.jdl.oms.express.domain.ability.address;

import cn.jdl.batrix.core.flow.domain.BDomainFlowNode;
import cn.jdl.oms.express.domain.ability.AbstractDomainAbility;
import cn.jdl.oms.express.domain.annotation.AbilityScene;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.address.IMultiAddressExtension;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.DomainConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import com.jd.matrix.core.SimpleReducer;
import com.jd.matrix.core.annotation.DomainAbility;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 多地址校验能力
 */
@DomainAbility(name = "纯配领域能力-多地址校验活动能力", parent = DomainConstants.EXPRESS_ORDER_DOMIAN_CODE)
@AbilityScene
public class MultiAddressAbility extends AbstractDomainAbility<ExpressOrderContext, IMultiAddressExtension> {

    private static final Logger LOGGER = LoggerFactory.getLogger(MultiAddressAbility.class);

    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;


    @Override
    public void execute(ExpressOrderContext expressOrderContext, BDomainFlowNode bDomainFlowNode) {

        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);

        try {

            // DUCC开关
            if (!expressUccConfigCenter.isMultiAddressSwitch()) {
                LOGGER.info("纯配领域能力-多地址校验活动执行跳过");
                return;
            }

            if (ModifySceneRuleUtil.isNoTaskFinishCollect(expressOrderContext.getOrderModel())) {
                LOGGER.info("纯配领域能力-无任务揽收，跳过多地址校验");
                return;
            }

            if (ModifySceneRuleUtil.isPickupTransferStation(expressOrderContext.getOrderModel())) {
                if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.PICKUP_TRANSFER_STATION_MULTI_ADDRESS_SWITCH)) {
                    LOGGER.info("纯配领域能力-揽收转站、跨站截单策略，多地址校验开关开启");
                } else {
                    return;
                }
            }

            IMultiAddressExtension extension = this.getMiddleExtensionFast(IMultiAddressExtension.class,
                    expressOrderContext,
                    SimpleReducer.listCollectOf(Objects::nonNull), bDomainFlowNode);
            LOGGER.info("纯配领域能力-多地址校验活动执行开始");
            if (extension != null) {
                extension.execute(expressOrderContext);
            }
            LOGGER.info("纯配领域能力-多地址校验活动执行结束");

        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            // 修改场景校验失败时返回修改失败(接单、取消场景不拒单)
            String businessScene = expressOrderContext.getOrderModel().getOrderBusinessIdentity().getBusinessScene();
            if (BusinessSceneEnum.MODIFY.getCode().equals(businessScene)) {
                LOGGER.error("纯配领域能力-多地址校验执行异常,拒单,businessScene: {}. ", businessScene, e);
                throw e;
            } else {
                LOGGER.error("纯配领域能力-多地址校验执行异常,不拒单,businessScene: {}. ", businessScene, e);
            }

        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }

    }

    @Override
    public IMultiAddressExtension getDefaultExtension() {
        return null;
    }
}
