package cn.jdl.oms.express.domain.flow.coupon;

import cn.jdl.batrix.core.flow.domain.BOutputMessage;
import cn.jdl.oms.express.domain.ability.AbstractDomainAbility;
import cn.jdl.oms.express.domain.ability.coupon.CallbackCouponReleaseAbility;
import cn.jdl.oms.express.domain.ability.coupon.CancelCouponReleaseAbility;
import cn.jdl.oms.express.domain.ability.coupon.ModifyCouponReleaseAbility;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.flow.AbstractDomainFlowNode;
import cn.jdl.oms.express.shared.common.constant.FlowConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 优惠券释放节点
 */
@Service
public class CouponReleaseFlowNode extends AbstractDomainFlowNode {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(CouponReleaseFlowNode.class);

    @Resource
    private ModifyCouponReleaseAbility modifyCouponReleaseAbility;

    @Resource
    private CancelCouponReleaseAbility cancelCouponReleaseAbility;

    @Resource
    private CallbackCouponReleaseAbility callbackCouponReleaseAbility;

    @Override
    public BOutputMessage call(InputMessage inputMessage) {
        CallerInfo info = Profiler.registerInfo(this.getClass().getName() + ".call"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            ExpressOrderContext context = (ExpressOrderContext) inputMessage.getBody();
            BOutputMessage outputMessage = new BOutputMessage();
            outputMessage.setBody(context);
            LOGGER.info("优惠券流程节点开始执行");
            AbstractDomainAbility ability = getDomainAbility(context, modifyCouponReleaseAbility,cancelCouponReleaseAbility,callbackCouponReleaseAbility);
            ability.execute(context, this);
            LOGGER.info("优惠券流程节点执行结束");
            return outputMessage;
        } catch (DomainAbilityException e) {
            LOGGER.error("优惠券流程节点执行异常: {}", e.fullMessage());
            throw e;
        } catch (Exception e) {
            Profiler.functionError(info);
            LOGGER.error("优惠券能力流程节点执行异常", e);
            throw e;
        } finally {
            Profiler.registerInfoEnd(info);
        }
    }

    @Override
    public String getCode() {
        return FlowConstants.EXPRESS_ORDER_COUPON_FLOW_CODE;
    }

    @Override
    public String getName() {
        return FlowConstants.EXPRESS_ORDER_COUPON_FLOW_NAME;
    }
}
