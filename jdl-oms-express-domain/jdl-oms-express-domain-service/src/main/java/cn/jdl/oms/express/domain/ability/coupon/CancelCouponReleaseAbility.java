package cn.jdl.oms.express.domain.ability.coupon;

import cn.jdl.batrix.core.flow.domain.BDomainFlowNode;
import cn.jdl.oms.express.domain.ability.AbstractDomainAbility;
import cn.jdl.oms.express.domain.annotation.AbilityScene;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.coupon.ICouponReleaseExtension;
import cn.jdl.oms.express.domain.infrs.acl.pl.coupon.FreightCouponTranslator;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.CouponReleaseMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.CancelStatusEnum;
import cn.jdl.oms.express.domain.vo.Ticket;
import cn.jdl.oms.express.shared.common.constant.DomainConstants;
import cn.jdl.oms.express.shared.common.constant.FlowConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.CouponExecuteTypeEnum;
import cn.jdl.oms.express.shared.common.dict.CouponStatusEnum;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.core.annotation.DomainAbility;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;

@DomainAbility(name = "纯配取消领域能力-优惠券释放活动能力", parent = DomainConstants.EXPRESS_ORDER_DOMIAN_CODE)
@AbilityScene(businessScenes = {BusinessSceneEnum.CANCEL}, isDefault = false)
public class CancelCouponReleaseAbility extends AbstractDomainAbility<ExpressOrderContext, ICouponReleaseExtension> {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(CancelCouponReleaseAbility.class);

    /**
     * 任务调度
     */
    @Resource
    private SchedulerService schedulerService;

    /**
     * 快运优惠券占用释放参数转换
     */
    @Resource
    private FreightCouponTranslator freightCouponTranslator;

    @Override
    public void execute(ExpressOrderContext expressOrderContext, BDomainFlowNode bDomainFlowNode) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("纯配取消领域能力-优惠券释放活动执行开始");
            cancelReleaseCoupon(expressOrderContext.getOrderModel());
            LOGGER.info("纯配取消领域能力-优惠券释放活动执行结束");
        } catch (DomainAbilityException e) {
            LOGGER.error("纯配取消领域能力-优惠券释放能力活动执行异常: {}", e.fullMessage());
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_COUPON_RELEASE_EXCEPTION_ALARM, "取消优惠券释放能力活动执行异常,orderNo=" + expressOrderContext.getOrderModel().orderNo());
            throw e;
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("纯配取消领域能力-优惠券释放能力活动执行异常 ", e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_COUPON_RELEASE_EXCEPTION_ALARM, "取消优惠券释放能力活动执行异常,orderNo=" + expressOrderContext.getOrderModel().orderNo());
            throw new DomainAbilityException(UnifiedErrorSpec.BasisOrder.COUPON_RELEASE_FAIL, e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 取消异步释放优惠券信息
     *
     * @param model
     * @return
     */
    private void cancelReleaseCoupon(ExpressOrderModel model) {
        // 取消拦截一体化之后
        // 取消成功才操作资源释放
        if(!CancelStatusEnum.CANCEL_SUCCESS.equals(model.getCancelStatus())){
            LOGGER.info("纯配取消领域能力-优惠券释放：订单取消成功才释放资源，cancelStatus={}",model.getCancelStatus());
            return;
        }
        if (model.getOrderSnapshot().getPromotion() == null || CollectionUtils.isEmpty(model.getOrderSnapshot().getPromotion().getTickets())) {
            LOGGER.info("未使用优惠券，不用释放优惠券");
            return;
        }
        List<Ticket> tickets = model.getOrderSnapshot().getPromotion().getTickets();
        for (Ticket ticket : tickets) {
            //未释放的优惠券才能进行释放
            if (CouponStatusEnum.ALLOW_ROLLBACK_NO.getCode().equals(ticket.getCouponStatus()) || CouponStatusEnum.DELETE_PENDING.getCode().equals(ticket.getCouponStatus())) {
                CouponReleaseMessageDto messageDto = new CouponReleaseMessageDto();
                messageDto.setCouponNo(ticket.getTicketNo());
                messageDto.setOrderCreatorNo(model.getOrderSnapshot().getOperator());
                messageDto.setOrderCreateTime(model.getOperateTime());
                messageDto.setUseAmount(ticket.getTicketUseAmount().getAmount());
                messageDto.setWaybillNo(model.getOrderSnapshot().getRefOrderInfoDelegate().getWaybillNo());
                // todo 快运切量完成且下游改造完毕后删除
                freightCouponTranslator.handleWaybillNo(messageDto, model);
                messageDto.setCouponExecuteType(CouponExecuteTypeEnum.NO_PICKUP_CANCEL.getCode());
                messageDto.setModifySomeData(true);
                messageDto.setOrderNo(model.orderNo());
                //messageDto.setOrderId(model.getOrderId());
                messageDto.setCustomOrderNo(model.getCustomOrderNo());
                messageDto.setBusinessIdentity(model.getOrderBusinessIdentity());
                messageDto.setRequestProfile(model.requestProfile());
                messageDto.setCouponSource(ticket.getTicketSource());
                messageDto.setPlatformOrderNo(model.getOrderSnapshot().getRefOrderInfoDelegate().getEnquiryOrderNo());
                //流向
                messageDto.setStartFlowDirection(getStartFlowDirection(model.getOrderSnapshot()));
                messageDto.setEndFlowDirection(getEndFlowDirection(model.getOrderSnapshot()));
                LOGGER.info("取消释放优惠券信息：{}", JSONUtils.beanToJSONDefault(messageDto));
                SchedulerMessage schedulerMessage = new SchedulerMessage();
                schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(messageDto));
                schedulerMessage.setDtoClass(CouponReleaseMessageDto.class);
                schedulerService.addSchedulerTask(PDQTopicEnum.RELEASE_COUPON, schedulerMessage, FlowConstants.EXPRESS_ORDER_COUPON_FLOW_CODE);
                ticket.setCouponStatus(CouponStatusEnum.ALLOW_ROLLBACK_YES.getCode());
            }
        }

    }

    @Override
    public ICouponReleaseExtension getDefaultExtension() {
        return null;
    }

    /**
     * 获取始发流向
     * @param orderModel
     * @return
     */
    private String getStartFlowDirection(ExpressOrderModel orderModel){
        if(null == orderModel.getCustoms()){
            return null;
        }
        if(null == orderModel.getCustoms().getStartFlowDirection()){
            return null;
        }
        return orderModel.getCustoms().getStartFlowDirection().name();
    }

    /**
     * 获取目的流向
     * @param orderModel
     * @return
     */
    private String getEndFlowDirection(ExpressOrderModel orderModel){
        if(null == orderModel.getCustoms()){
            return null;
        }
        if(null == orderModel.getCustoms().getEndFlowDirection()){
            return null;
        }
        return orderModel.getCustoms().getEndFlowDirection().name();
    }
}
