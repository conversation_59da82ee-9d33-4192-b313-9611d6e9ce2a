package cn.jdl.oms.express.domain.ability.compare;

import cn.jdl.batrix.core.flow.domain.BDomainFlowNode;
import cn.jdl.oms.express.domain.ability.AbstractDomainAbility;
import cn.jdl.oms.express.domain.annotation.AbilityScene;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.converter.ProductMapper;
import cn.jdl.oms.express.domain.extension.compare.ICompareExtension;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.ContextInfoEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSignEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderExtendTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceRequirementsEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.vo.Activity;
import cn.jdl.oms.express.domain.vo.Agreement;
import cn.jdl.oms.express.domain.vo.Attachment;
import cn.jdl.oms.express.domain.vo.BusinessSolution;
import cn.jdl.oms.express.domain.vo.Cargo;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.domain.vo.Customs;
import cn.jdl.oms.express.domain.vo.Discount;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.Fulfillment;
import cn.jdl.oms.express.domain.vo.Goods;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.ProductDelegate;
import cn.jdl.oms.express.domain.vo.RefOrderDelegate;
import cn.jdl.oms.express.domain.vo.Shipment;
import cn.jdl.oms.express.domain.vo.Ticket;
import cn.jdl.oms.express.domain.vo.modify.ChangedProperty;
import cn.jdl.oms.express.domain.vo.modify.ChangedPropertyDelegate;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.DomainConstants;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.constant.FinanceConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.express.shared.common.dict.CouponStatusEnum;
import cn.jdl.oms.express.shared.common.dict.ExpressOrderStatusCustomEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldValueEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.DateUtils;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.core.SimpleReducer;
import com.jd.matrix.core.annotation.DomainAbility;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@DomainAbility(name = "纯配领域能力-修改服务比对能力", parent = DomainConstants.EXPRESS_ORDER_DOMIAN_CODE)
@AbilityScene(businessScenes = {BusinessSceneEnum.MODIFY, BusinessSceneEnum.MODIFY_FINANCE}, isDefault = false)
public class ModifyCompareAbility extends AbstractDomainAbility<ExpressOrderContext, ICompareExtension> {
    /**
     * Log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ModifyCompareAbility.class);
    /**
     * 地址修改标识
     */
    private static final String ADDRESS_MODIFY_FLAG = "addressModifyFlag";
    /**
     * 代收货款修改标识
     */
    private static final String COD_MODIFY_FLAG = "CODModifyFlag";
    /**
     * 代收货款编码
     */
    private static final String COD_PRODUCT_NO = "ed-a-0009";
    /**
     * 修改标识值
     */
    private static final String MODIFY = "1";
    /**
     * 修改标识值
     */
    private static final String UN_MODIFY = "0";
    /**
     * 警报
     */
    @Resource
    private UmpUtil umpUtil;

    /**
     * 对比修改项
     * @param expressOrderContext         领域上下文
     * @param bDomainFlowNode 当前执行流程节点便于batrix查找当前能力ability
     * @throws DomainAbilityException 领域能力异常
     */
    @Override
    public void execute(ExpressOrderContext expressOrderContext, BDomainFlowNode bDomainFlowNode) throws DomainAbilityException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            //比对订单信息
            LOGGER.info("比对修改记录SystemCaller:{}", expressOrderContext.getOrderModel().getChannel().getSystemCaller());
            List<ChangedProperty> changedProperties = compareProperties(expressOrderContext.getOrderModel());
            //放入上下文
            ChangedPropertyDelegate changedPropertyDelegate = new ChangedPropertyDelegate();
            changedPropertyDelegate.setChangedProperties(changedProperties);
            expressOrderContext.setChangedPropertyDelegate(changedPropertyDelegate);
            //依赖比对结果的 后置业务打标处理
            businessFlagAfterCompare(expressOrderContext, changedPropertyDelegate);

            ICompareExtension extension = this.getMiddleExtensionFast(ICompareExtension.class,
                    expressOrderContext, SimpleReducer.listCollectOf(Objects::nonNull), bDomainFlowNode);
            if(extension != null){
                LOGGER.info("纯配领域能力-修改对比项拓展能力执行开始");
                extension.execute(expressOrderContext);
                LOGGER.info("纯配领域能力-修改对比项拓展能力执行结束");
            }
            LOGGER.info("修改业务，订单信息比对，修改项={}", JSONUtils.beanToJSONDefault(changedProperties));
            if (expressOrderContext.getChangedPropertyDelegate() == null || CollectionUtils.isEmpty(expressOrderContext.getChangedPropertyDelegate().getChangedProperties())) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_INFO_NO_CHANGE).withCustom("订单信息未发生变更，不支持修改");
            }
        } catch (AbilityExtensionException e) {
            LOGGER.error("纯配领域能力-修改服务订单信息比对能力活动执行异常: {}", e.fullMessage());
            throw e;
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("纯配领域能力-修改服务订单信息比对能力活动执行异常", e);
            throw new DomainAbilityException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL, e).withCustom("订单信息比对失败");
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 依赖比对结果的 后置业务打标处理
     * @param expressOrderContext 上下文
     * @param changedPropertyDelegate 比较器
     */
    private void businessFlagAfterCompare(ExpressOrderContext expressOrderContext, ChangedPropertyDelegate changedPropertyDelegate) {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        // 暂存服务费业务标识
        if (changedPropertyDelegate.tempStorageDayHaveChange()) {
            tempStorageBusinessFlag(orderModel, orderSnapshot, expressOrderContext);
        }
    }

    /**
     * 暂存服务费 业务标识处理
     * @param orderModel 修改请求模型
     * @param orderSnapshot 原始订单模型
     * @param context 上下文
     */
    private void tempStorageBusinessFlag(ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot, ExpressOrderContext context) {
        if ("0".equals(orderModel.getShipment().getServiceRequirementByKey(ServiceRequirementsEnum.TEMP_STORAGE_DAY.getCode()))) {
            if (orderSnapshot.haveTempStorageOrder()) {
                //暂存服务单号不为空，则清台账  打标
                LOGGER.info("订单:{}，暂存天数改为0，且已询价写账，需清台账",orderModel.orderNo());
                context.getOrderModel().putAttachment(EnquiryConstants.TEMP_STORAGE_ATTACH_FEE, "");
                context.putExtMaps(ContextInfoEnum.TEMP_STORAGE_ORDER_BANK_CLEAR_FLAG.getCode(), OrderConstants.YES_VAL);
            } else {
                //不处理
                LOGGER.info("订单:{}，暂存天数改为0，但未询价写账，无需后续处理",orderModel.orderNo());
            }
        } else {
            LOGGER.info("订单:{}，暂存天数改为非0，且已经询价写账，B2C到付订单，订单中心需匹配附加服务费",orderModel.orderNo());
            context.putExtMaps(ContextInfoEnum.TEMP_STORAGE_FEE_PRODUCT_MATCH_FLAG.getCode(), OrderConstants.YES_VAL);
            // 匹配附加服务费使用ProductSurchargeFlowNode，新建ability
            // 匹配成功后 B2C到付现结 复用能力BuildEnquiryOrderFlowNode 处理服务单，先询价，后写账，有服务单改账，无服务单新建账
        }
    }

    /**
     * 获取默认扩展点
     * @return null
     */
    @Override
    public ICompareExtension getDefaultExtension() {
        return null;
    }

    /**
     * 对比各项信息
     * @param orderModel 订单模型
     * @return 变更项
     * @throws Exception 异常
     */
    private List<ChangedProperty> compareProperties(ExpressOrderModel orderModel) throws Exception {
        List<ChangedProperty> changedProperties = new ArrayList<>();
        List<String> clearFields = orderModel.getClearFields();
        //比对主档信息,获取类中的全部字段
        compareOrderMain(orderModel, orderModel.getOrderSnapshot(), clearFields, changedProperties);
        //比对channel
        if (orderModel.getChannel() != null) {
            if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.COMPARE_CHANNEL_SWITCH)) {
                LOGGER.info("比对渠道信息开关开启");
                compareChannel(orderModel, changedProperties);
            } else {
                LOGGER.info("比对渠道信息开关关闭");
            }
        }

        //比对Shipment
        if (orderModel.getShipment() != null) {
            compareShipment(orderModel, changedProperties);
        }

        //比对Consignor()
        if (orderModel.getConsignor() != null) {
            compareConsignor(orderModel.getConsignor(), orderModel.getOrderSnapshot().getConsignor(), clearFields, changedProperties);
        }

        //比对Consignee
        if (orderModel.getConsignee() != null) {
            compareConsignee(orderModel, clearFields, changedProperties);
        }

        //比对finance
        if (orderModel.getFinance() != null) {
            compareFinance(orderModel, orderModel.getFinance(), orderModel.getOrderSnapshot().getFinance(), clearFields, changedProperties);
        }

        if (orderModel.getModifiedFields() != null) {
            //比对productInfos
            if (orderModel.getModifiedFields().get(ModifiedFieldEnum.PRODUCT_INFOS.getCode()) != null) {
                compareProducts(orderModel, changedProperties);
            }

            //比对cargoInfos货品信息
            if (orderModel.getModifiedFields().get(ModifiedFieldEnum.CARGO_INFOS.getCode()) != null) {
                compareCargoes(orderModel, changedProperties);
            }

            //比对goodsInfos商品信息
            if (orderModel.getModifiedFields().get(ModifiedFieldEnum.GOODS_INFOS.getCode()) != null) {
                compareGoods(orderModel, changedProperties);
            }

            //比对financeCostInfos收费要求信息
            if (orderModel.getModifiedFields().get(ModifiedFieldEnum.COST_INFOS.getCode()) != null) {
                compareFinanceCostInfos(orderModel, changedProperties);
            }

            //比对financeAttachFees附加费用信息
            if (orderModel.getModifiedFields().get(ModifiedFieldEnum.ATTACH_FEES.getCode()) != null) {
                compareFinanceAttachFees(orderModel, changedProperties, clearFields);
            }

            //比对优惠券信息
            if (orderModel.getModifiedFields().get(ModifiedFieldEnum.TICKET_INFOS.getCode()) != null) {
                compareTickets(orderModel, changedProperties);
            }

            //比对折扣信息
            if (orderModel.getModifiedFields().get(ModifiedFieldEnum.DISCOUNT_INFOS.getCode()) != null) {
                compareDiscounts(orderModel, changedProperties);

            }
            //比对活动信息
            if (orderModel.getModifiedFields().get(ModifiedFieldEnum.ACTIVITY_INFOS.getCode()) != null) {
                compareActivities(orderModel, changedProperties);
            }
            // 对比协议信息
            if (orderModel.getModifiedFields().get(ModifiedFieldEnum.AGREEMENT_INFOS.getCode()) != null) {
                compareAgreement(orderModel, changedProperties, clearFields);
            }

            // 对比附件信息
            if (orderModel.getModifiedFields().get(ModifiedFieldEnum.ATTACHMENT_INFOS.getCode()) != null) {
                compareAttachments(orderModel, changedProperties, clearFields);
            }
        }

        //对比退货信息
        if (orderModel.getReturnInfoVo() != null){
            compareReturnInfo(orderModel, changedProperties);
        }

        //对比关务信息
        if (orderModel.getCustoms() != null){
            compareCustoms(orderModel, clearFields, changedProperties);
        }

        //对比履约信息
        if (orderModel.getFulfillment() != null){
            compareFulfillment(orderModel, changedProperties);
        }

        //对比解决方案信息
        if (orderModel.getBusinessSolution() != null) {
            compareBusinessSolution(orderModel, changedProperties);
        }

        try {
            //对比关联单信息
            if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.COMPARE_REF_ORDER_SWITCH) && orderModel.getRefOrderInfoDelegate() != null) {
                compareRefOrderInfoDelegate(orderModel, changedProperties);
            }
        } catch (Exception e) {
            // todo 大促后去除开关和报警
            LOGGER.error("对比关联单信息异常，Exception=" + e.getMessage());
            umpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_COMPARE_REF_ORDER_EXCEPTION_ALARM, "对比关联单信息异常", orderModel.traceId());
            throw e;
        }

        return changedProperties;
    }

    /**
     * 比对关联单信息
     *
     * @param orderModel
     * @param changedProperties
     */
    private void compareRefOrderInfoDelegate(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties) {

        // 比对关联单扩展字段
        if (MapUtils.isNotEmpty(orderModel.getRefOrderInfoDelegate().getExtendProps())) {
            compareRefOrderExtendProps(orderModel, changedProperties);
        }

    }

    /**
     * 关联单扩展字段-按key对比
     *
     * @param orderModel
     * @param changedProperties
     */
    private void compareRefOrderExtendProps(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties) {
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();

        // 比对签单返单运单号
        doCompareExtRefOrders(RefOrderExtendTypeEnum.SIGN_RETURN_WAYBILL_NO, ModifyItemConfigEnum.SIGN_RETURN_WAYBILL_NO, orderModel, orderSnapshot, changedProperties);
    }

    /**
     * 执行对比关联单信息-扩展单据
     */
    private void doCompareExtRefOrders(RefOrderExtendTypeEnum refOrderExtendTypeEnum, ModifyItemConfigEnum modifyItemConfigEnum, ExpressOrderModel order, ExpressOrderModel snapShot, List<ChangedProperty> changedProperties) {
        String value = getExtRefOrders(order, refOrderExtendTypeEnum);
        String originValue = getExtRefOrders(snapShot, refOrderExtendTypeEnum);
        compareStringTemplateMethod(value, originValue, modifyItemConfigEnum, changedProperties);
    }

    /**
     * 获取关联单信息-扩展单据
     */
    private String getExtRefOrders(ExpressOrderModel orderModel, RefOrderExtendTypeEnum refOrderExtendTypeEnum) {
        return Optional.ofNullable(orderModel)
                .map(ExpressOrderModel::getRefOrderInfoDelegate)
                .map(RefOrderDelegate::getExtendProps)
                .map(extRefOrders -> extRefOrders.get(refOrderExtendTypeEnum.getCode()))
                .filter(StringUtils::isNotBlank)
                .orElse(null);
    }

    /**
     * String比对模版方法
     */
    private void compareStringTemplateMethod(String value, String originValue, ModifyItemConfigEnum modifyItemConfigEnum, List<ChangedProperty> changedProperties) {
        if (value != null) {
            OperateTypeEnum operateType = compareString(value, originValue);
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, modifyItemConfigEnum, value, originValue));
            }
        }
    }

    /**
     * 比对配送信息
     *
     * @param orderModel
     * @param changedProperties 发生变化的属性集合
     */
    private void compareChannel(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties) throws Exception {
        Channel channel = orderModel.getChannel();
        Channel orgChannel = orderModel.getOrderSnapshot().getChannel();
        if (orderModel.getChannel() == null) {
            return;
        }
        Map<String, String> ext = channel.getExtendProps();
        Map<String, String> orgExt = orgChannel.getExtendProps();
        //目前渠道只存在修改扩展字段的场景，只比对扩展字段是否变更，暂不比对具体改动明细,目的是允许修改订单信息，若后续需要比对具体变更的KEY，再细化比对
        if (compareMap(ext, orgExt)) {
            //发生修改
            changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.CHANNEL_EXTEND_PROPS, JSONUtils.beanToJSONDefault(ext), JSONUtils.beanToJSONDefault(orgExt)));
            return;
        }
    }

    /**
     * 比对map
     * false：未修改，true：发生修改
     * @param map
     * @param orgMap
     * @return
     */
    public boolean compareMap(Map<String, String> map, Map<String, String> orgMap) {
        if (MapUtils.isEmpty(map)) {
            //修改参数为空，说明不修改
            return false;
        }

        // 比对KEY-VALUE
        HashSet<String> keySet = new HashSet<>(map.keySet());

        for (String key : keySet) {
            String value = map.get(key);
            if (value == null) {
                //值为null，数据层不更新，认为不修改
                continue;
            }
            if (orgMap == null) {
                return true;
            }
            String originValue = orgMap.get(key);
            if (!Objects.equals(value, originValue)) {
                return true;
            }
        }
        return false;
    }
    /**
     * 比对退货信息
     * @param orderModel
     * @param changedProperties
     */
    private void compareReturnInfo(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties){
        //退货类型
        if (orderModel.getReturnInfoVo().getReturnType() != null) {
            OperateTypeEnum operateType = compareString(orderModel.getReturnInfoVo().getReturnType(), orderModel.getOrderSnapshot().getReturnInfoVo().getReturnType());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RETURN_INTO_RETURN_TYPE, orderModel.getReturnInfoVo().getReturnType(), orderModel.getOrderSnapshot().getReturnInfoVo().getReturnType()));
            }
        }
        Consignee returnConsignee = orderModel.getReturnInfoVo().getConsignee();
        if (null == returnConsignee) {
            return;
        }
        Consignee snapShot = orderModel.getOrderSnapshot().getReturnInfoVo().getConsignee();
        if (null == snapShot) {
            LOGGER.info("退货收件人信息新增");
            return;
        }
        //收货人姓名
        if (returnConsignee.getConsigneeName() != null) {
            OperateTypeEnum operateType = compareString(returnConsignee.getConsigneeName(), snapShot.getConsigneeName());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RETURN_INTO_CONSIGNEE_NAME, returnConsignee.getConsigneeName(), snapShot.getConsigneeName()));
            }
        }
        //收货人手机
        if (returnConsignee.getConsigneeMobile() != null) {
            OperateTypeEnum operateType = compareString(returnConsignee.getConsigneeMobile(), snapShot.getConsigneeMobile());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RETURN_INTO_CONSIGNEE_PHONE, returnConsignee.getConsigneeMobile(), snapShot.getConsigneeMobile()));
            }
        }
        //收货人电话
        if (returnConsignee.getConsigneePhone() != null) {
            OperateTypeEnum operateType = compareString(returnConsignee.getConsigneePhone(), snapShot.getConsigneePhone());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RETURN_INTO_CONSIGNEE_MOBILE, returnConsignee.getConsigneePhone(), snapShot.getConsigneePhone()));
            }
        }

        //收货人地址信息
        if (returnConsignee.getAddress() != null) {
            if (returnConsignee.getAddress().getProvinceNo() != null) {
                OperateTypeEnum operateType = compareString(returnConsignee.getAddress().getProvinceNo(), snapShot.getAddress().getProvinceNo());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RETURN_INTO_CONSIGNEE_PROVINCE_NO, returnConsignee.getAddress().getProvinceNo(), snapShot.getAddress().getProvinceNo()));
                }
            }
            if (returnConsignee.getAddress().getCityNo() != null) {
                OperateTypeEnum operateType = compareString(returnConsignee.getAddress().getCityNo(), snapShot.getAddress().getCityNo());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RETURN_INTO_CONSIGNEE_CITY_NO, returnConsignee.getAddress().getCityNo(), snapShot.getAddress().getCityNo()));
                }
            }
            if (returnConsignee.getAddress().getCountyNo() != null) {
                OperateTypeEnum operateType = compareString(returnConsignee.getAddress().getCountyNo(), snapShot.getAddress().getCountyNo());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RETURN_INTO_CONSIGNEE_COUNTY_NO, returnConsignee.getAddress().getCountyNo(), snapShot.getAddress().getCountyNo()));
                }
            }
            if (returnConsignee.getAddress().getTownNo() != null) {
                OperateTypeEnum operateType = compareString(returnConsignee.getAddress().getTownNo(), snapShot.getAddress().getTownNo());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RETURN_INTO_CONSIGNEE_TOWN_NO, returnConsignee.getAddress().getTownNo(), snapShot.getAddress().getTownNo()));
                }
            }
            if (returnConsignee.getAddress().getAddress() != null) {
                OperateTypeEnum operateType = compareString(returnConsignee.getAddress().getAddress(), snapShot.getAddress().getAddress());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RETURN_INTO_CONSIGNEE_ADDRESS, returnConsignee.getAddress().getAddress(), snapShot.getAddress().getAddress()));
                }
            }
            //行政区编码
            if (returnConsignee.getAddress().getRegionNo() != null) {
                OperateTypeEnum operateType = compareString(returnConsignee.getAddress().getRegionNo(), snapShot.getAddress().getRegionNo());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RETURN_INTO_CONSIGNEE_ADDRESS_REGION_NO, returnConsignee.getAddress().getRegionNo(), snapShot.getAddress().getRegionNo()));
                }
            }

            //行政区名称
            if (returnConsignee.getAddress().getRegionName() != null) {
                OperateTypeEnum operateType = compareString(returnConsignee.getAddress().getRegionName(), snapShot.getAddress().getRegionName());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RETURN_INTO_CONSIGNEE_ADDRESS_REGION_NAME, returnConsignee.getAddress().getRegionName(), snapShot.getAddress().getRegionName()));
                }
            }
        }
    }

    /**
     * 比对订单主档信息
     *
     * @param order
     * @param snapShot
     * @param clearFields
     * @param changedProperties
     */
    private void compareOrderMain(ExpressOrderModel order, ExpressOrderModel snapShot, List<String> clearFields, List<ChangedProperty> changedProperties) {
        if (order.getRemark() != null) {
            OperateTypeEnum operateType = compareString(order.getRemark(), snapShot.getRemark());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.ORDER_REMARK, order.getRemark(), snapShot.getRemark()));
            }
        }

        // 对比订单总净重
        if (order.getOrderNetWeight() != null) {
            // 对比值
            OperateTypeEnum operateType = compareBigDecimal(order.getOrderNetWeight().getValue(), snapShot.getOrderNetWeight().getValue());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.ORDER_NET_WEIGHT, String.valueOf(order.getOrderNetWeight().getValue()), String.valueOf(snapShot.getOrderNetWeight().getValue())));
            }

            // 对比单位
            if (order.getOrderNetWeight().getUnit() != null) {
                operateType = compareString(order.getOrderNetWeight().getUnit().getCode(), snapShot.getOrderNetWeight().getUnit().getCode());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.ORDER_NET_WEIGHT_UNIT, String.valueOf(order.getOrderNetWeight().getUnit().getCode()), String.valueOf(snapShot.getOrderNetWeight().getUnit().getCode())));
                }
            }
        }

        // 对比订单总毛重
        if (order.getOrderWeight() != null) {
            // 对比值
            OperateTypeEnum operateType = compareBigDecimal(order.getOrderWeight().getValue(), snapShot.getOrderWeight().getValue());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.ORDER_WEIGHT, String.valueOf(order.getOrderWeight().getValue()), String.valueOf(snapShot.getOrderWeight().getValue())));
            }

            // 对比单位
            if (order.getOrderWeight().getUnit() != null) {
                operateType = compareString(order.getOrderWeight().getUnit().getCode(), snapShot.getOrderWeight().getUnit().getCode());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.ORDER_WEIGHT_UNIT, String.valueOf(order.getOrderWeight().getUnit().getCode()), String.valueOf(snapShot.getOrderWeight().getUnit().getCode())));
                }
            }
        }

        // 对比订单总体积
        if (order.getOrderVolume() != null) {
            // 对比值
            OperateTypeEnum operateType = compareBigDecimal(order.getOrderVolume().getValue(), snapShot.getOrderVolume().getValue());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.ORDER_VOLUME, String.valueOf(order.getOrderVolume().getValue()), String.valueOf(snapShot.getOrderVolume().getValue())));
            }

            // 对比单位
            if (order.getOrderVolume().getUnit() != null) {
                operateType = compareString(order.getOrderVolume().getUnit().getCode(), snapShot.getOrderVolume().getUnit().getCode());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.ORDER_VOLUME_UNIT, String.valueOf(order.getOrderVolume().getUnit().getCode()), String.valueOf(snapShot.getOrderVolume().getUnit().getCode())));
                }
            }
        }

        // 对比复核重量
        if (order.getRecheckWeight() != null) {
            // 对比值
            OperateTypeEnum operateType = compareBigDecimal(order.getRecheckWeight().getValue(), snapShot.getRecheckWeight().getValue());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RECHECK_WEIGHT, String.valueOf(order.getRecheckWeight().getValue()), String.valueOf(snapShot.getRecheckWeight().getValue())));
            }

            // 对比单位
            if (order.getRecheckWeight().getUnit() != null) {
                operateType = compareString(order.getRecheckWeight().getUnit().getCode(), snapShot.getRecheckWeight().getUnit().getCode());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RECHECK_WEIGHT_UNIT, String.valueOf(order.getRecheckWeight().getUnit().getCode()), String.valueOf(snapShot.getRecheckWeight().getUnit().getCode())));
                }
            }
        }

        // 对比复核体积
        if (order.getRecheckVolume() != null) {
            // 对比值
            OperateTypeEnum operateType = compareBigDecimal(order.getRecheckVolume().getValue(), snapShot.getRecheckVolume().getValue());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RECHECK_VOLUME, String.valueOf(order.getRecheckVolume().getValue()), String.valueOf(snapShot.getRecheckVolume().getValue())));
            }

            // 对比单位
            if (order.getRecheckVolume().getUnit() != null) {
                operateType = compareString(order.getRecheckVolume().getUnit().getCode(), snapShot.getRecheckVolume().getUnit().getCode());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RECHECK_VOLUME_UNIT, String.valueOf(order.getRecheckVolume().getUnit().getCode()), String.valueOf(snapShot.getRecheckVolume().getUnit().getCode())));
                }
            }
        }

        String customer = order.getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS);
        String snapShotCustomer = snapShot.getAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS);
        //比对客户大扩展字段
        if (customer != null) {
            OperateTypeEnum operateType = compareString(customer, snapShotCustomer);
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CUSTOMER_INFO_EXTEND_PROPS, customer, snapShotCustomer));
            }
        }
        //比对自定义大扩展字段
        String customExtendMsg = order.getAttachment(AttachmentKeyEnum.CUSTOM_EXTEND_MSG.getKey());
        String originCustomExtendMsg = snapShot.getAttachment(AttachmentKeyEnum.CUSTOM_EXTEND_MSG.getKey());

        if (customExtendMsg != null) {
            OperateTypeEnum operateType = compareString(customExtendMsg, originCustomExtendMsg);
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CUSTOM_EXTEND_MSG, customExtendMsg, originCustomExtendMsg));
            }
        }
        //b2c保存催单次数
        if (order.isB2C() || order.isB_O2O() || order.isC2B() || order.isCCB2C() || order.isFreight() || order.isCCB2B()) {
            // customer和snapShotCustomer可能为""，JSONUtils.jsonToMap返回null
            Map<String, String> customerInfoExtendProps = customer != null ? JSONUtils.jsonToMap(customer) : null;
            Map<String, String> snapShotCustomerInfoExtendProps = StringUtils.isBlank(snapShotCustomer) ? new HashMap<>() : JSONUtils.jsonToMap(snapShotCustomer);

            //催单次数
            if (null != customerInfoExtendProps && null != customerInfoExtendProps.get(AttachmentKeyEnum.REMINDER_TIMES.getKey())) {
                if (null != snapShotCustomerInfoExtendProps.get(AttachmentKeyEnum.REMINDER_TIMES.getKey())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.ORDER_REMINDER_TIMES, customerInfoExtendProps.get(AttachmentKeyEnum.REMINDER_TIMES.getKey()), snapShotCustomerInfoExtendProps.get(AttachmentKeyEnum.REMINDER_TIMES.getKey())));
                } else {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.ORDER_REMINDER_TIMES, customerInfoExtendProps.get(AttachmentKeyEnum.REMINDER_TIMES.getKey()),""));
                }
                snapShotCustomerInfoExtendProps.put(AttachmentKeyEnum.REMINDER_TIMES.getKey(), customerInfoExtendProps.get(AttachmentKeyEnum.REMINDER_TIMES.getKey()));
            }

            //单单保
            if (null != customerInfoExtendProps && null != customerInfoExtendProps.get(AttachmentKeyEnum.SINGLE_INSURANCE.getKey())) {
                if (null != snapShotCustomerInfoExtendProps.get(AttachmentKeyEnum.SINGLE_INSURANCE.getKey())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.SINGLE_INSURANCE, customerInfoExtendProps.get(AttachmentKeyEnum.SINGLE_INSURANCE.getKey()), snapShotCustomerInfoExtendProps.get(AttachmentKeyEnum.SINGLE_INSURANCE.getKey())));
                } else {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.SINGLE_INSURANCE, customerInfoExtendProps.get(AttachmentKeyEnum.SINGLE_INSURANCE.getKey()),""));
                }
                snapShotCustomerInfoExtendProps.put(AttachmentKeyEnum.SINGLE_INSURANCE.getKey(), customerInfoExtendProps.get(AttachmentKeyEnum.SINGLE_INSURANCE.getKey()));
            }

            if (null != customerInfoExtendProps) {
                // 修改场景，若入参有afterSaleType取入参，否则取原单afterSaleType
                String afterSaleType = customerInfoExtendProps.get(OrderConstants.AFTER_SALE_TYPE);
                if (StringUtils.isNotBlank(afterSaleType)) {
                    snapShotCustomerInfoExtendProps.put(OrderConstants.AFTER_SALE_TYPE, afterSaleType);
                }
            }

            // TODO: 入参的扩展属性被快照覆盖了
            if (snapShotCustomerInfoExtendProps != null) {
                // 若snapShotCustomerInfoExtendProps=null，JSONUtils.mapToJson会返回"null"，因此必须先判断非空
                order.putAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS, JSONUtils.mapToJson(snapShotCustomerInfoExtendProps));
            }

            //比较是否散客挂月结
            compareIndividualMsType(order, snapShot, changedProperties);
        }
        //对比收货平台
        String returnPlatformCode = order.getAttachment(AttachmentKeyEnum.RETURN_PLATFORM.getKey());
        String originReturnPlatformCode = snapShot.getAttachment(AttachmentKeyEnum.RETURN_PLATFORM.getKey());

        if (returnPlatformCode != null) {
            OperateTypeEnum operateType = compareString(returnPlatformCode, originReturnPlatformCode);
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RETURN_PLATFORM, returnPlatformCode, originReturnPlatformCode));
            }
        }
        //比较打印次数
        compareExtPrintTimes(order, snapShot, changedProperties);

        // 比较订单标识
        if (MapUtils.isNotEmpty(order.getOrderSign())) {
            compareOrderSign(order, snapShot, changedProperties);
        }

        // 比较微信小程序OpenId和费用审核状态
        compareExtItem(ModifyItemConfigEnum.WECHAT_C_MINI_OPENID, order, snapShot, changedProperties, true);
        compareExtItem(ModifyItemConfigEnum.FEE_CHECK_STATUS, order, snapShot, changedProperties, true);
        // 比较多地址审核状态
        compareExtItem(ModifyItemConfigEnum.MULTI_ADDRESS_VERIFY_STATUS, order, snapShot, changedProperties, false);
        compareExtItem(ModifyItemConfigEnum.USER_CHECK_TYPE, order, snapShot, changedProperties, true);
        // 比较主产品修改来源标识
        compareExtItem(ModifyItemConfigEnum.MAIN_PRODUCT_CHANGED_SOURCE, order, snapShot, changedProperties, true);
        // 是否发起补签
        compareExtItem(ModifyItemConfigEnum.RE_SIGN_FLAG, order, snapShot, changedProperties, true);
        // 比较成本中心编码
        compareExtItem(ModifyItemConfigEnum.DEPARTMENT_NO, order, snapShot, changedProperties, true);
        // 比较成本中心名称
        compareExtItem(ModifyItemConfigEnum.DEPARTMENT_NAME, order, snapShot, changedProperties, true);
        // 下单人的末级部门编码
        compareExtItem(ModifyItemConfigEnum.FINAL_DEPARTMENT_NO, order, snapShot, changedProperties, true);
        // 下单人的末级部门名称
        compareExtItem(ModifyItemConfigEnum.FINAL_DEPARTMENT_NAME, order, snapShot, changedProperties, true);
        // 国补审核状态
        compareExtItem(ModifyItemConfigEnum.GOV_SUBSIDY_APPROVAL_STATUS, order, snapShot, changedProperties, true);
        // 终止揽收状态
        compareExtItem(ModifyItemConfigEnum.COLLECTION_TERMINATION_CHECK_STATUS, order, snapShot, changedProperties, true);
        // 握手交接信息
        compareExtItem(ModifyItemConfigEnum.HAND_SHAKE_HAND_OVER_INFO, order, snapShot, changedProperties, true);
        // 整车业务派送批次号
        compareExtItem(ModifyItemConfigEnum.DELIVERY_BATCH_NUMBER, order, snapShot, changedProperties, true);
        if (UnitedB2CUtil.isUnitedFreightB2C(order)) {
            // 比较扩展字段-箱号（快运扩展点迁移过来）
            compareExtItem(ModifyItemConfigEnum.ORDER_BOX_CODE, order, snapShot, changedProperties, true);

            // 对比扩展字段-扩展信息-展会名称（快运扩展点迁移过来）
            compareExtendInfos(ModifyItemConfigEnum.EXHIBITION_NAME, order, snapShot, changedProperties);

            // 对比扩展字段-扩展信息-开票类型（快运扩展点迁移过来）
            compareExtendInfos(ModifyItemConfigEnum.INVOICE_TYPE, order, snapShot, changedProperties);

            // 对比是否允许到付转月结（快运扩展点迁移过来）
            doCompareOrderSign(OrderSignEnum.ALLOW_DELIVERY_TO_MONTHLY, ModifyItemConfigEnum.ORDER_SIGN_ALLOW_DELIVERY_TO_MONTHLY, order, snapShot, changedProperties);
        }

    }

    /**
     * 主档扩展字段对比（不支持删除）
     *
     * @param modifyItemConfigEnum
     * @param order
     * @param snapShot
     * @param changedProperties
     * @param allowNoChange 是否允许修改前后的值未变更
     */
    private void compareExtItem(ModifyItemConfigEnum modifyItemConfigEnum, ExpressOrderModel order, ExpressOrderModel snapShot, List<ChangedProperty> changedProperties, boolean allowNoChange) {

        // 当前传入值
        String currentValue = order.getAttachment(modifyItemConfigEnum.getCode());
        // 原值
        String originValue = snapShot.getAttachment(modifyItemConfigEnum.getCode());

        // 当前值不传或为空时不做处理
        if (StringUtils.isBlank(currentValue)) {
            return;
        }

        if (StringUtils.isNotBlank(originValue)) {
            // 当前值和原值 都存在但不相等时，修改
            if (!currentValue.equals(originValue)) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, modifyItemConfigEnum, currentValue, originValue));
            } else {
                // 不允许修改前后的值未变更则抛异常。
                if (!allowNoChange) {
                    LOGGER.error("主档扩展字段[{}]未发生变更，不允许修改，值: {}", modifyItemConfigEnum.getCode(), currentValue);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.MODIFY_MAIN_EXT_FAIL).withCustom("主档扩展字段[" + modifyItemConfigEnum.getCode() + "]未发生变更，不允许修改");
                }
            }
        } else {
            // 当前值存在，原值为空时，插入
            changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, modifyItemConfigEnum, currentValue, ""));
        }
    }

    /**
     * 比较打印次数
     *
     * @param order
     * @param snapShot
     * @param changedProperties
     */
    private void compareExtPrintTimes(ExpressOrderModel order, ExpressOrderModel snapShot, List<ChangedProperty> changedProperties) {
        //当前打印次数
        String currentPrintTimes = order.getAttachment(AttachmentKeyEnum.PRINT_TIMES.getKey());
        //快照打印次数
        String snapShotPrintTimes = snapShot.getAttachment(AttachmentKeyEnum.PRINT_TIMES.getKey());
        //当前打印状态
        String currentPrintStatus = order.getAttachment(AttachmentKeyEnum.PRINT_STATUS.getKey());

        if (StringUtils.isNotBlank(currentPrintTimes)) {
            //打印次数有值
            if (StringUtils.isNotBlank(snapShotPrintTimes)) {
                if (StringUtils.isBlank(currentPrintStatus)) {
                    LOGGER.info("未传打印状态，订单处理打印状态逻辑");
                    if (OrderConstants.INIT_VALUE.equals(snapShotPrintTimes)) {
                        //第一次打印时，更新打印状态为已打印
                        order.putAttachment(AttachmentKeyEnum.PRINT_STATUS.getKey(), OrderConstants.PRINTED_VALUE);
                    }
                }
                //当前和快照都有值时比对是否相等
                if (!currentPrintTimes.equals(snapShotPrintTimes)) {
                    //打印次数有调整
                    changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.ORDER_PRINT_TIMES, currentPrintTimes, snapShotPrintTimes));
                }
            } else {
                //第一次打印时，更新打印状态为已打印
                if (StringUtils.isBlank(currentPrintStatus)) {
                    LOGGER.info("未传打印状态，订单处理打印状态逻辑");
                    order.putAttachment(AttachmentKeyEnum.PRINT_STATUS.getKey(), OrderConstants.PRINTED_VALUE);
                }
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.ORDER_PRINT_TIMES, currentPrintTimes, ""));
            }
        }
        //当前打印状态
        currentPrintStatus = order.getAttachment(AttachmentKeyEnum.PRINT_STATUS.getKey());
        //快照打印状态
        String snapShotPrintStatus = snapShot.getAttachment(AttachmentKeyEnum.PRINT_STATUS.getKey());

        if (StringUtils.isNotBlank(currentPrintStatus)) {
            OperateTypeEnum operateType = compareString(currentPrintStatus, snapShotPrintStatus);
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.ORDER_PRINT_STATUS, currentPrintStatus, snapShotPrintStatus));
            }
        }
    }

    /**
     * 比对配送信息
     *
     * @param orderModel
     * @param changedProperties 发生变化的属性集合
     */
    private void compareShipment(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties) throws Exception {
        List<String> clearFields = orderModel.getClearFields();
        Shipment shipment = orderModel.getShipment();
        Shipment snapShot = orderModel.getOrderSnapshot().getShipment();
        //比对预计送达时间
        if (shipment.getPlanDeliveryTime() == null) {
            if (snapShot != null && snapShot.getPlanDeliveryTime() != null) {
                //是否在要清空的字段里
                if (clearFields.contains(ModifyItemConfigEnum.PLAN_DELIVERY_TIME.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.PLAN_DELIVERY_TIME, null, DateUtils.formatDatetime(snapShot.getPlanDeliveryTime())));
                }
            } else {
                if (CollectionUtils.isNotEmpty(clearFields)) {
                    clearFields.remove(ModifyItemConfigEnum.PLAN_DELIVERY_TIME.getCode());
                }
            }
        } else {
            if (snapShot == null || snapShot.getPlanDeliveryTime() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.PLAN_DELIVERY_TIME, DateUtils.formatDatetime(shipment.getPlanDeliveryTime()), null));
            } else {
                OperateTypeEnum operateType = compareDate(shipment.getPlanDeliveryTime(), snapShot.getPlanDeliveryTime());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.PLAN_DELIVERY_TIME, DateUtils.formatDatetime(shipment.getPlanDeliveryTime()), DateUtils.formatDatetime(snapShot.getPlanDeliveryTime())));

                }
            }
        }
        //TODO 预计送达时间段

        //期望送达开始时间
        if (shipment.getExpectDeliveryStartTime() == null) {
            if (snapShot != null && snapShot.getExpectDeliveryStartTime() != null) {
                //是否在要清空的字段里
                if (clearFields.contains(ModifyItemConfigEnum.EXPECT_DELIVERY_START_TIME.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.EXPECT_DELIVERY_START_TIME, null, DateUtils.formatDatetime(snapShot.getExpectDeliveryStartTime())));
                }
            } else {
                if (CollectionUtils.isNotEmpty(clearFields)) {
                    clearFields.remove(ModifyItemConfigEnum.EXPECT_DELIVERY_START_TIME.getCode());
                }
            }
        } else {
            if (snapShot == null || snapShot.getExpectDeliveryStartTime() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.EXPECT_DELIVERY_START_TIME, DateUtils.formatDatetime(shipment.getExpectDeliveryStartTime()), null));
            } else {
                OperateTypeEnum operateType = compareDate(shipment.getExpectDeliveryStartTime(), snapShot.getExpectDeliveryStartTime());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.EXPECT_DELIVERY_START_TIME, DateUtils.formatDatetime(shipment.getExpectDeliveryStartTime()), DateUtils.formatDatetime(snapShot.getExpectDeliveryStartTime())));

                }
            }
        }
        //期望送达结束时间
        if (shipment.getExpectDeliveryEndTime() == null) {
            if (snapShot != null && snapShot.getExpectDeliveryEndTime() != null) {
                //是否在要清空的字段里
                if (clearFields.contains(ModifyItemConfigEnum.EXPECT_DELIVERY_END_TIME.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.EXPECT_DELIVERY_END_TIME, null, DateUtils.formatDatetime(snapShot.getExpectDeliveryEndTime())));
                }
            } else {
                if (CollectionUtils.isNotEmpty(clearFields)) {
                    clearFields.remove(ModifyItemConfigEnum.EXPECT_DELIVERY_END_TIME.getCode());
                }
            }
        } else {
            if (snapShot == null || snapShot.getExpectDeliveryEndTime() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.EXPECT_DELIVERY_END_TIME, DateUtils.formatDatetime(shipment.getExpectDeliveryEndTime()), null));
            } else {
                OperateTypeEnum operateType = compareDate(shipment.getExpectDeliveryEndTime(), snapShot.getExpectDeliveryEndTime());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.EXPECT_DELIVERY_END_TIME, DateUtils.formatDatetime(shipment.getExpectDeliveryEndTime()), DateUtils.formatDatetime(snapShot.getExpectDeliveryEndTime())));

                }
            }
        }
        //期望取件开始时间
        if (shipment.getExpectPickupStartTime() == null) {
            if (snapShot != null && snapShot.getExpectPickupStartTime() != null) {
                //是否在要清空的字段里
                if (clearFields.contains(ModifyItemConfigEnum.EXPECT_PICKUP_START_TIME.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.EXPECT_PICKUP_START_TIME, null, DateUtils.formatDatetime(snapShot.getExpectPickupStartTime())));
                }
            } else {
                if (CollectionUtils.isNotEmpty(clearFields)) {
                    clearFields.remove(ModifyItemConfigEnum.EXPECT_PICKUP_START_TIME.getCode());
                }
            }
        } else {
            if (snapShot == null || snapShot.getExpectPickupStartTime() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.EXPECT_PICKUP_START_TIME, DateUtils.formatDatetime(shipment.getExpectPickupStartTime()), null));
            } else {
                OperateTypeEnum operateType = compareDate(shipment.getExpectPickupStartTime(), snapShot.getExpectPickupStartTime());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.EXPECT_PICKUP_START_TIME, DateUtils.formatDatetime(shipment.getExpectPickupStartTime()), DateUtils.formatDatetime(snapShot.getExpectPickupStartTime())));

                }
            }
        }
        //期望取件结束时间
        if (shipment.getExpectPickupEndTime() == null) {
            if (snapShot != null && snapShot.getExpectPickupEndTime() != null) {
                //是否在要清空的字段里
                if (clearFields.contains(ModifyItemConfigEnum.EXPECT_PICKUP_END_TIME.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.EXPECT_PICKUP_END_TIME, null, DateUtils.formatDatetime(snapShot.getExpectPickupEndTime())));
                }
            } else {
                if (CollectionUtils.isNotEmpty(clearFields)) {
                    clearFields.remove(ModifyItemConfigEnum.EXPECT_PICKUP_END_TIME.getCode());
                }
            }
        } else {
            if (snapShot == null || snapShot.getExpectPickupEndTime() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.EXPECT_PICKUP_END_TIME, DateUtils.formatDatetime(shipment.getExpectPickupEndTime()), null));
            } else {
                OperateTypeEnum operateType = compareDate(shipment.getExpectPickupEndTime(), snapShot.getExpectPickupEndTime());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.EXPECT_PICKUP_END_TIME, DateUtils.formatDatetime(shipment.getExpectPickupEndTime()), DateUtils.formatDatetime(snapShot.getExpectPickupEndTime())));

                }
            }
        }

        //比对预计接单时间
        if (shipment.getPlanReceiveTime() == null) {
            if (snapShot != null && snapShot.getPlanReceiveTime() != null) {
                if (clearFields.contains(ModifyItemConfigEnum.PLAN_RECEIVE_TIME.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.PLAN_RECEIVE_TIME, null, DateUtils.formatDatetime(snapShot.getPlanReceiveTime())));
                }
            } else {
                if (CollectionUtils.isNotEmpty(clearFields)) {
                    clearFields.remove(ModifyItemConfigEnum.PLAN_RECEIVE_TIME.getCode());
                }
            }
        } else {
            if (snapShot == null || snapShot.getPlanReceiveTime() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.PLAN_RECEIVE_TIME, DateUtils.formatDatetime(shipment.getPlanReceiveTime()), null));
            } else {
                OperateTypeEnum operateType = compareDate(shipment.getPlanReceiveTime(), snapShot.getPlanReceiveTime());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.PLAN_RECEIVE_TIME, DateUtils.formatDatetime(shipment.getPlanReceiveTime()), DateUtils.formatDatetime(snapShot.getPlanReceiveTime())));
                }
            }
        }

        //比对期望派货开始时间
        if (shipment.getExpectDispatchStartTime() != null) {
            if (snapShot == null || snapShot.getExpectDispatchStartTime() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.EXPECT_DISPATCH_START_TIME, DateUtils.formatDatetime(shipment.getExpectDispatchStartTime()), null));
            } else {
                OperateTypeEnum operateType = compareDate(shipment.getExpectDispatchStartTime(), snapShot.getExpectDispatchStartTime());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.EXPECT_DISPATCH_START_TIME, DateUtils.formatDatetime(shipment.getExpectDispatchStartTime()), DateUtils.formatDatetime(snapShot.getExpectDispatchStartTime())));
                }
            }
        }

        //比对期望派货结束时间
        if (shipment.getExpectDispatchEndTime() != null) {
            if (snapShot == null || snapShot.getExpectDispatchEndTime() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.EXPECT_DISPATCH_END_TIME, DateUtils.formatDatetime(shipment.getExpectDispatchEndTime()), null));
            } else {
                OperateTypeEnum operateType = compareDate(shipment.getExpectDispatchEndTime(), snapShot.getExpectDispatchEndTime());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.EXPECT_DISPATCH_END_TIME, DateUtils.formatDatetime(shipment.getExpectDispatchEndTime()), DateUtils.formatDatetime(snapShot.getExpectDispatchEndTime())));
                }
            }
        }

        //揽收方式
        if (shipment.getPickupType() == null) {
            if (snapShot != null && snapShot.getPickupType() != null) {
                if (clearFields.contains(ModifyItemConfigEnum.PICKUP_TYPE.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.PICKUP_TYPE, null, String.valueOf(snapShot.getPickupType().getCode())));
                }
            } else {
                if (CollectionUtils.isNotEmpty(clearFields)) {
                    clearFields.remove(ModifyItemConfigEnum.PICKUP_TYPE.getCode());
                }
            }
        } else {
            if (snapShot == null || snapShot.getPickupType() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.PICKUP_TYPE, String.valueOf(shipment.getPickupType().getCode()), null));
            } else {
                OperateTypeEnum operateType = compareEnum(shipment.getPickupType(), snapShot.getPickupType());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.PICKUP_TYPE, String.valueOf(shipment.getPickupType().getCode()), String.valueOf(snapShot.getPickupType().getCode())));
                }
            }
        }

        //派送方式
        if (shipment.getDeliveryType() == null) {
            if (snapShot != null && snapShot.getDeliveryType() != null) {
                if (clearFields.contains(ModifyItemConfigEnum.DELIVERY_TYPE.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.DELIVERY_TYPE, null, String.valueOf(snapShot.getDeliveryType().getCode())));
                }
            } else {
                if (CollectionUtils.isNotEmpty(clearFields)) {
                    clearFields.remove(ModifyItemConfigEnum.DELIVERY_TYPE.getCode());
                }
            }
        } else {
            if (snapShot == null || snapShot.getDeliveryType() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.DELIVERY_TYPE, String.valueOf(shipment.getDeliveryType().getCode()), null));
            } else {
                OperateTypeEnum operateType = compareEnum(shipment.getDeliveryType(), snapShot.getDeliveryType());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.DELIVERY_TYPE, String.valueOf(shipment.getDeliveryType().getCode()), String.valueOf(snapShot.getDeliveryType().getCode())));
                }
            }
        }
        //温层
        if (shipment.getWarmLayer() != null) {
            if (snapShot == null || snapShot.getWarmLayer() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.WARM_LAYER, shipment.getWarmLayer().getCode(), null));
            } else {
                OperateTypeEnum operateType = compareEnum(shipment.getWarmLayer(), snapShot.getWarmLayer());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.WARM_LAYER, shipment.getWarmLayer().getCode(), snapShot.getWarmLayer().getCode()));
                }
            }
        }
        //起始站点编码
        if (shipment.getStartStationNo() != null) {
            if (snapShot == null || snapShot.getStartStationNo() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.START_STATION_NO, shipment.getStartStationNo(), null));
            } else {
                OperateTypeEnum operateType = compareString(shipment.getStartStationNo(), snapShot.getStartStationNo());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.START_STATION_NO, shipment.getStartStationNo(), snapShot.getStartStationNo()));
                }
            }
        }
        //目的站点编码
        if (shipment.getEndStationNo() != null) {
            if (snapShot == null || snapShot.getEndStationNo() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.END_STATION_NO, shipment.getEndStationNo(), null));
            } else {
                OperateTypeEnum operateType = compareString(shipment.getEndStationNo(), snapShot.getEndStationNo());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.END_STATION_NO, shipment.getEndStationNo(), snapShot.getEndStationNo()));
                }
            }
        }
        //运输类型
        if (shipment.getTransportType() == null) {
            if (snapShot != null && snapShot.getTransportType() != null) {
                if (clearFields.contains(ModifyItemConfigEnum.TRANSPORT_TYPE.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.TRANSPORT_TYPE, null, String.valueOf(snapShot.getTransportType().getCode())));
                }
            } else {
                if (CollectionUtils.isNotEmpty(clearFields)) {
                    clearFields.remove(ModifyItemConfigEnum.TRANSPORT_TYPE.getCode());
                }
            }
        } else {
            if (snapShot == null || snapShot.getTransportType() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.TRANSPORT_TYPE, String.valueOf(shipment.getTransportType().getCode()), null));
            } else {
                OperateTypeEnum operateType = compareEnum(shipment.getTransportType(), snapShot.getTransportType());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.TRANSPORT_TYPE, String.valueOf(shipment.getTransportType().getCode()), String.valueOf(snapShot.getTransportType().getCode())));
                }
            }
        }

        //无接触收货方式
        if (shipment.getContactlessType() == null) {
            if (snapShot != null && snapShot.getContactlessType() != null) {
                if (clearFields.contains(ModifyItemConfigEnum.CONTACTLESS_TYPE.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.CONTACTLESS_TYPE, null, String.valueOf(snapShot.getContactlessType().getCode())));
                }
            } else {
                if (CollectionUtils.isNotEmpty(clearFields)) {
                    clearFields.remove(ModifyItemConfigEnum.CONTACTLESS_TYPE.getCode());
                }
            }
        } else {
            if (snapShot == null || snapShot.getTransportType() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.CONTACTLESS_TYPE, String.valueOf(shipment.getContactlessType().getCode()), null));
            } else {
                OperateTypeEnum operateType = compareEnum(shipment.getContactlessType(), snapShot.getContactlessType());
                if (operateType != null) {
                    changedProperties.add(
                            toChangedProperty(operateType,
                                    ModifyItemConfigEnum.CONTACTLESS_TYPE,
                                    String.valueOf(shipment.getContactlessType().getCode()),
                                    null == snapShot.getContactlessType()
                                            ? null
                                            : String.valueOf(snapShot.getContactlessType().getCode())));
                }
            }
        }
        //指定地点
        if (shipment.getAssignedAddress() != null) {
            if (snapShot == null || snapShot.getAssignedAddress() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.ASSIGNED_ADDRESS, shipment.getAssignedAddress(), null));
            } else {
                OperateTypeEnum operateType = compareString(shipment.getAssignedAddress(), snapShot.getAssignedAddress());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.ASSIGNED_ADDRESS, shipment.getAssignedAddress(), snapShot.getAssignedAddress()));
                }
            }
        }
        //物流服务要求
        if (orderModel.getModifiedFields() != null
                && orderModel.getModifiedFields().get(ModifiedFieldEnum.SERVICE_REQUIREMENTS.getCode()) != null) {
            //比对serviceRequirements
            compareServiceRequirements(orderModel, changedProperties);
        }

        //收货偏好
        if (shipment.getReceivingPreference() != null) {
            if (snapShot == null || snapShot.getReceivingPreference() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.RECEIVING_PREFERENCE, shipment.getReceivingPreference(), null));
            } else {
                OperateTypeEnum operateType = compareString(shipment.getReceivingPreference(), snapShot.getReceivingPreference());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RECEIVING_PREFERENCE, shipment.getReceivingPreference(), snapShot.getReceivingPreference()));
                }
            }
        }

        //收货偏好
        String receivePreferenceCode = ModifyItemConfigEnum.RECEIVE_PREFERENCE.getCode();
        if (null != shipment.getExtendProps() && null != shipment.getExtendProps().get(receivePreferenceCode)) {
            if (null == snapShot || null == snapShot.getExtendProps() || null == snapShot.getExtendProps().get(receivePreferenceCode)) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.RECEIVE_PREFERENCE, shipment.getExtendProps().get(receivePreferenceCode), null));
            } else {
                String receivePreference = shipment.getExtendProps().get(receivePreferenceCode);
                String receivePreferenceSnapShot = snapShot.getExtendProps().get(receivePreferenceCode);
                OperateTypeEnum operateType = compareString(receivePreference, receivePreferenceSnapShot);
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RECEIVE_PREFERENCE, receivePreference, receivePreferenceSnapShot));
                }
            }
        }
        //发货偏好
        String sendPreferenceCode = ModifyItemConfigEnum.SEND_PREFERENCE.getCode();
        if (null != shipment.getExtendProps() && null != shipment.getExtendProps().get(sendPreferenceCode)) {
            if (null == snapShot || null == snapShot.getExtendProps() || null == snapShot.getExtendProps().get(sendPreferenceCode)) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.SEND_PREFERENCE, shipment.getExtendProps().get(sendPreferenceCode), null));
            } else {
                String sendPreference = shipment.getExtendProps().get(sendPreferenceCode);
                String sendPreferenceSnapShot = snapShot.getExtendProps().get(sendPreferenceCode);
                OperateTypeEnum operateType = compareString(sendPreference, sendPreferenceSnapShot);
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.SEND_PREFERENCE, sendPreference, sendPreferenceSnapShot));
                }
            }
        }
        //配送信息扩展字段,值不一样就认为修改了
        //TODO 后续考虑优化，是否细化到具体的扩展信息是否变更
        if (shipment.getExtendProps() != null) {
            if (snapShot == null || snapShot.getExtendProps() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.SHIPMENT_EXTENDPROPS, JSONUtils.beanToJSONDefault(shipment.getExtendProps()), null));
            } else {
                OperateTypeEnum operateType = compareString(JSONUtils.beanToJSONDefault(shipment.getExtendProps()), JSONUtils.beanToJSONDefault(snapShot.getExtendProps()));
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.SHIPMENT_EXTENDPROPS, JSONUtils.beanToJSONDefault(shipment.getExtendProps()), JSONUtils.beanToJSONDefault(snapShot.getExtendProps())));
                }
            }
        }
        //单独比对，扩展字段里的KEY
        compareShipmentExtendProps(orderModel, changedProperties);
    }

    /**
     * 单独比对，配送信息-扩展字段里的KEY
     *
     * @param orderModel
     * @param changedProperties
     */
    public void compareShipmentExtendProps(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties) {
        Shipment shipment = orderModel.getShipment();
        Shipment snapShot = orderModel.getOrderSnapshot().getShipment();
        //物资周转
        String materialTurnover = shipment.getExtendProps(AttachmentKeyEnum.MATERIAL_TURNOVER.getKey());
        if (null != materialTurnover) {
            // 修改物资周转
            String snapShotMaterialTurnover = snapShot.getExtendProps(AttachmentKeyEnum.MATERIAL_TURNOVER.getKey());
            if (!materialTurnover.equals(snapShotMaterialTurnover)) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.MATERIAL_TURNOVER,
                        materialTurnover, snapShotMaterialTurnover));
            }
        }
    }

    /**
     * 比对发货人信息
     *
     * @param consignor
     * @param snapShot
     * @param clearFields
     * @param changedProperties
     */
    private void compareConsignor(Consignor consignor, Consignor snapShot, List<String> clearFields, List<ChangedProperty> changedProperties) {
        //发货人姓名
        if (consignor.getConsignorName() != null) {
            OperateTypeEnum operateType = compareString(consignor.getConsignorName(), snapShot.getConsignorName());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_NAME, consignor.getConsignorName(), snapShot.getConsignorName()));
            }
        }
        //发货人手机
        if (consignor.getConsignorMobile() != null) {
            OperateTypeEnum operateType = compareString(consignor.getConsignorMobile(), snapShot.getConsignorMobile());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_MOBILE, consignor.getConsignorMobile(), snapShot.getConsignorMobile()));
            }
        }
        //发货人电话
        if (consignor.getConsignorPhone() != null) {
            OperateTypeEnum operateType = compareString(consignor.getConsignorPhone(), snapShot.getConsignorPhone());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_PHONE, consignor.getConsignorPhone(), snapShot.getConsignorPhone()));
            }
        }
        //发货人证件类型
        if (consignor.getConsignorIdType() == null) {
            if (snapShot != null && snapShot.getConsignorIdType() != null) {
                if (clearFields.contains(ModifyItemConfigEnum.CONSIGNOR_ID_TYPE.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.CONSIGNOR_ID_TYPE, null, String.valueOf(snapShot.getConsignorIdType().getCode())));
                } else {
                    if (CollectionUtils.isNotEmpty(clearFields)) {
                        clearFields.remove(ModifyItemConfigEnum.CONSIGNOR_ID_TYPE.getCode());
                    }
                }
            }
        } else {
            if (snapShot == null || snapShot.getConsignorIdType() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.CONSIGNOR_ID_TYPE, String.valueOf(consignor.getConsignorIdType().getCode()), null));
            } else {
                OperateTypeEnum operateType = compareEnum(consignor.getConsignorIdType(), snapShot.getConsignorIdType());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_ID_TYPE, String.valueOf(consignor.getConsignorIdType().getCode()), String.valueOf(snapShot.getConsignorIdType().getCode())));
                }
            }
        }
        //发货人证件号
        if (consignor.getConsignorIdNo() != null) {
            OperateTypeEnum operateType = compareString(consignor.getConsignorIdNo(), snapShot.getConsignorIdNo());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_ID_NO, consignor.getConsignorIdNo(), snapShot.getConsignorIdNo()));
            }
        }
        //发货人证件姓名
        if (consignor.getConsignorIdName() != null) {
            OperateTypeEnum operateType = compareString(consignor.getConsignorIdName(), snapShot.getConsignorIdName());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_ID_NAME, consignor.getConsignorIdName(), snapShot.getConsignorIdName()));
            }
        }
        //发货人公司
        if (consignor.getConsignorCompany() != null) {
            OperateTypeEnum operateType = compareString(consignor.getConsignorCompany(), snapShot.getConsignorCompany());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_COMPANY, consignor.getConsignorCompany(), snapShot.getConsignorCompany()));
            }
        }
        //发货人国家编码
        if (consignor.getConsignorNationNo() != null) {
            OperateTypeEnum operateType = compareString(consignor.getConsignorNationNo(), snapShot.getConsignorNationNo());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_NATION_NO, consignor.getConsignorNationNo(), snapShot.getConsignorNationNo()));
            }
        }
        //发货人国家名称
        if (consignor.getConsignorNation() != null) {
            OperateTypeEnum operateType = compareString(consignor.getConsignorNation(), snapShot.getConsignorNation());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_NATION, consignor.getConsignorNation(), snapShot.getConsignorNation()));
            }
        }
        //发货地邮编
        if (consignor.getConsignorZipCode() != null) {
            OperateTypeEnum operateType = compareString(consignor.getConsignorZipCode(), snapShot.getConsignorZipCode());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_ZIP_CODE, consignor.getConsignorZipCode(), snapShot.getConsignorZipCode()));
            }
        }

        //英文发件人姓名
        if (consignor.getConsignorEnName() != null) {
            OperateTypeEnum operateType = compareString(consignor.getConsignorEnName(), snapShot.getConsignorEnName());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_CONSIGNOR_EN_NAME, consignor.getConsignorEnName(), snapShot.getConsignorEnName()));
            }
        }

        //发货仓信息
        if (consignor.getCustomerWarehouse() != null && consignor.getCustomerWarehouse().getWarehouseNo() != null) {
            String snapShotWarehouseNo = snapShot.getCustomerWarehouse() != null ? snapShot.getCustomerWarehouse().getWarehouseNo() : null;
            OperateTypeEnum operateType = compareString(consignor.getCustomerWarehouse().getWarehouseNo(), snapShotWarehouseNo);
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CUSTOMER_WAREHOUSE_NO, consignor.getCustomerWarehouse().getWarehouseNo(), snapShotWarehouseNo));
            }
        }

        //发货人地址信息--只比对gis解析后的地址
        if (consignor.getAddress() != null) {
            if (consignor.getAddress().getProvinceNoGis() != null) {
                OperateTypeEnum operateType = compareString(consignor.getAddress().getProvinceNoGis(), snapShot.getAddress().getProvinceNoGis());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_PROVINCE_NO_GIS, consignor.getAddress().getProvinceNoGis(), snapShot.getAddress().getProvinceNoGis()));
                }
            }
            if (consignor.getAddress().getCityNoGis() != null) {
                OperateTypeEnum operateType = compareString(consignor.getAddress().getCityNoGis(), snapShot.getAddress().getCityNoGis());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_CITY_NO_GIS, consignor.getAddress().getCityNoGis(), snapShot.getAddress().getCityNoGis()));
                }
            }
            if (consignor.getAddress().getCountyNoGis() != null) {
                OperateTypeEnum operateType = compareString(consignor.getAddress().getCountyNoGis(), snapShot.getAddress().getCountyNoGis());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_COUNTY_NO_GIS, consignor.getAddress().getCountyNoGis(), snapShot.getAddress().getCountyNoGis()));
                }
            }
            if (consignor.getAddress().getTownNoGis() != null) {
                OperateTypeEnum operateType = compareString(consignor.getAddress().getTownNoGis(), snapShot.getAddress().getTownNoGis());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_TOWN_NO_GIS, consignor.getAddress().getTownNoGis(), snapShot.getAddress().getTownNoGis()));
                }
            }
            if (consignor.getAddress().getProvinceNameGis() != null) {
                OperateTypeEnum operateType = compareString(consignor.getAddress().getProvinceNameGis(), snapShot.getAddress().getProvinceNameGis());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_PROVINCE_NAME_GIS, consignor.getAddress().getProvinceNameGis(), snapShot.getAddress().getProvinceNameGis()));
                }
            }
            if (consignor.getAddress().getCityNameGis() != null) {
                OperateTypeEnum operateType = compareString(consignor.getAddress().getCityNameGis(), snapShot.getAddress().getCityNameGis());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_CITY_NAME_GIS, consignor.getAddress().getCityNameGis(), snapShot.getAddress().getCityNameGis()));
                }
            }
            if (consignor.getAddress().getCountyNameGis() != null) {
                OperateTypeEnum operateType = compareString(consignor.getAddress().getCountyNameGis(), snapShot.getAddress().getCountyNameGis());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_COUNTY_NAME_GIS, consignor.getAddress().getCountyNameGis(), snapShot.getAddress().getCountyNameGis()));
                }
            }
            if (consignor.getAddress().getTownNameGis() != null) {
                OperateTypeEnum operateType = compareString(consignor.getAddress().getTownNameGis(), snapShot.getAddress().getTownNameGis());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_TOWN_NAME_GIS, consignor.getAddress().getTownNameGis(), snapShot.getAddress().getTownNameGis()));
                }
            }
            if (consignor.getAddress().getAddress() != null) {
                OperateTypeEnum operateType = compareString(consignor.getAddress().getAddress(), snapShot.getAddress().getAddress());
                if (operateType != null) {
                    // TODO 对比信息里 新老数据取值位置不对
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_ADDRESS, consignor.getAddress().getAddressGis(), snapShot.getAddress().getAddressGis()));
                }
            }
            if (consignor.getAddress().getChinaPostAddressCode() != null) {
                OperateTypeEnum operateType = compareString(consignor.getAddress().getChinaPostAddressCode(), snapShot.getAddress().getChinaPostAddressCode());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_CHINA_POST_ADDRESS_CODE, consignor.getAddress().getChinaPostAddressCode(), snapShot.getAddress().getChinaPostAddressCode()));
                }
            }

            //行政区编码
            if (consignor.getAddress().getRegionNo() != null) {
                OperateTypeEnum operateType = compareString(consignor.getAddress().getRegionNo(), snapShot.getAddress().getRegionNo());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_ADDRESS_REGION_NO, consignor.getAddress().getRegionNo(), snapShot.getAddress().getRegionNo()));
                }
            }

            //行政区名称
            if (consignor.getAddress().getRegionName() != null) {
                OperateTypeEnum operateType = compareString(consignor.getAddress().getRegionName(), snapShot.getAddress().getRegionName());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_ADDRESS_REGION_NAME, consignor.getAddress().getRegionName(), snapShot.getAddress().getRegionName()));
                }
            }

            //英文发件人城市
            if (consignor.getAddress().getEnCityName() != null) {
                OperateTypeEnum operateType = compareString(consignor.getAddress().getEnCityName(), snapShot.getAddress().getEnCityName());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_ADDRESS_EN_CITY_NAME, consignor.getAddress().getEnCityName(), snapShot.getAddress().getEnCityName()));
                }
            }

            //英文发件人地址
            if (consignor.getAddress().getEnAddress() != null) {
                OperateTypeEnum operateType = compareString(consignor.getAddress().getEnAddress(), snapShot.getAddress().getEnAddress());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_ADDRESS_EN_ADDRESS, consignor.getAddress().getEnAddress(), snapShot.getAddress().getEnAddress()));
                }
            }
            //poiCode
            if (consignor.getAddress().getPoiCode() != null) {
                OperateTypeEnum operateType = compareString(consignor.getAddress().getPoiCode(), snapShot.getAddress().getPoiCode());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_ADDRESS_POI_CODE, consignor.getAddress().getPoiCode(), snapShot.getAddress().getPoiCode()));
                }
            }
            //poiName
            if (consignor.getAddress().getPoiName() != null) {
                OperateTypeEnum operateType = compareString(consignor.getAddress().getPoiName(), snapShot.getAddress().getPoiName());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_ADDRESS_POI_NAME, consignor.getAddress().getPoiName(), snapShot.getAddress().getPoiName()));
                }
            }
            //houseNumber
            if (consignor.getAddress().getHouseNumber() != null) {
                OperateTypeEnum operateType = compareString(consignor.getAddress().getHouseNumber(), snapShot.getAddress().getHouseNumber());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNOR_ADDRESS_HOUSE_NUMBER, consignor.getAddress().getHouseNumber(), snapShot.getAddress().getHouseNumber()));
                }
            }
        }
    }

    /**
     * 比对收货人信息
     *
     * @param orderModel
     * @param clearFields
     * @param changedProperties
     */
    private void compareConsignee(ExpressOrderModel orderModel, List<String> clearFields, List<ChangedProperty> changedProperties) {
        Consignee consignee = orderModel.getConsignee();
        Consignee snapShot = orderModel.getOrderSnapshot().getConsignee();
        String addressModifyFlag = UN_MODIFY;//收货人地址修改标识，0标识未修改
        //收货人姓名
        if (consignee.getConsigneeName() != null) {
            OperateTypeEnum operateType = compareString(consignee.getConsigneeName(), snapShot.getConsigneeName());
            if (operateType != null) {
                addressModifyFlag = MODIFY;
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_NAME, consignee.getConsigneeName(), snapShot.getConsigneeName()));
            }
        }
        //收货人手机
        if (consignee.getConsigneeMobile() != null) {
            OperateTypeEnum operateType = compareString(consignee.getConsigneeMobile(), snapShot.getConsigneeMobile());
            if (operateType != null) {
                addressModifyFlag = MODIFY;
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_MOBILE, consignee.getConsigneeMobile(), snapShot.getConsigneeMobile()));
            }
        }
        //收货人电话
        if (consignee.getConsigneePhone() != null) {
            OperateTypeEnum operateType = compareString(consignee.getConsigneePhone(), snapShot.getConsigneePhone());
            if (operateType != null) {
                addressModifyFlag = MODIFY;
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_PHONE, consignee.getConsigneePhone(), snapShot.getConsigneePhone()));
            }
        }
        //收货人证件类型
        if (consignee.getConsigneeIdType() == null) {
            if (snapShot != null && snapShot.getConsigneeIdType() != null) {
                if (clearFields.contains(ModifyItemConfigEnum.CONSIGNEE_ID_TYPE.getCode())) {
                    addressModifyFlag = MODIFY;
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.CONSIGNEE_ID_TYPE, null, String.valueOf(snapShot.getConsigneeIdType().getCode())));
                } else {
                    if (CollectionUtils.isNotEmpty(clearFields)) {
                        addressModifyFlag = MODIFY;
                        clearFields.remove(ModifyItemConfigEnum.CONSIGNEE_ID_TYPE.getCode());
                    }
                }
            }
        } else {
            if (snapShot == null || snapShot.getConsigneeIdType() == null) {
                addressModifyFlag = MODIFY;
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.CONSIGNEE_ID_TYPE, String.valueOf(consignee.getConsigneeIdType().getCode()), null));
            } else {
                OperateTypeEnum operateType = compareEnum(consignee.getConsigneeIdType(), snapShot.getConsigneeIdType());
                if (operateType != null) {
                    addressModifyFlag = MODIFY;
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_ID_TYPE, String.valueOf(consignee.getConsigneeIdType().getCode()), String.valueOf(snapShot.getConsigneeIdType().getCode())));
                }
            }
        }
        //收货人证件号
        if (consignee.getConsigneeIdNo() != null) {
            OperateTypeEnum operateType = compareString(consignee.getConsigneeIdNo(), snapShot.getConsigneeIdNo());
            if (operateType != null) {
                addressModifyFlag = MODIFY;
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_ID_NO, consignee.getConsigneeIdNo(), snapShot.getConsigneeIdNo()));
            }
        }

        //收货人证件姓名
        if (consignee.getConsigneeIdName() != null) {
            OperateTypeEnum operateType = compareString(consignee.getConsigneeIdName(), snapShot.getConsigneeIdName());
            if (operateType != null) {
                addressModifyFlag = MODIFY;
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_ID_NAME, consignee.getConsigneeIdName(), snapShot.getConsigneeIdName()));
            }
        }

        //收货人公司
        if (consignee.getConsigneeCompany() != null) {
            OperateTypeEnum operateType = compareString(consignee.getConsigneeCompany(), snapShot.getConsigneeCompany());
            if (operateType != null) {
                addressModifyFlag = MODIFY;
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_COMPANY, consignee.getConsigneeCompany(), snapShot.getConsigneeCompany()));
            }
        }
        //收货人国家编码
        if (consignee.getConsigneeNationNo() != null) {
            OperateTypeEnum operateType = compareString(consignee.getConsigneeNationNo(), snapShot.getConsigneeNationNo());
            if (operateType != null) {
                addressModifyFlag = MODIFY;
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_NATION_NO, consignee.getConsigneeNationNo(), snapShot.getConsigneeNationNo()));
            }
        }
        //收货人国家名称
        if (consignee.getConsigneeNation() != null) {
            OperateTypeEnum operateType = compareString(consignee.getConsigneeNation(), snapShot.getConsigneeNation());
            if (operateType != null) {
                addressModifyFlag = MODIFY;
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_NATION, consignee.getConsigneeNation(), snapShot.getConsigneeNation()));
            }
        }
        //收货地邮编
        if (consignee.getConsigneeZipCode() != null) {
            OperateTypeEnum operateType = compareString(consignee.getConsigneeZipCode(), snapShot.getConsigneeZipCode());
            if (operateType != null) {
                addressModifyFlag = MODIFY;
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_ZIP_CODE, consignee.getConsigneeZipCode(), snapShot.getConsigneeZipCode()));
            }
        }
        //收货仓编码
        if (consignee.getDeliveryPlaceCode() != null) {
            OperateTypeEnum operateType = compareString(consignee.getDeliveryPlaceCode(), snapShot.getDeliveryPlaceCode());
            if (operateType != null) {
                addressModifyFlag = MODIFY;
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.DELIVERY_PLACE_CODE, consignee.getDeliveryPlaceCode(), snapShot.getDeliveryPlaceCode()));
            }
        }


        //收货仓信息
        if(consignee.getReceiveWarehouse() != null){
             if(snapShot == null || snapShot.getReceiveWarehouse() == null){
                 if(consignee.getReceiveWarehouse().getWarehouseNo() != null){
                     changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.RECEIVE_WAREHOUSE_NO, consignee.getReceiveWarehouse().getWarehouseNo(),null));
                 }
                 if(consignee.getReceiveWarehouse().getWarehouseName() != null){
                     changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.RECEIVE_WAREHOUSE_NAME, consignee.getReceiveWarehouse().getWarehouseName(), null));
                 }
             }else{
                 if(consignee.getReceiveWarehouse().getWarehouseNo() != null){
                     OperateTypeEnum operateType = compareString(consignee.getReceiveWarehouse().getWarehouseNo(), snapShot.getReceiveWarehouse().getWarehouseNo());
                     if (operateType != null) {
                         changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RECEIVE_WAREHOUSE_NO, consignee.getReceiveWarehouse().getWarehouseNo(), snapShot.getReceiveWarehouse().getWarehouseNo()));
                     }
                 }
                 if(consignee.getReceiveWarehouse().getWarehouseName() != null){
                     OperateTypeEnum operateType = compareString(consignee.getReceiveWarehouse().getWarehouseName(), snapShot.getReceiveWarehouse().getWarehouseName());
                     if (operateType != null) {
                         changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.RECEIVE_WAREHOUSE_NAME, consignee.getReceiveWarehouse().getWarehouseName(), snapShot.getReceiveWarehouse().getWarehouseName()));
                     }
                 }
             }

        }

        //收货人地址信息--只比对gis解析后的地址
        if (consignee.getAddress() != null) {
            if (consignee.getAddress().getProvinceNoGis() != null) {
                OperateTypeEnum operateType = compareString(consignee.getAddress().getProvinceNoGis(), snapShot.getAddress().getProvinceNoGis());
                if (operateType != null) {
                    addressModifyFlag = MODIFY;
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_PROVINCE_NO_GIS, consignee.getAddress().getProvinceNoGis(), snapShot.getAddress().getProvinceNoGis()));
                }
            }
            if (consignee.getAddress().getCityNoGis() != null) {
                OperateTypeEnum operateType = compareString(consignee.getAddress().getCityNoGis(), snapShot.getAddress().getCityNoGis());
                if (operateType != null) {
                    addressModifyFlag = MODIFY;
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_CITY_NO_GIS, consignee.getAddress().getCityNoGis(), snapShot.getAddress().getCityNoGis()));
                }
            }
            if (consignee.getAddress().getCountyNoGis() != null) {
                OperateTypeEnum operateType = compareString(consignee.getAddress().getCountyNoGis(), snapShot.getAddress().getCountyNoGis());
                if (operateType != null) {
                    addressModifyFlag = MODIFY;
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_COUNTY_NO_GIS, consignee.getAddress().getCountyNoGis(), snapShot.getAddress().getCountyNoGis()));
                }
            }
            if (consignee.getAddress().getTownNoGis() != null) {
                OperateTypeEnum operateType = compareString(consignee.getAddress().getTownNoGis(), snapShot.getAddress().getTownNoGis());
                if (operateType != null) {
                    addressModifyFlag = MODIFY;
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_TOWN_NO_GIS, consignee.getAddress().getTownNoGis(), snapShot.getAddress().getTownNoGis()));
                }
            }

            //修改记录添加--用于发送修改数据流水
            if (consignee.getAddress().getProvinceNameGis() != null) {
                OperateTypeEnum operateType = compareString(consignee.getAddress().getProvinceNameGis(), snapShot.getAddress().getProvinceNameGis());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_PROVINCE_NAME_GIS, consignee.getAddress().getProvinceNameGis(), snapShot.getAddress().getProvinceNameGis()));
                }
            }
            if (consignee.getAddress().getCityNameGis() != null) {
                OperateTypeEnum operateType = compareString(consignee.getAddress().getCityNameGis(), snapShot.getAddress().getCityNameGis());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_CITY_NAME_GIS, consignee.getAddress().getCityNameGis(), snapShot.getAddress().getCityNameGis()));
                }
            }
            if (consignee.getAddress().getCountyNameGis() != null) {
                OperateTypeEnum operateType = compareString(consignee.getAddress().getCountyNameGis(), snapShot.getAddress().getCountyNameGis());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_COUNTY_NAME_GIS, consignee.getAddress().getCountyNameGis(), snapShot.getAddress().getCountyNameGis()));
                }
            }
            if (consignee.getAddress().getTownNameGis() != null) {
                OperateTypeEnum operateType = compareString(consignee.getAddress().getTownNameGis(), snapShot.getAddress().getTownNameGis());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_TOWN_NAME_GIS, consignee.getAddress().getTownNameGis(), snapShot.getAddress().getTownNameGis()));
                }
            }
            if (consignee.getAddress().getAddress() != null) {
                OperateTypeEnum operateType = compareString(consignee.getAddress().getAddress(), snapShot.getAddress().getAddress());
                if (operateType != null) {
                    addressModifyFlag = MODIFY;
                    // TODO 对比信息里 新老数据取值位置不对
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_ADDRESS, consignee.getAddress().getAddressGis(), snapShot.getAddress().getAddressGis()));
                }
            }
            if (consignee.getAddress().getChinaPostAddressCode() != null) {
                OperateTypeEnum operateType = compareString(consignee.getAddress().getChinaPostAddressCode(), snapShot.getAddress().getChinaPostAddressCode());
                if (operateType != null) {
                    addressModifyFlag = MODIFY;
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_CHINA_POST_ADDRESS_CODE, consignee.getAddress().getChinaPostAddressCode(), snapShot.getAddress().getChinaPostAddressCode()));
                }
            }

            //行政区编码
            if (consignee.getAddress().getRegionNo() != null) {
                OperateTypeEnum operateType = compareString(consignee.getAddress().getRegionNo(), snapShot.getAddress().getRegionNo());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_ADDRESS_REGION_NO, consignee.getAddress().getRegionNo(), snapShot.getAddress().getRegionNo()));
                }
            }

            //行政区名称
            if (consignee.getAddress().getRegionName() != null) {
                OperateTypeEnum operateType = compareString(consignee.getAddress().getRegionName(), snapShot.getAddress().getRegionName());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_ADDRESS_REGION_NAME, consignee.getAddress().getRegionName(), snapShot.getAddress().getRegionName()));
                }
            }

            //poiCode
            if (consignee.getAddress().getPoiCode() != null) {
                OperateTypeEnum operateType = compareString(consignee.getAddress().getPoiCode(), snapShot.getAddress().getPoiCode());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_ADDRESS_POI_CODE, consignee.getAddress().getPoiCode(), snapShot.getAddress().getPoiCode()));
                }
            }
            //poiName
            if (consignee.getAddress().getPoiName() != null) {
                OperateTypeEnum operateType = compareString(consignee.getAddress().getPoiName(), snapShot.getAddress().getPoiName());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_ADDRESS_POI_NAME, consignee.getAddress().getPoiName(), snapShot.getAddress().getPoiName()));
                }
            }
            //houseNumber
            if (consignee.getAddress().getHouseNumber() != null) {
                OperateTypeEnum operateType = compareString(consignee.getAddress().getHouseNumber(), snapShot.getAddress().getHouseNumber());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_ADDRESS_HOUSE_NUMBER, consignee.getAddress().getHouseNumber(), snapShot.getAddress().getHouseNumber()));
                }
            }
        }

        //揽收后收件人信息只允许修改一次，B2C记录收件人信息修改标识，
        if (orderModel.isB2C() || orderModel.isC2B() || orderModel.isB_O2O() || orderModel.isCCB2C()) {
            ExpressOrderStatusCustomEnum b2COrderStatusCustomEnum = ExpressOrderStatusCustomEnum.ofCustomOrderStatus(orderModel.getOrderSnapshot().getCustomStatus());
            if (MODIFY.equals(addressModifyFlag)
                && b2COrderStatusCustomEnum.index() >= ExpressOrderStatusCustomEnum.PICKED_UP.index()
                && SystemCallerEnum.EXPRESS_OMS != orderModel.getChannel().getSystemCaller()) {
                // 修改策略为仅修改收货人联系方式或修改联系方式时，不设置标识addressModifyFlag
                if (ModifySceneRuleUtil.isOnlyModifyConsigneeContactInformation(orderModel)
                        || ModifySceneRuleUtil.isModifyContactInformation(orderModel)) {
                    LOGGER.info("修改策略为仅修改收件人联系方式或修改联系方式，不设置标识，揽收后改收件人信息，addressModifyFlag:{},b2COrderStatusCustomEnum:{},SystemCallerEnum:{}",
                            addressModifyFlag, b2COrderStatusCustomEnum.customOrderStatus(),
                            orderModel.getChannel().getSystemCaller());
                } else {
                    LOGGER.info("揽收后改收件人信息，addressModifyFlag:{},b2COrderStatusCustomEnum:{},SystemCallerEnum:{}",
                            addressModifyFlag, b2COrderStatusCustomEnum.customOrderStatus(),
                            orderModel.getChannel().getSystemCaller());
                    setModifyFlag(orderModel, ADDRESS_MODIFY_FLAG);
                }
            }
        }

        //收件人邮箱
        if (consignee.getConsigneeEmail() != null) {
            OperateTypeEnum operateType = compareString(consignee.getConsigneeEmail(), snapShot.getConsigneeEmail());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_EMAIL, consignee.getConsigneeEmail(), snapShot.getConsigneeEmail()));
            }
        }

        //收件人身份ID
        String consigneeContactInfoIDCode = ModifyItemConfigEnum.CONSIGNEE_EXTENDPROPS_CONSIGNEE_CONTACTINFO_ID.getCode();
        if (null != consignee.getExtendProps() && null != consignee.getExtendProps().get(consigneeContactInfoIDCode)) {
            if (null == snapShot || null == snapShot.getExtendProps() || null == snapShot.getExtendProps().get(consigneeContactInfoIDCode)) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.CONSIGNEE_EXTENDPROPS_CONSIGNEE_CONTACTINFO_ID, consignee.getExtendProps().get(consigneeContactInfoIDCode), null));
            } else {
                String consigneeContactInfo = consignee.getExtendProps().get(consigneeContactInfoIDCode);
                String consigneeContactInfoShot = snapShot.getExtendProps().get(consigneeContactInfoIDCode);
                OperateTypeEnum operateType = compareString(consigneeContactInfo, consigneeContactInfoShot);
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_EXTENDPROPS_CONSIGNEE_CONTACTINFO_ID, consigneeContactInfo, consigneeContactInfoShot));
                }
            }
        }
        //收件人身份ID备注
        String consigneeContactInfoIDRemark = ModifyItemConfigEnum.CONSIGNEE_EXTENDPROPS_CONSIGNEE_CONTACTINFO_ID_REMARK.getCode();
        if (null != consignee.getExtendProps() && null != consignee.getExtendProps().get(consigneeContactInfoIDRemark)) {
            if (null == snapShot || null == snapShot.getExtendProps() || null == snapShot.getExtendProps().get(consigneeContactInfoIDRemark)) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.CONSIGNEE_EXTENDPROPS_CONSIGNEE_CONTACTINFO_ID_REMARK, consignee.getExtendProps().get(consigneeContactInfoIDRemark), null));
            } else {
                String consigneeContactInfo = consignee.getExtendProps().get(consigneeContactInfoIDRemark);
                String consigneeContactInfoShot = snapShot.getExtendProps().get(consigneeContactInfoIDRemark);
                OperateTypeEnum operateType = compareString(consigneeContactInfo, consigneeContactInfoShot);
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CONSIGNEE_EXTENDPROPS_CONSIGNEE_CONTACTINFO_ID_REMARK, consigneeContactInfo, consigneeContactInfoShot));
                }
            }
        }
    }

    /**
     * 记录收件人地址修改标识和COD修改标识
     *
     * @param model
     * @param modifyField
     */
    private void setModifyFlag(ExpressOrderModel model, String modifyField) {
        //b2c记录收件人地址修改标识和COD修改标识，和品训确认，c2c和C2C的O2O不需要
        if (BusinessUnitEnum.CN_JDL_B2C.getCode().equals(model.getOrderBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_O2O_B.getCode().equals(model.getOrderBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_C2B.getCode().equals(model.getOrderBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_CC_B2C.getCode().equals(model.getOrderBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_CC_B2B.getCode().equals(model.getOrderBusinessIdentity().getBusinessUnit())) {
            Map<String, String> extendProps = model.getExtendProps();
            Map<String, String> customerInfoExtendProps = new HashMap<>();
            if (StringUtils.isNotBlank(extendProps.get(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS))) {
                //取出主档map转json扩展字段，反转成map
                customerInfoExtendProps = JSONUtils.jsonToMap(extendProps.get(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS));
                if (customerInfoExtendProps == null) {
                    // JSONUtils.jsonToMap("null")=null，重新判空赋值避免NPE
                    customerInfoExtendProps = new HashMap<>();
                }
            }
            if (StringUtils.isNotBlank(extendProps.get(OrderConstants.IGNORE_MODIFY_FLAG))){
                //由于终端不切订单中心，终端揽收完成时称重量方通过外单修改（揽收完成是揽收前的动作--揽收前修改），不经过订单中心；
                //外单修改调运单修改，此时修改请求经过运单处理后会发出运单变化消息，订单中心监听运单变更消息调外单信息更新到订单中心；
                //在处理运单消息变更的过程中，订单的状态可能已经回传成揽收后，
                //因此如果只判断订单状态是揽收后，会导致揽收后修改的标识记录有误（因为终端的修改是揽收前，订单中心的状态被回传更新为揽收后）
                //与外单及订单中心产品（严飞、王维金确认后），同步外单数据更新时，
                // 判断运单标位waybillSign（第8位和第103位为0表示揽收前修改）的第8位和第103位是否为0，
                //如果为0，则为揽收前修改，如果不是，则为揽收后修改
                String ignoreModifyFlag = extendProps.get(OrderConstants.IGNORE_MODIFY_FLAG);
                LOGGER.info("运单数据变化发起修改，ignoreModifyFlag:{}",ignoreModifyFlag);
                if (OrderConstants.NOT_IGNORE_MODIFY_FLAG_VALUE.equals(ignoreModifyFlag)){
                    if (ADDRESS_MODIFY_FLAG.equals(modifyField)) {
                        //记录收件人地址修改标识
                        customerInfoExtendProps.put(ADDRESS_MODIFY_FLAG, MODIFY);
                    }
                    if (COD_MODIFY_FLAG.equals(modifyField)) {
                        //记录COD修改标识
                        customerInfoExtendProps.put(COD_MODIFY_FLAG, MODIFY);
                    }
                }
            }else {
                if (ADDRESS_MODIFY_FLAG.equals(modifyField)) {
                    //记录收件人地址修改标识
                    customerInfoExtendProps.put(ADDRESS_MODIFY_FLAG, MODIFY);
                }
                if (COD_MODIFY_FLAG.equals(modifyField)) {
                    //记录COD修改标识
                    customerInfoExtendProps.put(COD_MODIFY_FLAG, MODIFY);
                }
            }
            extendProps.put(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS, JSONUtils.mapToJson(customerInfoExtendProps));
            LOGGER.info("揽收后修改代收货款、地址,modifyField:{}", modifyField);
        }
    }

    /**
     * 比对财务信息
     *
     * @param finance
     * @param snapShot
     * @param clearFields
     * @param changedProperties
     */
    private void compareFinance(ExpressOrderModel orderModel, Finance finance, Finance snapShot, List<String> clearFields, List<ChangedProperty> changedProperties) {
        SettlementTypeEnum settlementType = null;
        PaymentStageEnum paymentStage = null;
        if (orderModel.isReaddress1Order2End() || orderModel.isKKInterceptionThroughOrderRecord()) {
            Map<String, String> financeExt = orderModel.getFinance().getExtendProps();//新单财务域扩展信息
            if (MapUtils.isNotEmpty(financeExt)) {
                // 原单的结算方式
                String orderSettlementType = financeExt.get(AttachmentKeyEnum.ORIGIN_SETTLEMENT_TYPE.getKey());
                if (StringUtils.isNotBlank(orderSettlementType)) {
                    settlementType = SettlementTypeEnum.of(Integer.valueOf(orderSettlementType));
                }
                // 原单的支付环节
                String orderPaymentStage = financeExt.get(AttachmentKeyEnum.ORIGIN_PAYMENT_STAGE.getKey());
                if (StringUtils.isNotBlank(orderPaymentStage)) {
                    paymentStage = PaymentStageEnum.of(Integer.valueOf(orderPaymentStage));
                }
            }

        } else {
            settlementType = finance.getSettlementType();
            paymentStage = finance.getPaymentStage();
        }

        //结算方式
        if (settlementType == null) {
            if (snapShot != null && snapShot.getSettlementType() != null) {
                if (clearFields.contains(ModifyItemConfigEnum.SETTLEMENT_TYPE.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.SETTLEMENT_TYPE, null, String.valueOf(snapShot.getSettlementType().getCode())));
                } else {
                    if (CollectionUtils.isNotEmpty(clearFields)) {
                        clearFields.remove(ModifyItemConfigEnum.SETTLEMENT_TYPE.getCode());
                    }
                }
            }
        } else {
            if (snapShot == null || snapShot.getSettlementType() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.SETTLEMENT_TYPE, String.valueOf(settlementType.getCode()), null));
            } else {
                OperateTypeEnum operateType = compareEnum(settlementType, snapShot.getSettlementType());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.SETTLEMENT_TYPE, String.valueOf(settlementType.getCode()), String.valueOf(snapShot.getSettlementType().getCode())));
                }
            }
        }

        //TODO 询价

        //预估费用
        if (finance.getEstimateAmount() != null) {
            if (snapShot == null || snapShot.getEstimateAmount() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.ESTIMATE_AMOUNT, String.valueOf(finance.getEstimateAmount().getAmount()), null));
            } else {
                OperateTypeEnum operateType = compareBigDecimal(finance.getEstimateAmount().getAmount(), snapShot.getEstimateAmount().getAmount());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.ESTIMATE_AMOUNT, String.valueOf(finance.getEstimateAmount().getAmount()), String.valueOf(snapShot.getEstimateAmount().getAmount())));
                }
            }
        }
        //折后金额
        if (finance.getDiscountAmount() != null) {
            if (snapShot == null || snapShot.getDiscountAmount() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.DISCOUNT_AMOUNT, String.valueOf(finance.getDiscountAmount().getAmount()), null));
            } else {
                OperateTypeEnum operateType = compareBigDecimal(finance.getDiscountAmount().getAmount(), snapShot.getDiscountAmount().getAmount());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.DISCOUNT_AMOUNT, String.valueOf(finance.getDiscountAmount().getAmount()), String.valueOf(snapShot.getDiscountAmount().getAmount())));
                }
            }
        }
        //预占标示
        if (finance.getPreemptType() == null) {
            if (snapShot != null && snapShot.getPreemptType() != null) {
                if (clearFields.contains(ModifyItemConfigEnum.PREEMPT_TYPE.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.PREEMPT_TYPE, null, String.valueOf(snapShot.getPreemptType())));
                } else {
                    if (CollectionUtils.isNotEmpty(clearFields)) {
                        clearFields.remove(ModifyItemConfigEnum.PREEMPT_TYPE.getCode());
                    }
                }
            }
        } else {
            if (snapShot == null || snapShot.getPreemptType() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.PREEMPT_TYPE, String.valueOf(finance.getPreemptType()), null));
            } else {
                OperateTypeEnum operateType = compareInteger(finance.getPreemptType(), snapShot.getPreemptType());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.PREEMPT_TYPE, String.valueOf(finance.getPreemptType()), String.valueOf(snapShot.getPreemptType())));
                }
            }
        }
        //支付账号
        if (finance.getPaymentAccountNo() != null) {
            OperateTypeEnum operateType = compareString(finance.getPaymentAccountNo(), snapShot.getPaymentAccountNo());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.PAYMENT_ACCOUNT_NO, finance.getPaymentAccountNo(), snapShot.getPaymentAccountNo()));
            }
        }

        //支付方式
        if (finance.getPayment() == null) {
            if (snapShot != null && snapShot.getPayment() != null) {
                if (clearFields.contains(ModifyItemConfigEnum.PAYMENT.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.PAYMENT, null, String.valueOf(snapShot.getPayment().getCode())));
                } else {
                    if (CollectionUtils.isNotEmpty(clearFields)) {
                        clearFields.remove(ModifyItemConfigEnum.PAYMENT.getCode());
                    }
                }
            }
        } else {
            if (snapShot == null || snapShot.getPayment() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.PAYMENT, String.valueOf(finance.getPayment().getCode()), null));
            } else {
                OperateTypeEnum operateType = compareEnum(finance.getPayment(), snapShot.getPayment());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.PAYMENT, String.valueOf(finance.getPayment().getCode()), String.valueOf(snapShot.getPayment().getCode())));
                }
            }
        }

        //支付环节
        if (paymentStage == null) {
            if (snapShot != null && snapShot.getPaymentStage() != null) {
                if (clearFields.contains(ModifyItemConfigEnum.PAYMENT_STAGE.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.PAYMENT_STAGE, null, String.valueOf(snapShot.getPaymentStage().getCode())));
                } else {
                    if (CollectionUtils.isNotEmpty(clearFields)) {
                        clearFields.remove(ModifyItemConfigEnum.PAYMENT_STAGE.getCode());
                    }
                }
            }
        } else {
            if (snapShot == null || snapShot.getPaymentStage() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.PAYMENT_STAGE, String.valueOf(paymentStage.getCode()), null));
            } else {
                OperateTypeEnum operateType = compareEnum(paymentStage, snapShot.getPaymentStage());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.PAYMENT_STAGE, String.valueOf(paymentStage.getCode()), String.valueOf(snapShot.getPaymentStage().getCode())));
                }
            }
        }
        //结算账号
        if (finance.getSettlementAccountNo() != null) {
            OperateTypeEnum operateType = compareString(finance.getSettlementAccountNo(), snapShot.getSettlementAccountNo());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.SETTLEMENT_ACCOUNT_NO, finance.getSettlementAccountNo(), snapShot.getSettlementAccountNo()));
            }
        }
        //积分信息
        if (finance.getPoints() != null) {
            //积分数量
            if (finance.getPoints().getRedeemPointsQuantity() != null && finance.getPoints().getRedeemPointsQuantity().getValue() != null) {
                if (snapShot.getPoints() == null || snapShot.getPoints().getRedeemPointsQuantity() == null || snapShot.getPoints().getRedeemPointsQuantity().getValue() == null) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.REDEEM_POINTS_QUANTITY, String.valueOf(finance.getPoints().getRedeemPointsQuantity().getValue()), null));
                } else {
                    OperateTypeEnum operateType = compareBigDecimal(finance.getPoints().getRedeemPointsQuantity().getValue(), snapShot.getPoints().getRedeemPointsQuantity().getValue());
                    if (operateType != null) {
                        changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.REDEEM_POINTS_QUANTITY, String.valueOf(finance.getPoints().getRedeemPointsQuantity().getValue()), String.valueOf(snapShot.getPoints().getRedeemPointsQuantity().getValue())));
                    }
                }
            }
            //积分使用金额
            if (finance.getPoints().getRedeemPointsAmount() != null && finance.getPoints().getRedeemPointsAmount().getAmount() != null) {
                if (snapShot.getPoints() == null || snapShot.getPoints().getRedeemPointsAmount() == null || snapShot.getPoints().getRedeemPointsAmount().getAmount() == null) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.REDEEM_POINTS_AMOUNT, String.valueOf(finance.getPoints().getRedeemPointsAmount().getAmount()), null));
                } else {
                    OperateTypeEnum operateType = compareBigDecimal(finance.getPoints().getRedeemPointsAmount().getAmount(), snapShot.getPoints().getRedeemPointsAmount().getAmount());
                    if (operateType != null) {
                        changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.REDEEM_POINTS_AMOUNT, String.valueOf(finance.getPoints().getRedeemPointsAmount().getAmount()), String.valueOf(snapShot.getPoints().getRedeemPointsAmount().getAmount())));
                    }
                }
            }
        }

        //积分支付信息 TODO 包含积分支付数量和积分支付费用，分开判断还是捆绑判断
        if (finance.getPoints() != null) {
            if (snapShot == null || snapShot.getPoints() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.POINTS_AMOUNT, String.valueOf(finance.getPoints().getRedeemPointsAmount().getAmount()), null));
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.POINTS_QUANTITY, String.valueOf(finance.getPoints().getRedeemPointsQuantity().getValue()), null));
            } else {
                OperateTypeEnum operateType = compareBigDecimal(finance.getPoints().getRedeemPointsAmount().getAmount(), snapShot.getPoints().getRedeemPointsAmount().getAmount());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.POINTS_AMOUNT, String.valueOf(finance.getPoints().getRedeemPointsAmount().getAmount()), String.valueOf(snapShot.getPoints().getRedeemPointsAmount().getAmount())));
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.POINTS_QUANTITY, String.valueOf(finance.getPoints().getRedeemPointsQuantity().getValue()), String.valueOf(snapShot.getPoints().getRedeemPointsQuantity().getValue())));
                }
            }
        }

        // todo 抵扣信息
        if (finance.getDeductionDelegate() != null && !finance.getDeductionDelegate().isEmpty()) {
            if (snapShot == null || snapShot.getDeductionDelegate() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.DEDUCTION_INFOS, JSONUtils.beanToJSONDefault(finance.getDeductionDelegate().getDeductions()), null));
            } else {
                changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.DEDUCTION_INFOS, JSONUtils.beanToJSONDefault(finance.getDeductionDelegate().getDeductions()), JSONUtils.beanToJSONDefault(snapShot.getDeductionDelegate().getDeductions())));
            }
        }

        //预估税金
        if (finance.getEstimatedTax() != null) {
            if (snapShot == null || snapShot.getEstimatedTax() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.ESTIMATED_TAX, String.valueOf(finance.getEstimatedTax().getAmount()), null));
            } else {
                OperateTypeEnum operateType = compareBigDecimal(finance.getEstimatedTax().getAmount(), snapShot.getEstimatedTax().getAmount());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.ESTIMATED_TAX, String.valueOf(finance.getEstimatedTax().getAmount()), String.valueOf(snapShot.getEstimatedTax().getAmount())));
                }
            }
        }

        //真实税金
        if (finance.getActualTax() != null) {
            if (snapShot == null || snapShot.getActualTax() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.ACTUAL_TAX, String.valueOf(finance.getActualTax().getAmount()), null));
            } else {
                OperateTypeEnum operateType = compareBigDecimal(finance.getActualTax().getAmount(), snapShot.getActualTax().getAmount());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.ACTUAL_TAX, String.valueOf(finance.getActualTax().getAmount()), String.valueOf(snapShot.getActualTax().getAmount())));
                }
            }
        }

        //税金结算方式
        if (finance.getTaxSettlementType() == null) {
            if (snapShot != null && snapShot.getTaxSettlementType() != null) {
                if (clearFields.contains(ModifyItemConfigEnum.TAX_SETTLEMENT_TYPE.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.TAX_SETTLEMENT_TYPE, null, String.valueOf(snapShot.getTaxSettlementType())));
                } else {
                    if (CollectionUtils.isNotEmpty(clearFields)) {
                        clearFields.remove(ModifyItemConfigEnum.TAX_SETTLEMENT_TYPE.getCode());
                    }
                }
            }
        } else {
            if (snapShot == null || snapShot.getTaxSettlementType() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.TAX_SETTLEMENT_TYPE, String.valueOf(finance.getTaxSettlementType()), null));
            } else {
                OperateTypeEnum operateType = compareInteger(finance.getTaxSettlementType(), snapShot.getTaxSettlementType());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.TAX_SETTLEMENT_TYPE, String.valueOf(finance.getTaxSettlementType()), String.valueOf(snapShot.getTaxSettlementType())));
                }
            }
        }
        //todo 历史数据调整后下线
        //支付状态集合
        if (null != finance.getPayStatusMap()) {
            changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.PAY_STATUS_MAP, JSONUtils.beanToJSONDefault(finance.getPayStatusMap()), JSONUtils.beanToJSONDefault(snapShot.getPayStatusMap())));
        }

        //扩展信息比对
        if(MapUtils.isNotEmpty(finance.getExtendProps())){
            Map<String, String> extendProps = finance.getExtendProps();
            Map<String, String> snapShotExtendProps = snapShot.getExtendProps();
            // 自计费模式，商家计价金额比对
            if(extendProps.containsKey(FinanceConstants.CUSTOMER_BILLING_AMOUNT)){//我有他无
                if(MapUtils.isEmpty(snapShotExtendProps) || !snapShotExtendProps.containsKey(FinanceConstants.CUSTOMER_BILLING_AMOUNT)){
                    changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.CUSTOMER_BILLING_AMOUNT, JSONUtils.beanToJSONDefault(finance.getExtendProps().get(FinanceConstants.CUSTOMER_BILLING_AMOUNT)), JSONUtils.beanToJSONDefault("")));
                } else {
                    //我有他有 不一致
                    if (!extendProps.get(FinanceConstants.CUSTOMER_BILLING_AMOUNT).equals(snapShotExtendProps.get(FinanceConstants.CUSTOMER_BILLING_AMOUNT))) {
                        changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.CUSTOMER_BILLING_AMOUNT, JSONUtils.beanToJSONDefault(finance.getExtendProps().get(FinanceConstants.CUSTOMER_BILLING_AMOUNT)), JSONUtils.beanToJSONDefault(snapShot.getExtendProps().get(FinanceConstants.CUSTOMER_BILLING_AMOUNT))));
                    } else if ("0".equals(extendProps.get(FinanceConstants.CUSTOMER_BILLING_AMOUNT))) {
                        changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.CUSTOMER_BILLING_AMOUNT, JSONUtils.beanToJSONDefault(finance.getExtendProps().get(FinanceConstants.CUSTOMER_BILLING_AMOUNT)), JSONUtils.beanToJSONDefault(snapShot.getExtendProps().get(FinanceConstants.CUSTOMER_BILLING_AMOUNT))));
                    }
                }
            } else{//我无他有
                if(MapUtils.isNotEmpty(snapShotExtendProps) && snapShotExtendProps.containsKey(FinanceConstants.CUSTOMER_BILLING_AMOUNT)){
                    changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.CUSTOMER_BILLING_AMOUNT, JSONUtils.beanToJSONDefault(""), JSONUtils.beanToJSONDefault(snapShot.getExtendProps().get(FinanceConstants.CUSTOMER_BILLING_AMOUNT))));
                }
            }

            if(extendProps.containsKey(FinanceConstants.CUSTOMER_MERGE_PAY_AMOUNT)){//我有他无
                if(MapUtils.isEmpty(snapShotExtendProps) || !snapShotExtendProps.containsKey(FinanceConstants.CUSTOMER_MERGE_PAY_AMOUNT)){
                    changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.CUSTOMER_BILLING_AMOUNT, JSONUtils.beanToJSONDefault(finance.getExtendProps().get(FinanceConstants.CUSTOMER_MERGE_PAY_AMOUNT)), JSONUtils.beanToJSONDefault("")));
                } else {
                    //我有他有 不一致
                    if (!extendProps.get(FinanceConstants.CUSTOMER_MERGE_PAY_AMOUNT).equals(snapShotExtendProps.get(FinanceConstants.CUSTOMER_MERGE_PAY_AMOUNT))) {
                        changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.CUSTOMER_BILLING_AMOUNT, JSONUtils.beanToJSONDefault(finance.getExtendProps().get(FinanceConstants.CUSTOMER_MERGE_PAY_AMOUNT)), JSONUtils.beanToJSONDefault(snapShot.getExtendProps().get(FinanceConstants.CUSTOMER_MERGE_PAY_AMOUNT))));
                    } else if ("0".equals(extendProps.get(FinanceConstants.CUSTOMER_MERGE_PAY_AMOUNT))) {
                        changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.CUSTOMER_BILLING_AMOUNT, JSONUtils.beanToJSONDefault(finance.getExtendProps().get(FinanceConstants.CUSTOMER_MERGE_PAY_AMOUNT)), JSONUtils.beanToJSONDefault(snapShot.getExtendProps().get(FinanceConstants.CUSTOMER_MERGE_PAY_AMOUNT))));
                    }
                }
            } else{//我无他有
                if(MapUtils.isNotEmpty(snapShotExtendProps) && snapShotExtendProps.containsKey(FinanceConstants.CUSTOMER_MERGE_PAY_AMOUNT)){
                    changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.CUSTOMER_BILLING_AMOUNT, JSONUtils.beanToJSONDefault(""), JSONUtils.beanToJSONDefault(snapShot.getExtendProps().get(FinanceConstants.CUSTOMER_MERGE_PAY_AMOUNT))));
                }
            }
        }

    }

    /**
     * 生成修改的对象
     *
     * @param operateType
     * @param itemConfig
     * @param modifyValue
     * @param originValue
     * @return
     */
    private ChangedProperty toChangedProperty(OperateTypeEnum operateType, ModifyItemConfigEnum itemConfig, String modifyValue, String originValue) {
        ChangedProperty changedProperty = new ChangedProperty();
        changedProperty.setOperateType(operateType);
        changedProperty.setProperty(itemConfig.getField());
        changedProperty.setItemCode(itemConfig.getCode());
        changedProperty.setPropertyDesc(itemConfig.getDesc());
        changedProperty.setOriginValue(originValue);
        changedProperty.setModifyValue(modifyValue);
        return changedProperty;
    }

    /**
     * 对比字符串
     * @param targetVal
     * @param orginVal
     * @return
     */
    private OperateTypeEnum compareString(String targetVal, String orginVal) {
        if (StringUtils.isBlank(targetVal) && StringUtils.isBlank(orginVal)) {
            return null;
        } else if (StringUtils.isBlank(targetVal) && StringUtils.isNotBlank(orginVal)) {
            return OperateTypeEnum.DELETE;
        } else if (StringUtils.isNotBlank(targetVal) && StringUtils.isBlank(orginVal)) {
            return OperateTypeEnum.INSERT;
        } else if (StringUtils.isNotBlank(targetVal) && StringUtils.isNotBlank(orginVal)) {
            if (!targetVal.equals(orginVal)) {
                return OperateTypeEnum.UPDATE;
            }
        }
        return null;
    }

    /**
     * 对比Integer类型
     *
     * @param targetVal
     * @param orginVal
     * @return
     */
    private OperateTypeEnum compareInteger(Integer targetVal, Integer orginVal) {
        if (targetVal == null) {
            return null;
        }

        if (orginVal == null) {
            if (targetVal == -128) {
                return null;
            } else {
                return OperateTypeEnum.INSERT;
            }
        } else {
            if (targetVal == -128) {
                return OperateTypeEnum.DELETE;
            } else if (targetVal.intValue() != orginVal.intValue()) {
                return OperateTypeEnum.UPDATE;
            }
        }
        return null;
    }

    /**
     * 对比Integer类型
     *
     * @param targetVal
     * @param orginVal
     * @return
     */
    private OperateTypeEnum compareBigDecimal(BigDecimal targetVal, BigDecimal orginVal) {
        if (targetVal == null) {
            return null;
        }

        if (orginVal == null) {
            return OperateTypeEnum.INSERT;
        } else {
            if (0 != targetVal.compareTo(orginVal)) {
                return OperateTypeEnum.UPDATE;
            }
        }
        return null;
    }

    /**
     * 对比时间
     *
     * @param targetVal
     * @param orginVal
     * @return
     * @throws Exception
     */
    private OperateTypeEnum compareDate(Date targetVal, Date orginVal) throws Exception {
        Date eraDateTime = DateUtils.getEraDateTime();

        if (targetVal == null) {
            return null;
        }

        if (DateUtils.isEqual(targetVal, eraDateTime)) { //表示想要删除
            if (orginVal == null) {
                return null;
            } else {
                return OperateTypeEnum.DELETE;
            }
        } else {
            if (orginVal == null) {
                return OperateTypeEnum.INSERT;
            } else if (!DateUtils.isEqual(targetVal, orginVal)) {
                return OperateTypeEnum.UPDATE;
            }
        }
        return null;
    }

    /**
     * 对比枚举
     * @param targetVal
     * @param originVal
     * @return
     */
    private OperateTypeEnum compareEnum(Enum targetVal, Enum originVal) {
        if (targetVal == null) {
            return null;
        }
        if (originVal == null) {
            return OperateTypeEnum.INSERT;
        } else if (targetVal == originVal) {
            return null;
        } else if (targetVal != originVal) {
            return OperateTypeEnum.UPDATE;
        }

        return null;
    }

    /**
     * 比对产品信息
     */
    public void compareProducts(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties) {
        String modifiedFieldsValue = orderModel.getModifiedFields().get(ModifiedFieldEnum.PRODUCT_INFOS.getCode());

        IProduct targetMainProduct = orderModel.getProductDelegate().getMainProduct();
        if (null != targetMainProduct
                // 增加此条件判读主要是B2C修改的时候会将产品信息补全
                && StringUtils.isNotBlank(targetMainProduct.getProductNo())) {
            IProduct originMainProduct = orderModel.getOrderSnapshot().getProductDelegate().getMainProduct();
            if (!targetMainProduct.getProductNo().equals(originMainProduct.getProductNo())) {
                if (!ModifiedFieldValueEnum.ALL_COVER.getCode().equals(modifiedFieldsValue)) {
                    LOGGER.error("主产品替换时，更新模式只能全量覆盖，不能增量更新和全量删除。原主产品：{}，新主产品:{}", originMainProduct.getProductNo(), targetMainProduct.getProductNo());
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.MODIFY_ORDER_REPLACE_MAIN_PRODUCT_FAIL).withCustom("主产品替换时，更新模式只能全量覆盖，不能增量更新和全量删除");
                }
                LOGGER.info("主产品发生变更。变更后: {}, 变更前: {}", targetMainProduct.getProductNo(), originMainProduct.getProductNo());
                OperateTypeEnum operateType = compareString(targetMainProduct.getProductNo(), originMainProduct.getProductNo());
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.MAIN_PRODUCT, targetMainProduct.getProductNo(), originMainProduct.getProductNo()));
            }
        }
        if (ModifiedFieldValueEnum.INCREMENT_UPDATE.getCode().equals(modifiedFieldsValue)) {
            if (!orderModel.getProductDelegate().isEmpty()) {
                //增量更新
                List<Product> targetList = (List<Product>) orderModel.getProductDelegate().getProducts();
                List<Product> originList = (List<Product>) orderModel.getOrderSnapshot().getProductDelegate().getProducts();

                for (Product product : targetList) {
                    Product originProduct = orderModel.getOrderSnapshot().getProductDelegate().ofProductNo(product.getProductNo());
                    //判断是否在原集合中存在
                    if (product.getOperateType() == OperateTypeEnum.INSERT && originProduct == null) {
                        //新增
                        product.setOperateType(OperateTypeEnum.INSERT);
                    } else if (product.getOperateType() == OperateTypeEnum.UPDATE && originProduct != null) {
                        //修改
                        handleOperateTypeUpdate(product, originProduct);
                    } else if (product.getOperateType() == OperateTypeEnum.UPSERT && originProduct == null) {
                        //覆盖-新增
                        product.setOperateType(OperateTypeEnum.INSERT);
                    } else if (product.getOperateType() == OperateTypeEnum.UPSERT && originProduct != null) {
                        //覆盖-修改
                        product.setOperateType(OperateTypeEnum.UPDATE);
                    } else if (product.getOperateType() == OperateTypeEnum.DELETE) {
                        if (originProduct != null) {
                            product.setProductNo(originProduct.getProductNo()); // FIXME 不是用的一个productNo来从原单取的产品信息吗，为啥还要重新set?
                            product.setProductName(originProduct.getProductName());
                            product.setProductType(originProduct.getProductType());
                            product.setOriginalProductNo(originProduct.getOriginalProductNo());
                            product.setOriginalProductName(originProduct.getOriginalProductName());
                            product.setProductAttrs(originProduct.getProductAttrs());
                            product.setParentNo(originProduct.getParentNo());
                            product.setExtendProps(originProduct.getExtendProps());
                        } else {
                            //不存在要删除的数据
                            LOGGER.info("产品{}不存在", product.getProductNo());
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(product.getProductNo() + "产品信息不存在，不允许标记删除");
                        }
                    } else {
                        LOGGER.error("产品{}增量更新失败，操作类型{}", product.getProductNo(),product.getOperateType());
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("产品信息不允许修改");

                    }
                }

                for (Product product : originList) {
                    //判断是否在新集合中存在,不存在，则聚合到新集合中
                    if (orderModel.getProductDelegate().ofProductNo(product.getProductNo()) == null) {
                        Product productNew = ProductMapper.INSTANCE.copyProduct(product);
                        //未发生操作
                        productNew.setOperateType(null);
                        targetList.add(productNew);
                    }
                }

                for (Product product : targetList) {
                    if (product.getOperateType() == null) {
                        continue;
                    }
                    if (product.getOperateType() == OperateTypeEnum.INSERT) {
                        if (COD_PRODUCT_NO.equals(product.getProductNo())
                                && ExpressOrderStatusCustomEnum.ofCustomOrderStatus(orderModel.getOrderSnapshot().getCustomStatus()).index() >= ExpressOrderStatusCustomEnum.PICKED_UP.index()) {
                            setModifyFlag(orderModel, COD_MODIFY_FLAG);
                        }
                        changedProperties.add(toChangedProperty(product.getOperateType(), ModifyItemConfigEnum.PRODUCT_NO, product.getProductNo(), null));
                    } else if (product.getOperateType() == OperateTypeEnum.DELETE) {
                        if (COD_PRODUCT_NO.equals(product.getProductNo())
                                && ExpressOrderStatusCustomEnum.ofCustomOrderStatus(orderModel.getOrderSnapshot().getCustomStatus()).index() >= ExpressOrderStatusCustomEnum.PICKED_UP.index()) {
                            setModifyFlag(orderModel, COD_MODIFY_FLAG);
                        }
                        changedProperties.add(toChangedProperty(product.getOperateType(), ModifyItemConfigEnum.PRODUCT_NO, null, product.getProductNo()));
                    } else if (product.getOperateType() == OperateTypeEnum.UPDATE) {
                        if (COD_PRODUCT_NO.equals(product.getProductNo())
                                && ExpressOrderStatusCustomEnum.ofCustomOrderStatus(orderModel.getOrderSnapshot().getCustomStatus()).index() >= ExpressOrderStatusCustomEnum.PICKED_UP.index()) {
                            setModifyFlag(orderModel, COD_MODIFY_FLAG);
                        }
                        //TODO 修改场景
                        changedProperties.add(toChangedProperty(product.getOperateType(), ModifyItemConfigEnum.PRODUCT_NO, product.getProductNo(), product.getProductNo()));
                    }
                }
            }
        } else if (ModifiedFieldValueEnum.ALL_COVER.getCode().equals(modifiedFieldsValue)) {
            if (!orderModel.getProductDelegate().isEmpty()) {
                //全量覆盖，比对list
                List<Product> targetList = (List<Product>) orderModel.getProductDelegate().getProducts();
                List<Product> originList = (List<Product>) orderModel.getOrderSnapshot().getProductDelegate().getProducts();
                // 全量覆盖时主产品可能变化也可能没变化，需要先判断
                IProduct mainProduct = orderModel.getProductDelegate().getMainProduct();
                if (mainProduct == null) {
                    LOGGER.error("全量覆盖时，主产品不能为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("全量覆盖时，主产品不能为空");
                }
                IProduct originMainProduct = orderModel.getOrderSnapshot().getProductDelegate().getMainProduct();
                boolean isModifiedMainProductCode = isModifiedMainProductCode(mainProduct, originMainProduct);

                for (Product product : targetList) {
                    //判断是否在原集合中存在
                    Product originProduct = orderModel.getOrderSnapshot().getProductDelegate().ofProductNo(product.getProductNo());
                    if (originProduct == null) {
                        //新增
                        product.setOperateType(OperateTypeEnum.INSERT);
                    } else {
                        //修改
                        if(isModifiedMainProductCode){
                            // 主产品编码变化，直接赋值 UPDATE
                            product.setOperateType(OperateTypeEnum.UPDATE);
                        } else {
                            handleOperateTypeUpdate(product,originProduct);
                        }

                    }
                }
                for (Product product : originList) {
                    //判断是否在新集合中存在
                    if (orderModel.getProductDelegate().ofProductNo(product.getProductNo()) == null) {
                        //删除
                        product.setOperateType(OperateTypeEnum.DELETE);
                        targetList.add(product);
                    }
                }
                for (Product product : (List<Product>) orderModel.getProductDelegate().getProducts()) {
                    if (product.getOperateType() == null) {
                        continue;
                    }
                    if (product.getOperateType() == OperateTypeEnum.INSERT) {
                        //揽收后新增代收货款
                        if (COD_PRODUCT_NO.equals(product.getProductNo())
                                && ExpressOrderStatusCustomEnum.ofCustomOrderStatus(orderModel.getOrderSnapshot().getCustomStatus()).index() >= ExpressOrderStatusCustomEnum.PICKED_UP.index()) {
                            String targetMainProductCode = orderModel.getProductDelegate().getMajorProductNo();
                            String originMainProductCode = orderModel.getOrderSnapshot().getProductDelegate().getMajorProductNo();
                            //若主产品编码不相等，说明是替换主产品，存在三种情况：非生鲜和非生鲜互改、生鲜和生鲜互改、非生鲜和生鲜互改
                            if (!targetMainProductCode.equals(originMainProductCode)){
                                if ((!ProductEnum.isFreshMainProduct(targetMainProductCode) && !ProductEnum.isFreshMainProduct(originMainProductCode))
                                        || (ProductEnum.isFreshMainProduct(targetMainProductCode) && ProductEnum.isFreshMainProduct(originMainProductCode))){
                                    //非生鲜和非生鲜互改、生鲜和生鲜互改需要标记代收货款修改标识为已修改
                                    setModifyFlag(orderModel, COD_MODIFY_FLAG);
                                }else {
                                    //非生鲜和生鲜互改不需要标记代收货款修改标识
                                    LOGGER.info("原单主产品是否生鲜:{},修改后的主产品是否生鲜:{}",ProductEnum.isFreshMainProduct(originMainProductCode),
                                            ProductEnum.isFreshMainProduct(targetMainProductCode));
                                }
                            }else {
                                setModifyFlag(orderModel, COD_MODIFY_FLAG);
                            }
                        }
                        changedProperties.add(toChangedProperty(product.getOperateType(), ModifyItemConfigEnum.PRODUCT_NO, product.getProductNo(), null));
                    } else if (product.getOperateType() == OperateTypeEnum.DELETE) {
                        if (COD_PRODUCT_NO.equals(product.getProductNo())
                                && ExpressOrderStatusCustomEnum.ofCustomOrderStatus(orderModel.getOrderSnapshot().getCustomStatus()).index() >= ExpressOrderStatusCustomEnum.PICKED_UP.index()) {
                            setModifyFlag(orderModel, COD_MODIFY_FLAG);
                        }
                        changedProperties.add(toChangedProperty(product.getOperateType(), ModifyItemConfigEnum.PRODUCT_NO, null, product.getProductNo()));
                    } else if (product.getOperateType() == OperateTypeEnum.UPDATE) {
                        if (COD_PRODUCT_NO.equals(product.getProductNo())
                                && ExpressOrderStatusCustomEnum.ofCustomOrderStatus(orderModel.getOrderSnapshot().getCustomStatus()).index() >= ExpressOrderStatusCustomEnum.PICKED_UP.index()) {
                            setModifyFlag(orderModel, COD_MODIFY_FLAG);
                        }
                        //TODO 修改场景
                        changedProperties.add(toChangedProperty(product.getOperateType(), ModifyItemConfigEnum.PRODUCT_NO, product.getProductNo(), product.getProductNo()));
                    }
                }
            }
        } else if (ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFieldsValue)) {
            if (!orderModel.getOrderSnapshot().getProductDelegate().isEmpty()) {
                //全量删除
                for (Product product : (List<Product>) orderModel.getOrderSnapshot().getProductDelegate().getProducts()) {
                    if (COD_PRODUCT_NO.equals(product.getProductNo())
                            && ExpressOrderStatusCustomEnum.ofCustomOrderStatus(orderModel.getOrderSnapshot().getCustomStatus()).index() >= ExpressOrderStatusCustomEnum.PICKED_UP.index()) {
                        setModifyFlag(orderModel, COD_MODIFY_FLAG);
                    }
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.PRODUCT_NO, null, product.getProductNo()));
                    product.setOperateType(OperateTypeEnum.DELETE);
                }
                //将需要删除的产品信息赋值到model里
                orderModel.setProductDelegate(ProductDelegate.productDelegateOf((List<Product>) orderModel.getOrderSnapshot().getProductDelegate().getProducts()));
            }
        }
    }

    /**
     * 比对券信息
     */
    public void compareTickets(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties) {
        String modifiedFieldsValue = orderModel.getModifiedFields().get(ModifiedFieldEnum.TICKET_INFOS.getCode());
        if (ModifiedFieldValueEnum.INCREMENT_UPDATE.getCode().equals(modifiedFieldsValue)) {
            //增量更新
            if (CollectionUtils.isNotEmpty(orderModel.getPromotion().getTickets())) {
                List<Ticket> targetList = orderModel.getPromotion().getTickets();
                List<Ticket> originList = orderModel.getOrderSnapshot().getPromotion().getTickets();

                for (Ticket ticket : targetList) {
                    //判断是否在原集合中存在
                    if (ticket.getOperateType() == OperateTypeEnum.INSERT && orderModel.getOrderSnapshot().getPromotion().ofTicket(ticket.getTicketNo()) == null) {
                        //新增
                        ticket.setOperateType(OperateTypeEnum.INSERT);
                    } else if (ticket.getOperateType() == OperateTypeEnum.INSERT && orderModel.getOrderSnapshot().getPromotion().ofTicket(ticket.getTicketNo()) != null) {
                        //修改
                        Ticket orgTicket = orderModel.getOrderSnapshot().getPromotion().ofTicket(ticket.getTicketNo());
                        ticket.setTicketCategory(orgTicket.getTicketCategory());
                        ticket.setTicketType(orgTicket.getTicketType());
                        ticket.setTicketDiscountAmount(orgTicket.getTicketDiscountAmount());
                        ticket.setTicketDescription(orgTicket.getTicketDescription());
                        ticket.setTicketDiscountRate(orgTicket.getTicketDiscountRate());
                        ticket.setTicketDiscountUpperLimit(orgTicket.getTicketDiscountUpperLimit());
                        ticket.setCouponStatus(orgTicket.getCouponStatus());
                        ticket.setTicketUseAmount(orgTicket.getTicketUseAmount());
                        ticket.setOperateType(OperateTypeEnum.UPDATE);
                    }
                }

                if (CollectionUtils.isNotEmpty(originList)) {
                    for (Ticket ticket : originList) {
                        //判断是否在新集合中存在,不存在，则聚合到新集合中
                        if (orderModel.getPromotion().ofTicket(ticket.getTicketNo()) == null) {
                            //未发生操作
                            ticket.setOperateType(null);
                            targetList.add(ticket);
                        }
                    }
                }

                for (Ticket ticket : targetList) {
                    if (ticket.getOperateType() == null) {
                        continue;
                    }
                    if (ticket.getOperateType() == OperateTypeEnum.INSERT) {
                        changedProperties.add(toChangedProperty(ticket.getOperateType(), ModifyItemConfigEnum.TICKET_NO, ticket.getTicketNo(), null));
                    } else if (ticket.getOperateType() == OperateTypeEnum.DELETE) {
                        changedProperties.add(toChangedProperty(ticket.getOperateType(), ModifyItemConfigEnum.TICKET_NO, null, ticket.getTicketNo()));
                    } else if (ticket.getOperateType() == OperateTypeEnum.UPDATE) {
                        //TODO 修改场景
                        changedProperties.add(toChangedProperty(ticket.getOperateType(), ModifyItemConfigEnum.TICKET_NO, ticket.getTicketNo(), ticket.getTicketNo()));
                    }

                }
            }
        } else if (ModifiedFieldValueEnum.ALL_COVER.getCode().equals(modifiedFieldsValue)) {
            if (CollectionUtils.isNotEmpty(orderModel.getPromotion().getTickets())) {
                //全量覆盖，比对list
                List<Ticket> targetList = orderModel.getPromotion().getTickets();
                List<Ticket> originList = orderModel.getOrderSnapshot().getPromotion().getTickets();

                for (Ticket ticket : targetList) {
                    //判断是否在原集合中存在
                    if (orderModel.getOrderSnapshot().getPromotion().ofTicket(ticket.getTicketNo()) == null) {
                        //新增
                        ticket.setOperateType(OperateTypeEnum.INSERT);
                    } else {
                        //修改
                        Ticket orgTicket = orderModel.getOrderSnapshot().getPromotion().ofTicket(ticket.getTicketNo());
                        ticket.setTicketCategory(orgTicket.getTicketCategory());
                        ticket.setTicketType(orgTicket.getTicketType());
                        ticket.setTicketDiscountAmount(orgTicket.getTicketDiscountAmount());
                        ticket.setTicketDescription(orgTicket.getTicketDescription());
                        ticket.setTicketDiscountRate(orgTicket.getTicketDiscountRate());
                        ticket.setTicketDiscountUpperLimit(orgTicket.getTicketDiscountUpperLimit());
                        ticket.setCouponStatus(orgTicket.getCouponStatus());
                        ticket.setTicketUseAmount(orgTicket.getTicketUseAmount());
                        ticket.setOperateType(OperateTypeEnum.UPDATE);
                    }
                }
                if (CollectionUtils.isNotEmpty(originList)) {
                    for (Ticket ticket : originList) {
                        //判断是否在新集合中存在
                        if (orderModel.getPromotion().ofTicket(ticket.getTicketNo()) == null) {
                            //删除
                            ticket.setOperateType(OperateTypeEnum.DELETE);
                            targetList.add(ticket);
                        }
                    }
                }

                for (Ticket ticket : orderModel.getPromotion().getTickets()) {
                    if (ticket.getOperateType() == null) {
                        continue;
                    }

                    if (ticket.getOperateType() == OperateTypeEnum.INSERT) {
                        changedProperties.add(toChangedProperty(ticket.getOperateType(), ModifyItemConfigEnum.TICKET_NO, ticket.getTicketNo(), null));
                    } else if (ticket.getOperateType() == OperateTypeEnum.DELETE) {
                        changedProperties.add(toChangedProperty(ticket.getOperateType(), ModifyItemConfigEnum.TICKET_NO, null, ticket.getTicketNo()));
                    } else if (ticket.getOperateType() == OperateTypeEnum.UPDATE) {
                        //TODO 修改场景
                        changedProperties.add(toChangedProperty(ticket.getOperateType(), ModifyItemConfigEnum.TICKET_NO, ticket.getTicketNo(), ticket.getTicketNo()));
                    }

                }
            }
        } else if (ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFieldsValue)) {
            List<Ticket> tickets = orderModel.getOrderSnapshot().getPromotion().getTickets();
            if (CollectionUtils.isNotEmpty(tickets)) {
                //全量删除
                for (Ticket ticket : tickets) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.TICKET_NO, null, ticket.getTicketNo()));
                    ticket.setOperateType(OperateTypeEnum.DELETE);
                }
                //将需要删除的优惠券信息赋值到model里，供后续节点使用
                orderModel.getPromotion().setTickets(new ArrayList<>(tickets));
            }
        }

        // 终端需求：修改策略为【优惠券逻辑删除标识】且本次优惠券标识为删除时，优惠券状态存为【待回滚】
        if (ModifySceneRuleUtil.isReleaseTicketPendingFlag(orderModel)) {
            List<Ticket> tickets = orderModel.getOrderSnapshot().getPromotion().getTickets();
            if (CollectionUtils.isNotEmpty(tickets)) {
                for (Ticket ticket : tickets) {
                    if (OperateTypeEnum.DELETE.equals(ticket.getOperateType())) {
                        ticket.setCouponStatus(CouponStatusEnum.DELETE_PENDING.getCode());
                        ticket.setOperateType(OperateTypeEnum.UPDATE);
                    }
                }
            }
        }
    }

    /**
     * 比对折扣信息
     */
    public void compareDiscounts(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties) {
        String modifiedFieldsValue = orderModel.getModifiedFields().get(ModifiedFieldEnum.DISCOUNT_INFOS.getCode());
        if (ModifiedFieldValueEnum.INCREMENT_UPDATE.getCode().equals(modifiedFieldsValue)) {
            //增量更新
            if (CollectionUtils.isNotEmpty(orderModel.getPromotion().getDiscounts())) {
                List<Discount> targetList = orderModel.getPromotion().getDiscounts();
                List<Discount> originList = orderModel.getOrderSnapshot().getPromotion().getDiscounts();

                for (Discount discount : targetList) {
                    //判断是否在原集合中存在
                    if (discount.getOperateType() == OperateTypeEnum.INSERT && orderModel.getOrderSnapshot().getPromotion().ofDiscount(discount.getDiscountNo()) == null) {
                        //新增
                        discount.setOperateType(OperateTypeEnum.INSERT);
                    } else if (discount.getOperateType() == OperateTypeEnum.INSERT && orderModel.getOrderSnapshot().getPromotion().ofDiscount(discount.getDiscountNo()) != null) {
                        //修改
                        discount.setOperateType(OperateTypeEnum.UPDATE);
                    }
                }

                if (CollectionUtils.isNotEmpty(originList)) {
                    for (Discount discount : originList) {
                        //判断是否在新集合中存在,不存在，则聚合到新集合中
                        if (orderModel.getPromotion().ofDiscount(discount.getDiscountNo()) == null) {
                            //未发生操作
                            discount.setOperateType(null);
                            targetList.add(discount);
                        }
                    }
                }

                for (Discount discount : targetList) {
                    if (discount.getOperateType() == null) {
                        continue;
                    }
                    if (discount.getOperateType() == OperateTypeEnum.INSERT) {
                        changedProperties.add(toChangedProperty(discount.getOperateType(), ModifyItemConfigEnum.DISCOUNT_NO, discount.getDiscountNo(), null));
                    } else if (discount.getOperateType() == OperateTypeEnum.DELETE) {
                        changedProperties.add(toChangedProperty(discount.getOperateType(), ModifyItemConfigEnum.DISCOUNT_NO, null, discount.getDiscountNo()));
                    } else if (discount.getOperateType() == OperateTypeEnum.UPDATE) {
                        //TODO 修改场景
                        changedProperties.add(toChangedProperty(discount.getOperateType(), ModifyItemConfigEnum.DISCOUNT_NO, discount.getDiscountNo(), discount.getDiscountNo()));
                    }

                }
            }
        } else if (ModifiedFieldValueEnum.ALL_COVER.getCode().equals(modifiedFieldsValue)) {
            if (CollectionUtils.isNotEmpty(orderModel.getPromotion().getDiscounts())) {
                //全量覆盖，比对list
                List<Discount> targetList = orderModel.getPromotion().getDiscounts();
                List<Discount> originList = orderModel.getOrderSnapshot().getPromotion().getDiscounts();

                for (Discount discount : targetList) {
                    //判断是否在原集合中存在
                    if (orderModel.getOrderSnapshot().getPromotion().ofDiscount(discount.getDiscountNo()) == null) {
                        //新增
                        discount.setOperateType(OperateTypeEnum.INSERT);
                    } else {
                        //修改
                        discount.setOperateType(OperateTypeEnum.UPDATE);
                    }
                }
                if (CollectionUtils.isNotEmpty(originList)) {
                    for (Discount discount : originList) {
                        //判断是否在新集合中存在
                        if (orderModel.getPromotion().ofDiscount(discount.getDiscountNo()) == null) {
                            //删除
                            discount.setOperateType(OperateTypeEnum.DELETE);
                            targetList.add(discount);
                        }
                    }
                }

                for (Discount discount : targetList) {
                    if (discount.getOperateType() == null) {
                        continue;
                    }
                    if (discount.getOperateType() == OperateTypeEnum.INSERT) {
                        changedProperties.add(toChangedProperty(discount.getOperateType(), ModifyItemConfigEnum.DISCOUNT_NO, discount.getDiscountNo(), null));
                    } else if (discount.getOperateType() == OperateTypeEnum.DELETE) {
                        changedProperties.add(toChangedProperty(discount.getOperateType(), ModifyItemConfigEnum.DISCOUNT_NO, null, discount.getDiscountNo()));
                    } else if (discount.getOperateType() == OperateTypeEnum.UPDATE) {
                        //TODO 修改场景
                        changedProperties.add(toChangedProperty(discount.getOperateType(), ModifyItemConfigEnum.DISCOUNT_NO, discount.getDiscountNo(), discount.getDiscountNo()));
                    }
                }
            }
        } else if (ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFieldsValue)) {
            if (CollectionUtils.isNotEmpty(orderModel.getOrderSnapshot().getPromotion().getDiscounts())) {
                //全量删除
                for (Discount discount : orderModel.getOrderSnapshot().getPromotion().getDiscounts()) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.DISCOUNT_NO, null, discount.getDiscountNo()));
                    discount.setOperateType(OperateTypeEnum.DELETE);
                }
                //将需要删除的优惠券信息赋值到model里，供后续节点使用
                orderModel.getPromotion().setDiscounts(orderModel.getOrderSnapshot().getPromotion().getDiscounts());
            }
        }
    }

    /**
     * 比对活动信息
     */
    public void compareActivities(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties) {
        String modifiedFieldsValue = orderModel.getModifiedFields().get(ModifiedFieldEnum.ACTIVITY_INFOS.getCode());
        if (ModifiedFieldValueEnum.INCREMENT_UPDATE.getCode().equals(modifiedFieldsValue)) {
            //增量更新
            if (CollectionUtils.isNotEmpty(orderModel.getPromotion().getActivities())) {
                List<Activity> targetList = orderModel.getPromotion().getActivities();
                List<Activity> originList = orderModel.getOrderSnapshot().getPromotion().getActivities();

                for (Activity activity : targetList) {
                    //判断是否在原集合中存在
                    if (activity.getOperateType() == OperateTypeEnum.INSERT && orderModel.getOrderSnapshot().getPromotion().ofActivity(activity.getActivityNo()) == null) {
                        //新增
                        activity.setOperateType(OperateTypeEnum.INSERT);
                    } else if (activity.getOperateType() == OperateTypeEnum.INSERT && orderModel.getOrderSnapshot().getPromotion().ofActivity(activity.getActivityNo()) != null) {
                        //修改
                        activity.setOperateType(OperateTypeEnum.UPDATE);
                    }
                }

                if (CollectionUtils.isNotEmpty(originList)) {
                    for (Activity activity : originList) {
                        //判断是否在新集合中存在,不存在，则聚合到新集合中
                        if (orderModel.getPromotion().ofActivity(activity.getActivityNo()) == null) {
                            //未发生操作
                            activity.setOperateType(null);
                            targetList.add(activity);
                        }
                    }
                }

                for (Activity activity : targetList) {
                    if (activity.getOperateType() == null) {
                        continue;
                    }
                    if (activity.getOperateType() == OperateTypeEnum.INSERT) {
                        changedProperties.add(toChangedProperty(activity.getOperateType(), ModifyItemConfigEnum.ACTIVITY_NO, activity.getActivityNo(), null));
                    } else if (activity.getOperateType() == OperateTypeEnum.DELETE) {
                        changedProperties.add(toChangedProperty(activity.getOperateType(), ModifyItemConfigEnum.ACTIVITY_NO, null, activity.getActivityNo()));
                    } else if (activity.getOperateType() == OperateTypeEnum.UPDATE) {
                        //TODO 修改场景
                        changedProperties.add(toChangedProperty(activity.getOperateType(), ModifyItemConfigEnum.ACTIVITY_NO, activity.getActivityNo(), activity.getActivityNo()));
                    }
                }
            }
        } else if (ModifiedFieldValueEnum.ALL_COVER.getCode().equals(modifiedFieldsValue)) {
            if (CollectionUtils.isNotEmpty(orderModel.getPromotion().getActivities())) {
                //全量覆盖，比对list
                List<Activity> targetList = orderModel.getPromotion().getActivities();
                List<Activity> originList = orderModel.getOrderSnapshot().getPromotion().getActivities();

                for (Activity activity : targetList) {
                    //判断是否在原集合中存在
                    if (orderModel.getOrderSnapshot().getPromotion().ofActivity(activity.getActivityNo()) == null) {
                        //新增
                        activity.setOperateType(OperateTypeEnum.INSERT);
                    } else {
                        //修改
                        activity.setOperateType(OperateTypeEnum.UPDATE);
                    }
                }
                if (CollectionUtils.isNotEmpty(originList)) {
                    for (Activity activity : originList) {
                        //判断是否在新集合中存在
                        if (orderModel.getPromotion().ofActivity(activity.getActivityNo()) == null) {
                            //删除
                            activity.setOperateType(OperateTypeEnum.DELETE);
                            targetList.add(activity);
                        }
                    }
                }

                for (Activity activity : orderModel.getPromotion().getActivities()) {
                    if (activity.getOperateType() == null) {
                        continue;
                    }

                    if (activity.getOperateType() == OperateTypeEnum.INSERT) {
                        changedProperties.add(toChangedProperty(activity.getOperateType(), ModifyItemConfigEnum.ACTIVITY_NO, activity.getActivityNo(), null));
                    } else if (activity.getOperateType() == OperateTypeEnum.DELETE) {
                        changedProperties.add(toChangedProperty(activity.getOperateType(), ModifyItemConfigEnum.ACTIVITY_NO, null, activity.getActivityNo()));
                    } else if (activity.getOperateType() == OperateTypeEnum.UPDATE) {
                        //TODO 修改场景
                        changedProperties.add(toChangedProperty(activity.getOperateType(), ModifyItemConfigEnum.ACTIVITY_NO, activity.getActivityNo(), activity.getActivityNo()));
                    }

                }
            }
        } else if (ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFieldsValue)) {
            if (CollectionUtils.isNotEmpty(orderModel.getOrderSnapshot().getPromotion().getActivities())) {
                //全量删除
                for (Activity activity : orderModel.getOrderSnapshot().getPromotion().getActivities()) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.ACTIVITY_NO, null, activity.getActivityNo()));
                    activity.setOperateType(OperateTypeEnum.DELETE);
                }
                //将需要删除的优惠券信息赋值到model里，供后续节点使用
                orderModel.getPromotion().setActivities(orderModel.getOrderSnapshot().getPromotion().getActivities());
            }
        }
    }

    /**
     * 比对货品信息
     *
     * @param orderModel
     * @param changedProperties
     */
    public void compareCargoes(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties) {
        String modifiedFieldsValue = orderModel.getModifiedFields().get(ModifiedFieldEnum.CARGO_INFOS.getCode());
        //TODO 操作类型定义枚举
        if (ModifiedFieldValueEnum.INCREMENT_UPDATE.getCode().equals(modifiedFieldsValue)) {
            throw new DomainAbilityException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("货品信息不支持增量更新");
        }

        List<Cargo> targetList = (List<Cargo>) orderModel.getCargoDelegate().getCargoList();
        List<Cargo> originList = (List<Cargo>) orderModel.getOrderSnapshot().getCargoDelegate().getCargoList();

        if (ModifiedFieldValueEnum.ALL_COVER.getCode().equals(modifiedFieldsValue)) {
            //全量覆盖
            if (!orderModel.getCargoDelegate().isEmpty()) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.CARGO, JSONUtils.beanToJSONDefault(targetList), JSONUtils.beanToJSONDefault(originList)));
            }
        } else if (ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFieldsValue)) {
            //全量删除
            changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.CARGO, null, JSONUtils.beanToJSONDefault(originList)));
        }
    }

    /**
     * 比对商品信息
     * @param orderModel
     * @param changedProperties
     */
    public void compareGoods(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties) {
        String modifiedFieldsValue = orderModel.getModifiedFields().get(ModifiedFieldEnum.GOODS_INFOS.getCode());
        if (ModifiedFieldValueEnum.INCREMENT_UPDATE.getCode().equals(modifiedFieldsValue)) {
            throw new DomainAbilityException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("商品信息不支持增量更新");
        }

        if (ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFieldsValue)) {
            // 大件业务支持商品信息全量删除
            if (orderModel.isLAS()) {
                List<Goods> originList = (List<Goods>) orderModel.getOrderSnapshot().getGoodsDelegate().getGoodsList();
                //全量删除
                changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.GOODS, null, JSONUtils.beanToJSONDefault(originList)));
            } else {
                throw new DomainAbilityException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("商品信息不支持全量删除");
            }
        }

        if (ModifiedFieldValueEnum.ALL_COVER.getCode().equals(modifiedFieldsValue)) {
            //全量覆盖
            List<Goods> targetList = (List<Goods>) orderModel.getGoodsDelegate().getGoodsList();
            List<Goods> originList = (List<Goods>) orderModel.getOrderSnapshot().getGoodsDelegate().getGoodsList();
            if (!orderModel.getGoodsDelegate().isEmpty()) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.GOODS, JSONUtils.beanToJSONDefault(targetList), JSONUtils.beanToJSONDefault(originList)));
            }
        }
    }

    /**
     * 比对财务收费要求信息
     * @param orderModel
     * @param changedProperties
     */
    public void compareFinanceCostInfos(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties) {
        String modifiedFieldsValue = orderModel.getModifiedFields().get(ModifiedFieldEnum.COST_INFOS.getCode());

        if (ModifiedFieldValueEnum.ALL_COVER.getCode().equals(modifiedFieldsValue)) { //全量覆盖
            List<CostInfo> targetList = orderModel.getFinance().getCostInfos();
            List<CostInfo> originList = orderModel.getOrderSnapshot().getFinance().getCostInfos();
            if (CollectionUtils.isNotEmpty(targetList)) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.COSTINFOS, JSONUtils.beanToJSONDefault(targetList), JSONUtils.beanToJSONDefault(originList)));
            }
        } else if (ModifiedFieldValueEnum.INCREMENT_UPDATE.getCode().equals(modifiedFieldsValue)) {
            throw new DomainAbilityException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("财务收费要求信息不支持增量更新");
        } else if (ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFieldsValue)) {
            throw new DomainAbilityException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("财务收费要求信息不支持全量删除");
        }
    }

    /**
     * 比对物流服务要求
     *
     * @param orderModel
     * @param changedProperties
     */
    public void compareServiceRequirements(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties){
        String modifiedFieldsValue = orderModel.getModifiedFields().get(ModifiedFieldEnum.SERVICE_REQUIREMENTS.getCode());
        Shipment shipment = orderModel.getShipment();
        Shipment snapShot = orderModel.getOrderSnapshot().getShipment();
        if (ModifiedFieldValueEnum.INCREMENT_UPDATE.getCode().equals(modifiedFieldsValue)) {
            //增量更新,将原单上的物流服务要求加上本次修改的
            Map<String, String> serviceRequirements = new HashMap<>();
            if (MapUtils.isNotEmpty(snapShot.getServiceRequirements())) {
                serviceRequirements.putAll(snapShot.getServiceRequirements());
            }
            if (MapUtils.isNotEmpty(shipment.getServiceRequirements())) {
                for (Map.Entry<String, String> serviceEntry : shipment.getServiceRequirements().entrySet()) {
                    if (StringUtils.isNotBlank(serviceEntry.getValue())){
                        serviceRequirements.put(serviceEntry.getKey(), serviceEntry.getValue());
                    }
                }
                changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE,ModifyItemConfigEnum.SERVICE_REQUIREMENTS,
                        JSONUtils.mapToJson(serviceRequirements),JSONUtils.mapToJson(snapShot.getServiceRequirements())));
            }
            shipment.setServiceRequirements(serviceRequirements);
        } else if (ModifiedFieldValueEnum.ALL_COVER.getCode().equals(modifiedFieldsValue)) {
            //全量覆盖
            Map<String, String> serviceRequirements = new HashMap<>();
            if (MapUtils.isNotEmpty(shipment.getServiceRequirements())) {
                for (Map.Entry<String, String> serviceEntry : shipment.getServiceRequirements().entrySet()) {
                    if (StringUtils.isNotBlank(serviceEntry.getValue())){
                        serviceRequirements.put(serviceEntry.getKey(), serviceEntry.getValue());
                    }
                }
                changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE,ModifyItemConfigEnum.SERVICE_REQUIREMENTS,
                        JSONUtils.mapToJson(serviceRequirements),JSONUtils.mapToJson(snapShot.getServiceRequirements())));
            }
            if (MapUtils.isEmpty(serviceRequirements)) {
                serviceRequirements = snapShot.getServiceRequirements();
            }
            shipment.setServiceRequirements(serviceRequirements);
        } else if (ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFieldsValue)) {
            if (MapUtils.isNotEmpty(snapShot.getServiceRequirements())) {
                //全量删除
                changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE,ModifyItemConfigEnum.SERVICE_REQUIREMENTS,
                        null,JSONUtils.mapToJson(snapShot.getServiceRequirements())));
            }
        }
        //增值服务-属性单独对比
        compareServiceRequirementsItem(shipment, snapShot, changedProperties);

        //按纸箱揽收服务要求
        compareServiceRequirementsItem(ServiceRequirementsEnum.PICKUP_BY_BOX_SERVICE, ModifyItemConfigEnum.PICKUP_BY_BOX_SERVICE, shipment, snapShot, changedProperties);
    }

    /**
     * 增值服务-属性单独对比
     * @param shipment
     * @param snapShot
     * @param changedProperties
     */
    private void compareServiceRequirementsItem(Shipment shipment, Shipment snapShot, List<ChangedProperty> changedProperties) {
        // 暂存天数比对
        String tempStorageDay = shipment.getServiceRequirementByKey(ServiceRequirementsEnum.TEMP_STORAGE_DAY);
        // 修改暂存天数
        if (null != tempStorageDay) {
            if (StringUtils.EMPTY.equals(tempStorageDay)) {
                tempStorageDay = "0";//传空默认改为0
            }
            // 原单暂存天数
            String snapShotTempStorageDay = snapShot.getServiceRequirementByKey(ServiceRequirementsEnum.TEMP_STORAGE_DAY);
            if (!tempStorageDay.equals(snapShotTempStorageDay)) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE,ModifyItemConfigEnum.TEMP_STORAGE_DAY,
                        tempStorageDay, snapShotTempStorageDay));
            }
        }
    }

    /**
     * 比对物流服务要求模版方法
     * @param serviceRequirementsEnum 物流服务要求枚举
     * @param modifyItemConfigEnum 物流服务要求枚举对应的修改项
     */
    private void compareServiceRequirementsItem(ServiceRequirementsEnum serviceRequirementsEnum, ModifyItemConfigEnum modifyItemConfigEnum, Shipment shipment, Shipment snapShotShipment, List<ChangedProperty> changedProperties) {
        String value = shipment != null ? shipment.getServiceRequirementByKey(serviceRequirementsEnum) : null;
        String originValue = snapShotShipment != null ? snapShotShipment.getServiceRequirementByKey(serviceRequirementsEnum) : null;
        compareStringTemplateMethod(value, originValue, modifyItemConfigEnum, changedProperties);
    }

    /**
     * 比较 是否散客挂月结
     *
     * @param order
     * @param snapShot
     * @param changedProperties
     */
    private void compareIndividualMsType(ExpressOrderModel order, ExpressOrderModel snapShot, List<ChangedProperty> changedProperties) {
        //当前值
        String currentIndividualMsType = order.getAttachment(AttachmentKeyEnum.INDIVIDUAL_MS_TYPE.getKey());
        //快照值
        String snapShotIndividualMsType = snapShot.getAttachment(AttachmentKeyEnum.INDIVIDUAL_MS_TYPE.getKey());
        if (StringUtils.isNotBlank(currentIndividualMsType)) {
            //快照有值
            if (StringUtils.isNotBlank(snapShotIndividualMsType)) {
                //当前和快照都有值时比对是否相等
                if (!currentIndividualMsType.equals(snapShotIndividualMsType)) {
                    //有调整
                    changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.INDIVIDUAL_MS_TYPE, currentIndividualMsType, snapShotIndividualMsType));
                }
            } else {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.INDIVIDUAL_MS_TYPE, currentIndividualMsType, ""));
            }
        }
        //当前值"" 快照有值 删除
        if("".equals(currentIndividualMsType) && StringUtils.isNotBlank(snapShotIndividualMsType)){
            changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.INDIVIDUAL_MS_TYPE, currentIndividualMsType, snapShotIndividualMsType));
        }
    }

    /**
     * 对比协议协议信息修改
     * @param orderModel
     * @param changedProperties
     * @param clearFields
     */
    private void compareAgreement(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties, List<String> clearFields) {
        String modifiedFieldsValue = orderModel.getModifiedFields().get(ModifiedFieldEnum.AGREEMENT_INFOS.getCode());
        if (ModifiedFieldValueEnum.INCREMENT_UPDATE.getCode().equals(modifiedFieldsValue)) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("协议信息不支持增量更新");
        }

        List<Agreement> targetList = orderModel.getAgreementDelegate().getAgreementList();
        List<Agreement> originList = orderModel.getOrderSnapshot().getAgreementDelegate().getAgreementList();

        if (ModifiedFieldValueEnum.ALL_COVER.getCode().equals(modifiedFieldsValue)) {
            //全量覆盖
            if (!orderModel.getAgreementDelegate().isEmpty()) {
                changedProperties.add(this.toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.AGREEMENTS, JSONUtils.beanToJSONDefault(targetList), JSONUtils.beanToJSONDefault(originList)));
            }
        } else if (ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFieldsValue)) {
            //全量删除
            changedProperties.add(this.toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.AGREEMENTS, null, JSONUtils.beanToJSONDefault(originList)));
            clearFields.add(ModifyItemConfigEnum.AGREEMENTS.getCode());
        }
    }

    /**
     * 比对财务附加费用信息
     * @param orderModel
     * @param changedProperties
     */
    public void compareFinanceAttachFees(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties, List<String> clearFields) {
        String modifiedFieldsValue = orderModel.getModifiedFields().get(ModifiedFieldEnum.ATTACH_FEES.getCode());

        List<CostInfo> targetList = orderModel.getFinance().getAttachFees();
        List<CostInfo> originList = (null != orderModel.getOrderSnapshot().getFinance()) ? orderModel.getOrderSnapshot().getFinance().getAttachFees() : null;
        if (ModifiedFieldValueEnum.ALL_COVER.getCode().equals(modifiedFieldsValue)) {
            //全量覆盖
            if (CollectionUtils.isNotEmpty(targetList)) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.ATTACH_FEES, JSONUtils.beanToJSONDefault(targetList), JSONUtils.beanToJSONDefault(originList)));
            }
        } else if (ModifiedFieldValueEnum.INCREMENT_UPDATE.getCode().equals(modifiedFieldsValue)) {
            throw new DomainAbilityException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("财务附加费用信息不支持增量更新");
        } else if (ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFieldsValue)) {
            //全量删除
            changedProperties.add(this.toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.ATTACH_FEES, null, JSONUtils.beanToJSONDefault(originList)));
            clearFields.add(ModifyItemConfigEnum.ATTACH_FEES.getCode());

            if (CollectionUtils.isNotEmpty(originList)) {
                for (CostInfo costInfo : originList) {
                    costInfo.setOperateType(OperateTypeEnum.DELETE);
                }
                //将需要删除的附加费信息赋值到model里，供后续节点使用
                orderModel.getFinance().setAttachFees(originList);
            }
        }
    }


    /**
     * 比对关务信息
     * @param orderModel
     * @param changedProperties
     */
    private void compareCustoms(ExpressOrderModel orderModel, List<String> clearFields, List<ChangedProperty> changedProperties){
        Customs customs = orderModel.getCustoms();
        Customs snapShot = orderModel.getOrderSnapshot().getCustoms();

        if (null == snapShot) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("原单不是港澳订单，不支持修改关务信息");
        }

        //始发流向
        if (customs.getStartFlowDirection() == null) {
            if (snapShot.getStartFlowDirection() != null) {
                if (clearFields.contains(ModifyItemConfigEnum.CUSTOMS_START_FLOW_DIRECTION.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.CUSTOMS_START_FLOW_DIRECTION, null, String.valueOf(snapShot.getStartFlowDirection().name())));
                } else {
                    if (CollectionUtils.isNotEmpty(clearFields)) {
                        clearFields.remove(ModifyItemConfigEnum.CUSTOMS_START_FLOW_DIRECTION.getCode());
                    }
                }
            }
        } else {
            if (snapShot.getStartFlowDirection() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.CUSTOMS_START_FLOW_DIRECTION, customs.getStartFlowDirection().name(), null));
            } else {
                OperateTypeEnum operateType = compareEnum(customs.getStartFlowDirection(), snapShot.getStartFlowDirection());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CUSTOMS_START_FLOW_DIRECTION, customs.getStartFlowDirection().name(), snapShot.getStartFlowDirection().name()));
                }
            }
        }

        //目的流向
        if (customs.getEndFlowDirection() == null) {
            if (snapShot.getEndFlowDirection() != null) {
                if (clearFields.contains(ModifyItemConfigEnum.CUSTOMS_END_FLOW_DIRECTION.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.CUSTOMS_END_FLOW_DIRECTION, null, snapShot.getEndFlowDirection().name()));
                } else {
                    if (CollectionUtils.isNotEmpty(clearFields)) {
                        clearFields.remove(ModifyItemConfigEnum.CUSTOMS_END_FLOW_DIRECTION.getCode());
                    }
                }
            }
        } else {
            if (snapShot.getEndFlowDirection() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.CUSTOMS_END_FLOW_DIRECTION, customs.getEndFlowDirection().name(), null));
            } else {
                OperateTypeEnum operateType = compareEnum(customs.getEndFlowDirection(), snapShot.getEndFlowDirection());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CUSTOMS_END_FLOW_DIRECTION, customs.getEndFlowDirection().name(), snapShot.getEndFlowDirection().name()));
                }
            }
        }

        //报关方式
        if (customs.getClearanceMode() != null) {
            OperateTypeEnum operateType = compareString(customs.getClearanceMode(), snapShot.getClearanceMode());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CUSTOMS_CLEARANCE_MODE, customs.getClearanceMode(), snapShot.getClearanceMode()));
            }
        }

        //报关类别
        if (customs.getClearanceType() != null) {
            OperateTypeEnum operateType = compareString(customs.getClearanceType(), snapShot.getClearanceType());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CUSTOMS_CLEARANCE_TYPE, customs.getClearanceType(), snapShot.getClearanceType()));
            }
        }

        //关务状态
        if (customs.getCustomsStatus() == null) {
            if (snapShot.getCustomsStatus() != null) {
                if (clearFields.contains(ModifyItemConfigEnum.CUSTOMS_CUSTOMS_STATUS.getCode())) {
                    changedProperties.add(toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.CUSTOMS_CUSTOMS_STATUS, null, String.valueOf(snapShot.getCustomsStatus().getCode())));
                } else {
                    if (CollectionUtils.isNotEmpty(clearFields)) {
                        clearFields.remove(ModifyItemConfigEnum.CUSTOMS_CUSTOMS_STATUS.getCode());
                    }
                }
            }
        } else {
            if (snapShot.getCustomsStatus() == null) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.CUSTOMS_CUSTOMS_STATUS, String.valueOf(customs.getCustomsStatus().getCode()), null));
            } else {
                OperateTypeEnum operateType = compareEnum(customs.getCustomsStatus(), snapShot.getCustomsStatus());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CUSTOMS_CUSTOMS_STATUS, String.valueOf(customs.getCustomsStatus().getCode()), String.valueOf(snapShot.getCustomsStatus().getCode())));
                }
            }
        }

        //是否文件
        if (customs.getFileTag() != null) {
            OperateTypeEnum operateType = compareInteger(customs.getFileTag(), snapShot.getFileTag());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CUSTOMS_FILE_TAG, String.valueOf(customs.getFileTag()), String.valueOf(snapShot.getFileTag())));
            }
        }

        //监管方式
        if (customs.getSupervision() != null) {
            OperateTypeEnum operateType = compareString(customs.getSupervision(), snapShot.getSupervision());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CUSTOMS_SUPERVISION, customs.getSupervision(), snapShot.getSupervision()));
            }
        }

        //成交方式
        if (customs.getTransactionMethod() != null) {
            OperateTypeEnum operateType = compareString(customs.getTransactionMethod(), snapShot.getTransactionMethod());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CUSTOMS_TRANSACTION_METHOD, customs.getTransactionMethod(), snapShot.getTransactionMethod()));
            }
        }

        //VAT号(增值税号)
        if (customs.getImporterVatNumber() != null) {
            OperateTypeEnum operateType = compareString(customs.getImporterVatNumber(), snapShot.getImporterVatNumber());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CUSTOMS_IMPORTER_VAT_NUMBER, customs.getImporterVatNumber(), snapShot.getImporterVatNumber()));
            }
        }

        //EORI号(经营者注册和识别号码)
        if (customs.getImporterEoriNumber() != null) {
            OperateTypeEnum operateType = compareString(customs.getImporterEoriNumber(), snapShot.getImporterEoriNumber());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CUSTOMS_IMPORTER_EORI_NUMBER, customs.getImporterEoriNumber(), snapShot.getImporterEoriNumber()));
            }
        }

        //IOSS号(IOSS增值税识别号)
        if (customs.getImporterIossNumber() != null) {
            OperateTypeEnum operateType = compareString(customs.getImporterIossNumber(), snapShot.getImporterIossNumber());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.CUSTOMS_IMPORTER_IOSS_NUMBER, customs.getImporterIossNumber(), snapShot.getImporterIossNumber()));
            }
        }

    }

    /**
     * 对比附件信息修改
     * @param orderModel
     * @param changedProperties
     * @param clearFields
     */
    private void compareAttachments(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties, List<String> clearFields) {
        String modifiedFieldsValue = orderModel.getModifiedFields().get(ModifiedFieldEnum.ATTACHMENT_INFOS.getCode());
        if (ModifiedFieldValueEnum.INCREMENT_UPDATE.getCode().equals(modifiedFieldsValue)) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("附件信息不支持增量更新");
        }

        List<Attachment> targetList = orderModel.getAttachments();
        List<Attachment> originList = orderModel.getOrderSnapshot().getAttachments();

        if (ModifiedFieldValueEnum.ALL_COVER.getCode().equals(modifiedFieldsValue)) {
            //全量覆盖
            if (CollectionUtils.isNotEmpty(targetList)) {
                changedProperties.add(this.toChangedProperty(OperateTypeEnum.UPDATE, ModifyItemConfigEnum.ATTACHMENTS, JSONUtils.beanToJSONDefault(targetList), JSONUtils.beanToJSONDefault(originList)));
            }
        } else if (ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modifiedFieldsValue)) {
            //全量删除
            changedProperties.add(this.toChangedProperty(OperateTypeEnum.DELETE, ModifyItemConfigEnum.ATTACHMENTS, null, JSONUtils.beanToJSONDefault(originList)));
            clearFields.add(ModifyItemConfigEnum.ATTACHMENTS.getCode());
        }
    }

    /**
     * 对比履约信息修改
     * @param orderModel
     * @param changedProperties
     */
    private void compareFulfillment(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties) {
        if (orderModel.getFulfillment() == null) {
            return;
        }

        Fulfillment fulfillment = orderModel.getFulfillment();
        Fulfillment snapShotFulfillment = orderModel.getOrderSnapshot().getFulfillment();
        // 对比实际揽收数量
        if (fulfillment.getActualReceivedQuantity() != null) {
            if (snapShotFulfillment != null && snapShotFulfillment.getActualReceivedQuantity() != null) {
                OperateTypeEnum operateType;
                // 对比值
                operateType = compareBigDecimal(fulfillment.getActualReceivedQuantity().getValue(), snapShotFulfillment.getActualReceivedQuantity().getValue());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.ACTUAL_RECEIVED_QUANTITY, String.valueOf(fulfillment.getActualReceivedQuantity().getValue()), String.valueOf(snapShotFulfillment.getActualReceivedQuantity().getValue())));
                }
                // 对比单位
                operateType = compareString(fulfillment.getActualReceivedQuantity().getUnit(), snapShotFulfillment.getActualReceivedQuantity().getUnit());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.ACTUAL_RECEIVED_QUANTITY_UNIT, String.valueOf(fulfillment.getActualReceivedQuantity().getUnit()), String.valueOf(snapShotFulfillment.getActualReceivedQuantity().getUnit())));
                }
            } else {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.ACTUAL_RECEIVED_QUANTITY, String.valueOf(fulfillment.getActualReceivedQuantity().getValue()), ""));
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.ACTUAL_RECEIVED_QUANTITY_UNIT, String.valueOf(fulfillment.getActualReceivedQuantity().getUnit()), ""));
            }
        }

        if(fulfillment.getPackageMaxLen() != null) {
            if(snapShotFulfillment != null && snapShotFulfillment.getPackageMaxLen() != null) {
                OperateTypeEnum operateType;
                // 对比值
                operateType = compareBigDecimal(fulfillment.getPackageMaxLen().getValue(), snapShotFulfillment.getPackageMaxLen().getValue());
                if (operateType != null) {
                    changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.PACKAGE_MAX_LEN, String.valueOf(fulfillment.getPackageMaxLen().getValue()), String.valueOf(snapShotFulfillment.getPackageMaxLen().getValue())));
                }
                // 对比单位
                if(snapShotFulfillment.getPackageMaxLen().getUnit() != null && fulfillment.getPackageMaxLen().getUnit() != null) {
                    operateType = compareString(fulfillment.getPackageMaxLen().getUnit().getCode(), snapShotFulfillment.getPackageMaxLen().getUnit().getCode());
                    if (operateType != null) {
                        changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.PACKAGE_MAX_LEN_UNIT, String.valueOf(fulfillment.getPackageMaxLen().getUnit().getCode()), String.valueOf(snapShotFulfillment.getPackageMaxLen().getUnit().getCode())));
                    }
                }
            } else {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.PACKAGE_MAX_LEN, String.valueOf(fulfillment.getPackageMaxLen().getValue()), ""));
                if(fulfillment.getPackageMaxLen().getUnit() != null){
                    changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.PACKAGE_MAX_LEN_UNIT, String.valueOf(fulfillment.getPackageMaxLen().getUnit().getCode()), ""));
                }
            }
        }
    }

    /**
     * 对比解决方案信息
     *
     * @param orderModel
     * @param changedProperties
     */
    private void compareBusinessSolution(ExpressOrderModel orderModel, List<ChangedProperty> changedProperties) {

        BusinessSolution businessSolution = orderModel.getBusinessSolution();
        // 没传 解决方案或解决方案编码 不对比
        if (businessSolution == null
                || businessSolution.getBusinessSolutionNo() == null) {
            return;
        }

        BusinessSolution snapshotBusinessSolution = orderModel.getOrderSnapshot().getBusinessSolution();

        // 对比解决方案编码
        // 从无到有
        if (snapshotBusinessSolution == null
                || snapshotBusinessSolution.getBusinessSolutionNo() == null) {
            // 原单解决方案为空
            if (StringUtils.isNotBlank(businessSolution.getBusinessSolutionNo())) {
                changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, ModifyItemConfigEnum.BUSINESS_SOLUTION_NO, businessSolution.getBusinessSolutionNo(), null));
            }

        } else {
            // 原单解决方案非空
            OperateTypeEnum operateType = compareString(businessSolution.getBusinessSolutionNo(), snapshotBusinessSolution.getBusinessSolutionNo());
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, ModifyItemConfigEnum.BUSINESS_SOLUTION_NO, businessSolution.getBusinessSolutionNo(), snapshotBusinessSolution.getBusinessSolutionNo()));
            }

        }

    }

    /**
     * 比对产品信息是否发生变化
     * @param product
     * @param originProduct
     */
    private void handleOperateTypeUpdate(Product product, Product originProduct) {
        // 目前只判断产品要素、扩展字段是否变化
        if (isModifiedProductMap(product.getProductAttrs(), originProduct.getProductAttrs())) {
            LOGGER.info("产品要素不一致");
            product.setOperateType(OperateTypeEnum.UPDATE);
        } else if (isModifiedProductMap(product.getExtendProps(), originProduct.getExtendProps())) {
            LOGGER.info("产品扩展字段不一致");
            product.setOperateType(OperateTypeEnum.UPDATE);
        } else if (product.getPattern() != null
                && originProduct.getPattern() != null
                && !Objects.equals(product.getPattern().getPatternNo(), originProduct.getPattern().getPatternNo())
        ) {
            LOGGER.info("产品模式不一致");
            product.setOperateType(OperateTypeEnum.UPDATE);
        }
        else {
            // 产品要素、扩展字段未变化，视为无变化，清空操作类型。
            product.setOperateType(null);
        }
    }

    /**
     * 判断主产品编码是否变化
     */
    private boolean isModifiedMainProductCode(IProduct mainProduct, IProduct originMainProduct) {
        return !mainProduct.getProductNo().equals(originMainProduct.getProductNo());
    }

    /**
     * 判断产品信息的产品要素或者扩展字段是否变化
     */
    private boolean isModifiedProductMap(Map<String, String> map, Map<String, String> originMap) {
        if (isEmptyMap(map) && isEmptyMap(originMap)) {
            return false;
        }
        if (isEmptyMap(map) || isEmptyMap(originMap)) {
            return true;
        }
        int newMapSize = map.size();
        int originMapSize = originMap.size();

        if (map.get(AttachmentKeyEnum.OPERATE_TYPE.getKey()) != null) {
            newMapSize = newMapSize - 1;
        }
        if (originMap.get(AttachmentKeyEnum.OPERATE_TYPE.getKey()) != null) {
            originMapSize = originMapSize - 1;
        }
        if (newMapSize != originMapSize) {
            return true;
        }

        // key对比不可忽略，避免后续对比方法，map1={key1=null},map2={key2=null}对比不出来
        HashSet<String> keySet = new HashSet<>(map.keySet());
        HashSet<String> originKeySet = new HashSet<>(originMap.keySet());
        // 排除operateType操作类型
        keySet.remove(AttachmentKeyEnum.OPERATE_TYPE.getKey());
        originKeySet.remove(AttachmentKeyEnum.OPERATE_TYPE.getKey());
        if (!keySet.equals(originKeySet)) {
            return true;
        }

        for (String key : map.keySet()) {
            if (AttachmentKeyEnum.OPERATE_TYPE.getKey().equals(key)) {
                continue;
            }
            String value = map.get(key);
            String originValue = originMap.get(key);
            if (!Objects.equals(value, originValue)) {
                // todo 有需求时再更进一步对比（value为集合类序列化时，可能顺序不同但实际相等）
                return true;
            }
        }

        return false;
    }

    /**
     * 是否空Map
     *
     * @param map
     * @return
     */
    private boolean isEmptyMap(Map<String, String> map) {
        if (MapUtils.isEmpty(map)) {
            return true;
        }
        //TODO 若上游传了operateType = null，需要特殊处理
        if (map.size() == 1 && null != map.get(AttachmentKeyEnum.OPERATE_TYPE.getKey())) {
            return true;
        }
        return false;
    }

    /**
     * 根据 ModifyItemConfigEnum 对比扩展字段-扩展信息中某一项
     */
    private void compareExtendInfos(ModifyItemConfigEnum modifyItemConfigEnum, ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot, List<ChangedProperty> changedProperties) {
        if (MapUtils.isEmpty(orderModel.getExtendProps())) {
            return;
        }

        // 修改值：从扩展字段-扩展信息根据键名获取值
        String value = getValueFromExtendInfos(modifyItemConfigEnum.getCode(), orderModel);
        if (value == null) {
            return;
        }

        // 原单值：从扩展字段-扩展信息根据键名获取值
        String snapValue = getValueFromExtendInfos(modifyItemConfigEnum.getCode(), orderSnapshot);
        if (snapValue == null) {
            changedProperties.add(toChangedProperty(OperateTypeEnum.INSERT, modifyItemConfigEnum, value, null));
        } else {
            OperateTypeEnum operateType = compareString(value, snapValue);
            if (operateType != null) {
                changedProperties.add(toChangedProperty(operateType, modifyItemConfigEnum, value, snapValue));
            }
        }
    }

    /**
     * 从扩展字段-扩展信息根据键名获取值
     */
    private String getValueFromExtendInfos(String key, ExpressOrderModel orderModel) {
        if (orderModel == null
                || orderModel.getExtendProps() == null) {
            return null;
        }
        String extendInfos = orderModel.getExtendProps().get(OrderConstants.EXTEND_INFOS);
        Map extendInfosMap = JSONUtils.jsonToMap(extendInfos);
        if (MapUtils.isEmpty(extendInfosMap)
                || extendInfosMap.get(key) == null) {
            return null;
        }
        return (String) extendInfosMap.get(key);
    }

    /**
     * 对比订单标识
     */
    private void compareOrderSign(ExpressOrderModel order, ExpressOrderModel snapShot, List<ChangedProperty> changedProperties) {
        // deliveryPickupSync：送取同步标识
        doCompareOrderSign(OrderSignEnum.DELIVERY_PICKUP_SYNC, ModifyItemConfigEnum.ORDER_SIGN_DELIVERY_PICKUP_SYNC, order, snapShot, changedProperties);
    }

    /**
     * 执行对比订单标识
     */
    private void doCompareOrderSign(OrderSignEnum orderSignEnum, ModifyItemConfigEnum modifyItemConfigEnum, ExpressOrderModel order, ExpressOrderModel snapShot, List<ChangedProperty> changedProperties) {
        String value = order.getOrderSignVal(orderSignEnum.getCode());
        String originValue = snapShot.getOrderSignVal(orderSignEnum.getCode());
        compareStringTemplateMethod(value, originValue, modifyItemConfigEnum, changedProperties);
    }
}