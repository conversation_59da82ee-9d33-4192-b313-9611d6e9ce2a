package cn.jdl.oms.express.domain.ability.coupon;

import cn.jdl.batrix.core.flow.domain.BDomainFlowNode;
import cn.jdl.oms.express.domain.ability.AbstractDomainAbility;
import cn.jdl.oms.express.domain.annotation.AbilityScene;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.coupon.ICouponReleaseExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.issue.ModifyIssueFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.coupon.FreightCouponTranslator;
import cn.jdl.oms.express.domain.infrs.ohs.locals.es.orderflow.ExpressOrderFlowService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.CallbackCouponProcessMessageDto;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.vo.Ticket;
import cn.jdl.oms.express.shared.common.constant.DomainConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.CouponStatusEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import com.alibaba.fastjson.JSONObject;
import com.jd.matrix.core.annotation.DomainAbility;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;

@DomainAbility(name = "纯配回传领域能力-优惠券释放活动能力", parent = DomainConstants.EXPRESS_ORDER_DOMIAN_CODE)
@AbilityScene(businessScenes = {BusinessSceneEnum.CALLBACK}, isDefault = false)
public class CallbackCouponReleaseAbility extends AbstractDomainAbility<ExpressOrderContext, ICouponReleaseExtension> {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(CallbackCouponReleaseAbility.class);

    /**
     * 快运优惠券占用释放参数转换
     */
    @Resource
    private FreightCouponTranslator freightCouponTranslator;

    /**
     * 修改下发服务
     */
    @Resource
    private ModifyIssueFacade modifyIssueFacade;

    /**
     * 纯配订单jmq服务生产者
     */
    @Resource
    private ExpressOrderFlowService expressOrderFlowService;

    @Override
    public void execute(ExpressOrderContext expressOrderContext, BDomainFlowNode bDomainFlowNode) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("纯配回传领域能力-优惠券释放活动执行开始");
            cancelReleaseCoupon(expressOrderContext.getOrderModel());
            LOGGER.info("纯配回传领域能力-优惠券释放活动执行结束");
        } catch (DomainAbilityException e) {
            LOGGER.error("纯配回传领域能力-优惠券释放能力活动执行异常: {}", e.fullMessage());
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_COUPON_RELEASE_EXCEPTION_ALARM, "回传优惠券释放能力活动执行异常,orderNo=" + expressOrderContext.getOrderModel().orderNo());
            throw e;
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("纯配回传领域能力-优惠券释放能力活动执行异常 ", e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_COUPON_RELEASE_EXCEPTION_ALARM, "回传优惠券释放能力活动执行异常,orderNo=" + expressOrderContext.getOrderModel().orderNo());
            throw new DomainAbilityException(UnifiedErrorSpec.BasisOrder.COUPON_RELEASE_FAIL, e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 回传异步释放优惠券信息
     *
     * @param model
     * @return
     */
    private void cancelReleaseCoupon(ExpressOrderModel model) {
        ExpressOrderModel snapshot = model.getOrderSnapshot();

        if (model.getOrderStatus() == null || model.getOrderStatus().isBeforePickedUp()) {
            LOGGER.info("当前订单【不满足】揽收完成后状态，无需后续处理");
            return;
        }

        boolean existDeletePendingFlag = false;

        if (snapshot.isC2C()) {
            if (snapshot.getPromotion() == null || CollectionUtils.isEmpty(snapshot.getPromotion().getTickets())) {
                LOGGER.info("未使用优惠券，不用释放优惠券");
                return;
            }
            LOGGER.info("当前订单【满足】回传释放优惠券卡控：快递C2C & 揽收完成后状态");
            List<Ticket> tickets = snapshot.getPromotion().getTickets();
            for (Ticket ticket : tickets) {
                if (CouponStatusEnum.DELETE_PENDING.getCode().equals(ticket.getCouponStatus())) {
                    existDeletePendingFlag = true;
                    break;
                }
            }
        }

        // 揽收后回传状态的待回滚优惠券处理逻辑
        if (existDeletePendingFlag) {
            CallbackCouponProcessMessageDto callbackCouponProcessMessageDto = new CallbackCouponProcessMessageDto();
            callbackCouponProcessMessageDto.setRequestProfile(model.requestProfile());
            callbackCouponProcessMessageDto.setOrderNo(model.orderNo());
            callbackCouponProcessMessageDto.setOperator(snapshot.getOperator());
            LOGGER.info("当前订单【满足】回传释放优惠券卡控：快递C2C & 揽收完成后状态，且优惠券状态为【待回滚】，进行异步处理流程，callbackCouponProcessMessageDto:{}", JSONObject.toJSONString(callbackCouponProcessMessageDto));
            expressOrderFlowService.sendCallbackCouponProcessMq(callbackCouponProcessMessageDto);
        }
    }

    @Override
    public ICouponReleaseExtension getDefaultExtension() {
        return null;
    }
}
