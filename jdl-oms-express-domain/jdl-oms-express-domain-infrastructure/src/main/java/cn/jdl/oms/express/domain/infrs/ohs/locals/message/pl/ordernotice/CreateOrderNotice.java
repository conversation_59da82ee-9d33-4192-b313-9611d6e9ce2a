package cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ordernotice;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.ConsigneeInfo;
import cn.jdl.oms.core.model.ConsignorInfo;
import cn.jdl.oms.core.model.CustomerInfo;
import cn.jdl.oms.core.model.CustomsInfo;
import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.ProductInfo;
import cn.jdl.oms.core.model.ShipmentInfo;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.entity.CommonDto;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class CreateOrderNotice extends CommonDto {
    private RequestProfile profile;

    private Data data;

    /**
     * 消息属性
     */
    @JSONField(serialize = false)
    private Map<String, String> msgAttributes;

    @lombok.Data
    public static class Data {
        /**
         * 业务身份
         */
        private BusinessIdentity businessIdentity;

        /**
         * 交易订单号
         */
        private String orderNo;
        /**
         * 业务自定义单号	值是：运单号
         */
        private String customOrderNo;
        /**
         * 交易单据类型(500-纯配、504-逆向、505-改址)
         */
        private String orderType;
        /**
         * 交易客户信息
         */
        private CustomerInfo customerInfo;
        /**
         * 渠道信息
         */
        private ChannelInfo channelInfo;
        /**
         * 交易产品/增值产品信息
         */
        private List<ProductInfo> productInfos;

        /**
         * 寄件人信息
         */
        private ConsignorInfo consignorInfo;

        /**
         * 收件人信息
         */
        private ConsigneeInfo consigneeInfo;

        /**
         * 财务信息
         */
        private FinanceInfo financeInfo;

        /**
         * 配送信息
         */
        private ShipmentInfo shipmentInfo;

        /**
         * 报关信息
         */
        private CustomsInfo customsInfo;

        /**
         * 下单人类型(改址单必填,4寄件人(默认4),5收件人,6客服)
         */
        private Integer initiatorType;
        /**
         * 订单备注
         */
        private String remark;
        /**
         * 下单人唯一标识
         */
        private String operator;

        /**
         * 订单中心下单时间
         */
        private Date operateTime;

        /**
         * 扩展信息
         */
        private Map<String, String> extendProps;

    }
}
