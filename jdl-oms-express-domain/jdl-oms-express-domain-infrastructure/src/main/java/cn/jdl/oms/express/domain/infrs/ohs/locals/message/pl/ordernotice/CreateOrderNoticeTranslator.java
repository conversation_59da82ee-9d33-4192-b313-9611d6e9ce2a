package cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ordernotice;

import cn.jdl.oms.core.model.AddressInfo;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.ConsigneeInfo;
import cn.jdl.oms.core.model.ConsignorInfo;
import cn.jdl.oms.core.model.CustomsInfo;
import cn.jdl.oms.core.model.ProductInfo;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderBroadcastConverter;
import cn.jdl.oms.express.domain.infrs.ohs.locals.security.TdeAcl;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.ContextInfoEnum;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.utils.ContextInfoUtil;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.Customs;
import cn.jdl.oms.express.domain.vo.Product;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 订单消息通知转换类
 * @Author： liupinxun
 * @CreateDate 2022/12/11 22:55
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
@Translator
public class CreateOrderNoticeTranslator {
    /**
     * 安全加密工具类
     */
    @Resource
    private TdeAcl tdeAcl;

    public CreateOrderNotice toCreateOrderNotice(ExpressOrderContext orderContext) throws Exception {
        CreateOrderNotice createOrderNotice = new CreateOrderNotice();
        createOrderNotice.setProfile(orderContext.getRequestProfile());
        ExpressOrderModel orderModel = orderContext.getOrderModel();
        CreateOrderNotice.Data data = new CreateOrderNotice.Data();
        //业务身份
        data.setBusinessIdentity(orderModel.getBusinessIdentity());
        //订单号
        data.setOrderNo(orderModel.orderNo());
        //业务单号
        data.setCustomOrderNo(orderModel.getCustomOrderNo());
        //订单类型
        data.setOrderType(orderModel.getOrderType() == null ? null : orderModel.getOrderType().getCode());
        //客户信息
        data.setCustomerInfo(OrderBroadcastConverter.toCustomerInfo(orderModel));
        //渠道信息
        data.setChannelInfo(toChannelInfo(orderModel.getChannel()));
        //产品信息
        data.setProductInfos(toProductInfos(orderModel.getProductDelegate().getProducts()));
        //发货人信息
        data.setConsignorInfo(toConsignorInfo(orderModel.getConsignor()));
        //收货人信息
        data.setConsigneeInfo(toConsigneeInfo(orderModel.getConsignee()));
        //财务信息
        data.setFinanceInfo(OrderBroadcastConverter.toFinanceInfo(orderModel));
        // 配送信息
        data.setShipmentInfo(OrderBroadcastConverter.toShipmentInfo(orderModel.getShipment()));
        // 报关信息
        data.setCustomsInfo(toCustomsInfo(orderModel.getCustoms()));
        //操作人
        data.setOperator(orderModel.getOperator());
        //操作时间
        data.setOperateTime(orderModel.getOperateTime());
        //扩展信息
        data.setExtendProps(orderModel.getExtendProps());
        createOrderNotice.setData(data);

        Map<String, String> attributes = new HashMap<>();
        attributes.put("businessUnit", orderModel.getBusinessIdentity().getBusinessUnit());
        attributes.put("businessScene", orderModel.getBusinessIdentity().getBusinessScene());

        // 获取审核系统标识，有则需要设置MQ属性 verifySystem
        String verifySystem = ContextInfoUtil.getVerifySystem(orderContext);
        if (StringUtils.isNotBlank(verifySystem)) {
            attributes.put(ContextInfoEnum.VERIFY_SYSTEM.getCode(), verifySystem);
        }
        createOrderNotice.setMsgAttributes(attributes);

        return createOrderNotice;
    }

    /**
     * 报关信息转换
     * @param customs
     * @return
     */
    private CustomsInfo toCustomsInfo(Customs customs) {
        if (null == customs) {
            return null;
        }
        CustomsInfo customsInfo = new CustomsInfo();
        customsInfo.setStartFlowDirection(null == customs.getStartFlowDirection() ? null : customs.getStartFlowDirection().name());
        customsInfo.setEndFlowDirection(null == customs.getEndFlowDirection() ? null : customs.getEndFlowDirection().name());
        return customsInfo;
    }

    /**
     * 接单下发渠道信息转换
     *
     * @param channel
     * @return
     */
    private ChannelInfo toChannelInfo(Channel channel) {
        if (channel == null) {
            return null;
        }

        ChannelInfo channelInfo = new ChannelInfo();
        channelInfo.setChannelNo(channel.getChannelNo());
        channelInfo.setChannelCustomerNo(channel.getChannelCustomerNo());
        channelInfo.setChannelOrderNo(channel.getChannelOrderNo());
        channelInfo.setCustomerOrderNo(channel.getCustomerOrderNo());
        channelInfo.setChannelOperateTime(channel.getChannelOperateTime());
        channelInfo.setSecondLevelChannel(channel.getSecondLevelChannel());
        channelInfo.setSecondLevelChannelOrderNo(channel.getSecondLevelChannelOrderNo());
        channelInfo.setSecondLevelChannelCustomerNo(channel.getSecondLevelChannelCustomerNo());
        channelInfo.setSystemCaller(channel.getSystemCaller().getCode());
        channelInfo.setSystemSubCaller(channel.getSystemSubCaller());
        channelInfo.setExtendProps(channel.getExtendProps());

        return channelInfo;
    }


    /**
     * 接单下发产品信息转换
     *
     * @param products 产品集合
     * @return List<ProductInfo>
     */
    private List<ProductInfo> toProductInfos(List<? extends IProduct> products) {
        if (CollectionUtils.isEmpty(products)) {
            return null;
        }

        List<Product> productList = (List<Product>) products;
        return productList.stream().map(product -> {
            ProductInfo productInfo = new ProductInfo();
            productInfo.setProductNo(product.getProductNo());
            productInfo.setProductName(product.getProductName());
            productInfo.setProductType(product.getProductType());
            productInfo.setParentNo(product.getParentNo());
            productInfo.setProductAttrs(product.getProductAttrs());
            productInfo.setExtendProps(product.getExtendProps());
            return productInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 发货人信息转换
     *
     * @param consignor
     * @return
     */
    private ConsignorInfo toConsignorInfo(Consignor consignor) throws Exception {
        if (consignor == null) {
            return null;
        }

        ConsignorInfo consignorInfo = new ConsignorInfo();
        //发货人姓名
        consignorInfo.setConsignorName(tdeAcl.encrypt(consignor.getConsignorName()));
        //发货人手机
        consignorInfo.setConsignorMobile(tdeAcl.encrypt(consignor.getConsignorMobile()));
        //发货人电话
        consignorInfo.setConsignorPhone(tdeAcl.encrypt(consignor.getConsignorPhone()));
        //发货人地址信息
        consignorInfo.setAddressInfo(this.toAddressInfo(consignor.getAddress()));
        return consignorInfo;
    }

    /**
     * 收货人信息
     *
     * @param consignee
     * @return
     */
    private ConsigneeInfo toConsigneeInfo(Consignee consignee) throws Exception {
        if (consignee == null) {
            return null;
        }

        ConsigneeInfo consigneeInfo = new ConsigneeInfo();
        //收货人姓名
        consigneeInfo.setConsigneeName(tdeAcl.encrypt(consignee.getConsigneeName()));
        //收货人手机号
        consigneeInfo.setConsigneeMobile(tdeAcl.encrypt(consignee.getConsigneeMobile()));
        //收货人电话
        consigneeInfo.setConsigneePhone(tdeAcl.encrypt(consignee.getConsigneePhone()));
        //收货人地址信息
        consigneeInfo.setAddressInfo(this.toAddressInfo(consignee.getAddress()));
        return consigneeInfo;
    }

    /**
     * 地址信息转换
     * @param address
     * @return
     */
    private AddressInfo toAddressInfo(Address address) throws Exception {
        if (null == address) {
            return null;
        }
        AddressInfo addressInfo = new AddressInfo();
        addressInfo.setProvinceNo(address.getProvinceNo());
        addressInfo.setCityNo(address.getCityNo());
        addressInfo.setCityName(address.getCityName());
        addressInfo.setCountyNo(address.getCountyNo());
        addressInfo.setTownNo(address.getTownNo());
        addressInfo.setProvinceNoGis(address.getProvinceNoGis());
        addressInfo.setCityNoGis(address.getCityNoGis());
        addressInfo.setCountyNoGis(address.getCountyNoGis());
        addressInfo.setTownNoGis(address.getTownNoGis());

        addressInfo.setAddress(tdeAcl.encrypt(address.getAddress()));
        return addressInfo;
    }

}
