package cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl;

import cn.jdl.oms.express.domain.infrs.ohs.locals.message.entity.CommonDto;
import lombok.Data;

import java.util.Date;

/**
 * 回传优惠券处理发送消息体
 *
 * <AUTHOR>
 * @create 2025-08-31 14:26
 **/
@Data
public class CallbackCouponProcessMessageDto extends CommonDto {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 操作人
     */
    private String operator;
}
