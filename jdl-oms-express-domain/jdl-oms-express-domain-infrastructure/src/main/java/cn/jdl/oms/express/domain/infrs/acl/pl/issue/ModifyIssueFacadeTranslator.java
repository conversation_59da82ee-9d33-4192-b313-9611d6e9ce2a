package cn.jdl.oms.express.domain.infrs.acl.pl.issue;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.ofc.api.oms.common.model.ModifyOrderOfcData;
import cn.jdl.oms.core.model.ActivityInfo;
import cn.jdl.oms.core.model.AddressInfo;
import cn.jdl.oms.core.model.AgreementInfo;
import cn.jdl.oms.core.model.AttachmentInfo;
import cn.jdl.oms.core.model.BusinessSolutionInfo;
import cn.jdl.oms.core.model.CargoInfo;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.ConsigneeInfo;
import cn.jdl.oms.core.model.ConsignorInfo;
import cn.jdl.oms.core.model.CostInfo;
import cn.jdl.oms.core.model.CustomerInfo;
import cn.jdl.oms.core.model.CustomsInfo;
import cn.jdl.oms.core.model.DeductionInfo;
import cn.jdl.oms.core.model.DimensionInfo;
import cn.jdl.oms.core.model.DiscountInfo;
import cn.jdl.oms.core.model.FenceInfo;
import cn.jdl.oms.core.model.FinanceDetailInfo;
import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.FulfillmentInfo;
import cn.jdl.oms.core.model.GoodsInfo;
import cn.jdl.oms.core.model.LengthInfo;
import cn.jdl.oms.core.model.ModifyData;
import cn.jdl.oms.core.model.ModifyDataInfo;
import cn.jdl.oms.core.model.MoneyInfo;
import cn.jdl.oms.core.model.PatternInfo;
import cn.jdl.oms.core.model.PointsInfo;
import cn.jdl.oms.core.model.ProductInfo;
import cn.jdl.oms.core.model.PromotionInfo;
import cn.jdl.oms.core.model.QuantityInfo;
import cn.jdl.oms.core.model.RefOrderInfo;
import cn.jdl.oms.core.model.ReturnInfo;
import cn.jdl.oms.core.model.ShipmentInfo;
import cn.jdl.oms.core.model.TicketInfo;
import cn.jdl.oms.core.model.VolumeInfo;
import cn.jdl.oms.core.model.WarehouseInfo;
import cn.jdl.oms.core.model.WeightInfo;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.converter.AttachmentMapper;
import cn.jdl.oms.express.domain.converter.CustomsMapper;
import cn.jdl.oms.express.domain.converter.DeductionMapper;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.converter.SerialMapper;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductAttrEnum;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.FenceTrustEnum;
import cn.jdl.oms.express.domain.spec.dict.LengthTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OFCSysSourceEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.ReaddressOperateTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ReaddressParamEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceProductTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SyncSourceEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.spec.dict.VolumeTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.WeightTypeEnum;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.utils.OrderDataFieldEnum;
import cn.jdl.oms.express.domain.vo.Activity;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.AgreementDelegate;
import cn.jdl.oms.express.domain.vo.Attachment;
import cn.jdl.oms.express.domain.vo.BusinessSolution;
import cn.jdl.oms.express.domain.vo.Cargo;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Customs;
import cn.jdl.oms.express.domain.vo.Deduction;
import cn.jdl.oms.express.domain.vo.DeductionDelegate;
import cn.jdl.oms.express.domain.vo.Dimension;
import cn.jdl.oms.express.domain.vo.Discount;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.FinanceDetail;
import cn.jdl.oms.express.domain.vo.Fulfillment;
import cn.jdl.oms.express.domain.vo.Goods;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.Pattern;
import cn.jdl.oms.express.domain.vo.Points;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.Promotion;
import cn.jdl.oms.express.domain.vo.Quantity;
import cn.jdl.oms.express.domain.vo.RefOrderDelegate;
import cn.jdl.oms.express.domain.vo.ReturnInfoVo;
import cn.jdl.oms.express.domain.vo.Shipment;
import cn.jdl.oms.express.domain.vo.Ticket;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Warehouse;
import cn.jdl.oms.express.domain.vo.Weight;
import cn.jdl.oms.express.domain.vo.modify.ChangedPropertyDelegate;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.constant.ModifySceneRuleConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.dict.CouponStatusEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldValueEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.DateUtils;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ProjectName：jdl-oms-express
 * @Package： cn.jdl.oms.express.domain.infs.acl.pl.issue
 * @ClassName: CreateIssueFacadeTranslator
 * @Description: 修改下发防腐层参数转换
 * @Author： wanghuanhuan632
 * @CreateDate 2021/3/20 11:40
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
@Translator
public class ModifyIssueFacadeTranslator {

    private static final Logger LOGGER = LoggerFactory.getLogger(ModifyIssueFacadeTranslator.class);

    private static final String CONSIGNOR = "consignor"; //发货
    private static final String CONSIGNEE = "consignee"; //收货
    private static final Integer SUCCESS = 1;


    public ModifyIssueFacadeRequest toEnquiryIssueFacadeRequest(ExpressOrderContext context, boolean syncModify) throws ParseException {
        if (context == null || context.getOrderModel() == null) {
            return null;
        }

        ExpressOrderModel orderModel = context.getOrderModel();
        ExpressOrderModel snapshot = orderModel.getOrderSnapshot();
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = new ModifyIssueFacadeRequest();
        modifyIssueFacadeRequest.setRequestProfile(orderModel.requestProfile());
        modifyIssueFacadeRequest.setOrderNo(orderModel.orderNo());
        modifyIssueFacadeRequest.setCustomOrderNo(!StringUtils.isBlank(orderModel.getCustomOrderNo()) ? orderModel.getCustomOrderNo() : orderModel.getOrderSnapshot().getCustomOrderNo());
        // 业务身份
        modifyIssueFacadeRequest.setBusinessIdentity(orderModel.toBusinessIdentity());
        //渠道信息
        modifyIssueFacadeRequest.setChannelInfo(this.toChannelInfo(orderModel.getChannel(), syncModify));
        //客户信息
        modifyIssueFacadeRequest.setCustomerInfo(this.toCustomerInfo(snapshot.getCustomer()));
        //财务信息
        modifyIssueFacadeRequest.setFinanceInfo(this.toFinanceInfo(orderModel));
        //营销信息
        modifyIssueFacadeRequest.setPromotionInfo(this.toPromotionInfo(orderModel.getPromotion(), orderModel));
        // 下单人类型
        modifyIssueFacadeRequest.setInitiatorType(orderModel.getInitiatorType() != null ? orderModel.getInitiatorType().getCode()
                : null);
        modifyIssueFacadeRequest.setOperator(orderModel.getOperator());
        modifyIssueFacadeRequest.setRemark(orderModel.getRemark());
        //扩展字段
        Map<String, String> ext;
        if(MapUtils.isNotEmpty(orderModel.getExtendProps())){
            ext = new HashMap<>(orderModel.getExtendProps());
        } else {
            ext = new HashMap<>();
        }
        ext.put(OrderConstants.BUSINESS_FLOW_FLAG,"1");//订单中心修改
        modifyIssueFacadeRequest.setExtendProps(ext);

        modifyIssueFacadeRequest.setSyncSource(orderModel.getSyncSource());

        // 订单标识
        modifyIssueFacadeRequest.setOrderSign(orderModel.getOrderSign());

        return modifyIssueFacadeRequest;
    }

    public ModifyIssueFacadeRequest toEnquiryOrderBankIssueFacadeRequest(ExpressOrderContext context, boolean syncModify) throws ParseException {
        if (context == null || context.getOrderModel() == null) {
            return null;
        }

        ExpressOrderModel orderModel = context.getOrderModel();
        ExpressOrderModel snapshot = orderModel.getOrderSnapshot();
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = new ModifyIssueFacadeRequest();
        modifyIssueFacadeRequest.setRequestProfile(orderModel.requestProfile());
        modifyIssueFacadeRequest.setOrderNo(orderModel.orderNo());
        modifyIssueFacadeRequest.setCustomOrderNo(!StringUtils.isBlank(orderModel.getCustomOrderNo()) ? orderModel.getCustomOrderNo() : orderModel.getOrderSnapshot().getCustomOrderNo());
        // 业务身份
        modifyIssueFacadeRequest.setBusinessIdentity(orderModel.toBusinessIdentity());
        //渠道信息
        modifyIssueFacadeRequest.setChannelInfo(this.toChannelInfo(orderModel.getChannel(), syncModify));
        //客户信息
        modifyIssueFacadeRequest.setCustomerInfo(this.toCustomerInfo(snapshot.getCustomer()));
        //财务信息
        modifyIssueFacadeRequest.setFinanceInfo(this.toFinanceInfo(snapshot));

        // 仅写帐（不询价）重新赋值接口传入的支付方式
        if (null != orderModel.getFinance().getPayment()) {
            modifyIssueFacadeRequest.getFinanceInfo().setPayment(orderModel.getFinance().getPayment().getCode());
        }

        //营销信息
        modifyIssueFacadeRequest.setPromotionInfo(this.toPromotionInfo(snapshot.getPromotion(), orderModel));
        // 下单人类型
        modifyIssueFacadeRequest.setInitiatorType(orderModel.getInitiatorType() != null ? orderModel.getInitiatorType().getCode()
                : null);
        modifyIssueFacadeRequest.setOperator(orderModel.getOperator());
        modifyIssueFacadeRequest.setRemark(orderModel.getRemark());
        //扩展字段
        Map ext;
        if(MapUtils.isNotEmpty(orderModel.getExtendProps())){
            ext = orderModel.getExtendProps();
        } else {
            ext = new HashMap();
        }
        ext.put(OrderConstants.BUSINESS_FLOW_FLAG,"1");//订单中心修改
        modifyIssueFacadeRequest.setExtendProps(ext);

        modifyIssueFacadeRequest.setSyncSource(orderModel.getSyncSource());

        // 订单标识
        modifyIssueFacadeRequest.setOrderSign(orderModel.getOrderSign());

        return modifyIssueFacadeRequest;
    }

    /**
     * 修改下发请求转换
     *
     * @param context
     * @return
     * <AUTHOR>
     */
    public ModifyIssueFacadeRequest toModifyIssueFacadeRequest(ExpressOrderContext context,boolean syncModify) throws ParseException {
        if (context == null || context.getOrderModel() == null) {
            return null;
        }
        ExpressOrderModel orderModel = context.getOrderModel();
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = new ModifyIssueFacadeRequest();
        modifyIssueFacadeRequest.setRequestProfile(orderModel.requestProfile());
        modifyIssueFacadeRequest.setOrderNo(orderModel.orderNo());
        modifyIssueFacadeRequest.setCustomOrderNo(!StringUtils.isBlank(orderModel.getCustomOrderNo()) ? orderModel.getCustomOrderNo() : orderModel.getOrderSnapshot().getCustomOrderNo());
        //渠道信息
        modifyIssueFacadeRequest.setChannelInfo(this.toChannelInfo(orderModel.getChannel(), syncModify));
        //客户信息
        modifyIssueFacadeRequest.setCustomerInfo(this.toCustomerInfo(orderModel.getCustomer()));
        //产品信息
        boolean businessSolutionNoHaveChange = false;
        if (context.getChangedPropertyDelegate() != null) {
            businessSolutionNoHaveChange = context.getChangedPropertyDelegate().businessSolutionNoHaveChange();
        }
        modifyIssueFacadeRequest.setProductInfos(this.toProductInfoList(orderModel.getProductDelegate() != null
                ? orderModel.getProductDelegate().getProducts() : null, orderModel, businessSolutionNoHaveChange));
        //发货人信息
        modifyIssueFacadeRequest.setConsignorInfo(this.toConsignorInfo(orderModel.getConsignor()));
        //收货人信息
        modifyIssueFacadeRequest.setConsigneeInfo(this.toConsigneeInfo(orderModel.getConsignee()));
        //货品信息
        if (orderModel.getCargoDelegate() != null) {
            List<Cargo> cargoList = (List<Cargo>) orderModel.getCargoDelegate().getCargoList();
            modifyIssueFacadeRequest.setCargoInfos(this.toCargoInfoList(cargoList));
        }
        //商品信息
        if(null != orderModel.getGoodsDelegate() && CollectionUtils.isNotEmpty(orderModel.getGoodsDelegate().getGoodsList())){
            List<Goods> goodsList = (List<Goods>) orderModel.getGoodsDelegate().getGoodsList();
            modifyIssueFacadeRequest.setGoodsInfos(this.toGoodsInfoList(goodsList));
        }
        //配送要求信息
        modifyIssueFacadeRequest.setShipmentInfo(this.toShipmentInfo(orderModel.getShipment()));
        //财务信息
        modifyIssueFacadeRequest.setFinanceInfo(this.toFinanceInfo(orderModel));

        // 经产品【齐鑫磊】确认，特殊替换逻辑原因：运单costInfos仅支持多方计费
        filterCostInfosBySettlementType(modifyIssueFacadeRequest, orderModel, orderModel.getOrderSnapshot());

        //营销信息
        modifyIssueFacadeRequest.setPromotionInfo(this.toPromotionInfo(orderModel.getPromotion(), orderModel));
        // 协议信息
        modifyIssueFacadeRequest.setAgreementInfos(this.toAgreementInfos(orderModel.getAgreementDelegate()));
        // 关联单信息
        modifyIssueFacadeRequest.setRefOrderInfo(this.toRefOrderInfo(orderModel));
        // 下单人类型
        modifyIssueFacadeRequest.setInitiatorType(orderModel.getInitiatorType() != null ? orderModel.getInitiatorType().getCode()
                : null);
        modifyIssueFacadeRequest.setOperator(orderModel.getOperator());
        modifyIssueFacadeRequest.setRemark(orderModel.getRemark());
        //扩展字段
        Map<String, String> ext = new HashMap();
        // 防止改变入参扩展字段，此处拷贝一份
        if (MapUtils.isNotEmpty(orderModel.getExtendProps())) {
            for (Map.Entry<String, String> entry : orderModel.getExtendProps().entrySet()) {
                ext.put(entry.getKey(), entry.getValue());
            }
        }
        ext.put(OrderConstants.BUSINESS_FLOW_FLAG, "1");//订单中心修改
        // 内部修改下发时，将修改策略改为 specialModify
        if (ext.containsKey(ModifySceneRuleConstants.MODIFY_SCENE_RULE)) {
            String modifySceneRule = String.valueOf(ext.get(ModifySceneRuleConstants.MODIFY_SCENE_RULE));
            if (StringUtils.isNotBlank(modifySceneRule)
                    && modifySceneRule.startsWith(ModifySceneRuleConstants.INTERNAL_MODIFY_PREFIX)) {

                ext.put(ModifySceneRuleConstants.MODIFY_SCENE_RULE, ModifySceneRuleConstants.SPECIAL_MODIFY);

            }
        }
        modifyIssueFacadeRequest.setExtendProps(ext);

        // 订单标识
        modifyIssueFacadeRequest.setOrderSign(orderModel.getOrderSign());
        // 跨境报关信息
        modifyIssueFacadeRequest.setCustomsInfo(this.toCustomsInfo(orderModel.getCustoms()));
        // 附件信息
        modifyIssueFacadeRequest.setAttachmentInfos(toAttachmentInfos(orderModel.getAttachments()));
        // 订单总净重
        modifyIssueFacadeRequest.setOrderNetWeight(this.toWeightInfo(orderModel.getOrderNetWeight()));
        //订单总毛重
        modifyIssueFacadeRequest.setOrderWeight(this.toWeightInfo(orderModel.getOrderWeight()));
        //订单总体积
        modifyIssueFacadeRequest.setOrderVolume(this.toVolumeInfo(orderModel.getOrderVolume()));

        //需清空的字段数据
        clearFieldData(modifyIssueFacadeRequest, orderModel);
        modifyIssueFacadeRequest.setBusinessIdentity(orderModel.toBusinessIdentity());
        modifyIssueFacadeRequest.setReturnInfo(this.toReturnInfo(orderModel.getReturnInfoVo()));
        modifyIssueFacadeRequest.setFulfillmentInfo(this.toFulfillmentInfo(orderModel.getFulfillment()));
        modifyIssueFacadeRequest.setSyncSource(orderModel.getSyncSource());
        //
        modifyIssueFacadeRequest.setModifyData(this.toModifyData(context));
        // 解决方案信息
        modifyIssueFacadeRequest.setBusinessSolutionInfo(this.toBusinessSolutionInfo(orderModel.getBusinessSolution()));

        return modifyIssueFacadeRequest;
    }

    /**
     * 解决方案信息转换
     *
     * @param businessSolution
     * @return
     */
    private BusinessSolutionInfo toBusinessSolutionInfo(BusinessSolution businessSolution) {
        if (businessSolution == null) {
            return null;
        }
        BusinessSolutionInfo solutionInfo = new BusinessSolutionInfo();
        solutionInfo.setBusinessSolutionNo(businessSolution.getBusinessSolutionNo());
        solutionInfo.setBusinessSolutionName(businessSolution.getBusinessSolutionName());
        solutionInfo.setProductAttrs(businessSolution.getProductAttrs());
        return solutionInfo;
    }

    private static void filterCostInfosBySettlementType(ModifyIssueFacadeRequest modifyIssueFacadeRequest, ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        // 特殊替换逻辑，原因：运单costInfos仅支持多方计费
        if (Objects.nonNull(orderModel.getFinance())) {
            SettlementTypeEnum settlementType = orderModel.getFinance().getSettlementType();
            if (null == settlementType && null != orderSnapshot && null != orderSnapshot.getFinance()) {
                settlementType = orderSnapshot.getFinance().getSettlementType();
            }
            if (null != settlementType && SettlementTypeEnum.CHARGE_MULTIPLE_PARTIES != settlementType
                    && Objects.nonNull(modifyIssueFacadeRequest.getFinanceInfo()) && CollectionUtils.isNotEmpty(modifyIssueFacadeRequest.getFinanceInfo().getCostInfos())) {
                modifyIssueFacadeRequest.getFinanceInfo().setCostInfos(null);
            }
        }
    }

    /**
     * 退货信息
     * @param returnInfoVo
     * @return
     */
    private ReturnInfo toReturnInfo(ReturnInfoVo returnInfoVo){
        ReturnInfo returnInfo = new ReturnInfo();
        returnInfo.setReturnType(returnInfoVo.getReturnType());
        returnInfo.setReturnConsigneeInfo(toConsigneeInfo(returnInfoVo.getConsignee()));
        return returnInfo;
    }

    private <T> boolean isNullObject(Object obj, Class<T> clazz) {
        if (obj == null) {
            return true;
        }
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if ("serialVersionUID".equals(field.getName())) {
                continue;
            }
            if (Modifier.isStatic(field.getModifiers())) {
                continue;
            }
            field.setAccessible(true);
            try {
                if (field.get(obj) != null) {
                    field.setAccessible(false);
                    return false;
                }
            } catch (IllegalAccessException e) {
                //不会有这个问题
            }
            field.setAccessible(false);
        }

        return true;
    }

    /**
     * 需清空的字段数据, 数字格式清空的需要设置为-128
     *
     * @param modifyIssueFacadeRequest
     * @param orderModel
     */
    private void clearFieldData(ModifyIssueFacadeRequest modifyIssueFacadeRequest, ExpressOrderModel orderModel) throws ParseException {
        if (orderModel == null || modifyIssueFacadeRequest == null) {
            return;
        }

        if (MapUtils.isNotEmpty(orderModel.getModifiedFields())) {
            if (orderModel.getSyncSource() != null && SyncSourceEnum.UNBAICHUAN.getSyncSource().equals(orderModel.getSyncSource())) {
                //非百川透传，不转译
                modifyIssueFacadeRequest.setModifiedFields(orderModel.getModifiedFields());
            } else {
                //百川-增量的修改为全量
                modifyIssueFacadeRequest.setModifiedFields(orderModel.getModifiedFields());
                for (Map.Entry<String, String> entry : modifyIssueFacadeRequest.getModifiedFields().entrySet()) {
                    if (ModifiedFieldValueEnum.INCREMENT_UPDATE.getCode().equals(entry.getValue())) {
                        entry.setValue(ModifiedFieldValueEnum.ALL_COVER.getCode());
                    }
                }

            }
        }

        //循环置空的集合
        if (CollectionUtils.isNotEmpty(orderModel.getClearFields())) {
            for (String clearFiled : orderModel.getClearFields()) {
                ModifyItemConfigEnum configEnum = ModifyItemConfigEnum.getConfigEnumByCode(clearFiled);
                if (configEnum == null) {
                    continue;
                }
                if (configEnum == ModifyItemConfigEnum.CONSIGNOR_NAME || configEnum == ModifyItemConfigEnum.CONSIGNOR_MOBILE
                        || configEnum == ModifyItemConfigEnum.CONSIGNOR_ID_TYPE) {
                    if (modifyIssueFacadeRequest.getConsignorInfo() == null) {
                        modifyIssueFacadeRequest.setConsignorInfo(new ConsignorInfo());
                    }
                }
                if (configEnum == ModifyItemConfigEnum.CONSIGNEE_ID_TYPE) {
                    if (modifyIssueFacadeRequest.getConsigneeInfo() == null) {
                        modifyIssueFacadeRequest.setConsigneeInfo(new ConsigneeInfo());
                    }
                }
                if (configEnum == ModifyItemConfigEnum.PLAN_DELIVERY_TIME || configEnum == ModifyItemConfigEnum.EXPECT_PICKUP_START_TIME
                        || configEnum == ModifyItemConfigEnum.EXPECT_PICKUP_END_TIME || configEnum == ModifyItemConfigEnum.PICKUP_TYPE
                        || configEnum == ModifyItemConfigEnum.DELIVERY_TYPE || configEnum == ModifyItemConfigEnum.TRANSPORT_TYPE
                        || configEnum == ModifyItemConfigEnum.PLAN_RECEIVE_TIME || configEnum == ModifyItemConfigEnum.CONTACTLESS_TYPE
                        || configEnum == ModifyItemConfigEnum.START_STATION_TYPE) {
                    if (modifyIssueFacadeRequest.getShipmentInfo() == null) {
                        modifyIssueFacadeRequest.setShipmentInfo(new ShipmentInfo());
                    }
                }
                if (configEnum == ModifyItemConfigEnum.SETTLEMENT_TYPE
                        || configEnum == ModifyItemConfigEnum.TAX_SETTLEMENT_TYPE
                        || configEnum == ModifyItemConfigEnum.PAYMENT
                        || configEnum == ModifyItemConfigEnum.PAYMENT_STAGE
                        || configEnum == ModifyItemConfigEnum.PREEMPT_TYPE) {
                    if (modifyIssueFacadeRequest.getFinanceInfo() == null) {
                        modifyIssueFacadeRequest.setFinanceInfo(new FinanceInfo());
                    }
                }
                switch (configEnum) {
                    case CONSIGNOR_NAME:
                        modifyIssueFacadeRequest.getConsignorInfo().setConsignorName("");
                        break;
                    case CONSIGNOR_MOBILE:
                        modifyIssueFacadeRequest.getConsignorInfo().setConsignorMobile("");
                        break;
                    case CONSIGNOR_ID_TYPE:
                        modifyIssueFacadeRequest.getConsignorInfo().setConsignorIdType(-128);
                        break;
                    case CONSIGNEE_ID_TYPE:
                        modifyIssueFacadeRequest.getConsigneeInfo().setConsigneeIdType(-128);
                        break;
                    case PLAN_DELIVERY_TIME:
                        modifyIssueFacadeRequest.getShipmentInfo().setPlanDeliveryTime(DateUtils.getEraDateTime());
                        break;
                    case EXPECT_PICKUP_START_TIME:
                        modifyIssueFacadeRequest.getShipmentInfo().setExpectPickupStartTime(DateUtils.getEraDateTime());
                        break;
                    case EXPECT_PICKUP_END_TIME:
                        modifyIssueFacadeRequest.getShipmentInfo().setExpectPickupEndTime(DateUtils.getEraDateTime());
                        break;
                    case PICKUP_TYPE:
                        modifyIssueFacadeRequest.getShipmentInfo().setPickupType(-128);
                        break;
                    case DELIVERY_TYPE:
                        modifyIssueFacadeRequest.getShipmentInfo().setDeliveryType(-128);
                        break;
                    case TRANSPORT_TYPE:
                        modifyIssueFacadeRequest.getShipmentInfo().setTransportType(-128);
                        break;
                    case PLAN_RECEIVE_TIME:
                        modifyIssueFacadeRequest.getShipmentInfo().setPlanReceiveTime(DateUtils.getEraDateTime());
                        break;
                    case CONTACTLESS_TYPE:
                        modifyIssueFacadeRequest.getShipmentInfo().setContactlessType(-128);
                        break;
                    case SETTLEMENT_TYPE:
                        modifyIssueFacadeRequest.getFinanceInfo().setSettlementType(-128);
                        break;
                    case TAX_SETTLEMENT_TYPE:
                        modifyIssueFacadeRequest.getFinanceInfo().setTaxSettlementType(-128);
                        break;
                    case PAYMENT:
                        modifyIssueFacadeRequest.getFinanceInfo().setPayment(-128);
                        break;
                    case PAYMENT_STAGE:
                        modifyIssueFacadeRequest.getFinanceInfo().setPaymentStage(-128);
                        break;
                    case PREEMPT_TYPE:
                        modifyIssueFacadeRequest.getFinanceInfo().setPreemptType(-128);
                        break;
                    case START_STATION_TYPE:
                        modifyIssueFacadeRequest.getShipmentInfo().setStartStationType(-128);
                        break;
                    default:
                        break;
                }
            }

        }
    }

    /**
     * 修改下发参数转换
     *
     * @param modifyOrderOfcData
     * @return
     */
    public ModifyIssueFacadeResponse toModifyIssueFacadeResponse(ModifyOrderOfcData modifyOrderOfcData) {
        ModifyIssueFacadeResponse response = new ModifyIssueFacadeResponse();
        if (SUCCESS.equals(modifyOrderOfcData.getCode())) {
            response.setIssueResult(true);
        } else {
            response.setIssueResult(false);
        }
        response.setExtendProps(modifyOrderOfcData.getExtendProps());
        return response;
    }

    public ModifyIssueFacadeResponse toModifyIssueFacadeResponse(boolean issueResult) {
        ModifyIssueFacadeResponse response = new ModifyIssueFacadeResponse();
        response.setIssueResult(issueResult);
        return response;
    }

    /**
     * 修改下发客户信息转换
     *
     * @param customer
     * @return
     */
    private CustomerInfo toCustomerInfo(Customer customer) {
        if (customer == null || isNullObject(customer, Customer.class)) {
            return null;
        }
        CustomerInfo customerInfo = new CustomerInfo();
        customerInfo.setAccountNo(customer.getAccountNo());
        customerInfo.setAccount2No(customer.getAccountNo2());
        customerInfo.setAccount3No(customer.getAccountNo3());

        return customerInfo;
    }

    /**
     * 修改下发渠道信息转换
     *
     * @param channel
     * @return
     */
    private ChannelInfo toChannelInfo(Channel channel, boolean syncModify) {
        if (channel == null || isNullObject(channel, Channel.class)) {
            return null;
        }
        ChannelInfo channelInfo = new ChannelInfo();
        channelInfo.setChannelNo(channel.getChannelNo());
        channelInfo.setChannelCustomerNo(channel.getChannelCustomerNo());
        channelInfo.setChannelOrderNo(channel.getChannelOrderNo());
        channelInfo.setCustomerOrderNo(channel.getCustomerOrderNo());
        channelInfo.setChannelOperateTime(channel.getChannelOperateTime());
        channelInfo.setSecondLevelChannel(channel.getSecondLevelChannel());
        channelInfo.setSecondLevelChannelOrderNo(channel.getSecondLevelChannelOrderNo());
        channelInfo.setSecondLevelChannelCustomerNo(channel.getSecondLevelChannelCustomerNo());
        channelInfo.setSystemCaller(channel.getSystemCaller().getCode());
        if (syncModify) {
            channelInfo.setSystemSubCaller(channel.getSystemSubCaller());
        } else {
            channelInfo.setSystemSubCaller(OFCSysSourceEnum.JDL_OMS.getCode());
        }
        channelInfo.setExtendProps(channel.getExtendProps());


        return channelInfo;
    }

    /**
     * 商品信息
     *
     * @param goodsList
     * @return
     */
    private List<GoodsInfo> toGoodsInfoList(List<Goods> goodsList) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return null;
        }
        List<GoodsInfo> goodsInfos = new ArrayList<>(goodsList.size());
        goodsList.forEach(goods -> {
            GoodsInfo goodsInfo = new GoodsInfo();
            if(StringUtils.isNotBlank(goods.getGoodsUniqueCode())){
                goodsInfo.setGoodsUniqueCode(goods.getGoodsUniqueCode());
            }else{
                Map<String, String> extendProps = goods.getExtendProps();
                if(MapUtils.isNotEmpty(extendProps)){
                    goodsInfo.setGoodsUniqueCode(extendProps.get(OrderConstants.GOODS_UNIQUE_CODE));
                }
            }
            goodsInfo.setGoodsNo(goods.getGoodsNo());
            goodsInfo.setChannelGoodsNo(goods.getChannelGoodsNo());
            goodsInfo.setGoodsName(goods.getGoodsName());
            Optional.ofNullable(goods.getGoodsAmount()).ifPresent(goodsAmount -> {
                MoneyInfo money = new MoneyInfo();
                money.setAmount(goodsAmount.getAmount());
                // 此处添加判空处理
                money.setCurrencyCode(null != goodsAmount.getCurrency() ? goodsAmount.getCurrency().getCode() : null);
                goodsInfo.setGoodsAmount(money);
            });
            Optional.ofNullable(goods.getGoodsPrice()).ifPresent(goodsPrice -> {
                MoneyInfo money = new MoneyInfo();
                money.setAmount(goodsPrice.getAmount());
                // 此处添加判空处理
                money.setCurrencyCode(null != goodsPrice.getCurrency() ? goodsPrice.getCurrency().getCode() : null);
                goodsInfo.setGoodsPrice(money);
            });
            goodsInfo.setGoodsType(goods.getGoodsType());
            goodsInfo.setCombinationGoodsVersion(goods.getCombinationGoodsVersion());
            goodsInfo.setExtendProps(goods.getExtendProps());
            //货品数量
            Optional.ofNullable(goods.getGoodsQuantity()).ifPresent(goodsQuantity -> {
                QuantityInfo quantity = new QuantityInfo();
                quantity.setValue(goodsQuantity.getValue());
                quantity.setUnit(goodsQuantity.getUnit());
                goodsInfo.setGoodsQuantity(quantity);
            });

            // 商品纬度序列号信息
            goodsInfo.setGoodsSerialInfos(SerialMapper.INSTANCE.toSerialInfoList(goods.getGoodsSerialInfos()));

            // 商品纬度增值服务信息
            Optional.ofNullable(goods.getGoodsProductInfos()).ifPresent(productList -> {
                List<ProductInfo> productInfoList = new ArrayList<>(productList.size());
                productList.forEach(product -> {
                    ProductInfo productInfo = new ProductInfo();
                    productInfo.setProductNo(product.getProductNo());
                    productInfo.setProductName(product.getProductName());
                    productInfo.setExtendProps(product.getExtendProps());
                    productInfo.setProductAttrs(product.getProductAttrs());
                    productInfo.setProductType(product.getProductType());
                    productInfo.setParentNo(product.getParentNo());
                    productInfo.setProductExecutionResult(product.getProductExecutionResult());
                    productInfoList.add(productInfo);
                });
                goodsInfo.setGoodsProductInfos(productInfoList);
            });
            //附件信息
            Optional.ofNullable(goods.getAttachments()).ifPresent(attachmentInfos -> {
                //遍历附件
                List<AttachmentInfo> attachmentInfoList = attachmentInfos.stream().map(attachment -> {
                    AttachmentInfo attachmentInfo = new AttachmentInfo();
                    attachmentInfo.setAttachmentSortNo(attachment.getAttachmentSortNo());
                    attachmentInfo.setAttachmentName(attachment.getAttachmentName());
                    attachmentInfo.setAttachmentType(attachment.getAttachmentType());
                    attachmentInfo.setAttachmentDocType(attachment.getAttachmentDocType());
                    attachmentInfo.setAttachmentUrl(attachment.getAttachmentUrl());
                    attachmentInfo.setAttachmentRemark(attachment.getAttachmentRemark());
                    return attachmentInfo;
                }).collect(Collectors.toList());
                //附件集
                goodsInfo.setGoodsAttachmentInfos(attachmentInfoList);
            });

            //货品重量
            Optional.ofNullable(goods.getGoodsWeight()).ifPresent(goodsWeight -> {
                WeightInfo weightInfo = new WeightInfo();
                weightInfo.setValue(goodsWeight.getValue());
                if (null != goodsWeight.getUnit()) {
                    weightInfo.setUnit(goodsWeight.getUnit().getCode());
                }
                goodsInfo.setGoodsWeight(weightInfo);
            });

            //货品体积
            Optional.ofNullable(goods.getGoodsVolume()).ifPresent(goodsVolume -> {
                VolumeInfo volumeInfo = new VolumeInfo();
                volumeInfo.setValue(goodsVolume.getValue());
                if (null != goodsVolume.getUnit()) {
                    volumeInfo.setUnit(goodsVolume.getUnit().getCode());
                }
                goodsInfo.setGoodsVolume(volumeInfo);
            });

            //货品三维
            Optional.ofNullable(goods.getGoodsDimension()).ifPresent(goodsDimension -> {
                DimensionInfo dimensionInfo = new DimensionInfo();
                dimensionInfo.setHeight(goodsDimension.getHeight());
                dimensionInfo.setLength(goodsDimension.getLength());
                dimensionInfo.setWidth(goodsDimension.getWidth());
                if (null != goodsDimension.getUnit()) {
                    dimensionInfo.setUnit(goodsDimension.getUnit().getCode());
                }
                goodsInfo.setGoodsDimension(dimensionInfo);
            });

            //促销信息
            goodsInfo.setSalesInfos(goods.getSalesInfos());

            //商品净重
            Optional.ofNullable(goods.getNetWeight()).ifPresent(netWeight -> {
                WeightInfo weightInfo = new WeightInfo();
                weightInfo.setValue(netWeight.getValue());
                if (null != netWeight.getUnit()) {
                    weightInfo.setUnit(netWeight.getUnit().getCode());
                }
                goodsInfo.setNetWeight(weightInfo);
            });

            goodsInfos.add(goodsInfo);
        });
        return goodsInfos;
    }

    private List<ProductInfo> toProductInfoList(List<? extends IProduct> products, ExpressOrderModel orderModel) {

        return  toProductInfoList(products, orderModel, false);
    }

    /**
     * 修改下发产品信息转换
     *
     * @param products
     * @return
     */
    private List<ProductInfo> toProductInfoList(List<? extends IProduct> products, ExpressOrderModel orderModel, boolean businessSolutionNoHaveChange) {
        // 只有B2C有产品补齐相关的内容，所以这个commit暂时只在B2C分支上保留
        if (CollectionUtils.isEmpty(products)
                || (MapUtils.isEmpty(orderModel.getModifiedFields()) && !businessSolutionNoHaveChange)) {
            return null;
        }
        String modified = orderModel.getModifiedFields().get(ModifiedFieldEnum.PRODUCT_INFOS.getCode());
        List<Product> productList = new ArrayList<>();
        productList.addAll((List<Product>) products);

        // 若当前单产品信息都是新产品（产品类型 >= 10 为新产品），则下发时要补齐原单老产品（产品类型 < 10）
        if (businessSolutionNoHaveChange// 暂限制只对解决方案变更的场景补齐
                && orderModel.getProductDelegate() != null
                && orderModel.getProductDelegate().isAllNewProducts()) {

            if (orderModel.getOrderSnapshot() != null
                    && orderModel.getOrderSnapshot().getProductDelegate() != null) {
                productList.addAll(orderModel.getOrderSnapshot().getProductDelegate().getProductList());
                LOGGER.info("若当前单产品信息都是新产品（产品类型 >= 10 为新产品），则下发时要补齐原单老产品");
            }

        }

        List<ProductInfo> productInfoList = productList.stream().map(product -> {
            if (!ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modified)
                    && OperateTypeEnum.DELETE == product.getOperateType()) {
                return null;
            }
            ProductInfo productInfo = new ProductInfo();
            productInfo.setProductNo(product.getProductNo());
            productInfo.setProductName(product.getProductName());
            productInfo.setProductType(product.getProductType());
            productInfo.setParentNo(product.getParentNo());
            productInfo.setProductAttrs(product.getProductAttrs());
            productInfo.setExtendProps(product.getExtendProps());
            //产品模式
            if(product.getPattern() !=null){
                PatternInfo patternInfo = new PatternInfo();
                patternInfo.setPatternNo(product.getPattern().getPatternNo());
                patternInfo.setPatternName(product.getPattern().getPatternName());
                productInfo.setPatternInfo(patternInfo);
            }
            return productInfo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(productInfoList)) {
            return null;
        }
        return productInfoList;
    }

    /**
     * 修改下发发货人信息转换
     *
     * @param consignor
     * @return
     */
    private ConsignorInfo toConsignorInfo(Consignor consignor) {
        if (consignor == null || isNullObject(consignor, Consignor.class)) {
            return null;
        }

        ConsignorInfo consignorInfo = new ConsignorInfo();
        consignorInfo.setConsignorName(consignor.getConsignorName());
        consignorInfo.setConsignorMobile(consignor.getConsignorMobile());
        consignorInfo.setConsignorPhone(consignor.getConsignorPhone());
        consignorInfo.setConsignorZipCode(consignor.getConsignorZipCode());
        consignorInfo.setConsignorCompany(consignor.getConsignorCompany());
        consignorInfo.setConsignorNationNo(consignor.getConsignorNationNo());
        consignorInfo.setConsignorNation(consignor.getConsignorNation());
        consignorInfo.setConsignorIdType(consignor.getConsignorIdType() != null
                ? consignor.getConsignorIdType().getCode() : null);
        consignorInfo.setConsignorIdNo(consignor.getConsignorIdNo());
        consignorInfo.setConsignorIdName(consignor.getConsignorIdName());
        consignorInfo.setCustomerWarehouse(toWarehouseInfo(consignor.getCustomerWarehouse()));
        consignorInfo.setAddressInfo(this.toAddressInfo(consignor.getAddress(), CONSIGNOR));
        //英文发货人姓名
        consignorInfo.setConsignorEnName(consignor.getConsignorEnName());

        if (isNullObject(consignorInfo, ConsignorInfo.class)) {
            return null;
        }
        return consignorInfo;
    }

    /**
     * 修改下发收货人信息转换
     *
     * @param consignee
     * @return
     */
    private ConsigneeInfo toConsigneeInfo(Consignee consignee) {
        if (consignee == null || isNullObject(consignee, Consignee.class)) {
            return null;
        }
        ConsigneeInfo consigneeInfo = new ConsigneeInfo();
        consigneeInfo.setConsigneeName(consignee.getConsigneeName());
        consigneeInfo.setConsigneeMobile(consignee.getConsigneeMobile());
        consigneeInfo.setConsigneePhone(consignee.getConsigneePhone());
        consigneeInfo.setConsigneeZipCode(consignee.getConsigneeZipCode());
        consigneeInfo.setConsigneeCompany(consignee.getConsigneeCompany());
        consigneeInfo.setConsigneeNationNo(consignee.getConsigneeNationNo());
        consigneeInfo.setConsigneeNation(consignee.getConsigneeNation());
        consigneeInfo.setConsigneeIdType(consignee.getConsigneeIdType() != null ? consignee.getConsigneeIdType().getCode() : null);
        consigneeInfo.setConsigneeIdNo(consignee.getConsigneeIdNo());
        consigneeInfo.setConsigneeIdName(consignee.getConsigneeIdName());
        consigneeInfo.setAddressInfo(this.toAddressInfo(consignee.getAddress(), CONSIGNEE));
        consigneeInfo.setReceiveWarehouse(this.toWarehouseInfo(consignee.getReceiveWarehouse()));
        consigneeInfo.setConsigneeEmail(consignee.getConsigneeEmail());
        consigneeInfo.setExtendProps(consignee.getExtendProps());

        if (isNullObject(consigneeInfo, ConsigneeInfo.class)) {
            return null;
        }

        return consigneeInfo;
    }

    /**
     * 下发仓库信息
     * @param warehouse
     * @return
     */
    private WarehouseInfo toWarehouseInfo(Warehouse warehouse){
        WarehouseInfo warehouseInfo = new WarehouseInfo();
        if(null != warehouse){
            warehouseInfo.setWarehouseNo(warehouse.getWarehouseNo());
            warehouseInfo.setWarehouseName(warehouse.getWarehouseName());
            warehouseInfo.setWarehouseSource(warehouse.getWarehouseSource());
        }
        return warehouseInfo;
    }

    /**
     * 修改下发收货人地址信息转换
     *
     * @param address
     * @return
     */
    private AddressInfo toAddressInfo(Address address, String consigneeOrConsignor) {
        if (address == null || isNullObject(address, Address.class)) {
            return null;
        }
        AddressInfo addressInfo = new AddressInfo();
        addressInfo.setAddress(address.getAddress());
        addressInfo.setProvinceNo(address.getProvinceNo());
        addressInfo.setProvinceName(address.getProvinceName());
        addressInfo.setCityNo(address.getCityNo());
        addressInfo.setCityName(address.getCityName());
        addressInfo.setCountyNo(address.getCountyNo());
        addressInfo.setCountyName(address.getCountyName());
        addressInfo.setTownNo(address.getTownNo());
        addressInfo.setTownName(address.getTownName());
        addressInfo.setCoordinateType(address.getCoordinateType() != null
                ? address.getCoordinateType().getCode() : null);
        addressInfo.setLongitude(address.getLongitude());
        addressInfo.setLatitude(address.getLatitude());
        addressInfo.setProvinceNoGis(address.getProvinceNoGis());
        addressInfo.setProvinceNameGis(address.getProvinceNameGis());
        addressInfo.setCityNoGis(address.getCityNoGis());
        addressInfo.setCityNameGis(address.getCityNameGis());
        addressInfo.setCountyNoGis(address.getCountyNoGis());
        addressInfo.setCountyNameGis(address.getCountyNameGis());
        addressInfo.setTownNoGis(address.getTownNoGis());
        addressInfo.setTownNameGis(address.getTownNameGis());
        addressInfo.setAddressGis(address.getAddressGis());
        addressInfo.setPreciseGis(address.getPreciseGis());
        addressInfo.setChinaPostAddressCode(address.getChinaPostAddressCode());
        //目前只有收货地址下发嵌套地址级别
        if(StringUtils.isNotBlank(consigneeOrConsignor) && consigneeOrConsignor.equals(CONSIGNEE)){
            addressInfo.setConflictLevel(address.getConflictLevel());
        }
        addressInfo.setAddressSource(address.getAddressSource());
        // 围栏信任
        addressInfo.setFenceTrusted(address.getFenceTrusted());
        // 围栏信息 目前只有传信任标识才会传入围栏信息
        if (FenceTrustEnum.TRUSTED.getCode().equals(address.getFenceTrusted())) {
            Optional.ofNullable(address.getFenceInfos()).ifPresent(fenceInfos -> {
                List<FenceInfo> fenceInfoList = new ArrayList<>(fenceInfos.size());
                fenceInfos.forEach(fenceInfo -> {
                    FenceInfo fence = new FenceInfo();
                    fence.setFenceId(fenceInfo.getFenceId());
                    fence.setFenceType(fenceInfo.getFenceType());
                    fenceInfoList.add(fence);
                });
                addressInfo.setFenceInfos(fenceInfoList);
            });
        }

        // 行政区编码
        addressInfo.setRegionNo(address.getRegionNo());
        // 行政区名称
        addressInfo.setRegionName(address.getRegionName());
        // 英文城市
        addressInfo.setEnCityName(address.getEnCityName());
        // 英文地址
        addressInfo.setEnAddress(address.getEnAddress());

        return addressInfo;
    }

    /**
     * 修改货品信息转换
     *
     * @param cargoList
     * @return
     */
    private List<CargoInfo> toCargoInfoList(List<Cargo> cargoList) {
        if (CollectionUtils.isEmpty(cargoList)) {
            return null;
        }
        List<CargoInfo> cargoInfoList = cargoList.stream().map(cargo -> {
            if (cargo == null || isNullObject(cargo, Cargo.class)) {
                return null;
            }

            CargoInfo cargoInfo = new CargoInfo();
            cargoInfo.setCargoName(cargo.getCargoName());
            cargoInfo.setCargoNo(cargo.getCargoNo());
            cargoInfo.setCargoType(cargo.getCargoType());
            cargoInfo.setCargoVolume(this.toVolumeInfo(cargo.getCargoVolume()));
            cargoInfo.setCargoWeight(this.toWeightInfo(cargo.getCargoWeight()));
            cargoInfo.setCargoQuantity(toCargoQuantityInfo(cargo.getCargoQuantity()));
            cargoInfo.setCargoDimension(this.toDimensionInfo(cargo.getCargoDimension()));
            cargoInfo.setSerialInfos(SerialMapper.INSTANCE.toSerialInfoList(cargo.getSerialInfos()));
            cargoInfo.setCargoRemark(cargo.getCargoRemark());
            cargoInfo.setPolluteSign(cargo.getPolluteSign() != null ? cargo.getPolluteSign().getCode() : null);
            cargoInfo.setCargoAttachmentInfos(this.toAttachmentInfoList(cargo.getAttachments()));
            //是否易损
            if (cargo.getCargoVulnerable() != null) {
                cargoInfo.setCargoVulnerable(cargo.getCargoVulnerable().getCode());
            }
            //货品标识
            cargoInfo.setCargoSign(cargo.getCargoSign());
            //货品信息增值服务
            Optional.ofNullable(cargo.getCargoProductInfos()).ifPresent(productList -> {
                List<ProductInfo> productInfoList = new ArrayList<>(productList.size());
                productList.forEach(product -> {
                    ProductInfo productInfo = new ProductInfo();
                    productInfo.setProductNo(product.getProductNo());
                    productInfo.setProductName(product.getProductName());
                    productInfo.setExtendProps(product.getExtendProps());
                    productInfo.setProductAttrs(product.getProductAttrs());
                    productInfo.setProductType(product.getProductType());
                    productInfo.setParentNo(product.getParentNo());
                    productInfoList.add(productInfo);
                });
                cargoInfo.setProductInfos(productInfoList);
            });
            //货品信息扩展字段
            cargoInfo.setExtendProps(cargo.getExtendProps());
            //隐私货品展示信息
            cargoInfo.setPrivacyCargoName(cargo.getPrivacyCargoName());
            return cargoInfo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(cargoInfoList)) {
            return null;
        }
        return cargoInfoList;
    }

    /**
     * 货物数量转换
     *
     * @param quantity
     * @return
     */
    private QuantityInfo toCargoQuantityInfo(Quantity quantity) {
        if (quantity == null || isNullObject(quantity, Quantity.class)) {
            return null;
        }
        QuantityInfo quantityInfo = new QuantityInfo();
        quantityInfo.setUnit(quantity.getUnit());
        quantityInfo.setValue(quantity.getValue());
        return quantityInfo;
    }

    /**
     * 附件信息转换
     *
     * @param attachments
     * @return
     */
    private List<AttachmentInfo> toAttachmentInfoList(List<Attachment> attachments) {
        if (CollectionUtils.isEmpty(attachments)) {
            return null;
        }
        return attachments.stream().map(attachment -> {
            AttachmentInfo attachmentInfo = new AttachmentInfo();
            attachmentInfo.setAttachmentSortNo(attachment.getAttachmentSortNo());
            attachmentInfo.setAttachmentName(attachment.getAttachmentName());
            attachmentInfo.setAttachmentType(attachment.getAttachmentType());
            attachmentInfo.setAttachmentDocType(attachment.getAttachmentDocType());
            attachmentInfo.setAttachmentUrl(attachment.getAttachmentUrl());
            attachmentInfo.setAttachmentRemark(attachment.getAttachmentRemark());
            return attachmentInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 修改下发体积信息转换
     *
     * @param volume
     * @return
     */
    private VolumeInfo toVolumeInfo(Volume volume) {
        if (volume == null || isNullObject(volume, Volume.class)) {
            return null;
        }
        VolumeInfo volumeInfo = new VolumeInfo();
        volumeInfo.setUnit(volume.getUnit() != null ? volume.getUnit().getCode()
                : VolumeTypeEnum.CM3.getCode());//为空默认立方厘米
        volumeInfo.setValue(volume.getValue());

        return volumeInfo;
    }

    /**
     * 修改下发重量信息转换
     *
     * @param weight
     * @return
     */
    private WeightInfo toWeightInfo(Weight weight) {
        if (weight == null || isNullObject(weight, Weight.class)) {
            return null;
        }
        WeightInfo weightInfo = new WeightInfo();
        weightInfo.setUnit(weight.getUnit() != null ? weight.getUnit().getCode()
                : WeightTypeEnum.KG.getCode());
        weightInfo.setValue(weight.getValue());

        return weightInfo;
    }

    /**
     * 修改下发长宽高聚合信息转换
     *
     * @param dimension
     * @return
     */
    private DimensionInfo toDimensionInfo(Dimension dimension) {
        if (dimension == null || isNullObject(dimension, Dimension.class)) {
            return null;
        }
        DimensionInfo dimensionInfo = new DimensionInfo();
        dimensionInfo.setLength(dimension.getLength());
        dimensionInfo.setWidth(dimension.getWidth());
        dimensionInfo.setHeight(dimension.getHeight());
        dimensionInfo.setUnit(dimension.getUnit() != null ? dimension.getUnit().getCode()
                : LengthTypeEnum.CM.getCode());

        return dimensionInfo;
    }

    /**
     * 修改下发配送要求信息转换
     *
     * @param shipment
     * @return
     */
    private ShipmentInfo toShipmentInfo(Shipment shipment) {
        if (shipment == null || isNullObject(shipment, Shipment.class)) {
            return null;
        }
        ShipmentInfo shipmentInfo = new ShipmentInfo();
        shipmentInfo.setPlanDeliveryTime(shipment.getPlanDeliveryTime());
        shipmentInfo.setPlanDeliveryPeriod(shipment.getPlanDeliveryPeriod());
        shipmentInfo.setExpectDeliveryStartTime(shipment.getExpectDeliveryStartTime());
        shipmentInfo.setExpectDeliveryEndTime(shipment.getExpectDeliveryEndTime());
        shipmentInfo.setExpectPickupStartTime(shipment.getExpectPickupStartTime());
        shipmentInfo.setExpectPickupEndTime(shipment.getExpectPickupEndTime());
        shipmentInfo.setPickupType(shipment.getPickupType() != null ? shipment.getPickupType().getCode()
                : null);
        shipmentInfo.setDeliveryType(shipment.getDeliveryType() != null ? shipment.getDeliveryType().getCode()
                : null);
        shipmentInfo.setTransportType(shipment.getTransportType() != null ? shipment.getTransportType().getCode()
                : null);
        shipmentInfo.setVehicleType(shipment.getVehicleType());
        shipmentInfo.setWarmLayer(shipment.getWarmLayer() != null ? shipment.getWarmLayer().getCode() : null);
        shipmentInfo.setStartStationNo(shipment.getStartStationNo());
        shipmentInfo.setStartStationName(shipment.getStartStationName());
        shipmentInfo.setStartStationType(shipment.getStartStationType());
        shipmentInfo.setEndStationNo(shipment.getEndStationNo());
        shipmentInfo.setEndStationName(shipment.getEndStationName());
        shipmentInfo.setEndStationType(shipment.getEndStationType());
        shipmentInfo.setWarehouseNo(shipment.getWarehouseNo());
        shipmentInfo.setReceiveWarehouseNo(shipment.getReceiveWarehouseNo());
        shipmentInfo.setPlanReceiveTime(shipment.getPlanReceiveTime());
        shipmentInfo.setContactlessType(shipment.getContactlessType() != null ? shipment.getContactlessType().getCode()
                : null);
        shipmentInfo.setAssignedAddress(shipment.getAssignedAddress());

        shipmentInfo.setPickupCode(shipment.getPickupCode());
        shipmentInfo.setPickupCodeCreateType(shipment.getPickupCodeCreateType() == null ? null : shipment.getPickupCodeCreateType().getCode());
        shipmentInfo.setServiceRequirements(shipment.getServiceRequirements());
        //收货偏好
        shipmentInfo.setReceivingPreference(shipment.getReceivingPreference());
        shipmentInfo.setExtendProps(shipment.getExtendProps());
        shipmentInfo.setStartCenterNo(shipment.getStartCenterNo());
        shipmentInfo.setEndCenterNo(shipment.getEndCenterNo());
        shipmentInfo.setShipperNo(shipment.getShipperNo());
        shipmentInfo.setShipperName(shipment.getShipperName());
        shipmentInfo.setShipperType(shipment.getShipperType());
        //接驳点
        shipmentInfo.setEndTransferStationNo(shipment.getEndTransferStationNo());
        shipmentInfo.setExpectDispatchStartTime(shipment.getExpectDispatchStartTime());
        shipmentInfo.setExpectDispatchEndTime(shipment.getExpectDispatchEndTime());
        shipmentInfo.setStartStationTypeL3(shipment.getStartStationTypeL3());
        shipmentInfo.setEndStationTypeL3(shipment.getEndStationTypeL3());
        return shipmentInfo;
    }

    /**
     * 修改下发财务信息转换
     *
     * @param orderModel
     * @return
     */
    private FinanceInfo toFinanceInfo(ExpressOrderModel orderModel) {
        Finance finance = orderModel.getFinance();
        if (finance == null || isNullObject(finance, Finance.class)) {
            return null;
        }
        FinanceInfo financeInfo = new FinanceInfo();
        //financeInfo.setSettlementType(finance.getSettlementType() == null ? null : finance.getSettlementType().getCode());
        financeInfo.setEnquiryType(finance.getEnquiryType() != null ? finance.getEnquiryType().getCode() : null);
        financeInfo.setEstimateAmount(toMoneyInfo(finance.getEstimateAmount()));
        financeInfo.setPaymentAccountNo(finance.getPaymentAccountNo());
        financeInfo.setPayment(finance.getPayment() != null ? finance.getPayment().getCode() : null);
        financeInfo.setSettlementAccountNo(finance.getSettlementAccountNo());
        financeInfo.setPreemptType(finance.getPreemptType());
        //financeInfo.setPaymentStage(finance.getPaymentStage() != null ? finance.getPaymentStage().getCode() : null);
        //改址一单到底需要特殊处理支付环节和结算方式
        if (orderModel.isReaddress1Order2End() || orderModel.isKKInterceptionThroughOrderRecord()) {
            Map<String, String> financeExt = orderModel.getFinance().getExtendProps();//新单财务域扩展信息
            if (MapUtils.isNotEmpty(financeExt)) {
                // 原单的结算方式
                String orderSettlementType = financeExt.get(AttachmentKeyEnum.ORIGIN_SETTLEMENT_TYPE.getKey());
                if (StringUtils.isNotBlank(orderSettlementType)) {
                    financeInfo.setSettlementType(Integer.valueOf(orderSettlementType));
                }
                // 原单的支付环节
                String orderPaymentStage = financeExt.get(AttachmentKeyEnum.ORIGIN_PAYMENT_STAGE.getKey());
                if (StringUtils.isNotBlank(orderPaymentStage)) {
                    financeInfo.setPaymentStage(Integer.valueOf(orderPaymentStage));
                }
            }
        } else {
            //结算方式
            if (finance.getSettlementType() != null) {
                financeInfo.setSettlementType(finance.getSettlementType().getCode());
            }
            //付款环节
            financeInfo.setPaymentStage(finance.getPaymentStage() != null ? finance.getPaymentStage().getCode() : null);
        }
        financeInfo.setPreAmount(toMoneyInfo(finance.getPreAmount()));
        financeInfo.setDiscountAmount(toMoneyInfo(finance.getDiscountAmount()));
        financeInfo.setTotalDiscountAmount(toMoneyInfo(finance.getTotalDiscountAmount()));
        financeInfo.setBillingWeight(toWeightInfo(finance.getBillingWeight()));
        financeInfo.setBillingVolume(toVolumeInfo(finance.getBillingVolume()));
        financeInfo.setBillingMode(finance.getBillingMode());
        // 港澳快运 按板计费承接
        financeInfo.setBillingType(finance.getBillingType());
        financeInfo.setCollectionOrgNo(finance.getCollectionOrgNo());
        financeInfo.setFinanceDetailInfos(this.toFinanceDetailInfoList(finance.getFinanceDetails()));
        financeInfo.setPaymentTime(finance.getPaymentTime());

        financeInfo.setPaymentStatus(finance.getPaymentStatus() != null ? finance.getPaymentStatus().getStatus() : null);
        financeInfo.setPaymentNo(finance.getPaymentNo());
        financeInfo.setPayDeadline(finance.getPayDeadline());
        financeInfo.setPointsInfo(toPointsInfo(finance.getPoints()));

        //抵扣信息
        financeInfo.setDeductionInfos(toDeductionInfos(finance.getDeductionDelegate()));

        //收费信息
        Optional.ofNullable(finance.getCostInfos()).ifPresent(costInfoList -> {
            List<CostInfo> costInfos = new ArrayList<>(costInfoList.size());
            costInfoList.forEach(cost -> {
                CostInfo costInfo = new CostInfo();
                costInfo.setCostNo(cost.getCostNo());
                costInfo.setCostName(cost.getCostName());
                costInfo.setChargingSource(cost.getChargingSource());
                costInfo.setSettlementAccountNo(cost.getSettlementAccountNo());
                costInfo.setExtendProps(cost.getExtendProps());
                costInfos.add(costInfo);
            });
            financeInfo.setCostInfos(costInfos);
        });
        //附加费用
        Optional.ofNullable(finance.getAttachFees()).ifPresent(attachFees -> {
            List<CostInfo> attachFeesList = new ArrayList<>(attachFees.size());
            attachFees.forEach(cost -> {
                CostInfo costInfo = new CostInfo();
                costInfo.setCostNo(cost.getCostNo());
                costInfo.setCostName(cost.getCostName());
                costInfo.setChargingSource(cost.getChargingSource());
                costInfo.setSettlementAccountNo(cost.getSettlementAccountNo());
                costInfo.setExtendProps(cost.getExtendProps());
                attachFeesList.add(costInfo);
            });
            financeInfo.setAttachFees(attachFeesList);
        });
        //预估税金
        financeInfo.setEstimatedTax(MoneyMapper.INSTANCE.toMoneyInfo(finance.getEstimatedTax()));
        //真实税金
        financeInfo.setActualTax(MoneyMapper.INSTANCE.toMoneyInfo(finance.getActualTax()));
        //费用支付状态归集
        financeInfo.setPayStatusMap(finance.getPayStatusMap());
        //扩展属性
        financeInfo.setExtendProps(finance.getExtendProps());
        //税金结算方式
        financeInfo.setTaxSettlementType(finance.getTaxSettlementType());
        financeInfo.setBillingMethod(finance.getBillingMethod());
        financeInfo.setBillingWeightMethod(finance.getBillingWeightMethod());
        return financeInfo;
    }

    /**
     * 抵扣信息转换
     *
     * @param deductionDelegate
     * @return
     */
    private List<DeductionInfo> toDeductionInfos(DeductionDelegate deductionDelegate) {
        if (null == deductionDelegate || deductionDelegate.isEmpty()) {
            return null;
        }
        List<Deduction> deductions = (List<Deduction>) deductionDelegate.getDeductions();
        return DeductionMapper.INSTANCE.toDeductionInfos(deductions);
    }

    /**
     * 积分信息
     *
     * @param points
     * @return
     */
    private PointsInfo toPointsInfo(Points points) {
        if (points == null || isNullObject(points, Points.class)) {
            return null;
        }
        PointsInfo pointsInfo = new PointsInfo();
        if (points.getRedeemPointsQuantity() != null && !isNullObject(points.getRedeemPointsQuantity(), Quantity.class)) {
            QuantityInfo redeemPointsQuantity = new QuantityInfo();
            redeemPointsQuantity.setUnit(points.getRedeemPointsQuantity().getUnit());
            redeemPointsQuantity.setValue(points.getRedeemPointsQuantity().getValue());
            pointsInfo.setRedeemPointsQuantity(redeemPointsQuantity);
        }
        if (points.getRedeemPointsAmount() != null && !isNullObject(points.getRedeemPointsAmount(), Money.class)) {
            MoneyInfo redeemPointsAmount = new MoneyInfo();
            redeemPointsAmount.setAmount(points.getRedeemPointsAmount().getAmount());
            redeemPointsAmount.setCurrencyCode(points.getRedeemPointsAmount().getCurrency() != null ? points.getRedeemPointsAmount().getCurrency().getCode() : null);
            pointsInfo.setRedeemPointsAmount(redeemPointsAmount);
        }
        if (isNullObject(pointsInfo, PointsInfo.class)) {
            return null;
        }
        return pointsInfo;
    }

    /**
     * 修改下发金额转换
     *
     * @param money
     * @return
     */
    private MoneyInfo toMoneyInfo(Money money) {
        if (money == null || isNullObject(money, Money.class)) {
            return null;
        }
        MoneyInfo moneyInfo = new MoneyInfo();
        moneyInfo.setAmount(money.getAmount());
        moneyInfo.setCurrencyCode(money.getCurrency() != null ? money.getCurrency().getCode() : null);
        return moneyInfo;
    }

    /**
     * 修改下发财务费用明细转换
     *
     * @param financeDetails
     * @return
     */
    private List<FinanceDetailInfo> toFinanceDetailInfoList(List<FinanceDetail> financeDetails) {
        if (CollectionUtils.isEmpty(financeDetails)) {
            return null;
        }
        List<FinanceDetailInfo> detailInfoList = financeDetails.stream().map(financeDetail -> {
            if (financeDetail == null || isNullObject(financeDetail, FinanceDetail.class)) {
                return null;
            }
            FinanceDetailInfo financeDetailInfo = new FinanceDetailInfo();
            financeDetailInfo.setCostNo(financeDetail.getCostNo());
            financeDetailInfo.setCostName(financeDetail.getCostName());
            financeDetailInfo.setProductNo(financeDetail.getProductNo());
            //产品名称
            financeDetailInfo.setProductName(financeDetail.getProductName());
            if (CollectionUtils.isNotEmpty(financeDetail.getDiscounts())) {
                List<DiscountInfo> discountInfos = new ArrayList<>(financeDetail.getDiscounts().size());
                financeDetail.getDiscounts().forEach(discount -> {
                    DiscountInfo discountInfo = new DiscountInfo();
                    discountInfo.setDiscountNo(discount.getDiscountNo());
                    discountInfo.setDiscountType(discount.getDiscountType());
                    if (discount.getDiscountedAmount() != null) {
                        MoneyInfo moneyInfo = new MoneyInfo();
                        moneyInfo.setAmount(discount.getDiscountedAmount().getAmount());
                        if (discount.getDiscountedAmount().getCurrency() != null) {
                            moneyInfo.setCurrencyCode(discount.getDiscountedAmount().getCurrency().getCode());
                        }
                        discountInfo.setDiscountedAmount(moneyInfo);
                    }
                    discountInfos.add(discountInfo);
                });
                financeDetailInfo.setDiscountInfos(discountInfos);
            }

            financeDetailInfo.setPreAmount(toMoneyInfo(financeDetail.getPreAmount()));
            financeDetailInfo.setDiscountAmount(toMoneyInfo(financeDetail.getDiscountAmount()));
            // 向谁收
            financeDetailInfo.setChargingSource(financeDetail.getChargingSource());
            financeDetailInfo.setExtendProps(financeDetail.getExtendProps());
            return financeDetailInfo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(detailInfoList)) {
            return null;
        }
        return detailInfoList;
    }

    /**
     * 修改下发营销信息转换
     *
     * @param promotion
     * @return
     */
    private PromotionInfo toPromotionInfo(Promotion promotion, ExpressOrderModel orderModel) {
        if (promotion == null || isNullObject(promotion, Promotion.class)) {
            return null;
        }
        PromotionInfo promotionInfo = new PromotionInfo();
        promotionInfo.setActivityInfos(this.toActivityInfoList(promotion.getActivities(), orderModel));
        promotionInfo.setDiscountRefOrderNo(promotion.getDiscountRefOrderNo());
        promotionInfo.setTicketInfos(this.toTicketInfoList(promotion.getTickets(), orderModel));
        promotionInfo.setDiscountInfos(this.toDiscountInfoList(promotion.getDiscounts(), orderModel));
        if (CollectionUtils.isEmpty(promotionInfo.getActivityInfos()) && StringUtils.isBlank(promotionInfo.getDiscountRefOrderNo())
                && CollectionUtils.isEmpty(promotionInfo.getTicketInfos())
                && CollectionUtils.isEmpty(promotionInfo.getDiscountInfos())) {
            return null;
        }
        return promotionInfo;
    }

    /**
     * 促销信息转换
     *
     * @param activities
     * @return
     */
    private List<ActivityInfo> toActivityInfoList(List<Activity> activities, ExpressOrderModel orderModel) {
        if (CollectionUtils.isEmpty(activities)) {
            return null;
        }
        String modified = null!= orderModel.getModifiedFields()
                ? orderModel.getModifiedFields().get(ModifiedFieldEnum.ACTIVITY_INFOS.getCode())
                : StringUtils.EMPTY;
        List<ActivityInfo> activityInfoList = activities.stream().map(activity -> {
            //类型为3，删除的，下发时需要给剔除掉。1，2，null的可以正常下发
            if (!ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modified)
                    && OperateTypeEnum.DELETE == activity.getOperateType()) {
                return null;
            }
            ActivityInfo activityInfo = new ActivityInfo();
            activityInfo.setActivityNo(activity.getActivityNo());
            activityInfo.setActivityName(activity.getActivityName());
            activityInfo.setActivityStatus(activity.getActivityStatus());
            activityInfo.setActivityValue(activity.getActivityValue());
            return activityInfo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(activityInfoList)) {
            return null;
        }
        return activityInfoList;
    }

    /**
     * 修改下发优惠劵信息转换
     *
     * @return
     */
    private List<TicketInfo> toTicketInfoList(List<Ticket> ticketList, ExpressOrderModel orderModel) {
        if (CollectionUtils.isEmpty(ticketList)) {
            return null;
        }
        String modified = null!= orderModel.getModifiedFields()
                ? orderModel.getModifiedFields().get(ModifiedFieldEnum.TICKET_INFOS.getCode())
                : StringUtils.EMPTY;
        List<TicketInfo> ticketInfoList = ticketList.stream().map(ticket -> {
            if (!ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modified)) {
                if (OperateTypeEnum.DELETE == ticket.getOperateType()) {
                    return null;
                }
                if (CouponStatusEnum.ALLOW_ROLLBACK_YES.getCode().equals(ticket.getCouponStatus())) {
                    return null;
                }
            }

            TicketInfo ticketInfo = new TicketInfo();
            ticketInfo.setTicketNo(ticket.getTicketNo());
            ticketInfo.setTicketCategory(ticket.getTicketCategory());
            ticketInfo.setTicketType(ticket.getTicketType());
            ticketInfo.setTicketDiscountAmount(this.toMoneyInfo(ticket.getTicketDiscountAmount()));
            ticketInfo.setTicketDiscountRate(ticket.getTicketDiscountRate());
            ticketInfo.setTicketDiscountUpperLimit(this.toMoneyInfo(
                    ticket.getTicketDiscountUpperLimit()));
            ticketInfo.setTicketDescription(ticket.getTicketDescription());
            ticketInfo.setTicketUseAmount(this.toMoneyInfo(ticket.getTicketUseAmount()));
            ticketInfo.setTicketSource(ticket.getTicketSource());
            return ticketInfo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ticketInfoList)) {
            return null;
        }
        return ticketInfoList;
    }

    /**
     * 修改下发折扣卷信息转换
     *
     * @param discountList
     * @return
     */
    private List<DiscountInfo> toDiscountInfoList(List<Discount> discountList, ExpressOrderModel orderModel) {
        if (CollectionUtils.isEmpty(discountList)) {
            return null;
        }
        String modified = null!= orderModel.getModifiedFields()
                ? orderModel.getModifiedFields().get(ModifiedFieldEnum.DISCOUNT_INFOS.getCode())
                : StringUtils.EMPTY;
        List<DiscountInfo> discountInfoList = discountList.stream().map(discount -> {
            if (!ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(modified)
                    && OperateTypeEnum.DELETE == discount.getOperateType()) {
                return null;
            }

            DiscountInfo discountInfo = new DiscountInfo();
            discountInfo.setDiscountNo(discount.getDiscountNo());
            discountInfo.setExtendProps(discount.getExtendProps());
            return discountInfo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(discountInfoList)) {
            return null;
        }
        return discountInfoList;
    }

    /**
     * 逆向单下发请求转换
     * 主要下发财务信息以及必要信息
     *
     * @param context
     * @return
     * <AUTHOR>
     */
    public ModifyIssueFacadeRequest toReverseOrChangeAddressIssueFacadeRequest(ExpressOrderContext context){
        if (context == null || context.getOrderModel() == null) {
            return null;
        }
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = new ModifyIssueFacadeRequest();

        ExpressOrderModel orderModel = context.getOrderModel();
        modifyIssueFacadeRequest.setRequestProfile(orderModel.requestProfile());
        modifyIssueFacadeRequest.setOrderNo(orderModel.orderNo());
        modifyIssueFacadeRequest.setCustomOrderNo(orderModel.getCustomOrderNo());
        //渠道信息 下发必填
        modifyIssueFacadeRequest.setChannelInfo(this.toChannelInfo(orderModel.getChannel(),false));
        //财务信息
        modifyIssueFacadeRequest.setFinanceInfo(this.toFinanceInfo(orderModel));
        modifyIssueFacadeRequest.setInitiatorType(orderModel.getInitiatorType() != null ? orderModel.getInitiatorType().getCode()
                : null);
        modifyIssueFacadeRequest.setOperator(orderModel.getOperator());
        modifyIssueFacadeRequest.setExtendProps(orderModel.getExtendProps());
        modifyIssueFacadeRequest.setBusinessIdentity(orderModel.toBusinessIdentity());
        // 指定修改的集合信息
        if (MapUtils.isNotEmpty(orderModel.getModifiedFields())) {
            modifyIssueFacadeRequest.setModifiedFields(orderModel.getModifiedFields());
        }
        return modifyIssueFacadeRequest;
    }

    /**
     * 原单绑定改址单
     *
     * @param model
     * @return
     */
    public ModifyIssueFacadeRequest toBindOriginalOrderRequest(ExpressOrderModel model) {

        ModifyIssueFacadeRequest request = new ModifyIssueFacadeRequest();
        request.setRequestProfile(model.requestProfile());
        //原订单号
        request.setOrderNo(model.getOrderSnapshot().orderNo());
        //原运单号
        request.setCustomOrderNo(model.getOrderSnapshot().getCustomOrderNo());
        //渠道信息
        ChannelInfo channelInfo = new ChannelInfo();
        channelInfo.setChannelOperateTime(model.getChannel().getChannelOperateTime());
        channelInfo.setSystemCaller(model.getChannel().getSystemCaller().getCode());
        channelInfo.setSystemSubCaller(OFCSysSourceEnum.JDL_OMS.getCode());
        request.setChannelInfo(channelInfo);

        Map<String, String> extendProps = new HashMap<>();
        //改址单号
        extendProps.put(ReaddressParamEnum.READDRESS_ORDERNO.getCode(), model.orderNo());
        //改址单运单号
        extendProps.put(ReaddressParamEnum.READDRESS_WAYBILLNO.getCode(), model.getCustomOrderNo());
        Product product = model.getProductDelegate().ofProductNo(AddOnProductEnum.READDRESS.getCode());
        //改址类型
        extendProps.put(ReaddressParamEnum.READDRESS_TYPE.getCode(), product.getProductAttrs().get(AddOnProductAttrEnum.READDRESS_TYPE.getCode()));
        //改址单操作类型
        //1:新增改址单
        //2:取消改址单
        extendProps.put(ReaddressParamEnum.READDRESS_OPERATE_TYPE.getCode(), ReaddressOperateTypeEnum.ADD.getCode());
        request.setExtendProps(extendProps);
        FinanceInfo financeInfo = new FinanceInfo();
        //改址单支付时机
        financeInfo.setPaymentStage(model.getFinance().getPaymentStage().getCode());
        //改址单支付结束时间
        financeInfo.setPayDeadline(model.getFinance().getPayDeadline());
        //改址单支付状态
        financeInfo.setPaymentStatus(model.getFinance().getPaymentStatus().getStatus());
        request.setFinanceInfo(financeInfo);
        //发起人类型
        request.setInitiatorType(model.getInitiatorType().getCode());
        //操作人
        request.setOperator(model.getOperator());
        //业务身份
        request.setBusinessIdentity(model.getOrderSnapshot().toBusinessIdentity());
        return request;
    }

    /**
     * 原单解绑改址单
     *
     * @param context
     * @return
     */
    public ModifyIssueFacadeRequest toUnbindOriginalOrderRequest(ExpressOrderContext context) {
        ExpressOrderModel model;
        if (context.getOrderModel().getOrderSnapshot() != null && context.getOrderModel().getOrderSnapshot().getOrderSnapshot() != null) {
            model = context.getOrderModel().getOrderSnapshot();
        } else {
            model = context.getOrderModel();
        }
        ModifyIssueFacadeRequest request = new ModifyIssueFacadeRequest();
        request.setRequestProfile(context.getOrderModel().requestProfile());
        //原订单号
        request.setOrderNo(model.getOrderSnapshot().orderNo());
        //原运单号
        request.setCustomOrderNo(model.getOrderSnapshot().getCustomOrderNo());

        //渠道信息
        ChannelInfo channelInfo = new ChannelInfo();
        channelInfo.setChannelOperateTime(model.getChannel().getChannelOperateTime());
        channelInfo.setSystemCaller(model.getChannel().getSystemCaller().getCode());
        channelInfo.setSystemSubCaller(OFCSysSourceEnum.JDL_OMS.getCode());
        request.setChannelInfo(channelInfo);

        Map<String, String> extendProps = new HashMap<>();
        //改址单号
        extendProps.put(ReaddressParamEnum.READDRESS_ORDERNO.getCode(), model.orderNo());
        //改址单运单号
        extendProps.put(ReaddressParamEnum.READDRESS_WAYBILLNO.getCode(), model.getCustomOrderNo());
        //改址单操作类型
        //1:新增改址单
        //2:取消改址单
        extendProps.put(ReaddressParamEnum.READDRESS_OPERATE_TYPE.getCode(), ReaddressOperateTypeEnum.CANCEL.getCode());
        Product product = model.getProductDelegate().ofProductNo(AddOnProductEnum.READDRESS.getCode());
        //改址类型
        extendProps.put(ReaddressParamEnum.READDRESS_TYPE.getCode(), product.getProductAttrs().get(AddOnProductAttrEnum.READDRESS_TYPE.getCode()));
        request.setExtendProps(extendProps);

        //发起人类型
        request.setInitiatorType(model.getInitiatorType().getCode());
        //操作人
        request.setOperator(model.getOperator());
        //业务身份
        request.setBusinessIdentity(model.getOrderSnapshot().toBusinessIdentity());
        return request;
    }

    /**
     * 改址单或者退货单修改原单信息
     *
     * @param readdressOrder 改址单或者退货单
     * @param originalOrder 原单
     * @return
     */
    public ModifyIssueFacadeRequest toModifyOriginalOrderRequest(ExpressOrderModel readdressOrder, ExpressOrderModel originalOrder) {
        ModifyIssueFacadeRequest request = new ModifyIssueFacadeRequest();
        //订单号：原订单号
        request.setOrderNo(originalOrder.orderNo());
        //运单号：原运单号
        request.setCustomOrderNo(originalOrder.getCustomOrderNo());
        //请求信息：原单
        request.setRequestProfile(originalOrder.requestProfile());
        //业务身份：原单
        request.setBusinessIdentity(originalOrder.toBusinessIdentity());
        //操作人：订单中心
        request.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        //渠道信息
        ChannelInfo channelInfo = new ChannelInfo();
        channelInfo.setChannelOperateTime(readdressOrder.getChannel().getChannelOperateTime());
        channelInfo.setSystemCaller(readdressOrder.getChannel().getSystemCaller().getCode());
        // 拒收换单，需要OFC支持修改终态订单。传source=12，OFC会放开修改场景对状态的卡控：快递B2C、快递C2B、快运B2C（其他会被卡，所以下面还得补充）
        channelInfo.setSystemSubCaller(OFCSysSourceEnum.JDL_OMS.getCode());
        request.setChannelInfo(channelInfo);
        //扩展字段
        Map<String, String> extendProps = new HashMap<>();
        extendProps.put(OrderConstants.BUSINESS_FLOW_FLAG,"1");//订单中心修改
        // 拒收换单，需要OFC支持修改终态订单。传modifySceneRule=specialModify，OFC会放开修改场景对状态的卡控：快递b2c、快递c2b、快递c2c、快运b2c、快运c2c
        extendProps.put(ModifySceneRuleConstants.MODIFY_SCENE_RULE, ModifySceneRuleConstants.SPECIAL_MODIFY);
        request.setExtendProps(extendProps);

        //产品信息：待删除的增值服务（改址单和退货单都可能需要删除）
        if (!originalOrder.getProductDelegate().isEmpty()) {
            boolean modifyProducts = false;
            List<ProductInfo> productInfoList = new ArrayList<>();

            List<Product> products = (List<Product>) originalOrder.getProductDelegate().getProducts();
            for (Product product : products) {
                if (product.getOperateType() != null) {
                    modifyProducts = true;
                }
                if (OperateTypeEnum.DELETE == product.getOperateType()) {
                    continue;
                }
                ProductInfo productInfo = new ProductInfo();
                productInfo.setProductNo(product.getProductNo());
                productInfo.setProductName(product.getProductName());
                productInfo.setProductType(product.getProductType());
                productInfo.setParentNo(product.getParentNo());
                productInfo.setProductAttrs(product.getProductAttrs());
                productInfo.setExtendProps(product.getExtendProps());
                //产品模式
                if(product.getPattern() !=null){
                    Pattern pattern = new Pattern();
                    pattern.setPatternNo(product.getPattern().getPatternNo());
                    pattern.setPatternName(product.getPattern().getPatternName());
                    product.setPattern(pattern);
                }
                productInfoList.add(productInfo);
            }

            // 因为下发是全量覆盖，所以123级产品存在也得下发。否则配运OFC下发后，运单的123级产品标会被删除。
            if (CollectionUtils.isNotEmpty(originalOrder.getProductDelegate().getNewProducts())) {
                // 123级产品按主产品映射，主产品不变，取落库的数据即可
                List<Product> newProducts = originalOrder.getProductDelegate().getNewProducts();
                for (Product product : newProducts) {
                    ProductInfo productInfo = new ProductInfo();
                    productInfo.setProductNo(product.getProductNo());
                    productInfo.setProductName(product.getProductName());
                    productInfo.setProductType(product.getProductType());
                    productInfo.setParentNo(product.getParentNo());
                    productInfo.setProductAttrs(product.getProductAttrs());
                    productInfo.setExtendProps(product.getExtendProps());
                    productInfoList.add(productInfo);
                }
            }

            if (modifyProducts) {
                Map<String, String> modifiedFields = new HashMap<>();
                request.setProductInfos(productInfoList);
                modifiedFields.put(ModifiedFieldEnum.PRODUCT_INFOS.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());
                request.setModifiedFields(modifiedFields);
            }
        }

        // 改址单的特殊逻辑，退货单没有
        if (OrderTypeEnum.READDRESS == readdressOrder.getOrderType()) {
            //改址服务
            Product readdressProduct = readdressOrder.getProductDelegate().ofProductNo(AddOnProductEnum.READDRESS.getCode());
            //改址类型
            extendProps.put(ReaddressParamEnum.READDRESS_TYPE.getCode(), readdressProduct.getProductAttrs().get(AddOnProductAttrEnum.READDRESS_TYPE.getCode()));
            //改址结果：
            if (PaymentStageEnum.ONLINEPAYMENT == readdressOrder.getFinance().getPaymentStage()) {
                //3 先款改址成功
                extendProps.put(ReaddressParamEnum.READDRESS_OPERATE_TYPE.getCode(), ReaddressOperateTypeEnum.ONLINE_PAY_SUCCESS.getCode());
            } else if (PaymentStageEnum.CASHONDELIVERY == readdressOrder.getFinance().getPaymentStage()) {
                //4 后款改址成功
                extendProps.put(ReaddressParamEnum.READDRESS_OPERATE_TYPE.getCode(), ReaddressOperateTypeEnum.CASH_ON_DELIVERY_SUCCESS.getCode());
            }
            //财务信息
            if (originalOrder.getFinance() != null) {
                LOGGER.info("原单财务信息:{}", JSONUtils.beanToJSONDefault(originalOrder.getFinance()));
                FinanceInfo financeInfo = new FinanceInfo();
                if (originalOrder.getFinance().getSettlementType() != null) {
                    financeInfo.setSettlementType(originalOrder.getFinance().getSettlementType().getCode());
                }
                if (originalOrder.getFinance().getPaymentStage() != null) {
                    financeInfo.setPaymentStage(originalOrder.getFinance().getPaymentStage().getCode());
                }
                //TODO OFC优化后需删除
                if (originalOrder.getFinance().getPaymentStatus() != null) {
                    financeInfo.setPaymentStatus(originalOrder.getFinance().getPaymentStatus().getStatus());
                }
                request.setFinanceInfo(financeInfo);
            }
        }

        return request;
    }

    /**
     * 协议信息转换
     * @param agreementDelegate
     * @return
     */
    private List<AgreementInfo> toAgreementInfos(AgreementDelegate agreementDelegate) {
        if (null == agreementDelegate || agreementDelegate.isEmpty()) {
            return null;
        }

        return agreementDelegate.getAgreementList().stream().filter(Objects::nonNull).map(agreement -> {
            AgreementInfo agreementInfo = new AgreementInfo();
            agreementInfo.setAgreementType(agreement.getAgreementType());
            agreementInfo.setAgreementId(agreement.getAgreementId());
            agreementInfo.setSigner(agreement.getSigner());
            agreementInfo.setSigningTime(agreement.getSigningTime());
            agreementInfo.setExtendProps(agreement.getExtendProps());
            return agreementInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 快运改址下发请求转换
     */
    public ModifyIssueFacadeRequest toFreightReaddressModifyIssueFacadeRequest(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        ModifyIssueFacadeRequest request = new ModifyIssueFacadeRequest();
        // 请求信息
        request.setRequestProfile(orderModel.requestProfile());
        // 原订单号
        request.setOrderNo(orderModel.orderNo());
        // 运单号
        request.setCustomOrderNo(!StringUtils.isBlank(orderModel.getCustomOrderNo()) ? orderModel.getCustomOrderNo() : orderModel.getOrderSnapshot().getCustomOrderNo());
        // 渠道信息
        ChannelInfo channelInfo = new ChannelInfo();
        channelInfo.setChannelOperateTime(orderModel.getChannel().getChannelOperateTime());
        channelInfo.setSystemCaller(orderModel.getChannel().getSystemCaller().getCode());
        channelInfo.setSystemSubCaller(OFCSysSourceEnum.JDL_OMS.getCode());
        request.setChannelInfo(channelInfo);
        // 操作人
        request.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        // 业务身份
        request.setBusinessIdentity(orderModel.toBusinessIdentity());

        // 收货人信息
        if (orderModel.getConsignee() != null) {
            request.setConsigneeInfo(this.toConsigneeInfo(orderModel.getConsignee()));
        }

        //改址需下发的配送信息
        if (orderModel.getShipment() != null) {
            ShipmentInfo shipmentInfo = new ShipmentInfo();
            shipmentInfo.setStartStationNo(orderModel.getShipment().getStartStationNo());
            shipmentInfo.setStartStationName(orderModel.getShipment().getStartStationName());
            shipmentInfo.setStartStationType(orderModel.getShipment().getStartStationType());
            shipmentInfo.setEndStationNo(orderModel.getShipment().getEndStationNo());
            shipmentInfo.setEndStationName(orderModel.getShipment().getEndStationName());
            shipmentInfo.setEndStationType(orderModel.getShipment().getEndStationType());
            shipmentInfo.setPlanDeliveryTime(orderModel.getShipment().getPlanDeliveryTime());
            if (null != orderModel.getShipment().getTransportType()) {
                shipmentInfo.setTransportType(orderModel.getShipment().getTransportType().getCode());
            }
            request.setShipmentInfo(shipmentInfo);
        }

        // 增值产品：改址时会新增改址服务，需要下发
        if (orderModel.getProductDelegate() != null) {
            // 改址时会传改址产品，异步处理时使用全量覆盖
            Map<String, String> modifiedFields = orderModel.getModifiedFields();
            if (modifiedFields == null) {
                modifiedFields = new HashMap<>();
                orderModel.complement().complementModifiedFields(this, modifiedFields);
            }
            modifiedFields.put(ModifiedFieldEnum.PRODUCT_INFOS.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());
            request.setProductInfos(toProductInfoList(orderModel.getProductDelegate().getProducts(), orderModel));
        }

        // 改址状态不需要下发

        // 财务信息
        if (orderModel.getFinance() != null) {
            request.setFinanceInfo(toFinanceInfo(orderModel));
        }

        // 扩展信息-修改信息标记
        if (orderModel.getExtendProps() != null && orderModel.getExtendProps().size() > 0) {
            request.setExtendProps(orderModel.getExtendProps());
        }
        //指定修改的集合信息
        if (MapUtils.isNotEmpty(orderModel.getModifiedFields())) {
            request.setModifiedFields(orderModel.getModifiedFields());
        }
        return request;
    }

    /**
     * 接单下发关联单信息转换
     *
     * @param orderModel
     * @return
     */
    private RefOrderInfo toRefOrderInfo(ExpressOrderModel orderModel) {
        RefOrderDelegate refOrderDelegate = orderModel.getRefOrderInfoDelegate();
        RefOrderInfo refOrderInfo = new RefOrderInfo();
        if (null != orderModel.getModifiedFields()) {
            if (ModifiedFieldValueEnum.ALL_COVER.getCode()
                    .equals(orderModel.getModifiedFields().get(ModifiedFieldEnum.SERVICE_ENQUIRY_ORDER_NOS.getCode()))) {
                List<String> serviceEnquiryOrderNos = refOrderDelegate.getServiceEnquiryOrderNos();
                if (CollectionUtils.isNotEmpty(serviceEnquiryOrderNos)) {
                    refOrderInfo.setServiceEnquiryOrderNos(serviceEnquiryOrderNos);
                }
            }
            if (ModifiedFieldValueEnum.ALL_COVER.getCode()
                    .equals(orderModel.getModifiedFields().get(ModifiedFieldEnum.SERVICE_ENQUIRY_WAYBILL_NOS.getCode()))) {
                List<String> serviceEnquiryWaybillNos = refOrderDelegate.getServiceEnquiryWaybillNos();
                if (CollectionUtils.isNotEmpty(serviceEnquiryWaybillNos)) {
                    refOrderInfo.setServiceEnquiryWaybillNos(serviceEnquiryWaybillNos);
                }
            }
        }

        if (refOrderDelegate != null) {
            refOrderInfo.setExtendProps(refOrderDelegate.getExtendProps());
            refOrderInfo.setCollectionOrderNo(refOrderDelegate.getCollectionOrderNo());
            // 送取同步-取件单-订单号
            refOrderInfo.setPickupOrderNo(refOrderDelegate.getPickupOrderNo());
            // 送取同步-取件单-运单号
            refOrderInfo.setPickupWaybillNo(refOrderDelegate.getPickupWaybillNo());
            // 送取同步-派送单-订单号
            refOrderInfo.setDeliveryOrderNo(refOrderDelegate.getDeliveryOrderNo());
            // 送取同步-派送单-运单号
            refOrderInfo.setDeliveryWaybillNo(refOrderDelegate.getDeliveryWaybillNo());
        }
        return refOrderInfo;
    }

    /**
     * 履约信息
     */
    private FulfillmentInfo toFulfillmentInfo(Fulfillment fulfillment) {
        if (fulfillment == null) {
            return null;
        }
        FulfillmentInfo fulfillmentInfo = new FulfillmentInfo();
        fulfillmentInfo.setFulfillmentSign(fulfillment.getFulfillmentSign());
        fulfillmentInfo.setExtendProps(fulfillment.getExtendProps());
        if(fulfillment.getActualReceivedQuantity() != null) {
            QuantityInfo actualReceivedQuantity = new QuantityInfo();
            actualReceivedQuantity.setUnit(fulfillment.getActualReceivedQuantity().getUnit());
            actualReceivedQuantity.setValue(fulfillment.getActualReceivedQuantity().getValue());
            fulfillmentInfo.setActualReceivedQuantity(actualReceivedQuantity);
        }
        if(fulfillment.getActualSignedQuantity() != null) {
            QuantityInfo actualSignedQuantity = new QuantityInfo();
            actualSignedQuantity.setUnit(fulfillment.getActualSignedQuantity().getUnit());
            actualSignedQuantity.setValue(fulfillment.getActualSignedQuantity().getValue());
            fulfillmentInfo.setActualSignedQuantity(actualSignedQuantity);
        }

        if(fulfillment.getPackageMaxLen() != null) {
            LengthInfo lengthInfo = new LengthInfo();
            if(fulfillment.getPackageMaxLen().getUnit() != null) {
                lengthInfo.setUnit(fulfillment.getPackageMaxLen().getUnit().getCode());
            }
            lengthInfo.setValue(fulfillment.getPackageMaxLen().getValue());
            fulfillmentInfo.setPackageMaxLen(lengthInfo);
        }
        return fulfillmentInfo;
    }



    /**
     * 跨境报关信息转换
     */
    private CustomsInfo toCustomsInfo(Customs customs) {
        if (customs == null) {
            return null;
        }
        return CustomsMapper.INSTANCE.toCustomsInfo(customs);
    }

    /**
     * 附件信息转换
     */
    private List<AttachmentInfo> toAttachmentInfos(List<Attachment> attachments) {
        if (CollectionUtils.isEmpty(attachments)) {
            return null;
        }
        return AttachmentMapper.INSTANCE.toAttachmentInfos(attachments);
    }

    /**
     * 构建服务询价单下发入参
     * @param orderModel
     * @return
     */
    public ModifyIssueFacadeRequest toServiceEnquiryModifyIssueRequest(ExpressOrderModel orderModel){
        ModifyIssueFacadeRequest issueRequest = new ModifyIssueFacadeRequest();
        // 请求信息
        issueRequest.setRequestProfile(orderModel.requestProfile());
        // 原订单号
        issueRequest.setOrderNo(orderModel.orderNo());
        // 运单号
        issueRequest.setCustomOrderNo(orderModel.getCustomOrderNo());
        // 渠道信息
        ChannelInfo channelInfo = new ChannelInfo();
        channelInfo.setChannelOperateTime(orderModel.getChannel().getChannelOperateTime());
        channelInfo.setSystemCaller(orderModel.getChannel().getSystemCaller().getCode());
        channelInfo.setSystemSubCaller(OFCSysSourceEnum.JDL_OMS.getCode());
        issueRequest.setChannelInfo(channelInfo);
        // 操作人
        issueRequest.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        // 业务身份
        issueRequest.setBusinessIdentity(orderModel.toBusinessIdentity());

        // 产品信息全量下发
        Map<String, String> modifiedFields = orderModel.getModifiedFields();
        if (modifiedFields == null) {
            modifiedFields = new HashMap<>();
            orderModel.complement().complementModifiedFields(this, modifiedFields);
        }
        if (orderModel.getProductDelegate() != null) {
            modifiedFields.put(ModifiedFieldEnum.PRODUCT_INFOS.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());
            issueRequest.setProductInfos(toProductInfoList(orderModel.getProductDelegate().getProducts(), orderModel));
        }
        modifiedFields.put(ModifiedFieldEnum.SERVICE_ENQUIRY_ORDER_NOS.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());
        modifiedFields.put(ModifiedFieldEnum.SERVICE_ENQUIRY_WAYBILL_NOS.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());
        issueRequest.setRefOrderInfo(toRefOrderInfo(orderModel));
        // 扩展信息-修改信息标记
        if (orderModel.getExtendProps() != null && orderModel.getExtendProps().size() > 0) {
            issueRequest.setExtendProps(orderModel.getExtendProps());
        }
        //指定修改的集合信息
        if (MapUtils.isNotEmpty(orderModel.getModifiedFields())) {
            issueRequest.setModifiedFields(orderModel.getModifiedFields());
        }

        return issueRequest;
    }

    /**
     * 构建取消服务询价单下发入参
     * 实际是原单修改下发
     */
    public ModifyIssueFacadeRequest toServiceEnquiryCancelIssueRequest(ExpressOrderContext context, boolean syncModify) {
        // 当前单（服务询价单）
        ExpressOrderModel orderModel = context.getOrderModel();
        // 服务询价单快照
        ExpressOrderModel enquiryOrderSnapshot = context.getOrderModel().getOrderSnapshot();
        // 原单
        ExpressOrderModel originOrder = enquiryOrderSnapshot.getOrderSnapshot();

        ModifyIssueFacadeRequest modifyIssueFacadeRequest = new ModifyIssueFacadeRequest();
        // 请求信息：当前单
        modifyIssueFacadeRequest.setRequestProfile(orderModel.requestProfile());
        // 订单号：原单
        modifyIssueFacadeRequest.setOrderNo(originOrder.orderNo());
        // 运单号：原单
        modifyIssueFacadeRequest.setCustomOrderNo(originOrder.getCustomOrderNo());
        // 渠道信息：当前单
        modifyIssueFacadeRequest.setChannelInfo(this.toChannelInfo(orderModel.getChannel(), syncModify));
        // 操作人：当前单
        modifyIssueFacadeRequest.setOperator(orderModel.getOperator());
        // 业务身份：原单
        modifyIssueFacadeRequest.setBusinessIdentity(originOrder.toBusinessIdentity());
        // 扩展信息-修改信息标记
        Map<String, String> modifiedFields = orderModel.getModifiedFields();
        if (modifiedFields == null) {
            modifiedFields = new HashMap<>();
            orderModel.complement().complementModifiedFields(this, modifiedFields);
        }

        // 产品信息：原单删除服务单快照的增值产品之后的所有产品
        Set<String> deleteProductCodeSet = new HashSet<>();
        if (enquiryOrderSnapshot.getProductDelegate() != null && !enquiryOrderSnapshot.getProductDelegate().isEmpty()) {
            for (IProduct iProduct : enquiryOrderSnapshot.getProductDelegate().getProducts()) {
                if (ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode().equals(iProduct.getProductType())) {
                    deleteProductCodeSet.add(iProduct.getProductNo());
                }
            }
        }

        List<ProductInfo> productInfos = new ArrayList<>();
        if (originOrder.getProductDelegate() != null && !originOrder.getProductDelegate().isEmpty()) {
            for (IProduct iProduct : originOrder.getProductDelegate().getProducts()) {
                if (deleteProductCodeSet.contains(iProduct.getProductNo())) {
                    continue;
                }
                Product product = (Product) iProduct;
                ProductInfo productInfo = new ProductInfo();
                productInfo.setProductNo(product.getProductNo());
                productInfo.setProductName(product.getProductName());
                productInfo.setProductType(product.getProductType());
                productInfo.setParentNo(product.getParentNo());
                productInfo.setProductAttrs(product.getProductAttrs());
                productInfo.setExtendProps(product.getExtendProps());
                //产品模式
                if(product.getPattern() !=null){
                    Pattern pattern = new Pattern();
                    pattern.setPatternNo(product.getPattern().getPatternNo());
                    pattern.setPatternName(product.getPattern().getPatternName());
                    product.setPattern(pattern);
                }
                productInfos.add(productInfo);
            }
        }
        if (!productInfos.isEmpty()) {
            modifiedFields.put(ModifiedFieldEnum.PRODUCT_INFOS.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());
            modifyIssueFacadeRequest.setProductInfos(productInfos);
        }

        // 清空服务询价单、服务询价单运单：与zengni5沟通，不支持清空，且终端不从运单取关联单号，实际修改为固定值“已删除”
        RefOrderInfo refOrderInfo = new RefOrderInfo();
        List<String> serviceEnquiryOrderNos = new ArrayList<>(1);
        serviceEnquiryOrderNos.add("已删除");
        refOrderInfo.setServiceEnquiryOrderNos(serviceEnquiryOrderNos);
        List<String> serviceEnquiryWaybillNos = new ArrayList<>(1);
        serviceEnquiryWaybillNos.add("已删除");
        refOrderInfo.setServiceEnquiryWaybillNos(serviceEnquiryWaybillNos);
        modifiedFields.put(ModifiedFieldEnum.SERVICE_ENQUIRY_ORDER_NOS.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());
        modifiedFields.put(ModifiedFieldEnum.SERVICE_ENQUIRY_WAYBILL_NOS.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());

        modifyIssueFacadeRequest.setRefOrderInfo(refOrderInfo);

        // 扩展信息-修改信息标记
        if (orderModel.getExtendProps() != null && orderModel.getExtendProps().size() > 0) {
            modifyIssueFacadeRequest.setExtendProps(orderModel.getExtendProps());
        }
        // 指定修改的集合信息
        if (MapUtils.isNotEmpty(orderModel.getModifiedFields())) {
            modifyIssueFacadeRequest.setModifiedFields(orderModel.getModifiedFields());
        }
        return modifyIssueFacadeRequest;
    }

    /**
     * 构造回传下发入参
     *
     * @param orderModel
     * @return
     */
    public ModifyIssueFacadeRequest toCallbackIssueFacadeRequest(ExpressOrderModel orderModel) {

        ModifyIssueFacadeRequest issueRequest = new ModifyIssueFacadeRequest();
        // 请求信息
        issueRequest.setRequestProfile(orderModel.requestProfile());
        // 原订单号
        issueRequest.setOrderNo(orderModel.orderNo());
        // 运单号
        issueRequest.setCustomOrderNo(orderModel.getOrderSnapshot().getCustomOrderNo());
        // 渠道信息
        ChannelInfo channelInfo = new ChannelInfo();
        channelInfo.setChannelOperateTime(orderModel.getChannel().getChannelOperateTime());
        channelInfo.setSystemCaller(orderModel.getChannel().getSystemCaller().getCode());
        channelInfo.setSystemSubCaller(OFCSysSourceEnum.JDL_OMS.getCode());
        issueRequest.setChannelInfo(channelInfo);
        // 操作人
        issueRequest.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        // 业务身份
        issueRequest.setBusinessIdentity(orderModel.toBusinessIdentity());

        Map<String, String> extendProps = new HashMap<>();
        // 扩展信息-修改信息标记-设置修改策略，避免被运单卡控
        extendProps.put(ModifySceneRuleConstants.MODIFY_SCENE_RULE, ModifySceneRuleConstants.AFTER_PICKUP);
        // 扩展信息-超长超重附加费
        if (hasOverLengthAndWeightAttachFee(orderModel)) {
            // 设置超长超重附加费编码，修改下发OFC后，OFC追加到运单上
            extendProps.put(EnquiryConstants.OVER_LENGTH_AND_WEIGHT_ATTACH_FEE, EnquiryConstants.CCCZF_0002);
        } else {
            // 设置空串，修改下发OFC后，OFC清除运单上的超长超重附加费
            extendProps.put(EnquiryConstants.OVER_LENGTH_AND_WEIGHT_ATTACH_FEE, "");
        }
        issueRequest.setExtendProps(extendProps);

        return issueRequest;
    }

    /**
     * 判断订单是否有超长超重附加费
     *
     * --附加费通过产品中心附加费查询能力写入，因此，回传下发节点需要在产品中心附加费查询节点之后
     *
     * @param orderModel
     * @return
     */
    private boolean hasOverLengthAndWeightAttachFee(ExpressOrderModel orderModel) {

        boolean hasOverLengthAndWeightAttachFee = false;

        // 获取财务-附加费信息
        List<cn.jdl.oms.express.domain.vo.CostInfo> attachFees = orderModel.getFinance().getAttachFees();

        if (CollectionUtils.isEmpty(attachFees)) {
            return false;
        }

        // 检查是否存在超长超重附加费
        for (cn.jdl.oms.express.domain.vo.CostInfo attachFee : attachFees) {
            if (EnquiryConstants.CCCZF_0002.equals(attachFee.getCostNo())) {
                hasOverLengthAndWeightAttachFee = true;
                break;
            }
        }

        return hasOverLengthAndWeightAttachFee;
    }

    /**
     *
     * @param orderModel 原单
     * @param profile 请求profile
     * @return
     */
    public ModifyIssueFacadeRequest toClearPaymentFacadeRequest(ExpressOrderModel orderModel, RequestProfile profile) {
        ModifyIssueFacadeRequest issueRequest = new ModifyIssueFacadeRequest();
        // 请求信息
        issueRequest.setRequestProfile(profile);
        // 原订单号
        issueRequest.setOrderNo(orderModel.orderNo());
        // 运单号
        issueRequest.setCustomOrderNo(orderModel.getCustomOrderNo());
        // 渠道信息
        ChannelInfo channelInfo = new ChannelInfo();
        channelInfo.setChannelOperateTime(new Date());
        channelInfo.setSystemCaller(SystemCallerEnum.EXPRESS_OMS.getCode());
        channelInfo.setSystemSubCaller(OFCSysSourceEnum.JDL_OMS.getCode());
        issueRequest.setChannelInfo(channelInfo);
        // 操作人
        issueRequest.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        // 业务身份
        issueRequest.setBusinessIdentity(orderModel.toBusinessIdentity());

        Map<String, String> extendProps = new HashMap<>();
        // 扩展信息-修改信息标记-设置修改策略，避免被运单卡控
        extendProps.put(ModifySceneRuleConstants.MODIFY_SCENE_RULE, ModifySceneRuleConstants.AFTER_PICKUP);
        issueRequest.setExtendProps(extendProps);

        // 清空支付方式
        FinanceInfo financeInfo = new FinanceInfo();
        financeInfo.setPayment(-128);
        issueRequest.setFinanceInfo(financeInfo);

        return issueRequest;
    }
    private ModifyData toModifyData(ExpressOrderContext context) {
        ExpressOrderModel orderModel = context.getOrderModel();
        // 目前只有大件需要下发对比字段
        if (!context.getOrderModel().isLAS()) {
            return null;
        }
        // 获取配置的需下发的对比字段
        List<String> fields = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.ISSUE_ALLOW_MODIFY_DATA_ITEM, ",");
        if (CollectionUtils.isEmpty(fields) && fields.get(0).isEmpty()) {
            LOGGER.info("未配置对比字段，不进行数据记录下发");
            return null;
        }

        // 获取修改项
        ChangedPropertyDelegate changedPropertyDelegate = context.getChangedPropertyDelegate();
        if (changedPropertyDelegate == null) {
            return null;
        }

        List<OrderDataFieldEnum> fieldsEnum = fields.stream()
                .map(OrderDataFieldEnum::valueOf)
                .collect(Collectors.toList());
        List<ModifyDataInfo> snapshotInfos = new ArrayList<>(fields.size());
        List<ModifyDataInfo> modifyInfos = new ArrayList<>(fields.size());

        fieldsEnum.forEach(field -> {
            if (changedPropertyDelegate.propertyHasChange(ModifyItemConfigEnum.valueOf(field.name()))) {
                snapshotInfos.add(this.toModifyDataInfo(orderModel.getOrderSnapshot(), field));
                modifyInfos.add(this.toModifyDataInfo(orderModel, field));
            }
        });
        ModifyData modifyData = new ModifyData();
        modifyData.setSnapshotInfos(snapshotInfos);
        modifyData.setModifyInfos(modifyInfos);
        return modifyData;
    }

    /**
     * 数据信息转换
     *
     * @param orderModel
     * @param field
     * @return
     */
    private ModifyDataInfo toModifyDataInfo(ExpressOrderModel orderModel, OrderDataFieldEnum field) {
        if (orderModel == null || field == null) {
            return null;
        }
        ModifyDataInfo modifyDataInfo = new ModifyDataInfo();
        modifyDataInfo.setField(field.getField());
        modifyDataInfo.setValue(field.getValue(orderModel));
        return modifyDataInfo;
    }

    /**
     * 已执行取件单绑定派送单(上游调订单接单接口传取件单)，异步处理派送单
     * @param deliveryOrder 派送单
     */
    public ModifyIssueFacadeRequest toAsyncPickupBindDelivery(ExpressOrderModel deliveryOrder, String refOrderNo, String refWaybillNo) {
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = toCommonAsyncDeliveryPickupSync(deliveryOrder);

        // 关联单信息
        RefOrderInfo refOrderInfo = new RefOrderInfo();
        // 送取同步-取件单-订单号
        refOrderInfo.setPickupOrderNo(refOrderNo);
        // 送取同步-取件单-运单号
        refOrderInfo.setPickupWaybillNo(refWaybillNo);
        modifyIssueFacadeRequest.setRefOrderInfo(refOrderInfo);

        return modifyIssueFacadeRequest;
    }

    /**
     * 已执行取件单解除绑定派送单(上游调订单修改接口传取件单)，异步处理派送单
     * @param deliveryOrder 派送单
     */
    public ModifyIssueFacadeRequest toAsyncPickupUnbindDelivery(ExpressOrderModel deliveryOrder, String refOrderNo, String refWaybillNo) {
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = toCommonAsyncDeliveryPickupSync(deliveryOrder);

        // 关联单信息：需要给下游传解绑单号
        RefOrderInfo refOrderInfo = new RefOrderInfo();
        // 送取同步-取件单-订单号
        refOrderInfo.setPickupOrderNo(refOrderNo);
        // 送取同步-取件单-运单号
        refOrderInfo.setPickupWaybillNo(refWaybillNo);
        modifyIssueFacadeRequest.setRefOrderInfo(refOrderInfo);

        return modifyIssueFacadeRequest;
    }

    /**
     * 已执行派送单解除绑定取件单(上游调订单修改接口传派送单)，异步处理取件单
     * @param pickupOrder 取件单
     */
    public ModifyIssueFacadeRequest toAsyncDeliveryUnbindPickup(ExpressOrderModel pickupOrder, String refOrderNo, String refWaybillNo) {
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = toCommonAsyncDeliveryPickupSync(pickupOrder);

        // 关联单信息：需要给下游传解绑单号
        RefOrderInfo refOrderInfo = new RefOrderInfo();
        // 送取同步-派送单-订单号
        refOrderInfo.setDeliveryOrderNo(refOrderNo);
        // 送取同步-派送单-运单号
        refOrderInfo.setDeliveryWaybillNo(refWaybillNo);
        modifyIssueFacadeRequest.setRefOrderInfo(refOrderInfo);

        return modifyIssueFacadeRequest;
    }

    /**
     * 送取同步绑定或解除绑定关联单，异步处理公共请求
     * @param orderModel 取件单或者派送单
     */
    private ModifyIssueFacadeRequest toCommonAsyncDeliveryPickupSync(ExpressOrderModel orderModel) {
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = new ModifyIssueFacadeRequest();
        // 请求信息
        modifyIssueFacadeRequest.setRequestProfile(orderModel.requestProfile());
        // 订单号
        modifyIssueFacadeRequest.setOrderNo(orderModel.orderNo());
        // 运单号
        modifyIssueFacadeRequest.setCustomOrderNo(orderModel.getCustomOrderNo());
        // 业务身份
        modifyIssueFacadeRequest.setBusinessIdentity(orderModel.toBusinessIdentity());
        // 渠道信息：syncModify=false，非同步修改
        modifyIssueFacadeRequest.setChannelInfo(this.toChannelInfo(orderModel.getChannel(), false));
        // 操作人：订单中心
        modifyIssueFacadeRequest.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());

        // 订单标识：orderSign.deliveryPickupSync
        modifyIssueFacadeRequest.setOrderSign(orderModel.getOrderSign());

        // 派送信息：Shipment.extendProps.shipmentExtendProps.sendAndPickupType
        ShipmentInfo shipmentInfo = new ShipmentInfo();
        shipmentInfo.setExtendProps(orderModel.getShipment().getExtendProps());
        modifyIssueFacadeRequest.setShipmentInfo(shipmentInfo);

        // 关联单：不在公共方法，在各自单独方法

        return modifyIssueFacadeRequest;
    }


    /**
     * 修改下发请求转换
     *
     * @param context
     * @return
     * <AUTHOR>
     */
    public ModifyIssueFacadeRequest toOnlyModifyFinanceIssueFacadeRequest(ExpressOrderContext context) throws ParseException {
        if (context == null || context.getOrderModel() == null) {
            return null;
        }
        ExpressOrderModel orderModel = context.getOrderModel();
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = new ModifyIssueFacadeRequest();
        modifyIssueFacadeRequest.setRequestProfile(orderModel.requestProfile());
        modifyIssueFacadeRequest.setOrderNo(orderModel.orderNo());
        modifyIssueFacadeRequest.setCustomOrderNo(!StringUtils.isBlank(orderModel.getCustomOrderNo()) ? orderModel.getCustomOrderNo() : orderModel.getOrderSnapshot().getCustomOrderNo());
        //渠道信息
        modifyIssueFacadeRequest.setChannelInfo(this.toChannelInfo(orderModel.getChannel(), false));
        //财务信息
        modifyIssueFacadeRequest.setFinanceInfo(this.toFinanceInfo(orderModel));
        //扩展字段
        Map<String, String> ext = new HashMap();
        // 防止改变入参扩展字段，此处拷贝一份
        if (MapUtils.isNotEmpty(orderModel.getExtendProps())) {
            for (Map.Entry<String, String> entry : orderModel.getExtendProps().entrySet()) {
                ext.put(entry.getKey(), entry.getValue());
            }
        }
        ext.put(OrderConstants.BUSINESS_FLOW_FLAG, "1");//订单中心修改
        // 内部修改下发时，将修改策略改为 specialModify
        if (ext.containsKey(ModifySceneRuleConstants.MODIFY_SCENE_RULE)) {
            String modifySceneRule = String.valueOf(ext.get(ModifySceneRuleConstants.MODIFY_SCENE_RULE));
            if (StringUtils.isNotBlank(modifySceneRule)
                    && modifySceneRule.startsWith(ModifySceneRuleConstants.INTERNAL_MODIFY_PREFIX)) {

                ext.put(ModifySceneRuleConstants.MODIFY_SCENE_RULE, ModifySceneRuleConstants.SPECIAL_MODIFY);

            }
        }
        modifyIssueFacadeRequest.setExtendProps(ext);
        //需清空的字段数据
        clearFieldData(modifyIssueFacadeRequest, orderModel);
        modifyIssueFacadeRequest.setBusinessIdentity(orderModel.toBusinessIdentity());
        return modifyIssueFacadeRequest;
    }
}
