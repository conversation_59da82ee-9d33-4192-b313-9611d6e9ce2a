package cn.jdl.oms.express.domain.infrs.acl.util;

import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 原单修改工具类
 */
@Component
@Slf4j
public class OriginalOrderModifyUtil {

    /**
     * 改址单、逆向单接单成功删除原单的增值产品名单
     */
    public List<String> reverseOrReaddressDeleteOriginalProduct(ExpressOrderModel model) {
        List<String> productNos = new ArrayList<>();

        // 新单必须是逆向单或改址单
        if (OrderTypeEnum.RETURN_ORDER != model.getOrderType()
                && OrderTypeEnum.READDRESS != model.getOrderType()) {
            return productNos;
        }

        // 原单必须是到付或者月结
        ExpressOrderModel orderSnapshot = model.getOrderSnapshot();
        SettlementTypeEnum snapshotSettlementType = orderSnapshot.getFinance().getSettlementType();
        if (SettlementTypeEnum.CASH_ON_DELIVERY != snapshotSettlementType
                && SettlementTypeEnum.MONTHLY_PAYMENT != snapshotSettlementType) {
            return productNos;
        }

        // 新单是改址单 并且 新单月结 并且 原单到付，不处理（产品反馈计费可以处理这种情况）
        SettlementTypeEnum settlementType = model.getFinance().getSettlementType();
        if (OrderTypeEnum.READDRESS == model.getOrderType()
                && SettlementTypeEnum.MONTHLY_PAYMENT == settlementType
                && SettlementTypeEnum.CASH_ON_DELIVERY == snapshotSettlementType) {
            return productNos;
        }

        // 必须是配置的增值产品
        Set<String> deleteProductNoSet = getOriginOrderEnquiryProductNoBlacklist();
        if (!deleteProductNoSet.isEmpty()
                && orderSnapshot.getProductDelegate() != null
                && !orderSnapshot.getProductDelegate().isEmpty()) {
            List<Product> products = (List<Product>) orderSnapshot.getProductDelegate().getProducts();
            for (Product product : products) {
                if (deleteProductNoSet.contains(product.getProductNo())) {
                    productNos.add(product.getProductNo());
                }
            }
        }

        if (CollectionUtils.isNotEmpty(productNos)) {
            log.info("需要删除的产品=" + JSONUtils.beanToJSONDefault(productNos));
        }
        return productNos;
    }

    /**
     * 获取异步询价，原单不参与询价的增值产品
     */
    public Set<String> getOriginOrderEnquiryProductNoBlacklist() {
        // 改址单、逆向单接单成功删除原单的增值产品名单：此配置主要是避免异步删除延迟、异步删除失败，导致原单多计费：https://joyspace.jd.com/pages/CAAeERwjW4rXKCafN5Ae
        return new HashSet<>(BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.REVERSE_OR_READDRESS_DELETE_ORIGINAL_PRODUCT_NO_LIST,","));
    }
}