package cn.jdl.oms.express.domain.infrs.ohs.locals.es.orderflow;

import cn.jdl.batrix.spec.BApiResult;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.infrs.annotation.ConvertMode;
import cn.jdl.oms.express.domain.infrs.annotation.UnitedBusinessIdentityConverter;
import cn.jdl.oms.express.domain.infrs.ohs.locals.es.translator.ExpressOrderFlowTranslator;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.BdWaybillUploadTraceDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.CallbackCouponProcessMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.CancelOrderMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.CommonJmqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressOrderFlowMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ExpressResourceReleaseMsgDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderBankFlowDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderDataFlowDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderDataUpdateDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderRightDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ReleasePaymentOrderMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.RetryOrderJmqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.ordernotice.CreateOrderNotice;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.vo.Enquiry;
import cn.jdl.oms.express.model.CallBackExpressOrderRequest;
import cn.jdl.oms.express.model.CancelExpressOrderRequest;
import cn.jdl.oms.express.model.CreateExpressOrderRequest;
import cn.jdl.oms.express.model.DeleteExpressOrderRequest;
import cn.jdl.oms.express.model.EnquiryExpressOrderRequest;
import cn.jdl.oms.express.model.InterceptExpressOrderRequest;
import cn.jdl.oms.express.model.ModifyExpressOrderFinanceRequest;
import cn.jdl.oms.express.model.ModifyExpressOrderRequest;
import cn.jdl.oms.express.model.PayExpressOrderData;
import cn.jdl.oms.express.model.PayExpressOrderRequest;
import cn.jdl.oms.express.model.ReacceptExpressOrderRequest;
import cn.jdl.oms.express.model.RecoverExpressOrderRequest;
import cn.jdl.oms.express.model.RefundExpressOrderData;
import cn.jdl.oms.express.model.RefundExpressOrderRequest;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.thread.MdcThreadPoolExecutor;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.alibaba.fastjson.JSON;
import com.jd.traceholder.TraceHolderExecutors;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * @Package： cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl
 * @ClassName: SchedulerService
 * @Description: 订单中心纯配jmq添加
 * @Author： wnaghuanhuan632
 * @CreateDate 2021/3/20 4:41 下午
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
@Component
public class ExpressOrderFlowService {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ExpressOrderFlowService.class);

    /**
     * 转换
     */
    @Resource
    private ExpressOrderFlowTranslator expressOrderFlowTranslator;

    /**
     * 配置中心
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 批量执行线程池
     */
    private ExecutorService threadPoolExecutor;

    /**
     * 核心线程大小
     */
    private int corePoolSize = 10;

    /**
     * 线程池大小
     */
    private int threadPoolSize = 50;

    /**
     * 线程队列大小
     */
    private int threadQueueSize = 10000;

    @Resource
    private UmpUtil umpUtil;

    /**
     * 台账流水消息体producer
     */
    @Resource
    private JMQMessageProducer orderBankFlowMessageProducer;
    /**
     * 回传订单操作记录
     */
    @Resource
    private JMQMessageProducer callbackRecordProducer;

    /**
     * 取消订单操作记录
     */
    @Resource
    private JMQMessageProducer cancelRecordProducer;

    /**
     * 删除订单操作记录
     */
    @Resource
    private JMQMessageProducer deleteOrderMessageProducer;

    /**
     * 恢复订单操作记录
     */
    @Resource
    private JMQMessageProducer recoverOrderMessageProducer;

    /**
     * 订单重处理操作记录
     */
    @Resource
    private JMQMessageProducer reacceptOrderMessageProducer;

    /**
     * 修改订单操作记录
     */
    @Resource
    private JMQMessageProducer modifyOrderMessageProducer;

    /**
     * 接单操作记录
     */
    @Resource
    private JMQMessageProducer receiveRecordProducer;

    /**
     * 询价操作记录
     */
    @Resource
    private JMQMessageProducer enquiryRecordProducer;

    /**
     * 询价接口发送询价结果消息
     */
    @Resource
    private JMQMessageProducer expressOrderEnquiryRecordProducer;

    /**
     * 拦截操作记录
     */
    @Resource
    private JMQMessageProducer interceptRecordProducer;

    /**
     * 支付订单操作记录
     */
    @Resource
    private JMQMessageProducer payRecordProducer;

    /**
     * 订单数据流水记录生产者
     */
    @Resource
    private JMQMessageProducer orderDataFlowMessageProducer;

    /**
     * 发送订单数据变更记录
     */
    @Resource
    private JMQMessageProducer orderDataUpdateNoticeMessageProducer;

    @Resource
    private JMQMessageProducer createOrderNoticeMessageProducer;

    /**
     * 订单退款操作记录
     */
    @Resource
    private JMQMessageProducer refundRecordProducer;

    /**
     * 订单数据更新消息生产者
     */
    @Resource
    private JMQMessageProducer orderDataUpdateMessageProducer;

    /**
     * 订单-权益核销-生产者
     * 订单主状态变更为妥投/拒收时
     */
    @Resource
    private JMQMessageProducer rightConfirmMessageProducer;

    /**
     * 订单--权益释放--生产者
     * 订单主状态变更为取消时
     */
    @Resource
    private JMQMessageProducer rightReleaseMessageProducer;

    /**
     * 资源释放JMQ生产者
     */
    @Resource
    private JMQMessageProducer orderResourceReleaseJmqProducer;

    @Resource
    private JMQMessageProducer orderTrackProducer;

    @Resource
    private JMQMessageProducer expressInvoiceJmqProducer;

    @Resource
    private JMQMessageProducer retryOrderJmqProducer;

    /**
     * 收发管家运费预占
     */
    @Resource
    private JMQMessageProducer prechargeOccupyJmqProducer;

    /**
     * 异步取消服务单
     */
    @Resource
    private JMQMessageProducer cancelServiceOrderJmqProducer;

    /**
     * 异步取消支付单
     */
    @Resource
    private JMQMessageProducer releasePaymentOrderJmqProducer;

    /**
     * 异步事后折询价
     */
    @Resource
    private JMQMessageProducer asyncStandardProductAndDiscountEnquiryJmqProducer;

    /**
     * 回传优惠券处理生产者
     */
    @Resource
    private JMQMessageProducer callbackCouponProcessProducer;

    @Value("${express.order.run.environment}")
    private String environment;

    @PostConstruct
    public void init() {
        //开启多线程并行查询服务
        threadPoolExecutor = TraceHolderExecutors.getTraceHolderExecutorService(MdcThreadPoolExecutor.newWithInheritedMdc(corePoolSize, threadPoolSize, 60L, TimeUnit.SECONDS
                , new ArrayBlockingQueue<Runnable>(threadQueueSize, true)));
        LOGGER.info("纯配系统线程池初始化成功:corePoolSize= {},threadPoolSize={}, threadQueueSize={} ", corePoolSize, threadPoolSize, threadQueueSize);
    }

    /**
     * @param
     * @return
     * @throws
     * @throws
     * @Description 发送回传消息数据
     * <AUTHOR>
     * @createDate 2021/6/23 6:14 下午
     * @lastModify
     */
    public void sendCallbackOrderRecordMq(ExpressOrderContext context, BApiResult apiResult, CallBackExpressOrderRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendCallbackOrderRecordMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            //发消息开关
            if (expressUccConfigCenter.isSendCallBackFlowMessageSwitch()) {
                ExpressOrderFlowMessageDto messageDto = expressOrderFlowTranslator.getOrderRecordMessageDto(context.getOrderModel(), apiResult, request);
                LOGGER.info("sendCallbackOrderRecordMq发送记录消息,callBackRecord:{}", JSONUtils.beanToJSONDefault(messageDto));
                threadPoolExecutor.submit(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            callbackRecordProducer.send(messageDto.getOrderNo(), JSONUtils.beanToJSONDefault(messageDto), null);
                        } catch (Exception e) {
                            LOGGER.error("订单记录发送mq失败", e);
                        }
                    }
                });
            } else {
                LOGGER.info("记录发送消息开关关闭，不发送消息");
            }
        } catch (Exception e) {
            LOGGER.error("订单记录发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * @param
     * @return
     * @throws
     * @throws
     * @Description 发送取消数据
     * <AUTHOR>
     * @createDate 2021/6/23 6:16 下午
     * @lastModify
     */
    public void sendCancelOrderRecordMq(ExpressOrderContext context, BApiResult apiResult, CancelExpressOrderRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendCancelOrderRecordMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            //发消息开关
            if (expressUccConfigCenter.isSendCancelFlowMessageSwitch()) {
                ExpressOrderFlowMessageDto messageDto = expressOrderFlowTranslator.getOrderRecordMessageDto(context.getOrderModel(), apiResult, request);
                LOGGER.info("sendCancelOrderRecordMq发送记录消息,messageDto:{}", JSONUtils.beanToJSONDefault(messageDto));
                threadPoolExecutor.submit(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            cancelRecordProducer.send(messageDto.getOrderNo(), JSONUtils.beanToJSONDefault(messageDto), null);
                        } catch (Exception e) {
                            LOGGER.error("订单记录发送mq失败", e);
                        }
                    }
                });
            } else {
                LOGGER.info("记录发送消息开关关闭，不发送消息");
            }
        } catch (Exception e) {
            LOGGER.error("订单记录发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * @param
     * @return
     * @throws
     * @throws
     * @Description 发送删单消息
     * <AUTHOR>
     * @createDate 2021/6/23 6:16 下午
     * @lastModify
     */
    public void sendDeleteOrderRecordMq(ExpressOrderContext context, BApiResult apiResult, DeleteExpressOrderRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendDeleteOrderRecordMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            //发消息开关
            if (expressUccConfigCenter.isSendDeleteFlowMessageSwitch()) {
                ExpressOrderFlowMessageDto messageDto = expressOrderFlowTranslator.getOrderRecordMessageDto(context.getOrderModel(), apiResult, request);
                LOGGER.info("sendDeleteOrderRecordMq发送记录消息,messageDto:{}", JSONUtils.beanToJSONDefault(messageDto));
                threadPoolExecutor.submit(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            deleteOrderMessageProducer.send(messageDto.getOrderNo(), JSONUtils.beanToJSONDefault(messageDto), null);
                        } catch (Exception e) {
                            LOGGER.error("订单记录发送mq失败", e);
                        }
                    }
                });
            } else {
                LOGGER.info("记录发送消息开关关闭，不发送消息");
            }
        } catch (Exception e) {
            LOGGER.error("订单记录发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * @Description 发送恢复消息
     */
    public void sendRecoverOrderRecordMq(ExpressOrderContext context, BApiResult apiResult, RecoverExpressOrderRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendRecoverOrderRecordMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            //发消息开关
            if (expressUccConfigCenter.isSendDeleteFlowMessageSwitch()) {
                ExpressOrderFlowMessageDto messageDto = expressOrderFlowTranslator.getOrderRecordMessageDto(context.getOrderModel(), apiResult, request);
                LOGGER.info("sendRecoverOrderRecordMq发送记录消息,messageDto:{}", JSONUtils.beanToJSONDefault(messageDto));
                threadPoolExecutor.submit(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            recoverOrderMessageProducer.send(messageDto.getOrderNo(), JSONUtils.beanToJSONDefault(messageDto), null);
                        } catch (Exception e) {
                            LOGGER.error("订单记录发送mq失败", e);
                        }
                    }
                });
            } else {
                LOGGER.info("记录发送消息开关关闭，不发送消息");
            }
        } catch (Exception e) {
            LOGGER.error("订单记录发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * @param
     * @return
     * @throws
     * @throws
     * @Description 发送修改订单数据
     * <AUTHOR>
     * @createDate 2021/6/23 6:17 下午
     * @lastModify
     */
    public void sendModifyOrderRecordMq(ExpressOrderContext context, BApiResult apiResult, ModifyExpressOrderRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendModifyOrderRecordMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            //发消息开关
            if (expressUccConfigCenter.isSendModifyFlowMessageSwitch()) {
                ExpressOrderFlowMessageDto messageDto = expressOrderFlowTranslator.getOrderRecordMessageDto(context.getOrderModel(), apiResult, request);
                LOGGER.info("sendModifyOrderRecordMq发送记录消息,messageDto:{}", JSONUtils.beanToJSONDefault(messageDto));
                threadPoolExecutor.submit(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            modifyOrderMessageProducer.send(messageDto.getOrderNo(), JSONUtils.beanToJSONDefault(messageDto), null);
                        } catch (Exception e) {
                            LOGGER.error("订单记录发送mq失败", e);
                        }
                    }
                });
            } else {
                LOGGER.info("记录发送消息开关关闭，不发送消息");
            }
        } catch (Exception e) {
            LOGGER.error("订单记录发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * @param
     * @return
     * @throws
     * @throws
     * @Description 发送接单数据
     * <AUTHOR>
     * @createDate 2021/6/23 6:17 下午
     * @lastModify
     */
    @UnitedBusinessIdentityConverter(convertMode = ConvertMode.BEFORE_ORIGINAL_AFTER_REAL)
    public void sendReceiveOrderRecordMq(ExpressOrderContext context, BApiResult apiResult, CreateExpressOrderRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendReceiveOrderRecordMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            //发消息开关
            if (expressUccConfigCenter.isSendCreateFlowMessageSwitch()) {
                ExpressOrderFlowMessageDto messageDto = expressOrderFlowTranslator.getOrderRecordMessageDto(context.getOrderModel(), apiResult, request);
                threadPoolExecutor.submit(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            receiveRecordProducer.send(messageDto.getOrderNo(), JSONUtils.beanToJSONDefault(messageDto), null);
                        } catch (Exception e) {
                            LOGGER.error("订单记录发送mq失败", e);
                        }
                    }
                });
            } else {
                LOGGER.info("记录发送消息开关关闭，不发送消息");
            }
        } catch (Exception e) {
            LOGGER.error("订单记录发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 台账记录
     *
     * @param orderBankFlowDto
     */
    public void sendOrderBankRecordMq(OrderBankFlowDto orderBankFlowDto) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendOrderBankRecordMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            //发消息开关
            if (expressUccConfigCenter.isSendMessageSwitch()) {
                String sendOrderBankRecordMq = JSONUtils.beanToJSONDefault(orderBankFlowDto);
                LOGGER.info("sendOrderBankRecordMq发送记录消息,orderBankFlowDto:{}", sendOrderBankRecordMq);
                threadPoolExecutor.submit(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            orderBankFlowMessageProducer.send(orderBankFlowDto.getOrderBankFacadeRequest().getUUid(), sendOrderBankRecordMq, null);
                        } catch (Exception e) {
                            LOGGER.error("订单台账记录发送mq失败", e);
                        }
                    }
                });
            } else {
                LOGGER.info("记录发送消息开关关闭，不发送消息");
            }
        } catch (Exception e) {
            LOGGER.error("订单台账记录发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 询价记录
     */
    @UnitedBusinessIdentityConverter(convertMode = ConvertMode.BEFORE_ORIGINAL_AFTER_REAL)
    public void sendEnquiryRecordMq(ExpressOrderContext context, BApiResult apiResult, Enquiry enquiry) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendReceiveOrderRecordMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            //发消息开关
            if (expressUccConfigCenter.isSendCreateFlowMessageSwitch()) {
                ExpressOrderFlowMessageDto messageDto = expressOrderFlowTranslator.getOrderRecordMessageDto(context.getOrderModel(), apiResult, enquiry);
                LOGGER.info("sendEnquiryRecordMq发送记录消息,messageDto:{}", JSONUtils.beanToJSONDefault(messageDto));
                threadPoolExecutor.submit(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            enquiryRecordProducer.send(messageDto.getOrderNo(), JSONUtils.beanToJSONDefault(messageDto), null);
                        } catch (Exception e) {
                            LOGGER.error("订单记录发送mq失败", e);
                        }
                    }
                });
            } else {
                LOGGER.info("记录发送消息开关关闭，不发送消息");
            }
        } catch (Exception e) {
            LOGGER.error("订单记录发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 询价接口发送询价结果消息
     */
    @UnitedBusinessIdentityConverter(convertMode = ConvertMode.BEFORE_ORIGINAL_AFTER_REAL)
    public void sendExpressOrderEnquiryRecordMq(RequestProfile requestProfile, ExpressOrderContext context, BApiResult apiResult, EnquiryExpressOrderRequest request, Date equiryTime) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendExpressOrderEnquiryRecordMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            //发消息开关
            if (expressUccConfigCenter.isSendExpressOrderEnquiryRecordSwitch()) {
                ExpressOrderFlowMessageDto messageDto = expressOrderFlowTranslator.getEnquiryOrderRecordMessageDto(context, apiResult, request, requestProfile, equiryTime);
                Map<String, String> attributes = new HashMap<>();
                if (Objects.nonNull(messageDto) && StringUtils.isNotBlank(messageDto.getBusinessUnit())) {
                    attributes.put("businessUnit", messageDto.getBusinessUnit());
                }
                if (Objects.nonNull(messageDto) && StringUtils.isNotBlank(messageDto.getBusinessScene())) {
                    attributes.put("businessScene", messageDto.getBusinessScene());
                }
                if (Objects.nonNull(apiResult) && StringUtils.isNotBlank(apiResult.getCode())) {
                    attributes.put("apiResultCode", apiResult.getCode());
                }
                if (Objects.nonNull(messageDto) && StringUtils.isNotBlank(messageDto.getOriginSystemCaller())) {
                    attributes.put("originSystemCaller", messageDto.getOriginSystemCaller());
                }

                LOGGER.info("sendExpressOrderEnquiryRecordMq发送记录消息,messageDto:{}, attributes:{}", JSONUtils.beanToJSONDefault(messageDto), JSONUtils.beanToJSONDefault(attributes));
                threadPoolExecutor.submit(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            expressOrderEnquiryRecordProducer.send(messageDto.getOrderNo(), JSONUtils.beanToJSONDefault(messageDto), attributes);
                        } catch (Exception e) {
                            LOGGER.error("sendExpressOrderEnquiryRecordMq发送记录消息失败", e);
                        }
                    }
                });
            } else {
                LOGGER.info("sendExpressOrderEnquiryRecordMq发送记录消息开关关闭，不发送消息");
            }
        } catch (Exception e) {
            LOGGER.error("sendExpressOrderEnquiryRecordMq发送记录消息失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 拦截记录
     */
    public void sendInterceptOrderRecordMq(ExpressOrderContext context, BApiResult apiResult, InterceptExpressOrderRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendReceiveOrderRecordMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            //发消息开关
            if (expressUccConfigCenter.isSendCreateFlowMessageSwitch()) {
                ExpressOrderFlowMessageDto messageDto = expressOrderFlowTranslator.getOrderRecordMessageDto(context.getOrderModel(), apiResult, request);
                LOGGER.info("sendInterceptOrderRecordMq发送记录消息,messageDto:{}", JSONUtils.beanToJSONDefault(messageDto));
                threadPoolExecutor.submit(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            interceptRecordProducer.send(messageDto.getOrderNo(), JSONUtils.beanToJSONDefault(messageDto), null);
                        } catch (Exception e) {
                            LOGGER.error("订单记录发送mq失败", e);
                        }
                    }
                });
            } else {
                LOGGER.info("记录发送消息开关关闭，不发送消息");
            }
        } catch (Exception e) {
            LOGGER.error("订单记录发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 订单数据流水记录
     *
     * @param orderDataFlowDto
     */
    public void sendOrderDataRecordMq(OrderDataFlowDto orderDataFlowDto) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendOrderDataRecordMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            String sendOrderDataRecordMq = JSONUtils.beanToJSONDefault(orderDataFlowDto);
            Map<String, String> attributes = new HashMap<>();
            if (MapUtils.isNotEmpty(orderDataFlowDto.getMsgAttributes())) {
                attributes.putAll(orderDataFlowDto.getMsgAttributes());
            }
            if (Objects.nonNull(orderDataFlowDto) && Objects.nonNull(orderDataFlowDto.getBusinessIdentity())) {
                attributes.put("businessUnit", orderDataFlowDto.getBusinessIdentity().getBusinessUnit());
                attributes.put("businessScene", orderDataFlowDto.getBusinessIdentity().getBusinessScene());
            }

            LOGGER.info("sendOrderDataRecordMq发送记录消息,orderDataFlowDto:{}, attributes:{}", sendOrderDataRecordMq, attributes);
            threadPoolExecutor.submit(() -> {
                try {
                    orderDataFlowMessageProducer.send(orderDataFlowDto.getOrderNo(), sendOrderDataRecordMq, attributes);
                } catch (Exception e) {
                    LOGGER.error("订单数据流水记录发送mq失败", e);
                }
            });
        } catch (Exception e) {
            LOGGER.error("订单数据流水记录发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 发送订单数据变更记录
     *
     * @param orderDataFlowDto
     */
    public void sendOrderDataUpdateNoticeMq(OrderDataUpdateDto orderDataFlowDto) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendOrderDataUpdateNoticeMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        String sendOrderDataUpdateNoticeMq = JSONUtils.beanToJSONDefault(orderDataFlowDto);
        try {
            LOGGER.info("sendOrderDataUpdateNoticeMq,发送订单数据变更记录,orderDataFlowDto:{}", sendOrderDataUpdateNoticeMq);
            threadPoolExecutor.submit(() -> orderDataUpdateNoticeMessageProducer
                    .send(orderDataFlowDto.getOrderNo(), sendOrderDataUpdateNoticeMq, null));
            LOGGER.info("sendOrderDataUpdateNoticeMq success,orderNo:{}", orderDataFlowDto.getOrderNo());
        } catch (Exception e) {
            LOGGER.error("sendOrderDataUpdateNoticeMq,订单数据变更记录mq发送失败", e);
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ORDER_UPDATE_NOTICE, "数据变更通知下游mq发送失败,msg：" + sendOrderDataUpdateNoticeMq);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 接单消息通知
     *
     * @param createOrderNotice
     */
    public void sendCreateOrderNoticeMq(CreateOrderNotice createOrderNotice) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendCreateOrderNoticeMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            String createOrderNoticeMq = JSONUtils.beanToJSONDefault(createOrderNotice);
            LOGGER.info("sendCreateOrderNoticeMq发送记录消息,createOrderNoticeMq:{}", createOrderNoticeMq);
            threadPoolExecutor.submit(() -> {
                try {
                    createOrderNoticeMessageProducer.send(createOrderNotice.getData().getOrderNo(), createOrderNoticeMq, createOrderNotice.getMsgAttributes());
                } catch (Exception e) {
                    LOGGER.error("接单消息通知发送mq失败", e);
                }
            });
        } catch (Exception e) {
            LOGGER.error("接单消息通知发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 发送修改订单财务记录MQ消息
     *
     * @param context
     * @param apiResult
     * @param request
     */
    public void sendModifyOrderFinanceRecordMq(ExpressOrderContext context, BApiResult apiResult, ModifyExpressOrderFinanceRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendModifyOrderFinanceRecordMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            //发消息开关
            if (expressUccConfigCenter.isSendModifyFlowMessageSwitch()) {
                ExpressOrderFlowMessageDto messageDto = expressOrderFlowTranslator.getOrderRecordMessageDto(context.getOrderModel(), apiResult, request);
                LOGGER.info("sendModifyOrderFinanceRecordMq发送记录消息,messageDto:{}", JSONUtils.beanToJSONDefault(messageDto));
                threadPoolExecutor.submit(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            modifyOrderMessageProducer.send(messageDto.getOrderNo(), JSONUtils.beanToJSONDefault(messageDto), null);
                        } catch (Exception e) {
                            LOGGER.error("订单记录发送mq失败", e);
                        }
                    }
                });
            } else {
                LOGGER.info("记录发送消息开关关闭，不发送消息");
            }
        } catch (Exception e) {
            LOGGER.error("订单记录发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 发送订单支付记录MQ消息
     *
     * @param context
     * @param apiResult
     * @param request
     */
    public void sendPayOrderRecordMq(ExpressOrderContext context, BApiResult<PayExpressOrderData> apiResult, PayExpressOrderRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendPayOrderRecordMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            //发消息开关 todo litongge ducc 配置
            if (expressUccConfigCenter.isSendPayFlowMessageSwitch()) {
                ExpressOrderFlowMessageDto messageDto = expressOrderFlowTranslator.getOrderRecordMessageDto(context.getOrderModel(), apiResult, request);
                LOGGER.info("sendPayOrderRecordMq发送记录消息,messageDto:{}", JSONUtils.beanToJSONDefault(messageDto));
                threadPoolExecutor.submit(() -> {
                    try {
                        payRecordProducer.send(messageDto.getOrderNo(), JSONUtils.beanToJSONDefault(messageDto), null);
                    } catch (Exception e) {
                        LOGGER.error("订单记录发送mq失败", e);
                    }
                });
            } else {
                LOGGER.info("记录发送消息开关关闭，不发送消息");
            }
        } catch (Exception e) {
            LOGGER.error("订单记录发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 退款记录MQ消息通知
     * @param context
     * @param bApiResult
     * @param request
     */
    public void sendRefundOrderRecordMq(ExpressOrderContext context, BApiResult<RefundExpressOrderData> bApiResult, RefundExpressOrderRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendRefundOrderRecordMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            //发消息开关
            if (expressUccConfigCenter.isSendRefundFlowMessageSwitch()) {
                ExpressOrderFlowMessageDto messageDto = expressOrderFlowTranslator.getOrderRecordMessageDto(context.getOrderModel(), bApiResult, request);
                LOGGER.info("sendRefundOrderRecordMq发送订单退款记录消息,refundRecord:{}", JSONUtils.beanToJSONDefault(messageDto));
                threadPoolExecutor.submit(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            refundRecordProducer.send(messageDto.getOrderNo(), JSONUtils.beanToJSONDefault(messageDto), null);
                        } catch (Exception e) {
                            LOGGER.error("sendRefundOrderRecordMq发送订单退款记录消息mq失败", e);
                        }
                    }
                });
            } else {
                LOGGER.info("sendRefundOrderRecordMq发送订单退款记录消息开关关闭，不发送消息");
            }
        } catch (Exception e) {
            LOGGER.error("sendRefundOrderRecordMq发送订单退款记录消息mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * POP售后订单中心内部数据同步mq
     * @param orderDataFlowDto
     */
    public void sendPopAfsInfoUpdateMq(OrderDataFlowDto orderDataFlowDto) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendPopAfsInfoUpdateMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            String message = JSONUtils.beanToJSONDefault(orderDataFlowDto);
            LOGGER.info("sendPopAfsInfoUpdateMq,messageDto:{}", message);
            threadPoolExecutor.submit(() -> {
                try {
                    orderDataUpdateMessageProducer.send(orderDataFlowDto.getOrderNo(), message, null);
                } catch (Exception e) {
                    LOGGER.error("订单数据更新记录发送mq失败, 消息报文:{} ", message, e);
                }
            });
        } catch (Exception e) {
            LOGGER.error("订单数据更新记录发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * @Description 发送重处理消息
     */
    public void sendReaccpetOrderRecordMq(ExpressOrderContext context, BApiResult apiResult, ReacceptExpressOrderRequest request) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendReaccpetOrderRecordMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            //发消息开关
            if (expressUccConfigCenter.isSendReacceptFlowMessageSwitch()) {
                ExpressOrderFlowMessageDto messageDto = expressOrderFlowTranslator.getOrderRecordMessageDto(context.getOrderModel(), apiResult, request);
                LOGGER.info("sendReaccpetOrderRecordMq发送记录消息,messageDto:{}", JSONUtils.beanToJSONDefault(messageDto));
                threadPoolExecutor.submit(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            reacceptOrderMessageProducer.send(messageDto.getOrderNo(), JSONUtils.beanToJSONDefault(messageDto), null);
                        } catch (Exception e) {
                            LOGGER.error("订单记录发送mq失败", e);
                        }
                    }
                });
            } else {
                LOGGER.info("记录发送消息开关关闭，不发送消息");
            }
        } catch (Exception e) {
            LOGGER.error("订单记录发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 订单主状态变更为妥投/拒收时 发送 权益核销MQ
     * @param orderRightDto
     */
    public void sendRightConfirmMq(OrderRightDto orderRightDto) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendRightConfirmMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            String message = JSONUtils.beanToJSONDefault(orderRightDto);
            LOGGER.info("sendRightConfirmMq,messageDto:{}", message);
            threadPoolExecutor.submit(() -> {
                try {
                    rightConfirmMessageProducer.send(orderRightDto.getOrderNo(), message, null);
                } catch (Exception e) {
                    LOGGER.error("订单权益核销发送mq失败, 消息报文:{} ", message, e);
                }
            });
        } catch (Exception e) {
            LOGGER.error("订单权益核销发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 订单主状态变更为取消时 发送 权益释放MQ
     * @param orderRightDto
     */
    public void sendRightReleaseMq(OrderRightDto orderRightDto) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendRightReleaseMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            String message = JSONUtils.beanToJSONDefault(orderRightDto);
            LOGGER.info("sendRightReleaseMq,messageDto:{}", message);
            threadPoolExecutor.submit(() -> {
                try {
                    rightReleaseMessageProducer.send(orderRightDto.getOrderNo(), message, null);
                } catch (Exception e) {
                    LOGGER.error("订单权益释放发送mq失败, 消息报文:{} ", message, e);
                }
            });
        } catch (Exception e) {
            LOGGER.error("订单权益释放发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }
    /**
     * 发送全程跟踪。https://joyspace.jd.com/pages/dKBSyKaVyRoX5wV18khH
     *
     * @param bdWaybillUploadTraceDto
     */
    public void sendOrderTrackMq(BdWaybillUploadTraceDto bdWaybillUploadTraceDto) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendOrderTrackMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            Map<String, String> attributes = new HashMap<>();
            if ("PRE".equals(environment)) {
                attributes.put("uat", "true");
            }

            String message = JSONUtils.beanToJSONDefault(bdWaybillUploadTraceDto);
            LOGGER.info("sendOrderTrackMq,messageDto:{}", message);
            threadPoolExecutor.submit(() -> {
                try {
                    LOGGER.info("异步发送全程跟踪mq OperateCode: {}",bdWaybillUploadTraceDto.getOperateCode());
                    orderTrackProducer.send(bdWaybillUploadTraceDto.getOperateCode(), JSON.toJSONString(bdWaybillUploadTraceDto), attributes);
                } catch (Exception e) {
                    LOGGER.error("发送全程跟踪, 消息报文:{} ", message, e);
                }
            });
        } catch (Exception e) {
            LOGGER.error("发送全程跟踪mq失败", e);
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * @Description 发送资源释放
     */
    public void sendResourceReleaseMq(ExpressResourceReleaseMsgDto msgDto) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".sendResourceReleaseMq"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            String message = JSONUtils.beanToJSONDefault(msgDto);
            LOGGER.info("sendResourceReleaseMq,messageDto:{}", message);
            threadPoolExecutor.submit(() -> {
                try {
                    orderResourceReleaseJmqProducer.send(msgDto.getOrderNo(), message, null);
                } catch (Exception e) {
                    LOGGER.error("订单资源释放发送mq失败, 消息报文:{} ", message, e);
                }
            });
        } catch (Exception e) {
            LOGGER.error("订单资源释放发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * @Description 发送开票
     */
    public void sendInvoiceMq(CommonJmqMessageDto msgDto) {
        CallerInfo callerInfo = umpUtil.registerInfo(this.getClass().getName() + ".sendInvoiceMq");
        try {
            String message = JSONUtils.beanToJSONDefault(msgDto);
            LOGGER.info("sendInvoiceMq,messageDto:{}", message);
            threadPoolExecutor.submit(() -> {
                try {
                    expressInvoiceJmqProducer.send(msgDto.getOrderNo(), message, null);
                } catch (Exception e) {
                    LOGGER.error("订单开票任务发送mq失败, 消息报文:{} ", message, e);
                }
            });
        } catch (Exception e) {
            LOGGER.error("订单开票任务发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * @Description 发送收发管家异步预占
     */
    public void sendPrechargeOccupyMq(CommonJmqMessageDto msgDto) {
        CallerInfo callerInfo = umpUtil.registerInfo(this.getClass().getName() + ".sendPrechargeOccupyMq");
        try {
            String message = JSONUtils.beanToJSONDefault(msgDto);
            LOGGER.info("sendPrechargeOccupyMq,messageDto:{}", message);
            threadPoolExecutor.submit(() -> {
                try {
                    prechargeOccupyJmqProducer.send(msgDto.getOrderNo(), message, null);
                } catch (Exception e) {
                    // TODO 重试
                    LOGGER.error("收发管家异步预占金额发送mq失败, 消息报文:{} ", message, e);
                }
            });
        } catch (Exception e) {
            LOGGER.error("收发管家异步预占金额发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * @Description 修改持久化重试
     */
    public void retryOrderJmq(RetryOrderJmqMessageDto retryOrderJmqMessageDto) {
        CallerInfo callerInfo = umpUtil.registerInfo(this.getClass().getName() + ".retryOrderJmq");
        try {
            String message = JSONUtils.beanToJSONDefault(retryOrderJmqMessageDto);
            LOGGER.info("retryOrderJmq,retryOrderJmqMessageDto:{}", message);
            threadPoolExecutor.submit(() -> {
                try {
                    retryOrderJmqProducer.send(retryOrderJmqMessageDto.getOrderNo(), message, null);
                } catch (Exception e) {
                    LOGGER.error("订单持久化重试发送mq失败, 消息报文:{} ", message, e);
                }
            });
        } catch (Exception e) {
            LOGGER.error("订单持久化重试发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * @Description 发送异步取消服务单任务
     */
    public void sendCancelServiceOrderMq(CancelOrderMessageDto msgDto) {
        CallerInfo callerInfo = umpUtil.registerInfo(this.getClass().getName() + ".sendCancelServiceOrderMq");
        try {
            String message = JSONUtils.beanToJSONDefault(msgDto);
            LOGGER.info("sendCancelServiceOrderMq,messageDto:{}", message);
            threadPoolExecutor.submit(() -> {
                try {
                    cancelServiceOrderJmqProducer.send(msgDto.getOrderNo(), message, null);
                } catch (Exception e) {
                    // TODO 重试
                    LOGGER.error("发送异步取消服务单任务mq失败, 消息报文:{} ", message, e);
                }
            });
        } catch (Exception e) {
            LOGGER.error("发送异步取消服务单任务mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * @Description 发送异步取消支付单任务
     * @param messageDto
     * @param isSync 是否同步调用
     *               -- 同步调用时异步线程发送
     *               -- 异步调用时同步发送
     */
    public void sendReleasePaymentOrderMq(ReleasePaymentOrderMessageDto messageDto, boolean isSync) {
        CallerInfo callerInfo = umpUtil.registerInfo(this.getClass().getName() + ".sendReleasePaymentOrderMq");
        try {

            String message = JSONUtils.beanToJSONDefault(messageDto);

            if (isSync) {
                threadPoolExecutor.submit(() -> {
                    doSendReleasePaymentOrderMq(messageDto.getOrderNo(), message);
                });
            } else {
                doSendReleasePaymentOrderMq(messageDto.getOrderNo(), message);
            }

        } catch (Exception e) {
            LOGGER.error("发送异步取消支付单任务mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * @Description 发送异步事后折询价MQ
     */
    public void sendAsyncStandardProductAndDiscountEnquiryMq(CommonJmqMessageDto msgDto) {
        CallerInfo callerInfo = umpUtil.registerInfo(this.getClass().getName() + ".sendAsyncStandardProductAndDiscountEnquiryMq");
        try {
            String message = JSONUtils.beanToJSONDefault(msgDto);
            LOGGER.info("sendAsyncStandardProductAndDiscountEnquiryMq, messageDto:{}", message);
            threadPoolExecutor.submit(() -> {
                try {
                    asyncStandardProductAndDiscountEnquiryJmqProducer.send(msgDto.getOrderNo(), message, null);
                } catch (Exception e) {
                    LOGGER.error("异步事后折询价发送mq失败, 消息报文:{} ", message, e);
                }
            });
        } catch (Exception e) {
            LOGGER.error("异步事后折询价发送mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 发送异步取消支付单任务
     * @param orderNo
     * @param message
     */
    private void doSendReleasePaymentOrderMq(String orderNo, String message) {
        try {
            LOGGER.info("jmq异步释放支付单消息发送开始");
            releasePaymentOrderJmqProducer.send(orderNo, message, null);
            LOGGER.info("jmq异步释放支付单消息发送结束");
        } catch (Exception e) {
            LOGGER.error("发送异步取消支付单任务mq失败, 消息报文:{} ", message, e);
        }
    }

    /**
     * @Description 发送异步取消支付单任务
     */
    public void sendReleasePaymentOrderMq(ReleasePaymentOrderMessageDto messageDto) {
        sendReleasePaymentOrderMq(messageDto, false);
    }

    /**
     * @Description 回传优惠券处理任务
     */
    public void sendCallbackCouponProcessMq(CallbackCouponProcessMessageDto msgDto) {
        CallerInfo callerInfo = umpUtil.registerInfo(this.getClass().getName() + ".sendCallbackCouponProcessMq");
        try {
            String message = JSONUtils.beanToJSONDefault(msgDto);
            LOGGER.info("sendCallbackCouponProcessMq, messageDto:{}", message);
            threadPoolExecutor.submit(() -> {
                try {
                    callbackCouponProcessProducer.send(msgDto.getOrderNo(), message, null);
                } catch (Exception e) {
                    LOGGER.error("发送回传优惠券处理任务mq失败, 消息报文:{} ", message, e);
                }
            });
        } catch (Exception e) {
            LOGGER.error("发送回传优惠券处理任务mq失败", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

}
