package cn.jdl.oms.express.domain.infrs.acl.baseHandler.basicInfo;

import cn.jdl.oms.express.domain.infrs.acl.util.BusinessSceneUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.CustomsUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.utils.OrderUnitsUtils;
import cn.jdl.oms.express.domain.vo.Cargo;
import cn.jdl.oms.express.domain.vo.CargoDelegate;
import cn.jdl.oms.express.domain.vo.Dimension;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 货品基本信息校验处理类
 */
@Component
public class CargoValidBaseHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(CargoValidBaseHandler.class);

    /**
     * 报警
     */
    @Resource
    private UmpUtil umpUtil;

    /**
     * 报关域工具类
     */
    @Resource
    private CustomsUtil customsUtil;

    /**
     * 香港离岛区列,最大重量限制-默认30kg
     */
    private final static BigDecimal HK_OUTLYING_ISLANDS_WEIGHT_MAX_VALUE = new BigDecimal(30);
    /**
     * 香港离岛区列,最大体积限制-默认3375000 cm3
     */
    private final static BigDecimal HK_OUTLYING_ISLANDS_VOLUME_MAX_VALUE = new BigDecimal(3375000);
    /**
     * 香港离岛区列,最大尺寸长宽高限制-默认150cm
     */
    private final static BigDecimal HK_OUTLYING_ISLANDS_DIMENSION_MAX_VALUE = new BigDecimal(150);

    /**
     * 港澳离岛区域-货品限制校验
     * @param cargos
     */
    public void hkOutlyingIslandsValid(List<Cargo> cargos){
        //下单重量cargoInfos-cargoWeight＞30KG
        //下单长宽高cargoInfos-cargoDimension-length/width/height＞150cm
        //下单体积cargoInfos-cargoVolume＞3375000cm³
        if (CollectionUtils.isNotEmpty(cargos)) {
            BigDecimal WEIGHT_MAX_VALUE = StringUtils.isNotBlank(BatrixSwitch.obtainByUccKey(BatrixSwitchKey.HK_OUTLYING_ISLANDS_WEIGHT_MAX_VALUE))
                    ? new BigDecimal(BatrixSwitch.obtainByUccKey(BatrixSwitchKey.HK_OUTLYING_ISLANDS_WEIGHT_MAX_VALUE))
                    : HK_OUTLYING_ISLANDS_WEIGHT_MAX_VALUE;
            BigDecimal VOLUME_MAX_VALUE = StringUtils.isNotBlank(BatrixSwitch.obtainByUccKey(BatrixSwitchKey.HK_OUTLYING_ISLANDS_VOLUME_MAX_VALUE))
                    ? new BigDecimal(BatrixSwitch.obtainByUccKey(BatrixSwitchKey.HK_OUTLYING_ISLANDS_VOLUME_MAX_VALUE))
                    : HK_OUTLYING_ISLANDS_VOLUME_MAX_VALUE;
            BigDecimal DIMENSION_MAX_VALUE = StringUtils.isNotBlank(BatrixSwitch.obtainByUccKey(BatrixSwitchKey.HK_OUTLYING_ISLANDS_DIMENSION_MAX_VALUE))
                    ? new BigDecimal(BatrixSwitch.obtainByUccKey(BatrixSwitchKey.HK_OUTLYING_ISLANDS_DIMENSION_MAX_VALUE))
                    : HK_OUTLYING_ISLANDS_DIMENSION_MAX_VALUE;

            cargos.forEach(cargo -> {
                if (WEIGHT_MAX_VALUE.compareTo(cargo.getCargoWeight().getValue()) < 0) {
                    LOGGER.error("离岛区仅支持寄递" + WEIGHT_MAX_VALUE + "kg以下的货物");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("离岛区仅支持寄递" + WEIGHT_MAX_VALUE + "kg以下且最大长度不超过" + DIMENSION_MAX_VALUE + "cm的货物");
                }

                BigDecimal volumeValue = OrderUnitsUtils.getVolumeValue(cargo.getCargoVolume());
                if (VOLUME_MAX_VALUE.compareTo(volumeValue) < 0) {
                    LOGGER.error("离岛区仅支持寄递" + VOLUME_MAX_VALUE + "cm³以下的货物");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("离岛区仅支持寄递" + WEIGHT_MAX_VALUE + "kg以下且最大长度不超过" + DIMENSION_MAX_VALUE + "cm的货物");
                }

                Dimension cargoDimension = cargo.getCargoDimension();
                if (null != cargoDimension) {
                    BigDecimal length = null == cargoDimension.getLength() ? BigDecimal.ZERO : cargoDimension.getLength();
                    BigDecimal width = null == cargoDimension.getWidth() ? BigDecimal.ZERO : cargoDimension.getWidth();
                    BigDecimal height = null == cargoDimension.getHeight() ? BigDecimal.ZERO : cargoDimension.getHeight();
                    if (DIMENSION_MAX_VALUE.compareTo(length) < 0
                            || DIMENSION_MAX_VALUE.compareTo(width) < 0
                            || DIMENSION_MAX_VALUE.compareTo(height) < 0) {
                        LOGGER.error("离岛区仅支持寄递" + DIMENSION_MAX_VALUE + "cm以下的货物");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom("离岛区仅支持寄递" + WEIGHT_MAX_VALUE + "kg以下且最大长度不超过" + DIMENSION_MAX_VALUE + "cm的货物");
                    }
                }
            });
        }
    }

    /**
     * 港澳电商标快校验
     */
    public void hkmoValidCargoWeightForDSTH(ExpressOrderModel orderModel) {
        try {
            if (!BatrixSwitch.applyByBoolean(BatrixSwitchKey.HKMO_VALID_CARGO_WEIGHT_FOR_DSTH_SWITCH)) {
                LOGGER.info("跳过校验：hkmoValidCargoWeightForDSTH");
                return;
            }

            // 港澳同城、港澳互寄，并且主产品是电商特惠ed-m-0059
            if (!customsUtil.isHKMOWithDSTH(orderModel)) {
                return;
            }

            ExpressOrderModel orderSnapshot = null;
            if (!BusinessSceneUtil.isCreate(orderModel)) {
                orderSnapshot = orderModel.getOrderSnapshot();
            }

            // cargoWeigh不能大于20KG
            CargoDelegate cargoDelegate = orderModel.getCargoDelegate();
            if ((cargoDelegate == null || cargoDelegate.isEmpty()) && orderSnapshot != null) {
                cargoDelegate = orderSnapshot.getCargoDelegate();
            }
            if (cargoDelegate != null && cargoDelegate.totalCargoWeight().compareTo(BigDecimal.valueOf(20)) > 0) {
                LOGGER.error("按箱下单场景仅支持寄递20kg以下的货物");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("按箱下单场景仅支持寄递20kg以下的货物");
            }
        } catch (Exception e) {
            // todo 稳定后去除报警和catch
            umpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_HKMO_VALID_CARGO_WEIGHT_FOR_DSTH_FAIL_ALARM, "校验报错：港澳电商标快校验货品信息", orderModel.traceId());
            throw e;
        }
    }
}