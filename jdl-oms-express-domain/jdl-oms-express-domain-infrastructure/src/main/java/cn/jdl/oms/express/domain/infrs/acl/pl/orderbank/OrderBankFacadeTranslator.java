package cn.jdl.oms.express.domain.infrs.acl.pl.orderbank;

import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.ReaddressRecordDetailInfo;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.dto.AddressInfoDto;
import cn.jdl.oms.express.domain.infrs.acl.facade.address.JdAddressFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.ecard.EcardFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.ecard.EcardFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.bdue.ModifyDueFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.bdue.ReceivableFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.bdue.ReceiveTypeEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.ots.OrderResourceFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.pos.WebOrderAccountsDto;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.pos.WebPosExtOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.pos.WebPosPayOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.pos.WebPosYunFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.GetFieldUtils;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.MerchantUtils;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.OTSLedgerUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderBankFlowDto;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductAttrEnum;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.ModifyRecordTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSignEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.vo.Activity;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.Discount;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.FinanceDetail;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.record.ModifyRecord;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.AddressConstants;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.ShipmentConstants;
import cn.jdl.oms.express.shared.common.dict.ECardDisableReasonEnum;
import cn.jdl.oms.express.shared.common.dict.MerchantEnum;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.DateUtils;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.shared.common.utils.TypeConversion;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static cn.jdl.oms.express.shared.common.constant.BusinessConstants.DEFAULT_AMOUNT_DECIMAL_SCALE;

/**
 * @Package： cn.jdl.oms.express.domain.infrs.acl.pl.orderbank
 * @ClassName: OrderBankFacadeTranslator
 * @Description:
 * @Author： zhangqi
 * @CreateDate 2021/3/31 19:22
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
@Translator
public class OrderBankFacadeTranslator {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderBankFacadeTranslator.class);

    /**
     * 金额精度：2位
     */
    private static final int AMOUNT_SCALE_TWO =2;

    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 外单台账扩展信息key:省编码
     */
    private static final String OTS_EXT_KEY_PROVINCE = "province";

    private static final String OTS_EXT_KEY_DATASOURCE = "datasource";

    /**
     * 京标-港澳一级省编码
     */
    private static final String HM_PROVINCE_NO = "52993";

    /**
     * 外单台账默认订单类型
     */
    private static final Integer OTS_CREATE_DEFAULT_ORDER_TYPE = 1;

    /**
     * 外单台账默认支付方式
     * 在线支付
     */
    private static final Integer OTS_CREATE_DEFAULT_PAY_MODE = 4;

    /**
     * 外单台账默认应收类型
     * 运费
     */
    private static final Integer OTS_CREATE_DEFAULT_RECEIVABLE_TYPE = 4;


    /**
     * 外单台账默认版本号
     */
    private static final Integer OTS_CREATE_DEFAULT_VER = 0;

    private static final String OTS_CREATE_DEFAULT_DATASOURCE = "0";

    /**
     * 地址编码转换
     */
    @Resource
    private JdAddressFacade jdAddressFacade;

    /**
     * E卡
     */
    @Resource
    private EcardFacade ecardFacade;

    /**
     * 通用B商家修改参数构建
     * @param request 台账修改请求
     * @param orderModel 订单模型
     * @param jf 寄付运费
     * @param df 到付运费
     * @param cod 代收货款
     */
    public void buildBModifyOrderBank(OrderBankFacadeRequest request, ExpressOrderModel orderModel, BigDecimal jf, BigDecimal df, BigDecimal cod) {
        if (null != cod) {
            request.setBMerchantCodModify(toBModify(cod, ReceiveTypeEnum.RECEIVE_TYPE_HuoKuan));
        }
        if (null != jf) {
            request.setBMerchantJfModify(toBModify(jf, ReceiveTypeEnum.RECEIVE_TYPE_JiFuYun));
        }
        if (null != df) {
            request.setBMerchantDfModify(toBModify(df, ReceiveTypeEnum.RECEIVE_TYPE_Yun));
        }
    }

    /**
     * 通用B商家修改参数构建
     * @param amount
     * @param receiveType
     * @return
     */
    public OrderBankFacadeRequest.BMerchantModify toBModify(BigDecimal amount, ReceiveTypeEnum receiveType) {
        OrderBankFacadeRequest.BMerchantModify bModify = new OrderBankFacadeRequest.BMerchantModify();
        bModify.setBMerchantDueDetailInfo(toBDetail(amount, receiveType));
        return bModify;
    }

    /**
     * 通用B商家应收明细构建
     * @param amount
     * @param receiveType
     * @return
     */
    public OrderBankFacadeRequest.BMerchantDueDetailInfo toBDetail(BigDecimal amount, ReceiveTypeEnum receiveType) {
        OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
        bMerchantDueDetailInfo.setAmount(amount);
        bMerchantDueDetailInfo.setReceiveType(receiveType);
        return bMerchantDueDetailInfo;
    }

    /**
     * 用当前单写外单台账
     * @param orderModel
     * @return
     */
    public OrderBankFacadeRequest toOtsCreateOrderBankRequest(ExpressOrderModel orderModel) {
        OrderBankFacadeRequest orderBankFacadeRequest = this.toCommonOrderBankFacadeRequest(orderModel, orderModel, orderModel.requestProfile().getTenantId());;
        Finance finance = orderModel.getFinance();
        orderBankFacadeRequest.setOrgId(orderModel.getFinance().getCollectionOrgNo());
        orderBankFacadeRequest.setWaybillNo(orderModel.getCustomOrderNo());
        OrderBankFacadeRequest.OtsCreate otsCreate = toOtsCreate(orderModel, MerchantUtils.getMerchantIdEnum(orderModel));
        orderBankFacadeRequest.setOtsCreate(otsCreate);
        return orderBankFacadeRequest;
    }

    /**
     * 外单台账新增防腐层转换 通用方法
     * @param orderModel
     * @param merchantEnum
     * @return
     */
    public OrderBankFacadeRequest.OtsCreate toOtsCreate(ExpressOrderModel orderModel, @NotNull MerchantEnum merchantEnum) {
        Finance finance = orderModel.getFinance();
        if (finance == null) {
            return null;
        }
        OrderBankFacadeRequest.OtsCreate otsCreate = new OrderBankFacadeRequest.OtsCreate();
        otsCreate.setMerchantId(merchantEnum.getMerchantId());
        if (finance.getDiscountAmount() != null) {
            //订单金额
            otsCreate.setTotalPrice(finance.getDiscountAmount().getAmount());
            //订单总价格，即同订单金额的赋值逻辑
            otsCreate.setOrderPrice(finance.getDiscountAmount().getAmount());
        }
        //订单折扣
        otsCreate.setDiscount(BigDecimal.ZERO);
        //运费
        otsCreate.setYun(BigDecimal.ZERO);
        //下单人编号
        otsCreate.setPin(orderModel.getOperator());
        //币种
        otsCreate.setCurrency(CurrencyCodeEnum.CNY.getOtsOrderbankCode());
        //当前时间
        otsCreate.setOrderTime(new Date());
        //订单类型
        otsCreate.setOrderType(OTS_CREATE_DEFAULT_ORDER_TYPE);
        //支付方式
        otsCreate.setPayMode(OTS_CREATE_DEFAULT_PAY_MODE);
        //版本
        otsCreate.setVer(OTS_CREATE_DEFAULT_VER);
        //明细
        List<OrderBankFacadeRequest.ReceivableDetailInfo> receivableDetails = new ArrayList<>();
        OrderBankFacadeRequest.ReceivableDetailInfo receivableDetailInfo = new OrderBankFacadeRequest.ReceivableDetailInfo();
        if (finance.getDiscountAmount() != null) {
            receivableDetailInfo.setAmount(finance.getDiscountAmount().getAmount());
        }
        //应收单号=receivableType
        receivableDetailInfo.setReceivableId(String.valueOf(OTS_CREATE_DEFAULT_ORDER_TYPE));
        //应收类型
        receivableDetailInfo.setReceivableType(OTS_CREATE_DEFAULT_ORDER_TYPE);
        //创建应收的时间
        receivableDetailInfo.setCreateTime(new Date());
        receivableDetails.add(receivableDetailInfo);
        otsCreate.setReceivableDetails(receivableDetails);
        String weiJianWei = GetFieldUtils.getOrderSign(orderModel, OrderSignEnum.WEI_JIAN_WEI);
        if (OrderConstants.YES_VAL.equals(weiJianWei)) {
            otsCreate.setDatasource(OTS_CREATE_DEFAULT_DATASOURCE);
        }
        return otsCreate;
    }

    /**
     * 外单台账请求对象转换
     * @param orderModel
     * @return
     */
    public OrderBankFacadeRequest.OtsCancel toOtsCancel(ExpressOrderModel orderModel, @NotNull MerchantEnum merchantEnum) {
        OrderBankFacadeRequest.OtsCancel otsCancel = new OrderBankFacadeRequest.OtsCancel();
        // 商户ID
        otsCancel.setMerchantId(merchantEnum.getMerchantId());
        // 下单人编号
        otsCancel.setPin(orderModel.getOperator());
        // 订单类型
        otsCancel.setOrderType(OTSLedgerUtil.ORDER_TYPE);
        // 支付方式
        otsCancel.setPayMode(OTS_CREATE_DEFAULT_PAY_MODE);
        // 版本
        otsCancel.setVer(OTSLedgerUtil.OTS_DEFAULT_VER);
        return otsCancel;
    }

    /**
     * pos寄付
     *
     * @param orderBankFacadeRequest
     * @return
     */
    public WebPosPayOrderFacadeRequest toWebPosPayOrderFacadeRequest(OrderBankFacadeRequest orderBankFacadeRequest) {
        WebPosPayOrderFacadeRequest webPosPayOrderFacadeRequest = new WebPosPayOrderFacadeRequest();
        webPosPayOrderFacadeRequest.setBusinessNo(orderBankFacadeRequest.getPosJfYun().getBusinessNo());
        webPosPayOrderFacadeRequest.setWebOrderAccountsDto(toWebOrderAccountsDto(orderBankFacadeRequest, false));
        return webPosPayOrderFacadeRequest;
    }

    /**
     * 生成0 B商家
     */
    public OrderBankFacadeRequest.BMerchantDueDetailInfo generateZeroBMerchantDueDetailInfo(ReceiveTypeEnum receiveTypeEnum) {
        OrderBankFacadeRequest.BMerchantDueDetailInfo bMerchantDueDetailInfo = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
        bMerchantDueDetailInfo.setReceiveType(receiveTypeEnum);
        bMerchantDueDetailInfo.setAmount(BigDecimal.ZERO);
        return bMerchantDueDetailInfo;
    }


    /**
     * 对象转换
     *
     * @param orderBankFacadeRequest
     * @param yunFlag                到付标志
     * @return
     */
    private WebOrderAccountsDto toWebOrderAccountsDto(OrderBankFacadeRequest orderBankFacadeRequest, boolean yunFlag) {
        WebOrderAccountsDto webOrderAccountsDto = new WebOrderAccountsDto();
        webOrderAccountsDto.setOrderId(orderBankFacadeRequest.getWaybillNo());
        webOrderAccountsDto.setOrgId(orderBankFacadeRequest.getOrgId());
        if (yunFlag) {
            if (expressUccConfigCenter.isAmountScaleDownSwitch()) {
                LOGGER.info("金额降级开关开启，小数点保留两位");
                if (orderBankFacadeRequest.getPosYun().getAmount() != null) {
                    webOrderAccountsDto.setAmountReceivable(String.valueOf(orderBankFacadeRequest.getPosYun().getAmount().setScale(AMOUNT_SCALE_TWO, BigDecimal.ROUND_HALF_UP)));
                }
            } else {
                LOGGER.info("金额降级开关关闭,走原有逻辑");
                webOrderAccountsDto.setAmountReceivable(String.valueOf(orderBankFacadeRequest.getPosYun().getAmount()));
            }

            webOrderAccountsDto.setWayBillSign(orderBankFacadeRequest.getPosYun().getWayBillSign());
            webOrderAccountsDto.setServiceCode(orderBankFacadeRequest.getPosYun().getServiceCode());
            if(StringUtils.isNotBlank(orderBankFacadeRequest.getPosYun().getCurrency())){
                webOrderAccountsDto.setCurrency(orderBankFacadeRequest.getPosYun().getCurrency());
            }
            //到付赋值收件人信息
            webOrderAccountsDto.setPayName(orderBankFacadeRequest.getConsigneeInfo().getConsigneeName());
            webOrderAccountsDto.setPayerAdd(orderBankFacadeRequest.getConsigneeInfo().getConsigneeAddress());
            webOrderAccountsDto.setIdNo(StringUtils.isNotBlank(orderBankFacadeRequest.getConsigneeInfo().getConsigneeMobile()) ?
                    orderBankFacadeRequest.getConsigneeInfo().getConsigneeMobile() : orderBankFacadeRequest.getConsigneeInfo().getConsigneePhone());
        } else {
            if (expressUccConfigCenter.isAmountScaleDownSwitch()) {
                LOGGER.info("金额降级开关开启，小数点保留两位");
                if (orderBankFacadeRequest.getPosJfYun().getAmount() != null) {
                    webOrderAccountsDto.setAmountReceivable(String.valueOf(orderBankFacadeRequest.getPosJfYun().getAmount().setScale(AMOUNT_SCALE_TWO, BigDecimal.ROUND_HALF_UP)));
                }
            } else {
                LOGGER.info("金额降级开关关闭,走原有逻辑");
                webOrderAccountsDto.setAmountReceivable(String.valueOf(orderBankFacadeRequest.getPosJfYun().getAmount()));
            }

            webOrderAccountsDto.setWayBillSign(orderBankFacadeRequest.getPosJfYun().getWayBillSign());
            webOrderAccountsDto.setServiceCode(orderBankFacadeRequest.getPosJfYun().getServiceCode());
            if(StringUtils.isNotBlank(orderBankFacadeRequest.getPosJfYun().getCurrency())){
                webOrderAccountsDto.setCurrency(orderBankFacadeRequest.getPosJfYun().getCurrency());
            }

            //寄付赋值寄件人信息
            webOrderAccountsDto.setPayName(orderBankFacadeRequest.getConsignorInfo().getConsignorName());
            webOrderAccountsDto.setPayerAdd(orderBankFacadeRequest.getConsignorInfo().getConsignorAddress());
            webOrderAccountsDto.setIdNo(StringUtils.isNotBlank(orderBankFacadeRequest.getConsignorInfo().getConsignorMobile()) ?
                    orderBankFacadeRequest.getConsignorInfo().getConsignorMobile() : orderBankFacadeRequest.getConsignorInfo().getConsignorPhone());
        }
        webOrderAccountsDto.setUUid(orderBankFacadeRequest.getUUid() + "_" + System.currentTimeMillis());

        return webOrderAccountsDto;
    }

    /**
     * pos到付
     *
     * @param orderBankFacadeRequest
     * @return
     */
    public WebPosYunFacadeRequest toWebPosYunFacadeRequest(OrderBankFacadeRequest orderBankFacadeRequest) {
        WebPosYunFacadeRequest webPosYunFacadeRequest = new WebPosYunFacadeRequest();
        webPosYunFacadeRequest.setType(orderBankFacadeRequest.getPosYun().getPosType());
        webPosYunFacadeRequest.setWebOrderAccountsDto(toWebOrderAccountsDto(orderBankFacadeRequest, true));
        return webPosYunFacadeRequest;
    }

    /**
     * B商家新增
     *
     * @param orderBankFacadeRequest
     * @return
     */
    public ReceivableFacadeRequest toReceivableFacadeRequest(OrderBankFacadeRequest orderBankFacadeRequest) {
        ReceivableFacadeRequest receivableFacadeRequest = new ReceivableFacadeRequest();
        receivableFacadeRequest.setWaybillNo(orderBankFacadeRequest.getWaybillNo());
        receivableFacadeRequest.setOrgId(orderBankFacadeRequest.getOrgId());
        receivableFacadeRequest.setOrgName(orderBankFacadeRequest.getOrgName());

        if (expressUccConfigCenter.isAmountScaleDownSwitch()) {
            LOGGER.info("金额降级开关开启，小数点保留两位");
            if (orderBankFacadeRequest.getBMerchantCreate().getAmount() != null) {
                receivableFacadeRequest.setReceivableAmount(orderBankFacadeRequest.getBMerchantCreate().getAmount().setScale(AMOUNT_SCALE_TWO, BigDecimal.ROUND_HALF_UP));
            }
        } else {
            LOGGER.info("金额降级开关关闭,走原有逻辑");
            receivableFacadeRequest.setReceivableAmount(orderBankFacadeRequest.getBMerchantCreate().getAmount());
        }

        receivableFacadeRequest.setDataSources(orderBankFacadeRequest.getBMerchantCreate().getDataSources());
        receivableFacadeRequest.setRecipientName(orderBankFacadeRequest.getConsigneeInfo().getConsigneeName());
        receivableFacadeRequest.setSellerId(orderBankFacadeRequest.getBMerchantCreate().getSellerId());
        receivableFacadeRequest.setSellerName(orderBankFacadeRequest.getBMerchantCreate().getSellerName());
        receivableFacadeRequest.setPayMode(orderBankFacadeRequest.getBMerchantCreate().getPayMode());
        receivableFacadeRequest.setCurrency(orderBankFacadeRequest.getBMerchantCreate().getCurrency());
        Map<String,String> ext = new HashMap<>();
        if(StringUtils.isNotBlank(orderBankFacadeRequest.getBMerchantCreate().getOtsMerchantId())){
            ext.put("wdMerchantId",orderBankFacadeRequest.getBMerchantCreate().getOtsMerchantId());
        }
        if(StringUtils.isNotBlank(orderBankFacadeRequest.getAccountNo())){
            ext.put("customerId",orderBankFacadeRequest.getAccountNo());
        }
        if (MapUtils.isNotEmpty(ext)) {
            receivableFacadeRequest.setExt(ext);
        }
        if (expressUccConfigCenter.isAmountScaleDownSwitch()) {
            LOGGER.info("金额降级开关开启，小数点保留两位");
            receivableFacadeRequest.setDueDetails(toDueDetails(orderBankFacadeRequest.getBMerchantCreate().getBMerchantDueDetailInfos()));
        } else {
            LOGGER.info("金额降级开关关闭,走原有逻辑");
            receivableFacadeRequest.setDueDetails(orderBankFacadeRequest.getBMerchantCreate().getBMerchantDueDetailInfos());
        }

        try {
            if (orderBankFacadeRequest.getConsignorInfo() != null
                    && StringUtils.isNotBlank(orderBankFacadeRequest.getConsignorInfo().getSpecialOrderBankProvinceNo())) {
                receivableFacadeRequest.setProvinceCode(orderBankFacadeRequest.getConsignorInfo().getSpecialOrderBankProvinceNo());
                receivableFacadeRequest.setProvinceName(orderBankFacadeRequest.getConsignorInfo().getSpecialOrderBankProvinceName());
            } else if (orderBankFacadeRequest.getConsignorInfo() != null && StringUtils.isNotBlank(orderBankFacadeRequest.getConsignorInfo().getConsignorProvinceNo())) {
                AddressInfoDto addressInfoDto;
                //fixme 国内B商家按国标省编码统计财务信息，港澳GIS京标是虚拟站点匹配不到国标省编码，需下沉用京标二级城市编码 获取中国香港，中国澳门国标省编码，【外单台账目前没有调整】
                if (HM_PROVINCE_NO.equals(orderBankFacadeRequest.getConsignorInfo().getConsignorProvinceNo())
                        && StringUtils.isNotBlank(orderBankFacadeRequest.getConsignorInfo().getConsignorCityNo())) {
                    addressInfoDto = jdAddressFacade.getGBDistrictByJDCode(orderBankFacadeRequest.getConsignorInfo().getConsignorCityNo());
                } else {
                    addressInfoDto = jdAddressFacade.getGBDistrictByJDCode(orderBankFacadeRequest.getConsignorInfo().getConsignorProvinceNo());
                }

                if (addressInfoDto != null) {
                    //省信息--国标
                    receivableFacadeRequest.setProvinceCode(addressInfoDto.getProvinceNo());
                    receivableFacadeRequest.setProvinceName(addressInfoDto.getProvinceName());
                }

            }
        } catch (Exception e) {
            LOGGER.error("B商家台账创建应收接口，增加国标省信息异常,不阻塞流程",e);
        }

        return receivableFacadeRequest;
    }

    /**
     * 金额小数位转换
     * @param bMerchantDueDetailInfos
     */
    private List<OrderBankFacadeRequest.BMerchantDueDetailInfo> toDueDetails(List<OrderBankFacadeRequest.BMerchantDueDetailInfo> bMerchantDueDetailInfos) {
        List<OrderBankFacadeRequest.BMerchantDueDetailInfo> result = new ArrayList<>(bMerchantDueDetailInfos.size());

        for (OrderBankFacadeRequest.BMerchantDueDetailInfo dueDetailInfo : bMerchantDueDetailInfos) {
            OrderBankFacadeRequest.BMerchantDueDetailInfo temp = new OrderBankFacadeRequest.BMerchantDueDetailInfo();
            temp.setReceiveType(dueDetailInfo.getReceiveType());
            temp.setCurrency(dueDetailInfo.getCurrency());

            if (dueDetailInfo.getAmount() != null) {
                temp.setAmount(dueDetailInfo.getAmount().setScale(AMOUNT_SCALE_TWO, BigDecimal.ROUND_HALF_UP));
            }

            result.add(temp);
        }

        return result;
    }
    /**
     * B商家修改
     *
     * @param orderBankFacadeRequest
     * @return
     */
    public ModifyDueFacadeRequest toModifyDueFacadeRequest(OrderBankFacadeRequest orderBankFacadeRequest) {
        ModifyDueFacadeRequest modifyDueFacadeRequest = new ModifyDueFacadeRequest();
        modifyDueFacadeRequest.setWaybillNo(orderBankFacadeRequest.getWaybillNo());
        modifyDueFacadeRequest.setReceiveTypeEnum(orderBankFacadeRequest.getBMerchantModify().getBMerchantDueDetailInfo().getReceiveType());

        if (expressUccConfigCenter.isAmountScaleDownSwitch()) {
            LOGGER.info("金额降级开关开启，小数点保留两位");
            if (orderBankFacadeRequest.getBMerchantModify().getBMerchantDueDetailInfo().getAmount() != null) {
                modifyDueFacadeRequest.setAmount(orderBankFacadeRequest.getBMerchantModify().getBMerchantDueDetailInfo().getAmount().setScale(AMOUNT_SCALE_TWO, BigDecimal.ROUND_HALF_UP));
            }
        } else {
            LOGGER.info("金额降级开关关闭,走原有逻辑");
            modifyDueFacadeRequest.setAmount(orderBankFacadeRequest.getBMerchantModify().getBMerchantDueDetailInfo().getAmount());
        }
        if(StringUtils.isNotBlank(orderBankFacadeRequest.getBMerchantModify().getBMerchantDueDetailInfo().getCurrency())){
            modifyDueFacadeRequest.setCurrency(orderBankFacadeRequest.getBMerchantModify().getBMerchantDueDetailInfo().getCurrency());
        }
        modifyDueFacadeRequest.setBusiness(orderBankFacadeRequest.getUUid() + "_" + System.currentTimeMillis());
        modifyDueFacadeRequest.setOrgId(orderBankFacadeRequest.getOrgId());
        modifyDueFacadeRequest.setOrgName(orderBankFacadeRequest.getOrgName());
        if(StringUtils.isNotBlank(orderBankFacadeRequest.getBMerchantModify().getOtsMerchantId())){
            Map<String,String> ext = new HashMap<>(1);
            ext.put("wdMerchantId",orderBankFacadeRequest.getBMerchantModify().getOtsMerchantId());
            modifyDueFacadeRequest.setExt(ext);
        }
        return modifyDueFacadeRequest;
    }

    /**
     * 外单台账新增
     *
     * @param orderBankFacadeRequest
     * @return
     */
    public OrderResourceFacadeRequest toOrderResourceFacadeRequest(OrderBankFacadeRequest orderBankFacadeRequest) {
        OrderResourceFacadeRequest orderResourceFacadeRequest = new OrderResourceFacadeRequest();
        orderResourceFacadeRequest.setOrderId(orderBankFacadeRequest.getWaybillNo());
        orderResourceFacadeRequest.setMerchantId(orderBankFacadeRequest.getOtsCreate().getMerchantId());
        orderResourceFacadeRequest.setTotalPrice(orderBankFacadeRequest.getOtsCreate().getTotalPrice());
        orderResourceFacadeRequest.setOrderPrice(orderBankFacadeRequest.getOtsCreate().getOrderPrice());
        orderResourceFacadeRequest.setDiscount(orderBankFacadeRequest.getOtsCreate().getDiscount());
        orderResourceFacadeRequest.setYun(orderBankFacadeRequest.getOtsCreate().getYun());
        orderResourceFacadeRequest.setPin(orderBankFacadeRequest.getOtsCreate().getPin());
        orderResourceFacadeRequest.setCurrency(orderBankFacadeRequest.getOtsCreate().getCurrency());
        orderResourceFacadeRequest.setOrderCode(Integer.valueOf(orderBankFacadeRequest.getOrgId()));
        orderResourceFacadeRequest.setOrderType(orderBankFacadeRequest.getOtsCreate().getOrderType());
        orderResourceFacadeRequest.setOrderTime(orderBankFacadeRequest.getOtsCreate().getOrderTime());
        orderResourceFacadeRequest.setPayMode(orderBankFacadeRequest.getOtsCreate().getPayMode());
        orderResourceFacadeRequest.setVer(orderBankFacadeRequest.getOtsCreate().getVer());
        orderResourceFacadeRequest.setReceivableDetails(orderBankFacadeRequest.getOtsCreate().getReceivableDetails());
        //扩展信息
        HashMap<String, String> ext = new HashMap<>();

        if (orderBankFacadeRequest.getConsignorInfo() != null
                && StringUtils.isNotBlank(orderBankFacadeRequest.getConsignorInfo().getSpecialOrderBankProvinceNo())) {
            // 跳过地址替换，从当前的对象中获取省区信息
            //fixme 港澳GIS京标是虚拟站点匹配不到国标省编码，需下沉用京标二级城市编码 获取中国香港，中国澳门国标省编码，【外单台账目前没有调整】
            LOGGER.info("跳过地址替换，从当前的对象中获取省区信息");
            if (null != orderBankFacadeRequest.getConsignorInfo()) {
                ext.put(OTS_EXT_KEY_PROVINCE, orderBankFacadeRequest.getConsignorInfo().getSpecialOrderBankProvinceNo());
            }
        } else {
            AddressInfoDto addressInfoDto = this.getReplaceAddress(orderBankFacadeRequest);
            if (addressInfoDto != null) {
                //省ID--国标
                ext.put(OTS_EXT_KEY_PROVINCE, addressInfoDto.getProvinceNo());
            }
        }

        if(StringUtils.isNotBlank(orderBankFacadeRequest.getOtsCreate().getDatasource())){
            ext.put(OTS_EXT_KEY_DATASOURCE, orderBankFacadeRequest.getOtsCreate().getDatasource());
        }

        orderResourceFacadeRequest.setExtMap(ext);
        return orderResourceFacadeRequest;
    }

    /**
     * 根据京标编码，转换国标编码
     * @param orderBankFacadeRequest
     * @return
     */
    private AddressInfoDto getReplaceAddress(OrderBankFacadeRequest orderBankFacadeRequest) {
        try {
            if (orderBankFacadeRequest.getConsignorInfo() != null
                    && StringUtils.isNotBlank(orderBankFacadeRequest.getConsignorInfo().getConsignorProvinceNo())) {
                return jdAddressFacade.getGBDistrictByJDCode(orderBankFacadeRequest.getConsignorInfo().getConsignorProvinceNo());
            }
        } catch (Exception e) {
            LOGGER.error("外单台账创建应收接口，增加国标省信息异常,不阻塞流程", e);
        }
        return null;
    }

    /**
     * 根据京标编码，转换国标编码
     * @param consignorProvinceNo
     * @return
     */
    private AddressInfoDto getReplaceAddress(String consignorProvinceNo) {
        try {
            if (StringUtils.isNotBlank(consignorProvinceNo)) {
                return jdAddressFacade.getGBDistrictByJDCode(consignorProvinceNo);
            }
        } catch (Exception e) {
            LOGGER.error("外单台账创建应收接口，增加国标省信息异常,不阻塞流程", e);
        }
        return null;
    }

    /**
     * 外单台账取消
     *
     * @param orderBankFacadeRequest
     * @return
     */
    public OrderResourceFacadeRequest toCancelOrderResourceFacadeRequest(OrderBankFacadeRequest orderBankFacadeRequest) {
        OrderResourceFacadeRequest orderResourceFacadeRequest = new OrderResourceFacadeRequest();
        orderResourceFacadeRequest.setOrderId(orderBankFacadeRequest.getWaybillNo());
        if (null != orderBankFacadeRequest.getOtsCancel()) {
            orderResourceFacadeRequest.setMerchantId(orderBankFacadeRequest.getOtsCancel().getMerchantId());
            orderResourceFacadeRequest.setOrderType(orderBankFacadeRequest.getOtsCancel().getOrderType());
            orderResourceFacadeRequest.setVer(orderBankFacadeRequest.getOtsCancel().getVer());
        }
        return orderResourceFacadeRequest;
    }

    /**
     * 生成台账流水消息Dto
     *
     * @param request
     * @param className
     * @param posPayAppNo
     * @param errMsg
     * @return
     */
    public OrderBankFlowDto toOrderBankFlowDto(OrderBankFacadeRequest request, String className, String posPayAppNo, int status, String errMsg) {
        OrderBankFlowDto dto = new OrderBankFlowDto();
        dto.setOrderBankFacadeRequest(request);
        dto.setClassName(className);
        dto.setPosPayAppNo(posPayAppNo);
        dto.setStatus(status);
        dto.setErrMsg(errMsg);
        dto.setVersion(String.valueOf(System.nanoTime()));
        return dto;
    }

    /**
     * 创建税金应收
     *
     * @param orderBankFacadeRequest
     * @return
     */
    public OrderResourceFacadeRequest toOrderResourceFacadeRequestForCreateTax(OrderBankFacadeRequest orderBankFacadeRequest) {
        OrderResourceFacadeRequest orderResourceFacadeRequest = new OrderResourceFacadeRequest();
        orderResourceFacadeRequest.setOrderId(orderBankFacadeRequest.getWaybillNo());
        orderResourceFacadeRequest.setMerchantId(orderBankFacadeRequest.getOtsCreate().getMerchantId());
        orderResourceFacadeRequest.setTotalPrice(orderBankFacadeRequest.getOtsCreate().getTotalPrice());
        orderResourceFacadeRequest.setOrderPrice(orderBankFacadeRequest.getOtsCreate().getOrderPrice());
        orderResourceFacadeRequest.setDiscount(orderBankFacadeRequest.getOtsCreate().getDiscount());
        orderResourceFacadeRequest.setYun(orderBankFacadeRequest.getOtsCreate().getYun());
        orderResourceFacadeRequest.setPin(orderBankFacadeRequest.getOtsCreate().getPin());
        orderResourceFacadeRequest.setCurrency(orderBankFacadeRequest.getOtsCreate().getCurrency());
        orderResourceFacadeRequest.setOrderCode(Integer.valueOf(orderBankFacadeRequest.getOrgId()));
        orderResourceFacadeRequest.setOrderType(orderBankFacadeRequest.getOtsCreate().getOrderType());
        orderResourceFacadeRequest.setOrderTime(orderBankFacadeRequest.getOtsCreate().getOrderTime());
        orderResourceFacadeRequest.setPayMode(orderBankFacadeRequest.getOtsCreate().getPayMode());
        orderResourceFacadeRequest.setVer(orderBankFacadeRequest.getOtsCreate().getVer());

        OrderBankFacadeRequest.ReceivableDetailInfo receivableDetailInfo = new OrderBankFacadeRequest.ReceivableDetailInfo();
        // 4：运费
        receivableDetailInfo.setReceivableId("4");
        // 4：运费
        receivableDetailInfo.setReceivableType(4);
        receivableDetailInfo.setAmount(orderBankFacadeRequest.getOtsCreate().getTotalPrice());
        receivableDetailInfo.setCurrency(orderBankFacadeRequest.getOtsCreate().getCurrency());
        receivableDetailInfo.setCreateTime(new Date());
        List<OrderBankFacadeRequest.ReceivableDetailInfo> receivableDetails = new ArrayList<>(1);
        receivableDetails.add(receivableDetailInfo);
        orderResourceFacadeRequest.setReceivableDetails(receivableDetails);
        return orderResourceFacadeRequest;
    }

    /**
     * B商家修改
     *
     * @param orderBankFacadeRequest
     * @return
     */
    public ModifyDueFacadeRequest toModifyDueFacadeRequest(OrderBankFacadeRequest orderBankFacadeRequest, OrderBankFacadeRequest.BMerchantModify bMerchantModify) {
        ModifyDueFacadeRequest modifyDueFacadeRequest = new ModifyDueFacadeRequest();
        modifyDueFacadeRequest.setWaybillNo(orderBankFacadeRequest.getWaybillNo());
        modifyDueFacadeRequest.setReceiveTypeEnum(bMerchantModify.getBMerchantDueDetailInfo().getReceiveType());

        if (expressUccConfigCenter.isAmountScaleDownSwitch()) {
            LOGGER.info("金额降级开关开启，小数点保留两位");
            if (bMerchantModify.getBMerchantDueDetailInfo().getAmount() != null) {
                modifyDueFacadeRequest.setAmount(bMerchantModify.getBMerchantDueDetailInfo().getAmount().setScale(AMOUNT_SCALE_TWO, BigDecimal.ROUND_HALF_UP));
            }
        } else {
            LOGGER.info("金额降级开关关闭,走原有逻辑");
            modifyDueFacadeRequest.setAmount(bMerchantModify.getBMerchantDueDetailInfo().getAmount());
        }
        if(StringUtils.isNotBlank(bMerchantModify.getBMerchantDueDetailInfo().getCurrency())){
            modifyDueFacadeRequest.setCurrency(bMerchantModify.getBMerchantDueDetailInfo().getCurrency());
        }
        modifyDueFacadeRequest.setBusiness(orderBankFacadeRequest.getUUid() + "_" + System.currentTimeMillis());
        modifyDueFacadeRequest.setOrgId(orderBankFacadeRequest.getOrgId());
        modifyDueFacadeRequest.setOrgName(orderBankFacadeRequest.getOrgName());
        if(StringUtils.isNotBlank(bMerchantModify.getOtsMerchantId())){
            Map<String,String> ext = new HashMap<>(1);
            ext.put("wdMerchantId",bMerchantModify.getOtsMerchantId());
            modifyDueFacadeRequest.setExt(ext);
        }
        return modifyDueFacadeRequest;
    }

    /**
     * 非商城订单应收
     */
    public WebPosExtOrderFacadeRequest toWebPosExtOrderFacadeRequest(OrderBankFacadeRequest orderBankFacadeRequest) {
        WebPosExtOrderFacadeRequest webPosExtOrderFacadeRequest = new WebPosExtOrderFacadeRequest();
        webPosExtOrderFacadeRequest.setBusinessNo(orderBankFacadeRequest.getPosExtYun().getBusinessNo());
        webPosExtOrderFacadeRequest.setWebOrderAccountsDto(posExtToWebOrderAccountsDto(orderBankFacadeRequest));
        return webPosExtOrderFacadeRequest;
    }

    /**
     * 非商城订单应收转为WebOrderAccountsDto
     * 目前只有快运整车直达C2C，寄付现结
     */
    private WebOrderAccountsDto posExtToWebOrderAccountsDto(OrderBankFacadeRequest orderBankFacadeRequest) {
        OrderBankFacadeRequest.PosExtYun posExtYun = orderBankFacadeRequest.getPosExtYun();
        WebOrderAccountsDto webOrderAccountsDto = new WebOrderAccountsDto();
        // 单号
        webOrderAccountsDto.setOrderId(orderBankFacadeRequest.getWaybillNo());
        // 机构id
        webOrderAccountsDto.setOrgId(orderBankFacadeRequest.getOrgId());
        // 应收金额
        if (expressUccConfigCenter.isAmountScaleDownSwitch()) {
            LOGGER.info("金额降级开关开启，小数点保留两位");
            if (posExtYun.getAmount() != null) {
                webOrderAccountsDto.setAmountReceivable(String.valueOf(posExtYun.getAmount().setScale(AMOUNT_SCALE_TWO, BigDecimal.ROUND_HALF_UP)));
            }
        } else {
            LOGGER.info("金额降级开关关闭,走原有逻辑");
            webOrderAccountsDto.setAmountReceivable(String.valueOf(posExtYun.getAmount()));
        }
        // 防重id
        webOrderAccountsDto.setUUid(orderBankFacadeRequest.getUUid() + "_" + System.currentTimeMillis());
        // 支付人姓名：寄件人信息
        webOrderAccountsDto.setPayName(orderBankFacadeRequest.getConsignorInfo().getConsignorName());
        // 地址：寄件人信息
        webOrderAccountsDto.setPayerAdd(orderBankFacadeRequest.getConsignorInfo().getConsignorAddress());
        return webOrderAccountsDto;
    }

    /**
     * 先款订单原单外单台账
     * 先款订单的改址（若原单的结算方式是到付现结，则原单需要获取费用信息写外单台账（无需询价）（用于小程序合并支付））
     *
     * @param orderModel
     */
    public OrderBankFacadeRequest toOtsCreateForModifyRecord(ModifyRecord modifyRecord,ExpressOrderModel orderModel) {
        OrderBankFacadeRequest orderBankFacadeRequest = null;
        Finance finance = orderModel.getFinance();
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if (finance == null || orderSnapshot == null) {
            return null;
        }
        //先款才需要处理
        if (PaymentStageEnum.ONLINEPAYMENT != finance.getPaymentStage()) {
            return orderBankFacadeRequest;
        }
        ReaddressRecordDetailInfo readdressRecordDetail = (ReaddressRecordDetailInfo)modifyRecord.getModifyRecordDetail();
        FinanceInfo recordFinance = readdressRecordDetail.getFinance();
        if(null == recordFinance){
            return null;
        }
        Money money = new Money();
        //待支付金额
        money.setAmount(recordFinance.getPendingMoney().getAmount());
        money.setCurrency(CurrencyCodeEnum.of(recordFinance.getPendingMoney().getCurrencyCode()));

        if(null == money.getAmount() || money.getAmount().compareTo(BigDecimal.ZERO) <= 0){
            return null;
        }
        Finance financeOld = orderSnapshot.getFinance();
        orderBankFacadeRequest = this.toCommonOrderBankFacadeRequest(orderModel, orderSnapshot, orderModel.requestProfile().getTenantId());
        orderBankFacadeRequest.setOrgId(financeOld.getCollectionOrgNo());
        orderBankFacadeRequest.setWaybillNo(modifyRecord.getModifyRecordNo());
        MerchantEnum merchantEnum = null;
        if(ModifyRecordTypeEnum.READDRESS.getCode().equals(modifyRecord.getModifyRecordType()) // 改址记录
                || ModifyRecordTypeEnum.INTERCEPT.getCode().equals(modifyRecord.getModifyRecordType())){// 拦截记录
            // 不处理拒收改址记录 ModifyRecordTypeEnum.REJECT ，原因：拒收一单到底，改址结算方式只有【到付现结】【寄付月结】+【后款支付】
            if(orderModel.isFreight() || UnitedB2CUtil.isUnitedFreightB2C(orderModel)){
                merchantEnum = MerchantEnum.FREIGHT_READDRESS;
            } else {
                merchantEnum = MerchantEnum.ONLINE_READDRESS;
            }
        }

        OrderBankFacadeRequest.OtsCreate otsCreate = this.toOtsCreate(modifyRecord.getOperator(),money, merchantEnum);
        orderBankFacadeRequest.setOtsCreate(otsCreate);
        return orderBankFacadeRequest;
    }


    /**
     * 将订单信息转换为OTS创建对象，包含快照信息
     * @param money 金额对象
     * @param merchantEnum 外单台账MERCHANT_ID枚举类型
     * @param orderModel 订单模型对象
     * @return 转换后的OTS创建对象
     */
    public OrderBankFacadeRequest toOtsCreateForOrderWithSnapshot(Money money, MerchantEnum merchantEnum, ExpressOrderModel orderModel) {
        OrderBankFacadeRequest orderBankFacadeRequest = null;
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if (orderSnapshot == null) {
            return null;
        }

        if(null == money.getAmount() || money.getAmount().compareTo(BigDecimal.ZERO) <= 0){
            return null;
        }
        Finance financeOld = orderSnapshot.getFinance();
        orderBankFacadeRequest = this.toCommonOrderBankFacadeRequest(orderModel, orderSnapshot, orderModel.requestProfile().getTenantId());
        orderBankFacadeRequest.setOrgId(financeOld.getCollectionOrgNo());
        orderBankFacadeRequest.setWaybillNo(orderSnapshot.getRefOrderInfoDelegate().getWaybillNo());

        OrderBankFacadeRequest.OtsCreate otsCreate = this.toOtsCreate(orderModel.getOperator(),money, merchantEnum);
        orderBankFacadeRequest.setOtsCreate(otsCreate);
        return orderBankFacadeRequest;
    }

    /**
     * 构建公共参数
     *
     * @param order
     * @return
     */
    public OrderBankFacadeRequest toCommonOrderBankFacadeRequest(ExpressOrderModel order,ExpressOrderModel snapShot, String tenantId) {
        OrderBankFacadeRequest orderBankFacadeRequest = new OrderBankFacadeRequest();
        orderBankFacadeRequest.setWaybillNo(snapShot.getRefOrderInfoDelegate().getWaybillNo());
        orderBankFacadeRequest.setOrgId(snapShot.getFinance().getCollectionOrgNo());
        orderBankFacadeRequest.setOrgName(snapShot.getFinance().getCollectionOrgName());
        orderBankFacadeRequest.setConsigneeInfo(toConsigneeInfo(order, snapShot));
        orderBankFacadeRequest.setConsignorInfo(toConsignorInfo(order, snapShot));
        orderBankFacadeRequest.setUUid(tenantId + "_" + order.orderNo());
        return orderBankFacadeRequest;
    }

    /**
     * 收件人信息(拼接原单新单组合)
     *
     * @param orderModel
     * @return
     */
    public OrderBankFacadeRequest.ConsigneeInfo toConsigneeInfo(ExpressOrderModel orderModel, ExpressOrderModel snapshot) {
        if (orderModel == null) {
            return null;
        }
        OrderBankFacadeRequest.ConsigneeInfo consigneeInfo = new OrderBankFacadeRequest.ConsigneeInfo();
        Consignee consigneeNew = orderModel.getConsignee();
        //原单为空直接赋值新单, 逆向单改址单使用新单数据
        //优先赋值新单
        Consignee consigneeOld = snapshot.getConsignee();
        consigneeInfo.setConsigneeName(StringUtils.isNotBlank(consigneeNew.getConsigneeName()) ? consigneeNew.getConsigneeName() : consigneeOld.getConsigneeName());
        consigneeInfo.setConsigneeMobile(StringUtils.isNotBlank(consigneeNew.getConsigneeMobile()) ? consigneeNew.getConsigneeMobile() : consigneeOld.getConsigneeMobile());
        consigneeInfo.setConsigneePhone(StringUtils.isNotBlank(consigneeNew.getConsigneePhone()) ? consigneeNew.getConsigneePhone() : consigneeOld.getConsigneePhone());
        consigneeInfo.setConsigneeAddress(StringUtils.isNotBlank(consigneeNew.getConsigneeFullAddress()) ? consigneeNew.getConsigneeFullAddress() : consigneeOld.getConsigneeFullAddress());

        return consigneeInfo;
    }

    /**
     * 发件人信息(拼接原单新单组合)
     *
     * @param orderModel
     * @return
     */
    public OrderBankFacadeRequest.ConsignorInfo toConsignorInfo(ExpressOrderModel orderModel, ExpressOrderModel snapshot) {
        if (orderModel == null) {
            return null;
        }
        OrderBankFacadeRequest.ConsignorInfo consignorInfo = new OrderBankFacadeRequest.ConsignorInfo();
        Consignor consignorNew = orderModel.getConsignor();
       //优先赋值新单
       Consignor consignorOld = snapshot.getConsignor();
       consignorInfo.setConsignorName(StringUtils.isNotBlank(consignorNew.getConsignorName()) ? consignorNew.getConsignorName() : consignorOld.getConsignorName());
       consignorInfo.setConsignorMobile(StringUtils.isNotBlank(consignorNew.getConsignorMobile()) ? consignorNew.getConsignorName() : consignorOld.getConsignorMobile());
       consignorInfo.setConsignorPhone(StringUtils.isNotBlank(consignorNew.getConsignorPhone()) ? consignorNew.getConsignorPhone() : consignorOld.getConsignorPhone());
       consignorInfo.setConsignorAddress(StringUtils.isNotBlank(consignorNew.getConsignorFullAddress()) ? consignorNew.getConsignorFullAddress() : consignorOld.getConsignorFullAddress());
       if (consignorNew.getAddress() != null) {
           consignorInfo.setConsignorProvinceNo(consignorNew.getAddress().getProvinceNoGis());
           consignorInfo.setConsignorProvinceName(consignorNew.getAddress().getProvinceNameGis());
           consignorInfo.setConsignorCityNo(consignorNew.getAddress().getCityNoGis());
           consignorInfo.setConsignorCityName(consignorNew.getAddress().getCityNameGis());
       } else if (consignorOld.getAddress() != null) {
           consignorInfo.setConsignorProvinceNo(consignorOld.getAddress().getProvinceNoGis());
           consignorInfo.setConsignorProvinceName(consignorOld.getAddress().getProvinceNameGis());
           consignorInfo.setConsignorCityNo(consignorOld.getAddress().getCityNoGis());
           consignorInfo.setConsignorCityName(consignorOld.getAddress().getCityNameGis());
       }
        return consignorInfo;
    }


    /**
     * 外单台账新增防腐层转换 通用方法
     * @param merchantEnum
     * @return
     */
    public OrderBankFacadeRequest.OtsCreate toOtsCreate(String operator, Money money, MerchantEnum merchantEnum) {
        if(null == money){
            return null;
        }
        OrderBankFacadeRequest.OtsCreate otsCreate = new OrderBankFacadeRequest.OtsCreate();

        otsCreate.setMerchantId(merchantEnum.getMerchantId());

        otsCreate.setTotalPrice(money.getAmount());
        otsCreate.setOrderPrice(money.getAmount());
        //订单折扣
        otsCreate.setDiscount(BigDecimal.ZERO);
        //运费
        otsCreate.setYun(BigDecimal.ZERO);
        //下单人编号
        otsCreate.setPin(operator);
        //币种
        otsCreate.setCurrency(money.getCurrency().getOtsOrderbankCode());
        //当前时间
        otsCreate.setOrderTime(new Date());
        //订单类型
        otsCreate.setOrderType(OTS_CREATE_DEFAULT_ORDER_TYPE);
        //支付方式
        otsCreate.setPayMode(OTS_CREATE_DEFAULT_PAY_MODE);
        //版本
        otsCreate.setVer(OTS_CREATE_DEFAULT_VER);
        //明细
        List<OrderBankFacadeRequest.ReceivableDetailInfo> receivableDetails = new ArrayList<>();
        OrderBankFacadeRequest.ReceivableDetailInfo receivableDetailInfo = new OrderBankFacadeRequest.ReceivableDetailInfo();
        if (money.getAmount() != null) {
            receivableDetailInfo.setAmount(money.getAmount());
        }
        //应收单号=receivableType
        receivableDetailInfo.setReceivableId(String.valueOf(OTS_CREATE_DEFAULT_ORDER_TYPE));
        //应收类型
        receivableDetailInfo.setReceivableType(OTS_CREATE_DEFAULT_ORDER_TYPE);
        //创建应收的时间
        receivableDetailInfo.setCreateTime(new Date());
        receivableDetails.add(receivableDetailInfo);
        otsCreate.setReceivableDetails(receivableDetails);
        return otsCreate;
    }

    /**
     * 构建公共参数
     * @return
     */
    public OrderBankFacadeRequest toCommonOrderBankFacadeRequest(Consignee consignee, Consignor consignor, Finance finance, String tenantId, String orderBankNo, String orderNo) {
        OrderBankFacadeRequest orderBankFacadeRequest = new OrderBankFacadeRequest();
        orderBankFacadeRequest.setWaybillNo(orderBankNo);
        orderBankFacadeRequest.setOrgId(finance.getCollectionOrgNo());
        orderBankFacadeRequest.setOrgName(finance.getCollectionOrgName());
        OrderBankFacadeRequest.ConsigneeInfo consigneeInfo = new OrderBankFacadeRequest.ConsigneeInfo();
        consigneeInfo.setConsigneeAddress(consignee.getConsigneeFullAddress());
        consigneeInfo.setConsigneeName(consignee.getConsigneeName());
        consigneeInfo.setConsigneeMobile(consignee.getConsigneeMobile());
        consigneeInfo.setConsigneePhone(consignee.getConsigneePhone());
        orderBankFacadeRequest.setConsigneeInfo(consigneeInfo);
        OrderBankFacadeRequest.ConsignorInfo consignorInfo = new OrderBankFacadeRequest.ConsignorInfo();
        consignorInfo.setConsignorName(consignor.getConsignorName());
        consignorInfo.setConsignorMobile(consignor.getConsignorMobile());
        consignorInfo.setConsignorPhone(consignor.getConsignorPhone());
        consignorInfo.setConsignorAddress(consignor.getConsignorFullAddress());
        if (consignor.getAddress() != null) {
            consignorInfo.setConsignorProvinceNo(consignor.getAddress().getProvinceNoGis());
            consignorInfo.setConsignorProvinceName(consignor.getAddress().getProvinceNameGis());
            consignorInfo.setConsignorCityNo(consignor.getAddress().getCityNoGis());
            consignorInfo.setConsignorCityName(consignor.getAddress().getCityNameGis());
        }
        orderBankFacadeRequest.setConsignorInfo(consignorInfo);
        orderBankFacadeRequest.setUUid(tenantId + "_" + orderNo);
        return orderBankFacadeRequest;
    }

    /**
     * 判断是否为偏远流向
     * 偏远流向定义：
     * 1. 目的地为新疆或西藏的线路
     * 4. 如果是新疆省内寄件或者西藏省内寄件，则不属于偏远流向
     *
     *
     * @return 是否为偏远流向
     */
    public boolean isRemoteRoute(String fromProvinceCode, String toProvinceCode) {
        if (!BatrixSwitch.applyByBoolean(BatrixSwitchKey.REMOTE_ROTE_SWITCH)) {
            return false;
        }

        // 如果始发地或目的地省份编码为空，则不判断为偏远流向
        if (StringUtils.isBlank(fromProvinceCode) || StringUtils.isBlank(toProvinceCode)) {
            return false;
        }

        // 偏远流向需要目的地为新疆/西藏
        if (AddressConstants.XIN_JIANG.equals(toProvinceCode) || AddressConstants.XI_ZANG.equals(toProvinceCode)) {
            if (toProvinceCode.equals(fromProvinceCode)) {
                LOGGER.info("偏远流向省内寄件");
                return false;
            }

            return true;
        }

        return false;
    }

    /**
     * waybillSign[0] 标位赋值
     * 不支持E卡，以下条件第1位赋值0，支持E卡，则赋值1：
     * a.营销信息表中的优惠模式中有1-优惠券  Promotion tickets 不为空
     * c.营销信息表中传了毕业季 Promotion activities activityNo=GRADUATION_SEND
     * f．询价结果返回的折扣列表里，若折扣类型存在2-揽派同时、3-专业市场、10-散单批量寄 finance financeDetails discounts discountType
     * e. 调用小程序接口获取城市维度未开通快递E卡支付（寄付现结用寄件地址京标省市id，到付现结用收件地址京标省市id，调用https://cf.jd.com/pages/viewpage.action?pageId=195954430，返回结果为1代表支持E卡，返回为0代表不支持）
     * d.站点类型为“便民站点”（寄付现结用揽收站点ID获取揽收站点子类型为46-便民站点，到付现结用派送站点ID获取派送站点子类型为46-便民站点）
     * <p>
     * 0.POS到付台账接口E卡标位（第一位）赋值逻辑调整：到付接口初始化按照结算方式是到付判断不能使用E卡；
     * i. 存在COD金额；
     * ii. 派送站点是三方站点
     * iii. 订单使用优惠券
     *
     * @param orderModel
     * @param dfFee
     * @return
     */
    public ECardDisableReasonEnum isDisableECard(ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot, Finance finance, boolean dfFee) {
        ExpressOrderModel valOrderModel = null;
        if (orderSnapshot != null) {
            valOrderModel = orderSnapshot;
        } else {
            valOrderModel = orderModel;
        }

        LOGGER.debug("waybillSignZeroBit,valOrderModel:{},dfFee:{}", JSONUtils.beanToJSONDefault(valOrderModel), dfFee);
        if (valOrderModel.isHKMO()) {
            LOGGER.info("港澳订单不支持 E卡");
            return ECardDisableReasonEnum.ORDER_HK_MO;
        }
        if (valOrderModel.isIntl() && BatrixSwitch.applyByBoolean(BatrixSwitchKey.INTL_ORDER_ECARD_VALID_SWITCH)) {
            LOGGER.info("国际订单不支持 E卡");
            return ECardDisableReasonEnum.ORDER_INTL;
        }

        // TODO 修改场景 优惠券和活动 当前单是否存在
        LOGGER.debug("waybillSignZeroBit,getPromotion:{}", JSONUtils.beanToJSONDefault(valOrderModel.getPromotion()));
        if (valOrderModel.getPromotion() != null && !CollectionUtils.isEmpty(valOrderModel.getPromotion().getTickets())) {
            LOGGER.info("waybillSignZeroBit,不支持E卡，优惠券 ");
            return ECardDisableReasonEnum.TICKET;
        }
        if (valOrderModel.getPromotion() != null && !CollectionUtils.isEmpty(valOrderModel.getPromotion().getActivities())) {
            for (Activity activity : valOrderModel.getPromotion().getActivities()) {
                if ("GRADUATION_SEND".equals(activity.getActivityNo())) {
                    LOGGER.info("waybillSignZeroBit,不支持E卡，活动 ");
                    return ECardDisableReasonEnum.ACTIVITY_GRADUATION_SEND;
                }
            }
        }
        //POS到付应收里同时写了到付运费和代收货款
        //到付现结带cod，且不为0
        if (dfFee) {
            Product product = valOrderModel.getProductDelegate().getCodProduct();
            if (product != null) {
                BigDecimal cod = TypeConversion.stringToBigDecimal(product.getProductAttrs().get(AddOnProductAttrEnum.COD.getCode()), DEFAULT_AMOUNT_DECIMAL_SCALE, null);
                if (cod != null && cod.compareTo(BigDecimal.ZERO) > 0) {
                    LOGGER.info("waybillSignZeroBit,不支持E卡，到付cod ");
                    return ECardDisableReasonEnum.DF_COD;
                }
            }
        }

        // 判断是否为偏远流向
        String fromProvinceCode = Optional.ofNullable(GetFieldUtils.getField(valOrderModel, GetFieldUtils.CONSIGNOR_ADDRESS))
                .map(Address::getProvinceNoGis)
                .orElse(null);
        String toProvinceCode = Optional.ofNullable(GetFieldUtils.getField(valOrderModel, GetFieldUtils.CONSIGNEE_ADDRESS))
                .map(Address::getProvinceNoGis)
                .orElse(null);
        if (isRemoteRoute(fromProvinceCode, toProvinceCode)) {
            LOGGER.info("偏远流向不支持E卡支付");
            return ECardDisableReasonEnum.REMOTE_ROUTE;
        }
        String site = GetFieldUtils.getField(valOrderModel, GetFieldUtils.END_STATION_TYPE_L3);

        if(finance !=null &&
                finance.getDiscountAmount()!= null &&
                finance.getDiscountAmount().equals(finance.getPreAmount())){
            if(ShipmentConstants.THIRD_PARTY_SITE_TYPE.equals(site) && orderModel.isC2C()){
                LOGGER.info("三方站点且原价订单，不支持E卡");
                return ECardDisableReasonEnum.SITE_THIRD_TYPE_ORIGINAL_PRICE;
            }
        }

        if (finance != null && !CollectionUtils.isEmpty(finance.getFinanceDetails())) {
            LOGGER.info("waybillSignZeroBit,getFinanceDetails:{}", JSONUtils.beanToJSONDefault(finance.getFinanceDetails()));
            boolean eCardSwitch = BatrixSwitch.applyByBoolean(BatrixSwitchKey.E_CARD_DISCOUNT_SWITCH);
            for (FinanceDetail financeDetail : finance.getFinanceDetails()) {
                if (!CollectionUtils.isEmpty(financeDetail.getDiscounts())) {
                    for (Discount discount : financeDetail.getDiscounts()) {
                        //2-揽派同时、3-专业市场、4-渠道优惠、10-散单批量寄、11-员工福利寄
                        if (null == discount || StringUtils.isBlank(discount.getDiscountType())) {
                            LOGGER.info("折扣信息为空跳过");
                            continue;
                        }
                        switch (discount.getDiscountType()) {
//                            case "1":
//                                LOGGER.info("waybillSignZeroBit,不支持E卡，折扣类型={} ", discount.getDiscountType());
//                                return ECardDisableReasonEnum.DISCOUNT_1;
                            case "2":
                                LOGGER.info("waybillSignZeroBit,不支持E卡，折扣类型={} ", discount.getDiscountType());
                                return ECardDisableReasonEnum.DISCOUNT_2;
                            case "3":
                                LOGGER.info("waybillSignZeroBit,不支持E卡，折扣类型={} ", discount.getDiscountType());
                                return ECardDisableReasonEnum.DISCOUNT_3;
                            case "4":
                                LOGGER.info("waybillSignZeroBit,不支持E卡，折扣类型={} ", discount.getDiscountType());
                                return ECardDisableReasonEnum.DISCOUNT_4;
                            case "6":
                                if (eCardSwitch) {
                                    LOGGER.info("waybillSignZeroBit,不支持E卡，折扣类型={} ", discount.getDiscountType());
                                    return ECardDisableReasonEnum.DISCOUNT_6;
                                } else {
                                    LOGGER.info("开关关闭");
                                    break;
                                }
                            case "10":
                                LOGGER.info("waybillSignZeroBit,不支持E卡，折扣类型={} ", discount.getDiscountType());
                                return ECardDisableReasonEnum.DISCOUNT_10;
                            case "11":
                                LOGGER.info("waybillSignZeroBit,不支持E卡，折扣类型={} ", discount.getDiscountType());
                                return ECardDisableReasonEnum.DISCOUNT_11;
                            default:
                                break;
                        }
                    }
                }
            }
        }

        LOGGER.info("waybillSignZeroBit,getEndStationType:{},getStartStationType:{}", valOrderModel.getShipment().getEndStationType(), valOrderModel.getShipment().getStartStationType());
        if (dfFee) {
            //到付现结用派送站点ID获取派送站点子类型为46-便民站点
            if (Integer.valueOf(46).equals(valOrderModel.getShipment().getEndStationType())) {
                LOGGER.info("waybillSignZeroBit,不支持E卡， 到付现结用派送站点ID获取派送站点子类型为46-便民站点");
                return ECardDisableReasonEnum.SITE_BM_DF;
            }
        } else {
            //寄付现结用揽收站点ID获取揽收站点子类型为46-便民站点
            if (Integer.valueOf(46).equals(valOrderModel.getShipment().getStartStationType())) {
                LOGGER.info("waybillSignZeroBit,不支持E卡， 寄付现结用揽收站点ID获取揽收站点子类型为46-便民站点");
                return ECardDisableReasonEnum.SITE_BM_JF;
            }
        }

        //由于不再配置E卡城市，增加开关，下线此逻辑
        if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.VAL_E_CARD_CITY_SWITCH)) {
            LOGGER.info("ECard城市校验切换开关：开启");
            EcardFacadeRequest ecardFacadeRequest = null;
            if (dfFee) {
                ecardFacadeRequest = new EcardFacadeRequest();
                //到付现结用收件地址京标省市id
                ecardFacadeRequest.setProvinceId(valOrderModel.getConsignee().getAddress() != null ? valOrderModel.getConsignee().getAddress().getProvinceNoGis() : null);
                ecardFacadeRequest.setCityId(valOrderModel.getConsignee().getAddress() != null ? valOrderModel.getConsignee().getAddress().getCityNoGis() : null);
            } else {
                if (valOrderModel.getConsignor() != null && valOrderModel.getConsignor().getAddress() != null) {
                    ecardFacadeRequest = new EcardFacadeRequest();
                    //寄付现结用寄件地址京标省市id
                    ecardFacadeRequest.setProvinceId(valOrderModel.getConsignor().getAddress().getProvinceNoGis());
                    ecardFacadeRequest.setCityId(valOrderModel.getConsignor().getAddress().getCityNoGis());
                }
            }
            if (ecardFacadeRequest != null) {
                //E卡支付查询接口，如果失败，则跳过这个判断，不影响主流程
                try {
                    boolean match = ecardFacade.ecardMatch(ecardFacadeRequest);
                    if (!match) {
                        LOGGER.info("该城市不支持 E卡");
                        if (dfFee) {
                            return ECardDisableReasonEnum.CITY_DF;
                        } else {
                            return ECardDisableReasonEnum.CITY_JF;
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("E卡查询错误，跳过该判断，继续主流程执行");
                }
            }
        } else {
            LOGGER.info("ECard城市校验切换开关：关闭");
        }

        return null;
    }

    /**
     * 外单台账新增
     * @return
     */
    public OrderResourceFacadeRequest toSelfBillingPayOrderFacadeRequest(Integer orgId, String userPin, String orderBankNo, String consignorProvinceNo, BigDecimal orderBankAmt, String merchantId, Integer ver) {
        OrderResourceFacadeRequest orderResourceFacadeRequest = new OrderResourceFacadeRequest();
        orderResourceFacadeRequest.setOrderId(orderBankNo);
        orderResourceFacadeRequest.setMerchantId(merchantId);
        orderResourceFacadeRequest.setTotalPrice(orderBankAmt);
        orderResourceFacadeRequest.setOrderPrice(orderBankAmt);
        orderResourceFacadeRequest.setDiscount(BigDecimal.ZERO);
        //应收=totalPrice+yun-discount，yun别传值
        orderResourceFacadeRequest.setYun(BigDecimal.ZERO);
        orderResourceFacadeRequest.setPin(userPin);
        orderResourceFacadeRequest.setCurrency(CurrencyCodeEnum.CNY.getOtsOrderbankCode());
        orderResourceFacadeRequest.setOrderCode(orgId);
        orderResourceFacadeRequest.setOrderType(OTS_CREATE_DEFAULT_ORDER_TYPE);//普通订单
        orderResourceFacadeRequest.setOrderTime(DateUtils.now());
        orderResourceFacadeRequest.setPayMode(OTS_CREATE_DEFAULT_PAY_MODE);//在线支付
        orderResourceFacadeRequest.setVer(ver);
        List<OrderBankFacadeRequest.ReceivableDetailInfo> receivableDetails = new ArrayList<>();
        OrderBankFacadeRequest.ReceivableDetailInfo receivableDetailInfo = new OrderBankFacadeRequest.ReceivableDetailInfo();
        receivableDetailInfo.setAmount(orderBankAmt);
        receivableDetailInfo.setCurrency(CurrencyCodeEnum.CNY.getOtsOrderbankCode());
        receivableDetailInfo.setCreateTime(DateUtils.now());
        receivableDetailInfo.setReceivableType(OTS_CREATE_DEFAULT_RECEIVABLE_TYPE);//运费
        receivableDetailInfo.setReceivableId(String.valueOf(OTS_CREATE_DEFAULT_RECEIVABLE_TYPE));
        receivableDetails.add(receivableDetailInfo);
        orderResourceFacadeRequest.setReceivableDetails(receivableDetails);
        //扩展信息
        HashMap<String, String> ext = new HashMap<>();
        ext.put(OTS_EXT_KEY_DATASOURCE, OTS_CREATE_DEFAULT_DATASOURCE);

        AddressInfoDto addressInfoDto = this.getReplaceAddress(consignorProvinceNo);
        if (addressInfoDto != null) {
            //省ID--国标
            ext.put(OTS_EXT_KEY_PROVINCE, addressInfoDto.getProvinceNo());
        }
        orderResourceFacadeRequest.setExtMap(ext);
        return orderResourceFacadeRequest;
    }
}
