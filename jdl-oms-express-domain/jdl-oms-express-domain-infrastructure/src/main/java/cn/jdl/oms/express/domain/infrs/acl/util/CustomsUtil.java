package cn.jdl.oms.express.domain.infrs.acl.util;

import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.ProductDelegate;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import org.springframework.stereotype.Component;

/**
 * 报关域工具类
 */
@Component
public class CustomsUtil {

    /**
     * 是否港澳同城、港澳互寄，并且主产品是电商特惠ed-m-0059
     */
    public boolean isHKMOWithDSTH(ExpressOrderModel orderModel) {
        ExpressOrderModel orderSnapshot = null;
        // 接单场景不看快照（避免改址换单、拒收换单从原单判断）
        if (!BusinessSceneUtil.isCreate(orderModel)) {
            orderSnapshot = orderModel.getOrderSnapshot();
        }

        // 必须是港澳同城、港澳互寄
        if (!isHKMOOnly(orderModel) && !isHKMOOnly(orderSnapshot)) {
            return false;
        }

        // 必须是ed-m-0059
        return isSpecialMainProduct(ProductEnum.DSTH, orderModel, orderSnapshot);
    }

    /**
     * 判断是否港澳同城、港澳互寄
     */
    private boolean isHKMOOnly(ExpressOrderModel orderModel) {
        return orderModel != null && orderModel.isHKMOOnly();
    }

    /**
     * 判断修改场景是否指定的主产品
     *
     * @param mainProductEnum 指定的主产品
     * @param orderModel 当前单
     * @return
     */
    private boolean isSpecialMainProduct(ProductEnum mainProductEnum, ExpressOrderModel orderModel, ExpressOrderModel snapshot) {
        // 获取产品代理：优先从当前单取；如果修改请求没有修改产品（当前单ProductDelegate为null），从快照取
        ProductDelegate productDelegate = orderModel.getProductDelegate();
        if ((productDelegate == null || productDelegate.isEmpty())
                && (snapshot != null && snapshot.getProductDelegate() != null && !snapshot.getProductDelegate().isEmpty())) {
            productDelegate = snapshot.getProductDelegate();
        }
        if (productDelegate == null || productDelegate.isEmpty()) {
            return false;
        }

        // 存在指定产品且未删除
        Product product = productDelegate.ofProductNo(mainProductEnum.getCode());
        return product != null && OperateTypeEnum.DELETE != product.getOperateType();
    }
}