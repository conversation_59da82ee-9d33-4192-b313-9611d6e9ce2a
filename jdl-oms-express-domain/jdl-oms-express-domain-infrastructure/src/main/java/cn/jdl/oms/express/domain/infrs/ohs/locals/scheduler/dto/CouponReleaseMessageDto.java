package cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 优惠券释放
 * <AUTHOR>
 */
@Data
public class CouponReleaseMessageDto extends AbstractMessageDto {

    private static final long serialVersionUID = -6153020473465260424L;
    /**
     * 优惠码
     */
    private String couponNo;
    /**
     * 下单人
     */
    private String orderCreatorNo;

    /**
     * 操作时间
     */
    private Date orderCreateTime;

    /**
     * 使用金额
     */
    private BigDecimal useAmount;

    /**
     * 运单号
     */
    private String waybillNo;

    private Integer couponExecuteType;

    /**
     * 是否修改数据库中优惠券状态
     */
    private boolean modifySomeData;

    /**
     * 优惠券来源
     */
    private Integer couponSource;

    /**
     * 平台订单号
     */
    private String platformOrderNo;

    /**
     * 始发流向
     */
    private String startFlowDirection;

    /**
     * 目的流向
     */
    private String endFlowDirection;

    /**
     * 寄件人手机号
     */
    private String fromUserTel;

    /**
     * 收件人手机号
     */
    private String toUserTel;

    /**
     * 优惠券逻辑删除标识，用于标记是否下发
     */
    private boolean releaseTicketPending;
}
