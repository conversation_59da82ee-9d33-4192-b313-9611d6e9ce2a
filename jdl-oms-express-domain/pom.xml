<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>cn.jdl.oms</groupId>
    <artifactId>jdl-oms-express-domain</artifactId>
    <packaging>pom</packaging>
    <version>1.0.0-SNAPSHOT</version>
    <name>jdl-oms-express-domain</name>
    <url>http://maven.apache.org</url>
    <description>纯配订单中心核心领域</description>
    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>3.8.1</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <modules>
        <module>jdl-oms-express-domain-adapter</module>
        <module>jdl-oms-express-domain-extension</module>
        <module>jdl-oms-express-domain-infrastructure</module>
        <module>jdl-oms-express-domain-model</module>
        <module>jdl-oms-express-domain-service</module>
        <module>jdl-oms-express-domain-spec</module>
    </modules>

    <!-- 项目属性 -->
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!--Spring版本-->
        <spring.version>5.2.9.RELEASE</spring.version>
        <lombok.version>1.18.16</lombok.version>
        <jsf.version>1.7.8-HOTFIX-T2</jsf.version>
        <!--Jimdb版本-->
        <jimdb.version>2.1.6</jimdb.version>
        <!--log4j2 日志配置-->
        <log4j2.version>2.18.0-jdsec.rc2</log4j2.version>
        <log4j.version>1.2.17</log4j.version>
        <slf4j.api.version>1.7.30</slf4j.api.version>
        <jcl.over.slf4j.version>1.7.5</jcl.over.slf4j.version>
        <!--disruptor-->
        <disruptor.version>3.3.6</disruptor.version>
        <!--google.guava.version-->
        <google.guava.version>29.0-jre</google.guava.version>
        <!--安全加密-->
        <security.configsec.version>1.0.2.RELEASE</security.configsec.version>
        <!--数据安全加密-->
        <sec.version>1.0.1-SNAPSHOT</sec.version>
        <!--ump-->
        <ump.version>20240630</ump.version>
        <!--jackson-->
        <jackson.version>1.9.2</jackson.version>
        <!--mysql-->
        <mysql.version>5.1.48</mysql.version>
        <!--dbcp2-->
        <commons.dbcp2.version>2.4.0</commons.dbcp2.version>
        <!--dbcp2-->
        <commons.pool2.version>2.6.2</commons.pool2.version>
        <!--common.io-->
        <common.io.version>2.0.1</common.io.version>
        <!--common.lang-->
        <common.lang3.version>3.10</common.lang3.version>

        <!--batrix.version-->
        <batrix.core.version>1.2.3-SNAPSHOT</batrix.core.version>
        <batrix.sdk.version>1.0.9-SNAPSHOT</batrix.sdk.version>
        <batrix.spec.version>1.0.0-SNAPSHOT</batrix.spec.version>
        <batrix.monitor.version>1.2.0-SNAPSHOT</batrix.monitor.version>
        <!--redis版本-->
        <redis.clients.version>2.9.0</redis.clients.version>
        <spring.data.redis.version>1.8.18.RELEASE</spring.data.redis.version>
        <!--产品校验-->
        <jdl.prodcentre.version>1.1.1-SNAPSHOT</jdl.prodcentre.version>
        <!--青龙下发-->
        <etms.receive.version>1.3.55-SNAPSHOT</etms.receive.version>
        <!--防重工具（箱号防重，订单号防重）-->
        <cp.core.function.version>0.0.8-SNAPSHOT</cp.core.function.version>
        <!--数据层模型-->
        <cp.core.order.version>1.0.0-SNAPSHOT</cp.core.order.version>
        <spring.version>5.2.9.RELEASE</spring.version>
        <!--JMQ消息发送-->
        <jmq.version>2.3.8</jmq.version>
        <!--traceholder-->
        <jmq.jd.traceholder.version>1.2.0</jmq.jd.traceholder.version>
        <!--coo ucc versioin-->
        <coo.ucc.version>0.0.3-SNAPSHOT</coo.ucc.version>
        <!--disruptor-->
        <disruptor.version>3.3.6</disruptor.version>
        <!--地址解析+京标转国标-->
        <addressTranslation.version>1.8.1-SNAPSHOT</addressTranslation.version>
        <!-- 京标转国标 -->
        <!--<addresstranslation.version>1.6.4-SNAPSHOT</addresstranslation.version>-->
        <!--门店基础资料-->
        <!--仓配快递修改依赖的阿尔法版本-->
        <!--确认引入的依赖包依赖的三方jar包版本是否冲突-->
        <lp.api.version>1.0.47-SNAPSHOT</lp.api.version>
        <!--加解密-->
        <tdeclient.version>3.0.4-SNAPSHOT</tdeclient.version>
        <tdecommon.version>3.0.4-SNAPSHOT</tdecommon.version>
        <aces.springclient.version>1.0.1-SNAPSHOT</aces.springclient.version>
        <!--eclp主数据-->
        <eclp.master.api>0.0.3-SNAPSHOT</eclp.master.api>
        <!--青龙基础资料-->
        <ldop.basic.api>1.1.45-SNAPSHOT</ldop.basic.api>
        <!--订单中继依赖-->
        <cp.db.op.client.version>1.0.0-SNAPSHOT</cp.db.op.client.version>
        <!--spring支持切入点表达式-->
        <aspectjweaver.version>1.8.9</aspectjweaver.version>
        <!--青龙运单服务-->
        <etms.waybill.agent.version>4.2.343</etms.waybill.agent.version>
        <!--返佣平台-->
        <express.dispatcher.api.version>1.1-SNAPSHOT</express.dispatcher.api.version>
        <!--主键生成器-->
        <cp.oms.pk.client.version>0.0.1-SNAPSHOT</cp.oms.pk.client.version>
        <!--订单下发-->
        <router.disp.version>0.0.3-SNAPSHOT</router.disp.version>
        <!--数科敏感词校验api-->
        <csia.gateway.version>0.2.8</csia.gateway.version>
        <!--规则引擎版本-->
        <drools.version>7.3.0.Final</drools.version>
        <!--httpclient-->
        <httpclient.version>4.5.12</httpclient.version>
        <httpcore.version>4.4.10</httpcore.version>
        <httpmime.verison>4.5.6</httpmime.verison>
        <!--fastjson-->
        <fastjson.version>1.2.83-jdsec.rc1</fastjson.version>
        <!--jdbc start-->
        <mybatis.version>3.5.6</mybatis.version>
        <mybatis.spring.version>1.3.1</mybatis.spring.version>
        <mysql.connector.java.version>5.1.48</mysql.connector.java.version>
        <dbcp.version>1.4</dbcp.version>
        <!--jdbc end-->
        <caffeine.cache.version>2.7.0</caffeine.cache.version>
        <!--青龙三合一接单接口-->
        <lbcc.delivery.api.version>1.1.103</lbcc.delivery.api.version>
        <!--零售订单基础信息查询-->
        <ioms.export.cbd.sdk.version>3.0.9-SNAPSHOT</ioms.export.cbd.sdk.version>
        <!--零售厂直订单基础信息查询-->
        <com.jd.dropship.version>1.0.0-SNAPSHOT</com.jd.dropship.version>
        <jdorders.export.dict.version>3.0.20-SNAPSHOT</jdorders.export.dict.version>
        <!--零售订单查询返回解析-->
        <purchase.sdk.domain.version>6.5.22-SNAPSHOT</purchase.sdk.domain.version>
        <purchase.serializer.utils>1.1-SNAPSHOT</purchase.serializer.utils>
        <!--任务调度框架-->
        <jdl-paq-all.version>2.0.12-SNAPSHOT</jdl-paq-all.version>
        <!-- 优惠券 -->
        <lcmc.out.api.version>2.0.1-SNAPSHOT</lcmc.out.api.version>
        <!--产品中心-->
        <jdl-pms-client.version>0.0.37-RELEASE</jdl-pms-client.version>
        <!--预分拣逻辑计算统一接口-->
        <ldop.center.api.version>1.7.458</ldop.center.api.version>
        <!--IOT资源-->
        <iot.clinet.version>1.0-SNAPSHOT</iot.clinet.version>
        <!--计费询价-->
        <lbs.product.price.version>0.0.2-SNAPSHOT</lbs.product.price.version>
        <!--本地缓存-->
        <ehcache.version>2.6.9</ehcache.version>
        <!--Pos台账-->
        <!-- 依赖升级需要注意WebOrderAccounts字段是否一致：posLink.posPay.accsyn.version、posaccsyn-jsf.version、posExtOrderAccounts.version -->
        <posLink.posPay.accsyn.version>0.0.5-SNAPSHOT</posLink.posPay.accsyn.version>
        <!--Pos直连-->
        <posLink.yunOrderServer.version>0.3-SNAPSHOT</posLink.yunOrderServer.version>
        <!--B商家台账-->
        <supplierrecon-common.version>1.22.0-SNAPSHOT</supplierrecon-common.version>
        <!--pos直连青龙运单写应收-->
        <!-- 依赖升级需要注意WebOrderAccounts字段是否一致：posLink.posPay.accsyn.version、posaccsyn-jsf.version、posExtOrderAccounts.version -->
        <posaccsyn-jsf.version>1.3.3-SNAPSHOT</posaccsyn-jsf.version>
        <!--地址机构服务查询-->
        <address.org.version>1.0-SNAPSHOT</address.org.version>
        <!--微信先享取消依赖服务-->
        <terminal.agent.version>1.0.12-SNAPSHOT</terminal.agent.version>
        <!--DUU客户端版本-->
        <ducc.client.version>3.0.0-SNAPSHOT</ducc.client.version>
        <!--外单台账-->
        <ots-orderbank-export.version>1.0.6-SNAPSHOT</ots-orderbank-export.version>
        <!--获取运单号-->
        <wbms-wcs-client.version>1.0.0-SNAPSHOT</wbms-wcs-client.version>

        <logistics-account-facade.version>1.0-SNAPSHOT</logistics-account-facade.version>
        <!--es client-->
        <org.elasticsearch.client.version>6.7.2</org.elasticsearch.client.version>
        <!--C后台-->
        <c.m.api.version>2.0.0-SNAPSHOT</c.m.api.version>
        <!--kafka能力统计依赖-->
        <kafka.client.version>2.8.2</kafka.client.version>
        <!--E卡-->
        <cms.api.open.version>1.0.1-SNAPSHOT</cms.api.open.version>
        <!--积分-->
        <integrate.middleground.api.version>1.0.0-SNAPSHOT</integrate.middleground.api.version>
        <!--外单台账-->
        <cornucopia.server.version>0.5.5-SNAPSHOT</cornucopia.server.version>
        <!--退款网关接口-->
        <paygateway.fm.version>1.6.1-SNAPSHOT</paygateway.fm.version>
        <!--全链路跟踪-->
        <ordertrack.version>1.0-SNAPSHOT</ordertrack.version>
        <!--sop商家黑名单、pin校验、打印流水记录-->
        <ldop.oms.api.version>uat-1.0-SNAPSHOT</ldop.oms.api.version>
        <!-- 高峰期附加费查询-->
        <jdl.pms.basic.api.version>0.0.39-RELEASE</jdl.pms.basic.api.version>
        <!-- 订单控单 -->
        <business.open.api.version>0.0.1-SNAPSHOT</business.open.api.version>
        <!-- 现结余额查询接口-->
        <bms.settlement.api.version>0.1.5-SNAPSHOT</bms.settlement.api.version>
        <!--订单台账详情查询-->
        <orderbank.export.version>2.6.0-SNAPSHOT</orderbank.export.version>
        <xstream.version>1.4.18</xstream.version>
        <!-- 快递短信验证码服务 -->
        <express.resource.api.version>1.0-SNAPSHOT</express.resource.api.version>
        <!--地址围栏-->
        <address.fence.version>2.4.1-SNAPSHOT</address.fence.version>
        <!--数科内容安全-->
        <ai.moderation.version>1.1.1</ai.moderation.version>
        <!--脱敏-->
        <Desen2.version>3.0-SNAPSHOT</Desen2.version>
        <!--路由揽收时间切片-->
        <pickup.range.version>2.8.52-SNAPSHOT</pickup.range.version>
        <!--单号关联关系存储-->
        <order.relation.version>1.0.0-SNAPSHOT</order.relation.version>
        <!--batrix 归因-->
        <jdl.batrix.tracer.version>1.0-SNAPSHOT</jdl.batrix.tracer.version>

        <google.protobuf.version>3.5.1</google.protobuf.version>
        <!--pfinder-->
        <pfinder.version>1.2.1-FINAL</pfinder.version>
        <org.mapstruct.version>1.5.3.Final</org.mapstruct.version>
        <!-- 外单取件获取站点 -->
        <tos.dispatch.version>1.2-SNAPSHOT</tos.dispatch.version>
        <!--open.lpc-->
        <open.lpc.api.version>1.0.0-SNAPSHOT</open.lpc.api.version>
        <!--las.refuse-->
        <las.waybill.adapter.refuse.version>1.1-SNAPSHOT</las.waybill.adapter.refuse.version>
        <!-- 京东保险 -->
        <jd.insurance.version>0.0.72</jd.insurance.version>
        <!-- 查询商家结算主体 -->
        <lbs-master-outer-api.version>0.0.1-SNAPSHOT</lbs-master-outer-api.version>
        <!-- 通用计费询价 -->
        <ccjf-data-api.version>0.0.1-SNAPSHOT</ccjf-data-api.version>
        <!-- 运单申请退款 -->
        <waybill-refund-agent.version>1.0.2-SNAPSHOT</waybill-refund-agent.version>
        <!-- 钱包扣款统一网关 -->
        <agreement.pay.gateway.version>0.0.10</agreement.pay.gateway.version>
        <!-- 区域服务能力相关服务 -->
        <dispatch.client.version>1.3.2-SNAPSHOT</dispatch.client.version>
        <!-- TMS运输基础资料 -->
        <tms.basic.client>1.8-SNAPSHOT</tms.basic.client>
        <!-- 港澳单量限制 -->
        <dac-client.version>0.0.1-SNAPSHOT</dac-client.version>
        <!-- eclp-co -->
        <eclp.co.api.version>0.0.1-SNAPSHOT</eclp.co.api.version>
        <!-- 纯配订单中心接口api -->
        <jdl.oms.api.version>1.0.1-SNAPSHOT</jdl.oms.api.version>
        <!-- eclp 异常中心 -->
        <eclp.exception.version>0.0.1-SNAPSHOT</eclp.exception.version>
        <!-- 用户权益中心 -->
        <jd.user.right.api.version>1.0.9-SNAPSHOT</jd.user.right.api.version>
        <!-- tms询价服务 -->
        <tms.rfq.client.version>1.0-SNAPSHOT</tms.rfq.client.version>
        <!-- batrix配置监听 -->
        <bsc.server.property.listener.version>1.0.0-SNAPSHOT</bsc.server.property.listener.version>
        <!--EBS收入集成-->
        <bms-payable-ebs-rpc-api.version>0.0.8-SNAPSHOT</bms-payable-ebs-rpc-api.version>
        <!--京东零售:跨境业务-换汇接口-->
        <sc.price.exchange.rate.version>1.2</sc.price.exchange.rate.version>
        <!--tms仓位-->
        <tms.csc.client.version>1.1.1-SNAPSHOT</tms.csc.client.version>
        <!-- 物损理赔 -->
        <inter.claim.export.verison>1.0.90</inter.claim.export.verison>
        <!-- POS扫码付 -->
        <pospay.mobile.gateway.export.version>0.0.9-SNAPSHOT</pospay.mobile.gateway.export.version>
        <!-- 非商城订单应收（仅涉及到快运C2C整车） -->
        <!-- 依赖升级需要注意WebOrderAccounts字段是否一致：posLink.posPay.accsyn.version、posaccsyn-jsf.version、posExtOrderAccounts.version -->
        <posExtOrderAccounts.version>1.2-SNAPSHOT</posExtOrderAccounts.version>
        <!--产品映射关系-->
        <product.mapping.version>0.0.1-RELEASE</product.mapping.version>
        <!-- 自动核销 -->
        <off.payment.expor.version>1.1.8-SNAPSHOT</off.payment.expor.version>
        <!--运单系统单号校验工具-->
        <etms.waybillcode.rule.version>3.0.4</etms.waybillcode.rule.version>
        <!--站点匹配服务-->
        <preseparate-saf.version>3.6.7-SNAPSHOT</preseparate-saf.version>
        <!--r2录制回放-->
        <r2.sdk.version>0.4.9-SNAPSHOT</r2.sdk.version>
        <!--零售敏感词-->
        <sensitive.word.version>2.3.9</sensitive.word.version>
        <!--收发管家-->
        <bms.account.outer>0.0.1-SNAPSHOT</bms.account.outer>
        <!-- 交易业务监控 -->
        <cp.core.log.version>1.0.0-SNAPSHOT</cp.core.log.version>
        <!-- 科技支付单 -->
        <jdpay.gateway.version>0.1.54</jdpay.gateway.version>
        <!--服务加服务单-->
        <serviceplus.version>1.5.3-SNAPSHOT</serviceplus.version>
        <!--零售售后中台服务-->
        <afs.biz.center.version>4.0.9-SNAPSHOT</afs.biz.center.version>
        <!--驾车距离-->
        <jdlbsapi.version>1.4.19-SNAPSHOT</jdlbsapi.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <!--百川流程编排依赖-->
            <dependency>
                <groupId>cn.jdl.batrix</groupId>
                <artifactId>jdl-batrix-core</artifactId>
                <version>${batrix.core.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring-core</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-beans</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-aop</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-expression</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-context</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.validation</groupId>
                        <artifactId>validation-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.jmq</groupId>
                        <artifactId>jmq-client-spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.laf.config</groupId>
                        <artifactId>laf-config-client-jd</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.jdl.batrix</groupId>
                <artifactId>jdl-batrix-spec</artifactId>
                <version>${batrix.spec.version}</version>
            </dependency>
            <!--batrix sdk 轻量级结构引用   BusinessIdentity 类所在包-->
            <dependency>
                <groupId>cn.jdl.batrix</groupId>
                <artifactId>jdl-batrix-sdk</artifactId>
                <version>${batrix.sdk.version}</version>
            </dependency>
            <!-- batrix数据监控上报-->
            <dependency>
                <groupId>cn.jdl.batrix</groupId>
                <artifactId>jdl-batrix-monitor</artifactId>
                <version>${batrix.monitor.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.laf.config</groupId>
                        <artifactId>laf-config-client-jd</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.jdl.batrix</groupId>
                        <artifactId>jdl-batrix-sdk</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.ump</groupId>
                        <artifactId>profiler</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--百川kafka能力采集-->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.client.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.jdl.oms</groupId>
                <artifactId>jdl-oms-express-horz-extension</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>cn.jdl.oms</groupId>
                <artifactId>jdl-oms-express-shared-common</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <!-- springframe start -->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-orm</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${spring.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring.version}</version>
                <scope>test</scope>
            </dependency>
            <!-- springframe end -->

            <!--lombok-->
            <!-- <dependency>
                 <groupId>org.projectlombok</groupId>
                 <artifactId>lombok</artifactId>
                 <version>${lombok.version}</version>
             </dependency>-->
            <!--slf4j日志门面-->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.api.version}</version>
            </dependency>
            <!--核心log4j2jar包-->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <!--用于与slf4j保持桥接-->
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>${jcl.over.slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j-impl</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <!--web工程需要包含log4j-web，非web工程不需要-->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-web</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <!--需要使用log4j2的AsyncLogger需要包含disruptor-->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-jcl</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <!-- async log4j2 -->
            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>${disruptor.version}</version>
            </dependency>
            <!-- logging end -->
            <!--ump-->
            <dependency>
                <groupId>com.jd.ump</groupId>
                <artifactId>profiler</artifactId>
                <version>${ump.version}</version>
            </dependency>

            <!--guava-->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${google.guava.version}</version>
            </dependency>

            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectjweaver.version}</version>
            </dependency>

            <!--JMQ-->
            <dependency>
                <groupId>com.jd.jmq</groupId>
                <artifactId>jmq-client-spring</artifactId>
                <version>${jmq.version}</version>
            </dependency>

            <!--traceholder-->
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>traceholder</artifactId>
                <version>${jmq.jd.traceholder.version}</version>
            </dependency>

            <!--jackson-->
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-jaxrs</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-xc</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-core-asl</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.codehaus.jackson</groupId>
                <artifactId>jackson-mapper-asl</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!--应用配置文件加密-->
            <dependency>
                <groupId>com.jd.security.configsec</groupId>
                <artifactId>spring-configsec-sdk</artifactId>
                <version>${security.configsec.version}</version>
            </dependency>
            <!--redis start-->
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${redis.clients.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-redis</artifactId>
                <version>${spring.data.redis.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--redis end-->

            <!--青龙三合一服务-->
            <dependency>
                <groupId>com.jd.etms.receive</groupId>
                <artifactId>etms-receive-api</artifactId>
                <version>${etms.receive.version}</version>
            </dependency>

            <!--防重工具（箱号防重，订单号防重）-->
            <dependency>
                <groupId>com.jdl.cp.core.ts</groupId>
                <artifactId>cp-core-function</artifactId>
                <version>${cp.core.function.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--数据层模型-->
            <dependency>
                <groupId>com.jdl.cp.core.ts</groupId>
                <artifactId>cp-core-order</artifactId>
                <version>${cp.core.order.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--产品统一校验接口-->
            <dependency>
                <groupId>com.jdl.prodcentre</groupId>
                <artifactId>jdl-prodcentre-api</artifactId>
                <version>${jdl.prodcentre.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--TC订单下单校验接口-->
            <dependency>
                <groupId>com.jd.sc.ofc</groupId>
                <artifactId>ofc-api</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <!-- drools依赖 start  -->
            <dependency>
                <groupId>org.drools</groupId>
                <artifactId>drools-core</artifactId>
                <version>${drools.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.kie</groupId>
                        <artifactId>kie-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.kie</groupId>
                        <artifactId>kie-internal</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.drools</groupId>
                <artifactId>drools-compiler</artifactId>
                <version>${drools.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.drools</groupId>
                        <artifactId>drools-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.kie</groupId>
                        <artifactId>kie-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.kie</groupId>
                        <artifactId>kie-internal</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.thoughtworks.xstream</groupId>
                        <artifactId>xstream</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.kie</groupId>
                <artifactId>kie-api</artifactId>
                <version>${drools.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.kie</groupId>
                <artifactId>kie-internal</artifactId>
                <version>${drools.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.kie</groupId>
                        <artifactId>kie-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--http访问调用外部服务-->
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpcore</artifactId>
                <version>${httpcore.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>${httpmime.verison}</version>
            </dependency>
            <!-- 调用op服务 -->
            <dependency>
                <groupId>com.jdl.cp.op</groupId>
                <artifactId>cp-op-client</artifactId>
                <version>${cp.db.op.client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>lombok</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--aces start-->
            <dependency>
                <groupId>com.jd.security</groupId>
                <artifactId>aces-springclient</artifactId>
                <version>${aces.springclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd.security</groupId>
                        <artifactId>tdeclient</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.security</groupId>
                        <artifactId>tdecommon</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.jd.security</groupId>
                <artifactId>tdecommon</artifactId>
                <version>${tdecommon.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.security</groupId>
                <artifactId>tdeclient</artifactId>
                <version>${tdeclient.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd.jmq</groupId>
                        <artifactId>jmq-client-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 商家信息查询接口 -->
            <dependency>
                <groupId>com.jd.ldop.basic</groupId>
                <artifactId>ldop-basic-api</artifactId>
                <version>${ldop.basic.api}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.framework</groupId>
                        <artifactId>dong-boot-doc</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 事业部编码校验 -->
            <dependency>
                <groupId>com.jd.eclp</groupId>
                <artifactId>eclp-master-api</artifactId>
                <version>${eclp.master.api}</version>
            </dependency>

            <!-- 返佣平台查询 -->
            <dependency>
                <groupId>com.jd.wl</groupId>
                <artifactId>express-dispatcher-api</artifactId>
                <version>${express.dispatcher.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.etms.waybill</groupId>
                <artifactId>etms-waybill-agent</artifactId>
                <version>${etms.waybill.agent.version}</version>
            </dependency>


            <dependency>
                <groupId>com.jdl.cp</groupId>
                <artifactId>cp-oms-pk-client</artifactId>
                <version>${cp.oms.pk.client.version}</version>
            </dependency>

            <!--三方快递虚拟站点查询-->
            <dependency>
                <groupId>com.jd.ldop.alpha</groupId>
                <artifactId>ldop-alpha-lp-api</artifactId>
                <version>${lp.api.version}</version>
            </dependency>

            <!--地址解析京标接口-->
            <dependency>
                <groupId>com.jd.addresstranslation</groupId>
                <artifactId>addressTranslation-api</artifactId>
                <version>${addressTranslation.version}</version>
            </dependency>

            <!-- 下发 -->
            <dependency>
                <groupId>cn.jdl.baichuan</groupId>
                <artifactId>router-disp-contract</artifactId>
                <version>${router.disp.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.jdl.batrix</groupId>
                        <artifactId>jdl-batrix-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.jdl.batrix</groupId>
                        <artifactId>jdl-batrix-sdk</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.jdl.batrix</groupId>
                        <artifactId>jdl-batrix-spec</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.jdl.batrix</groupId>
                        <artifactId>jdl-batrix-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.jdl.oms</groupId>
                        <artifactId>jdl-oms-spec</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!--数科敏感词校验api-->
            <dependency>
                <groupId>com.jdd.csia</groupId>
                <artifactId>csia-gateway-api</artifactId>
                <version>${csia.gateway.version}</version>
            </dependency>
            <!-- tms询价服务 -->
            <dependency>
                <groupId>com.jd.tms.rfq</groupId>
                <artifactId>tms-rfq-client</artifactId>
                <version>${tms.rfq.client.version}</version>
            </dependency>
            <!--aces end-->
            <!---大区信息获取-->
            <dependency>
                <groupId>com.jd.ql.basic</groupId>
                <artifactId>ql-basic-client</artifactId>
                <version>2.0.6-online-SNAPSHOT</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-collections</groupId>
                        <artifactId>commons-collections</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--调用订单服务-->
            <dependency>
                <groupId>com.jdl.cp.osc</groupId>
                <artifactId>cp-osc-client</artifactId>
                <version>1.0.3-RELEASE</version>
            </dependency>

            <!-- Batrix基础服务 -->
            <dependency>
                <groupId>com.jdl.bsc</groupId>
                <artifactId>bsc-server-api</artifactId>
                <version>0.0.5-SNAPSHOT</version>
            </dependency>
            <!--fastjson-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <!--数据库连接配置依赖 start-->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.connector.java.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-dbcp2</artifactId>
                <version>${commons.dbcp2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons.pool2.version}</version>
            </dependency>
            <!--数据库连接配置依赖 end-->
            <!-- 本地缓存 -->
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.cache.version}</version>
            </dependency>
            <!--青龙三合一接单接口-->
            <dependency>
                <groupId>com.jd.lbcc.delivery</groupId>
                <artifactId>lbcc-delivery-api</artifactId>
                <version>${lbcc.delivery.api.version}</version>
            </dependency>
            <!--零售订单基础信息查询-->
            <dependency>
                <groupId>com.jd.ioms</groupId>
                <artifactId>ioms-export-cbd-sdk</artifactId>
                <version>${ioms.export.cbd.sdk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--厂直订单信息-->
            <dependency>
                <groupId>com.jd.dropship</groupId>
                <artifactId>dropship-center-api</artifactId>
                <version>${com.jd.dropship.version}</version>
            </dependency>
            <!--零售订单查询接口字典包-->
            <dependency>
                <groupId>com.jd.ioms</groupId>
                <artifactId>jdorders-export-dict</artifactId>
                <version>${jdorders.export.dict.version}</version>
            </dependency>
            <!--零售订单查询返回解析-->
            <dependency>
                <groupId>com.jd.purchase.sdk.domain</groupId>
                <artifactId>purchase-sdk-domain</artifactId>
                <version>${purchase.sdk.domain.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 解析工具 -->
            <dependency>
                <groupId>com.jd.purchase.common</groupId>
                <artifactId>purchase-serializer-utils</artifactId>
                <version>${purchase.serializer.utils}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>


            <!--&lt;!&ndash;零售订单基础信息查询&ndash;&gt;
            <dependency>
                <groupId>com.jd.ioms</groupId>
                <artifactId>ioms-export-cbd-sdk</artifactId>
                <version>${ioms.export.cbd.sdk.version}</version>
            </dependency>
            &lt;!&ndash;零售订单查询接口字典包&ndash;&gt;
            <dependency>
                <groupId>com.jd.ioms</groupId>
                <artifactId>jdorders-export-dict</artifactId>
                <version>${jdorders.export.dict.version}</version>
            </dependency>
            &lt;!&ndash;零售订单查询返回XML解析&ndash;&gt;
            <dependency>
                <groupId>com.jd.purchase.sdk.domain</groupId>
                <artifactId>purchase-sdk-domain</artifactId>
                <version>${purchase.sdk.domain.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            &lt;!&ndash;零售订单查询返回XML解析类依赖引入&ndash;&gt;
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>${xstream.version}</version>
            </dependency>-->


            <!--订单台账详情查询-->
            <dependency>
                <groupId>com.jd.orderbank</groupId>
                <artifactId>order-bank-export</artifactId>
                <version>${orderbank.export.version}</version>
            </dependency>
            <!--任务调度框架-->
            <dependency>
                <groupId>com.jd.paq</groupId>
                <artifactId>jdl-paq-all</artifactId>
                <version>${jdl-paq-all.version}</version>
            </dependency>
            <!-- 优惠券 -->
            <dependency>
                <groupId>com.jd.lcmc</groupId>
                <artifactId>lcmc-out-api</artifactId>
                <version>${lcmc.out.api.version}</version>
            </dependency>
            <!--产品中心-->
            <dependency>
                <groupId>cn.jdl.pms</groupId>
                <artifactId>jdl-pms-client</artifactId>
                <version>${jdl-pms-client.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.jdl.batrix</groupId>
                        <artifactId>jdl-batrix-sdk</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.jdl.batrix</groupId>
                        <artifactId>jdl-batrix-spec</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--预分拣逻辑计算统一接口-->
            <dependency>
                <groupId>com.jd.ldop.center</groupId>
                <artifactId>ldop-center-api</artifactId>
                <version>${ldop.center.api.version}</version>
            </dependency>
            <!-- 鸡毛信设备的预占和释放-->
            <dependency>
                <groupId>com.jd.tms.iot</groupId>
                <artifactId>tms-iot-client</artifactId>
                <version>${iot.clinet.version}</version>
            </dependency>
            <!--计费询价-->
            <dependency>
                <groupId>com.jd.lbs.product.price</groupId>
                <artifactId>lbs-product-price-api</artifactId>
                <version>${lbs.product.price.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- Ehcache -->
            <dependency>
                <groupId>net.sf.ehcache</groupId>
                <artifactId>ehcache-core</artifactId>
                <version>${ehcache.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--pos台账-->
            <dependency>
                <groupId>com.jd.fms.posLink</groupId>
                <artifactId>posLink-posPay-accsyn</artifactId>
                <version>${posLink.posPay.accsyn.version}</version>
            </dependency>
            <!--pos直连-->
            <dependency>
                <groupId>com.jd.fms</groupId>
                <artifactId>posLink-yunOrderServer</artifactId>
                <version>${posLink.yunOrderServer.version}</version>
            </dependency>
            <!--B商家台账-->
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>supplierrecon-common</artifactId>
                <version>${supplierrecon-common.version}</version>
            </dependency>
            <!--pos直连青龙运单写应收-->
            <dependency>
                <groupId>com.jd.fms</groupId>
                <artifactId>posaccsyn-jsf</artifactId>
                <version>${posaccsyn-jsf.version}</version>
            </dependency>
            <!-- 地址机构服务查询 -->
            <dependency>
                <groupId>com.jd.lf</groupId>
                <artifactId>org-service-jsf</artifactId>
                <version>${address.org.version}</version>
            </dependency>
            <!--微信先享单取消 -->
            <dependency>
                <groupId>com.jd.ql.terminal.api</groupId>
                <artifactId>terminal-agent</artifactId>
                <version>${terminal.agent.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--DUCC 配置-->
            <dependency>
                <groupId>com.jd.std.ucc</groupId>
                <artifactId>ucc-client</artifactId>
                <version>${ducc.client.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--外单台账-->
            <dependency>
                <groupId>com.jd.ots.orderbank</groupId>
                <artifactId>ots-orderbank-export</artifactId>
                <version>${ots-orderbank-export.version}</version>
            </dependency>
            <dependency>
                <artifactId>wbms-wcs-client</artifactId>
                <groupId>com.jd.cp.wbms.wcs</groupId>
                <version>${wbms-wcs-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.logistics</groupId>
                <artifactId>account-facade</artifactId>
                <version>${logistics-account-facade.version}</version>
            </dependency>
            <!-- jsf -->
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>jsf</artifactId>
                <version>${jsf.version}</version>
            </dependency>
            <!--es client-->
            <dependency>
                <groupId>org.elasticsearch.client</groupId>
                <artifactId>elasticsearch-rest-high-level-client</artifactId>
                <version>${org.elasticsearch.client.version}</version>
            </dependency>
            <!--C后台-->
            <dependency>
                <groupId>com.jd.c.m</groupId>
                <artifactId>c-m-api</artifactId>
                <version>${c.m.api.version}</version>
            </dependency>
            <!-- 积分 -->
            <dependency>
                <groupId>com.jd.jdwl</groupId>
                <artifactId>jdl-integrate-middleground-api</artifactId>
                <version>${integrate.middleground.api.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jsf</artifactId>
                        <groupId>com.jd</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.ump</groupId>
                        <artifactId>profiler</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.ump</groupId>
                        <artifactId>jannotation</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.jmiss</groupId>
                        <artifactId>jim-cli-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-aop</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- E卡 -->
            <dependency>
                <groupId>com.jd.coo.cms</groupId>
                <artifactId>cms-api-open</artifactId>
                <version>${cms.api.open.version}</version>
            </dependency>
            <!--外单台账-外单退款-->
            <dependency>
                <groupId>com.jd.cornucopia-server</groupId>
                <artifactId>cornucopia-server</artifactId>
                <version>${cornucopia.server.version}</version>
            </dependency>
            <!--退款网关接口-->
            <dependency>
                <groupId>com.jd.fm.paygateway</groupId>
                <artifactId>paygateway-fm-api</artifactId>
                <version>${paygateway.fm.version}</version>
            </dependency>
            <!--全程跟踪-->
            <dependency>
                <groupId>cn.jdl.ordertrack</groupId>
                <artifactId>ordertrack.soa.client</artifactId>
                <version>${ordertrack.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.jdl.oms</groupId>
                        <artifactId>jdl-oms-spec</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.jdl.batrix</groupId>
                        <artifactId>jdl-batrix-spec</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.jdl.batrix</groupId>
                        <artifactId>jdl-batrix-sdk</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 现结余额查询接口 -->
            <dependency>
                <groupId>com.jd.bms</groupId>
                <artifactId>bms-settlement-api</artifactId>
                <version>${bms.settlement.api.version}</version>
            </dependency>

            <!-- 订单控单 -->
            <dependency>
                <groupId>com.jdl.eclp</groupId>
                <artifactId>business-open-api</artifactId>
                <version>${business.open.api.version}</version>
            </dependency>

            <!-- 高峰期附加费查询-->
<!--            <dependency>-->
<!--                <groupId>com.jd.open.sp</groupId>-->
<!--                <artifactId>open-lpc-api</artifactId>-->
<!--                <version>${open.lpc.api.version}</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>cn.jdl.pms</groupId>
                <artifactId>jdl-pms-basic-api</artifactId>
                <version>${jdl.pms.basic.api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--sop黑名单校验、校验pin和商家编码是否匹配-->
            <dependency>
                <groupId>com.jd.ldop.oms</groupId>
                <artifactId>ldop-oms-api</artifactId>
                <version>${ldop.oms.api.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>lombok</artifactId>
                        <groupId>org.projectlombok</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.lbcc.delivery</groupId>
                        <artifactId>lbcc-delivery-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.ldop.delivery</groupId>
                        <artifactId>eclp-delivery-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
                <dependency>
                    <groupId>com.thoughtworks.xstream</groupId>
                    <artifactId>xstream</artifactId>
                    <version>${xstream.version}</version>
                </dependency>
            <!-- 快递短信验证码服务 -->
            <dependency>
                <groupId>com.jdl.express</groupId>
                <artifactId>express-resource-api</artifactId>
                <version>${express.resource.api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-collections</groupId>
                        <artifactId>commons-collections</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-slf4j-impl</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.laf.config</groupId>
                        <artifactId>laf-config-client-jd-springboot-starter</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.common</groupId>
                        <artifactId>jd-common-util</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--地址围栏-->
            <dependency>
                <groupId>com.jd.lbs.geofencing</groupId>
                <artifactId>geofencing-api</artifactId>
                <version>${address.fence.version}</version>
            </dependency>

            <!--京东科技 内容安全校验服务-->
            <dependency>
                <groupId>com.jd.ai.comment.moderation</groupId>
                <artifactId>comment-moderation</artifactId>
                <version>${ai.moderation.version}</version>
            </dependency>

            <!--脱敏SDK-判断手机字段是否是京东脱敏后的字符串-->
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>Desen2</artifactId>
                <version>${Desen2.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>spring-boot-starter-logging</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-beans</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-log4j12</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd</groupId>
                        <artifactId>jsf</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-text</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd.etms.vrs</groupId>
                <artifactId>vrs-platform-outer-api</artifactId>
                <version>${pickup.range.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>lombok</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>cn.jdl.oms</groupId>
                <artifactId>jdl-oms-relation-client-service</artifactId>
                <version>${order.relation.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.jdl.oms</groupId>
                <artifactId>jdl-oms-relation-client-model</artifactId>
                <version>${order.relation.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>lombok</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.jdl.batrix</groupId>
                        <artifactId>jdl-batrix-sdk</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.jdl.batrix</groupId>
                        <artifactId>jdl-batrix-spec</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hibernate</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.validation</groupId>
                        <artifactId>validation-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.jdl.oms</groupId>
                        <artifactId>jdl-oms-spec</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>cn.jdl.oms</groupId>
                        <artifactId>osc-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!--batrix 归因平台-->
            <dependency>
                <artifactId>tracer-common</artifactId>
                <groupId>cn.jdl.batrix</groupId>
                <version>${jdl.batrix.tracer.version}</version>
            </dependency>
            <dependency>
                <artifactId>tracer-clientlog4j2</artifactId>
                <groupId>cn.jdl.batrix</groupId>
                <version>${jdl.batrix.tracer.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${google.protobuf.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.pfinder</groupId>
                <artifactId>pfinder-profiler-sdk</artifactId>
                <version>${pfinder.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.lbs</groupId>
                <artifactId>lbs-master-outer-api</artifactId>
                <version>${lbs-master-outer-api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <!-- 配置中心-配置方案-配置值读取服务 -->
            <dependency>
                <groupId>com.jd.open.sp</groupId>
                <artifactId>open-lpc-api</artifactId>
                <version>${open.lpc.api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd</groupId>
                        <artifactId>jsf</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 大件拒单下单校验/查询服务接口 -->
            <dependency>
                <groupId>com.jd.las</groupId>
                <artifactId>las-waybill-adapter-refuse-api</artifactId>
                <version>${las.waybill.adapter.refuse.version}</version>
            </dependency>

            <!-- 外单取件获取站点 -->
            <dependency>
                <groupId>com.jd.tos.dispatch</groupId>
                <artifactId>tos-dispatch-api</artifactId>
                <version>${tos.dispatch.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.insure.claim</groupId>
                <artifactId>ficlaims-center-export</artifactId>
                <!-- 测试环境版本1.0.1-SNAPSHOT -->
                <!-- 预发环境版本1.0.0-SNAPSHOT -->
                <!-- 生产找研发确认 -->
                <version>${jd.insurance.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.jdl.oms</groupId>
                <artifactId>osc-client</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.jd.ccjf</groupId>
                <artifactId>ccjf-data-api</artifactId>
                <version>${ccjf-data-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context-support</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-webmvc</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd.etms</groupId>
                <artifactId>waybill-refund-agent</artifactId>
                <version>${waybill-refund-agent.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context-support</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-webmvc</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd.jr.pay</groupId>
                <artifactId>agreementPayGateway_export</artifactId>
                <version>${agreement.pay.gateway.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.jboss.resteasy</groupId>
                        <artifactId>jaxrs-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.jd.ql.dispatch</groupId>
                <artifactId>dispatch-client</artifactId>
                <version>${dispatch.client.version}</version>
            </dependency>

            <!-- TMS运输基础资料 -->
            <dependency>
                <groupId>com.jd.tms.basic</groupId>
                <artifactId>tms-basic-client</artifactId>
                <version>${tms.basic.client}</version>
            </dependency>


            <!-- 港澳单量限制 -->
            <dependency>
                <groupId>com.jdl.oms</groupId>
                <artifactId>dac-client</artifactId>
                <version>${dac-client.version}</version>
            </dependency>
            <!--EBS 收入集成-->
            <dependency>
                <groupId>com.jd.bms.payable.ebs</groupId>
                <artifactId>bms-payable-ebs-rpc-api</artifactId>
                <version>${bms-payable-ebs-rpc-api.version}</version>
            </dependency>

            <!-- eclp-co -->
            <dependency>
                <groupId>com.jd.eclp</groupId>
                <artifactId>eclp-co-api</artifactId>
                <version>${eclp.co.api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd.security</groupId>
                        <artifactId>tdeclient</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>sc-request-router-api</artifactId>
                        <groupId>com.jdl.sc.request.router</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- OMS api-->
            <dependency>
                <groupId>cn.jdl.oms</groupId>
                <artifactId>jdl-oms-express-client-service</artifactId>
                <version>${jdl.oms.api.version}</version>
            </dependency>

            <!-- eclp 异常中心 -->
            <dependency>
                <groupId>com.jd.eclp.exception</groupId>
                <artifactId>eclp-exception-api</artifactId>
                <version>${eclp.exception.version}</version>
            </dependency>

            <!-- 用户权益中心 -->
            <dependency>
                <groupId>com.jd.omni.user.rights</groupId>
                <artifactId>omni-user-rights-cos-api</artifactId>
                <version>${jd.user.right.api.version}</version>
            </dependency>

            <!-- batrix配置监听 -->
            <dependency>
                <groupId>com.jdl.bsc</groupId>
                <artifactId>bsc-server-property-listener</artifactId>
                <version>${bsc.server.property.listener.version}</version>
            </dependency>

            <dependency>
                <groupId>com.jd.insure.claim</groupId>
                <artifactId>inter-claim-export</artifactId>
                <version>${inter.claim.export.verison}</version>
            </dependency>

            <!--仓位预占-->
            <dependency>
                <groupId>com.jd.tms.csc</groupId>
                <artifactId>tms-csc-client</artifactId>
                <version>${tms.csc.client.version}</version>
            </dependency>

            <!-- 京东零售:跨境业务-换汇接口 -->
            <dependency>
                <groupId>com.jdw.sc.price.exchange.rate.service</groupId>
                <artifactId>sc-price-exchange-rate-service-api</artifactId>
                <version>${sc.price.exchange.rate.version}</version>
            </dependency>

            <!-- POS扫码付 -->
            <dependency>
                <artifactId>pospay-mobile-gateway-export</artifactId>
                <groupId>com.jd.jr</groupId>
                <version>${pospay.mobile.gateway.export.version}</version>
            </dependency>

            <!-- 非商城订单应收 -->
            <dependency>
                <artifactId>posExtOrderAccounts</artifactId>
                <groupId>com.jd.fms</groupId>
                <version>${posExtOrderAccounts.version}</version>
            </dependency>
            <!--产品映射关系-->
            <dependency>
                <groupId>com.jdl.product</groupId>
                <artifactId>jdl-product-query-api</artifactId>
                <version>${product.mapping.version}</version>
            </dependency>
            <!-- 自动核销 -->
            <dependency>
                <groupId>com.wangyin.pos</groupId>
                <artifactId>off_payment_export</artifactId>
                <version>${off.payment.expor.version}</version>
            </dependency>

            <!--运单系统单号校验工具-->
            <dependency>
                <groupId>com.jd.etms.waybill</groupId>
                <artifactId>etms-waybillcode-rule</artifactId>
                <version>${etms.waybillcode.rule.version}</version>
            </dependency>

            <!--站点匹配服务-->
            <dependency>
                <groupId>com.jd.etms.preseparate</groupId>
                <artifactId>preseparate-saf-client</artifactId>
                <version>${preseparate-saf.version}</version>
            </dependency>
            <!--R2流量录制回放-->
            <dependency>
                <groupId>com.jd.r2</groupId>
                <artifactId>r2-sdk</artifactId>
                <version>${r2.sdk.version}</version>
            </dependency>
            <!--零售敏感词-->
            <dependency>
                <groupId>com.jd.risk.sensitive_word</groupId>
                <artifactId>sensitive_word_api</artifactId>
                <version>${sensitive.word.version}</version>
            </dependency>

            <!--财务收发管家 预收款校验&预占-->
            <dependency>
                <groupId>com.jd.bms</groupId>
                <artifactId>bms-account-rpc-outer-api</artifactId>
                <version>${bms.account.outer}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>lombok</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 交易业务监控 -->
            <dependency>
                <groupId>com.jdl.cp.core.ts</groupId>
                <artifactId>cp-core-log</artifactId>
                <version>${cp.core.log.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.projectlombok</groupId>
                        <artifactId>lombok</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.jmq</groupId>
                        <artifactId>jmq-client-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.jmq</groupId>
                        <artifactId>jmq-client-ump</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.jd.ump</groupId>
                        <artifactId>profiler</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-context</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 科技-支付宝芝麻代扣支付单 -->
            <dependency>
                <groupId>com.jd.payment</groupId>
                <artifactId>gateway-core-export</artifactId>
                <version>${jdpay.gateway.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.jd.ump</groupId>
                        <artifactId>profiler</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 服务加服务单 -->
            <dependency>
                <groupId>com.jd</groupId>
                <artifactId>ser-sku-open-api</artifactId>
                <version>${serviceplus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jd.afs.biz.center.api</groupId>
                <artifactId>afs_biz_center_api</artifactId>
                <version>${afs.biz.center.version}</version>
            </dependency>

            <!-- 驾车距离 -->
            <dependency>
                <groupId>com.jd.lbs.jdlbsapi</groupId>
                <artifactId>jdlbsapi-api</artifactId>
                <version>${jdlbsapi.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId> <!-- IMPORTANT - LOMBOK BEFORE MAPSTRUCT -->
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>jd-central</id>
            <name>libs-releases</name>
            <url>http://artifactory.jd.com/libs-releases-local</url>
        </repository>
        <snapshotRepository>
            <id>jd-snapshots</id>
            <name>libs-snapshots</name>
            <url>http://artifactory.jd.com/libs-snapshots-local</url>
        </snapshotRepository>
    </distributionManagement>
</project>
