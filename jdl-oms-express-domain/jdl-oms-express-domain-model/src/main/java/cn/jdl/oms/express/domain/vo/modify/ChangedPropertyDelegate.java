package cn.jdl.oms.express.domain.vo.modify;

import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName ChangedPropertyDelegate
 * @Description
 * <AUTHOR>
 * @Date 2021/3/30 10:27 下午
 * @ModifyDate 2021/3/30 10:27 下午
 * @Version 1.0
 */
@Data
public class ChangedPropertyDelegate {
    /**
     * 更改的属性集合
     */
    private List<ChangedProperty> changedProperties;

    private Map<String, ChangedProperty> changedPropertyMap;

    private String modifyMark;

    public void setChangedProperties(List<ChangedProperty> changedProperties) {
        this.changedProperties = changedProperties;
        initChangedPropertyMap();
    }

    public void initChangedPropertyMap() {
        if (CollectionUtils.isNotEmpty(changedProperties)) {
            changedPropertyMap = new HashMap<>();
            for (ChangedProperty changedProperty : changedProperties) {
                changedPropertyMap.put(changedProperty.getItemCode(), changedProperty);
            }
        }
    }

    public void addChangedProperties(ChangedProperty changedProperty) {
        if (changedPropertyMap.containsKey(changedProperty.getItemCode())) {
            return;
        }
        changedProperties.add(changedProperty);
        initChangedPropertyMap();
    }

    /**
     * 发件人地址是否发生变更
     *
     * @return
     */
    public boolean consignorAddressHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_PROVINCE_NO_GIS.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_CITY_NO_GIS.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_COUNTY_NO_GIS.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_ADDRESS.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_TOWN_NO_GIS.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_CHINA_POST_ADDRESS_CODE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_ADDRESS_REGION_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_ADDRESS_REGION_NAME.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 发件人联系方式（姓名、电话、手机）是否发生变更
     */
    public boolean consignorContactInformationHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        return changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_NAME.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_PHONE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_MOBILE.getCode()) != null;
    }

    /**
     * 发件人是否发生变更，忽略联系方式（姓名、电话、手机）
     */
    public boolean consignorHaveChangeIgnoreContactInformation() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_ID_TYPE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_ID_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_COMPANY.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_NATION_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_NATION.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_ZIP_CODE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.DELIVERY_PLACE_CODE.getCode()) != null
        ){
            return true;
        }
        return consignorAddressHaveChange();
    }

    /**
     * 收件人地址是否发生变更
     *
     * @return
     */
    public boolean consigneeAddressHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_PROVINCE_NO_GIS.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_CITY_NO_GIS.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_COUNTY_NO_GIS.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_ADDRESS.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_TOWN_NO_GIS.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_CHINA_POST_ADDRESS_CODE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_ADDRESS_REGION_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_ADDRESS_REGION_NAME.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 收件人地址是否发生跨城变更（省、市）
     *
     * @return
     */
    public boolean consigneeAddressHaveChangeDifferentCity() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_PROVINCE_NO_GIS.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_CITY_NO_GIS.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 收件人是否发生变更
     *
     * @return
     */
    public boolean consigneeHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_NAME.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_PHONE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_MOBILE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_ID_TYPE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_ID_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_COMPANY.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_NATION_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_NATION.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_ZIP_CODE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.DELIVERY_PLACE_CODE.getCode()) != null
                ){
            return true;
        }
        return consigneeAddressHaveChange();
    }

    /**
     * 收件人联系方式（姓名、电话、手机）是否发生变更
     */
    public boolean consigneeContactInformationHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        return changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_NAME.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_PHONE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_MOBILE.getCode()) != null;
    }

    /**
     * 收件人是否发生变更，忽略联系方式（姓名、电话、手机）
     */
    public boolean consigneeHaveChangeIgnoreContactInformation() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_ID_TYPE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_ID_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_COMPANY.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_NATION_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_NATION.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_ZIP_CODE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.DELIVERY_PLACE_CODE.getCode()) != null
        ){
            return true;
        }
        return consigneeAddressHaveChange();
    }

    public boolean cargoHaveChange(){
        if (changedPropertyMap == null){
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.CARGO.getCode()) != null){
            return true;
        }
        return false;
    }

    /** 商品信息是否发生变更 */
    public boolean goodsHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        return null != changedPropertyMap.get(ModifyItemConfigEnum.GOODS.getCode());
    }

    /**
     * 产品信息是否发生变更
     *
     * @return
     */
    public boolean productHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.PRODUCT_NO.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 起始站点信息是否发生变更
     */
    public boolean startStationHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.START_STATION_NO.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 目的站点信息是否发生变更
     */
    public boolean endStationHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.END_STATION_NO.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 起始场站信息是否发生变更
     */
    public boolean startCenterHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.START_CENTER_NO.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 目的场站信息是否发生变更
     */
    public boolean endCenterHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.END_CENTER_NO.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 结算方式是否发生变更
     */
    public boolean settlementTypeHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.SETTLEMENT_TYPE.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 折后金额是否发生变更
     */
    public boolean discountAmountHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.DISCOUNT_AMOUNT.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 揽收方式是否发生变更
     */
    public boolean pickupTypeHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.PICKUP_TYPE.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 派送方式是否发生变更
     */
    public boolean deliveryTypeHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.DELIVERY_TYPE.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 无接触收货方式是否发生变更
     */
    public boolean contactlessTypeHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.CONTACTLESS_TYPE.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 指定地点是否发生变更
     */
    public boolean assignedAddressHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.ASSIGNED_ADDRESS.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 支付方式是否发生变更
     * @return
     */
    public boolean paymentHaveChange(){
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.PAYMENT.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 支付环节是否发生变更
     * @return
     */
    public boolean paymentStageHaveChange(){
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.PAYMENT_STAGE.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 判断属性是否发生变更
     * @param configEnum
     * @return
     */
    public boolean propertyHasChange(ModifyItemConfigEnum configEnum) {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(configEnum.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 退货信息--收货人详细地址 是否发生变更
     */
    public boolean returnInfoConsigneeAddressHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.RETURN_INTO_CONSIGNEE_ADDRESS.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 预计送达时间 是否发生变更
     */
    public boolean planDeliveryTimeHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.PLAN_DELIVERY_TIME.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 取件时间是否发生变更
     */
    public boolean pickupTimeHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.EXPECT_PICKUP_START_TIME.getCode()) != null) {
            return true;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.EXPECT_PICKUP_END_TIME.getCode()) != null) {
            return true;
        }
        return false;
    }
    /**
     * 发货信息是否发生变更
     */
    public boolean consignorHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_NAME.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_PHONE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_MOBILE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_ID_TYPE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_ID_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_COMPANY.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_NATION_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_NATION.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_ZIP_CODE.getCode()) != null
        ){
            return true;
        }
        return consignorAddressHaveChange();
    }

    /**
     * 交易费用信息是否发生变更
     */
    public boolean financeHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.SETTLEMENT_TYPE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.TAX_SETTLEMENT_TYPE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.PAYMENT.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.PAYMENT_STAGE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.PREEMPT_TYPE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.PAYMENT_ACCOUNT_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.SETTLEMENT_ACCOUNT_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.ESTIMATE_AMOUNT.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.REDEEM_POINTS_QUANTITY.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.REDEEM_POINTS_AMOUNT.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.POINTS_AMOUNT.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.POINTS_QUANTITY.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.ESTIMATED_TAX.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.ACTUAL_TAX.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.ATTACH_FEES.getCode()) != null
        ){
            return true;
        }
        return false;
    }

    /**
     * 交易费用信息-揽收后不允许修改的
     */
    public boolean financeHaveChangeNotAllowAfterPickup() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.SETTLEMENT_TYPE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.TAX_SETTLEMENT_TYPE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.PAYMENT.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.PAYMENT_STAGE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.PREEMPT_TYPE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.PAYMENT_ACCOUNT_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.SETTLEMENT_ACCOUNT_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.ESTIMATE_AMOUNT.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.REDEEM_POINTS_QUANTITY.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.REDEEM_POINTS_AMOUNT.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.POINTS_AMOUNT.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.POINTS_QUANTITY.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.ESTIMATED_TAX.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.ACTUAL_TAX.getCode()) != null
        ){
            return true;
        }
        return false;
    }

    /**
     * 判断有无修改：交易费用信息，忽略财务明细
     */
    public boolean financeHaveChangeIgnoreFinanceDetail() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.SETTLEMENT_TYPE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.TAX_SETTLEMENT_TYPE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.PAYMENT.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.PAYMENT_STAGE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.PREEMPT_TYPE.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.PAYMENT_ACCOUNT_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.SETTLEMENT_ACCOUNT_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.ESTIMATE_AMOUNT.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.REDEEM_POINTS_QUANTITY.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.REDEEM_POINTS_AMOUNT.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.POINTS_AMOUNT.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.POINTS_QUANTITY.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.ESTIMATED_TAX.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.ACTUAL_TAX.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.ATTACH_FEES.getCode()) != null
        ){
            return true;
        }
        return false;
    }

    /**
     * 配送信息是否发生变更
     */
    public boolean shipmentHaveChange() {
        return shipmentHaveChangeIgnoreSomething(IGNORE_NOTHING_SET);
    }
    /**
     * 合同物流揽收前支持修改配送信息
     */
    public static final Set<ModifyItemConfigEnum> CONTRACT_BEFORE_PICKUP_IGNORE_SET = new HashSet<ModifyItemConfigEnum>() {{
        add(ModifyItemConfigEnum.EXPECT_DELIVERY_START_TIME);
        add(ModifyItemConfigEnum.EXPECT_DELIVERY_END_TIME);
        add(ModifyItemConfigEnum.SERVICE_REQUIREMENTS);
    }};

    /**
     * 配送信息是否发生变更
     */
    public boolean contractBeforePickupShipmentHaveChange() {
        return shipmentHaveChangeIgnoreSomething(CONTRACT_BEFORE_PICKUP_IGNORE_SET);
    }
    /**
     * 配送信息要对比的字段
     */
    public static final Set<ModifyItemConfigEnum> SHIPMENT_MODIFY_ITEM_CONFIG_ENUM_SET = new HashSet<ModifyItemConfigEnum>() {{
        add(ModifyItemConfigEnum.PLAN_DELIVERY_TIME);
        add(ModifyItemConfigEnum.EXPECT_PICKUP_START_TIME);
        add(ModifyItemConfigEnum.EXPECT_PICKUP_END_TIME);
        add(ModifyItemConfigEnum.EXPECT_DELIVERY_START_TIME);
        add(ModifyItemConfigEnum.EXPECT_DELIVERY_END_TIME);
        add(ModifyItemConfigEnum.PICKUP_TYPE);
        add(ModifyItemConfigEnum.DELIVERY_TYPE);
        add(ModifyItemConfigEnum.WARM_LAYER);
        add(ModifyItemConfigEnum.TRANSPORT_TYPE);
        add(ModifyItemConfigEnum.PLAN_RECEIVE_TIME);
        add(ModifyItemConfigEnum.CONTACTLESS_TYPE);
        add(ModifyItemConfigEnum.ASSIGNED_ADDRESS);
        add(ModifyItemConfigEnum.START_STATION_NO);
        add(ModifyItemConfigEnum.END_STATION_NO);
        add(ModifyItemConfigEnum.DELIVERY_TEMP_LAYER);
        add(ModifyItemConfigEnum.RECEIVING_PREFERENCE);
        add(ModifyItemConfigEnum.SERVICE_REQUIREMENTS);
    }};

    /**
     * 默认对比所有
     */
    public static final Set<ModifyItemConfigEnum> IGNORE_NOTHING_SET = new HashSet<>(0);

    /**
     * 配送信息是否发生变更，忽略某些字段
     * @param ignoreModifyItemConfigEnumSet 忽略字段
     */
    public boolean shipmentHaveChangeIgnoreSomething(Set<ModifyItemConfigEnum> ignoreModifyItemConfigEnumSet) {
        if (changedPropertyMap == null) {
            return false;
        }
        for (ModifyItemConfigEnum item : SHIPMENT_MODIFY_ITEM_CONFIG_ENUM_SET) {
            if (!ignoreModifyItemConfigEnumSet.contains(item) && changedPropertyMap.get(item.getCode()) != null) {
                return true;
            }
        }
        return false;
    }

    /**
     * 营销信息是否发生变更
     */
    public boolean promotionHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.TICKET_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.DISCOUNT_NO.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.ACTIVITY_NO.getCode()) != null
        ){
            return true;
        }
        return false;
    }

    /**
     * 开票类型是否发生变更
     */
    public boolean invoiceTypeChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.INVOICE_TYPE.getCode()) != null){
            return true;
        }
        return false;
    }

    /**
     * 特殊面单模板要求是否发生变更
     */
    public boolean specialTemplateChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.SPECIAL_TEMPLATE.getCode()) != null){
            return true;
        }
        return false;
    }

    /**
     * 协议信息是否发生变更
     */
    public boolean agreementInfosHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.AGREEMENTS.getCode()) != null){
            return true;
        }
        return false;
    }

    /**
     * 协议信息是否被删除
     */
    public boolean agreementInfosHaveDeleted() {
        if (changedPropertyMap == null) {
            return false;
        }
        ChangedProperty changedProperty = changedPropertyMap.get(ModifyItemConfigEnum.AGREEMENTS.getCode());
        return changedProperty != null && OperateTypeEnum.DELETE == changedProperty.getOperateType();
    }

    /**
     * 是否仅修改了协议信息
     * @return
     */
    public boolean onlyAgreementsChanged() {
        return CollectionUtils.size(changedProperties) == 1 && changedPropertyMap.containsKey(ModifyItemConfigEnum.AGREEMENTS.getCode());
    }

    private static final HashSet<String> FEE_CHECK_STATUS_ALLOW_PROPS = new HashSet<>();

    static {
        FEE_CHECK_STATUS_ALLOW_PROPS.add(ModifyItemConfigEnum.FEE_CHECK_STATUS.getCode());
        FEE_CHECK_STATUS_ALLOW_PROPS.add(ModifyItemConfigEnum.USER_CHECK_TYPE.getCode());
        FEE_CHECK_STATUS_ALLOW_PROPS.add(ModifyItemConfigEnum.MAIN_PRODUCT_CHANGED_SOURCE.getCode());
    }
    /**
     * 是否仅修改了费用审核状态
     * @return
     */
    public boolean onlyFeeCheckStatusChanged() {
        if (changedPropertyMap == null) {
            return false;
        }
        return changedPropertyMap.entrySet().stream()
                .allMatch(entry -> FEE_CHECK_STATUS_ALLOW_PROPS.contains(entry.getKey()));
    }

    /**
     * 是否仅修改了期望派货开始时间、期望派货结束时间
     */
    public boolean onlyExpectDispatchTimeChanged() {
        if (changedPropertyMap == null) {
            return false;
        }
        return changedPropertyMap.entrySet().stream()
                .allMatch(entry -> EXPECT_DISPATCH_TIME_PROPS.contains(entry.getKey()));
    }


    private static final HashSet<String> EXPECT_DISPATCH_TIME_PROPS = new HashSet<>();

    static {
        EXPECT_DISPATCH_TIME_PROPS.add(ModifyItemConfigEnum.EXPECT_DISPATCH_START_TIME.getCode());
        EXPECT_DISPATCH_TIME_PROPS.add(ModifyItemConfigEnum.EXPECT_DISPATCH_END_TIME.getCode());
    }

    /**
     * 协议信息是否发生变更
     */
    public boolean individualMsTypeHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        return changedPropertyMap.get(ModifyItemConfigEnum.INDIVIDUAL_MS_TYPE.getCode()) != null;
    }

    /**
     * 仅仅修改了收件人证件信息
     * @return
     */
    public boolean onlyConsigneeIDInfoHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        return !consigneeAddressHaveChange()
            && changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_NAME.getCode()) == null
            && changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_PHONE.getCode()) == null
            && changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_MOBILE.getCode()) == null
            && changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_COMPANY.getCode()) == null
            && changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_NATION_NO.getCode()) == null
            && changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_NATION.getCode()) == null
            && changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_ZIP_CODE.getCode()) == null
            && changedPropertyMap.get(ModifyItemConfigEnum.DELIVERY_PLACE_CODE.getCode()) == null
            && (changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_ID_TYPE.getCode()) != null
            || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_ID_NO.getCode()) != null);

    }

    /**
     * 收件人联系方式（手机）是否发生变更
     */
    public boolean consigneeMobileHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        return changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_MOBILE.getCode()) != null;
    }

    /**
     * 寄件人联系方式（手机）是否发生变更
     */
    public boolean consignorMobileHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        return changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNOR_MOBILE.getCode()) != null;
    }

    /**
     * 收寄件人联系方式（手机）是否发生变更
     */
    public boolean consignorOrConsigneeMobileHaveChange() {
        return consigneeMobileHaveChange() || consignorMobileHaveChange();
    }

    /**
     * 期望送达开始时间&期望送达结束时间 是否发生变更
     */
    public boolean expectDeliveryTimeHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.EXPECT_DELIVERY_START_TIME.getCode()) != null
                &&
                changedPropertyMap.get(ModifyItemConfigEnum.EXPECT_DELIVERY_END_TIME.getCode()) != null
        ) {
            return true;
        }
        return false;
    }

    /**
     * 是否仅修改了多地址审核状态 multiAddressVerifyStatus
     * @return
     */
    public boolean onlyMultiAddressVerifyStatusChanged() {
        return CollectionUtils.size(changedProperties) == 1
                && changedPropertyMap.containsKey(ModifyItemConfigEnum.MULTI_ADDRESS_VERIFY_STATUS.getCode());
    }

    /**
     * 内部修改允许修改的属性
     */
    private static final HashSet<String> INTERNAL_MODIFY_ALLOW_PROPS = new HashSet<>();

    static {
        INTERNAL_MODIFY_ALLOW_PROPS.add(ModifyItemConfigEnum.CONSIGNOR_NAME.getCode());
        INTERNAL_MODIFY_ALLOW_PROPS.add(ModifyItemConfigEnum.CONSIGNOR_PHONE.getCode());
        INTERNAL_MODIFY_ALLOW_PROPS.add(ModifyItemConfigEnum.CONSIGNOR_MOBILE.getCode());
        INTERNAL_MODIFY_ALLOW_PROPS.add(ModifyItemConfigEnum.CONSIGNOR_ADDRESS.getCode());
        INTERNAL_MODIFY_ALLOW_PROPS.add(ModifyItemConfigEnum.CONSIGNOR_ADDRESS_GIS.getCode());

        INTERNAL_MODIFY_ALLOW_PROPS.add(ModifyItemConfigEnum.CONSIGNEE_NAME.getCode());
        INTERNAL_MODIFY_ALLOW_PROPS.add(ModifyItemConfigEnum.CONSIGNEE_PHONE.getCode());
        INTERNAL_MODIFY_ALLOW_PROPS.add(ModifyItemConfigEnum.CONSIGNEE_MOBILE.getCode());
        INTERNAL_MODIFY_ALLOW_PROPS.add(ModifyItemConfigEnum.CONSIGNEE_ADDRESS.getCode());
        INTERNAL_MODIFY_ALLOW_PROPS.add(ModifyItemConfigEnum.CONSIGNEE_ADDRESS_GIS.getCode());
    }

    /**
     * 是否仅修改了收发货人信息（姓名、电话、手机、地址）
     *
     * @return
     */
    public boolean onlyConsignorAndConsigneeInfoChanged() {
        if (changedPropertyMap == null) {
            return false;
        }

        return changedPropertyMap.entrySet().stream()
                .allMatch(entry -> INTERNAL_MODIFY_ALLOW_PROPS.contains(entry.getKey()));

    }

    /**
     * 一二三四级地址是否发生变更
     *
     * @return
     */
    public boolean consigneeLevel4AddressHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_PROVINCE_NO_GIS.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_CITY_NO_GIS.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_COUNTY_NO_GIS.getCode()) != null
                || changedPropertyMap.get(ModifyItemConfigEnum.CONSIGNEE_TOWN_NO_GIS.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 是否主产品发生变更
     * @return
     */
      public boolean mainProductHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.MAIN_PRODUCT.getCode()) != null) {
            return true;
        }
        return false;
    }

    /**
     * 温层是否发生变更
     */
    public boolean warmLayerHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        return changedPropertyMap.get(ModifyItemConfigEnum.WARM_LAYER.getCode()) != null;
    }

    /**
     * 包裹最长边是否发生变更
     */
    public boolean packageMaxLenHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        return changedPropertyMap.get(ModifyItemConfigEnum.PACKAGE_MAX_LEN.getCode()) != null;
    }

    /**
     * 税金结算方式是否发生变更
     */
    public boolean taxSettlementTypeHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.TAX_SETTLEMENT_TYPE.getCode()) != null) {
            return true;
        }
        return false;
    }

    /** 暂存天数是否发生变更 */
    public boolean tempStorageDayHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        return null != changedPropertyMap.get(ModifyItemConfigEnum.TEMP_STORAGE_DAY.getCode());
    }

    /**
     * 解决方案编码 是否发生变更
     *
     * @return
     */
    public boolean businessSolutionNoHaveChange() {
        if (changedPropertyMap == null) {
            return false;
        }
        if (changedPropertyMap.get(ModifyItemConfigEnum.BUSINESS_SOLUTION_NO.getCode()) != null) {
            return true;
        }
        return false;
    }
}
