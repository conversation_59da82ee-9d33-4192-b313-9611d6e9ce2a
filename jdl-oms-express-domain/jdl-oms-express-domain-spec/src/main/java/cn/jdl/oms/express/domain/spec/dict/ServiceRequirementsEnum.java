package cn.jdl.oms.express.domain.spec.dict;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * 配送信息-物流服务要求枚举
 * <AUTHOR>
 * @date 2021/07/29
 */
public enum ServiceRequirementsEnum {
    /** 开箱验货 */
    UNPACKING_INSPECTION("unpackingInspection", "开箱验货"),
    /** 隐私通话 */
    HIDE_PRIVACY_TYPE("hidePrivacyType", "隐私通话"),
    /** 明细揽收 */
    PICKUP_DETAIL_TYPE("pickUpDetailType", "明细揽收"),
    /** 交接规范 */
    HANDOVER_SPECIFICATION("handoverSpecification", "交接规范"),
    /** 京准取 */
    PICKUP_IN_TIME("pickupInTime", "京准取"),
    /** 京尊取 */
    ON_TIME_SERVICE("onTimeService", "京尊取"),
    /** 揽收时采集明细 */
    PICKUP_COLLECT_GOODS_INFO("pickUpCollectGoodsInfo", "揽收时采集明细"),
    /** 包装要求 */
    PACKAGE_REQUIREMENT("packageRequirement", "包装要求"),
    /** B商家免赔 */
    MERCHANT_DEDUCTIBLE("merchantDeductible", "B商家免赔"),
    /** 1-全量接单 */
    RECEIVING_MODE("receivingMode", "全量接单"),
    /** 1-音需达服务 */
    SEND_ON_DEMAND("sendOnDemand", "音需达服务"),
    /** 验证码标识，1：揽收时校验 */
    VERIFICATION_CODE_MARK("verificationCodeMark", "验证码标识"),
    /** 特殊签收要求 */
    SPECIAL_SIGN_REQUIREMENTS("specialSignRequirements", "特殊签收要求"),
    /** 强制揽收 */
    MANDATORY_PICKUP("mandatoryPickup", "强制揽收"),
    /** 托寄物标识 */
    PRO_MOD_GOODS("ProModGoods", "托寄物标识"),
    /** 限制揽收终止 */
    NO_PICKUP_END("NoPickupEnd", "限制揽收终止"),
    /** 强制外呼 */
    FORCED_CALL("forcedCall", "强制外呼"),
    /** 上门带耗材 */
    PICK_UP_CONSUMABLES("pickUpConsumables", "上门带耗材"),
    /** 一日两揽 */
    TWO_COLLECTIONS_PER_DAY("twoCollectionsPerDay", "一日两揽"),
    /**
     * 是否自备纸箱：0：否；1是
     */
    SELF_PROVIDED_PACKAGE_BOX("selfProvidedPackageBox", "是否自备纸箱"),
    /** 是否允许半收 */
    HALF_RECEIVE("halfReceive", "是否允许半收"),
    /** 件型揽收 */
    PICKUP_REQUEST("pickupRequest", "件型揽收"),
    /** 特殊保障 */
    SPECIAL_GUARANTEE("specialGuarantee", "特殊保障"),
    REQUIRE_PICKUP_PRESORT_MODE("requirePickupPresortMode", "是否特殊揽收,0:否，1：是"),
    PERSONAL_INFO_SEC("personalInfoSec", "平台个人信息安全脱敏"),
    VERIFY_MULTI_ADDRESS_SHIPPING("verifyMultiAddressShipping", "多地址发货校验要求。0，不校验；1，校验"),
    /**
     * 外单预售服务-暂存位置
     * 1-暂存在出库前
     * 2-暂存在出库后交接前
     * 3-暂存在末端分拣中心
     * 4-暂存在末端站点
     * 5-暂存在快递柜
     */
    ORDER_TEMPORARY_STORAGE("orderTemporaryStorage", "外单预售服务-暂存位置"),

    /**
     * 暂存天数
     */
    TEMP_STORAGE_DAY("tempStorageDay", "暂存天数"),

    /**
     * 轨迹隐藏：0-否，1-是
     */
    track_Hidden("trackHidden", "轨迹隐藏"),

    /**
     * 证件配送服务
     * {
     *     "department": "管理部门",
     *     "deliveryContent": "寄递内容",
     *     "licensePlateNumber": "车牌号",
     *     "feeType": "资费类型",
     *     "deliveryMode": "寄递模式"
     * }
     * 寄送模式：1-同城；2-省内；3-省际
     * 资费类型: 11-证照单程；12-证照双程；21-号牌单程；22-号牌双程
     */
    CREDENTIALS_DELIVERY_SERVICE("credentialsDeliveryService", "证件配送服务"),

    /**
     * https://joyspace.jd.com/pages/VuUrBeFZf1BZZtfxJs5n
     */
    MULTI_DELIVERY_NOTICE_FLAG("multiDeliveryNoticeFlag", "多次再投通知标识:0否，1是"),
    /**
     *  https://joyspace.jd.com/pages/rYNowpuKRSRXj5Y35FHv
     */
    IS_INSTANT_PICKUP("isInstantPickup","是否即时揽收:0否，1是"),
    /**
     * https://joyspace.jd.com/pages/rYNowpuKRSRXj5Y35FHv
     */
    IS_PRODUCE_FINISH("isProduceFinish","是否生产完成:0否，1是"),

    /**
     * 按纸箱揽收服务要求
     */
    PICKUP_BY_BOX_SERVICE("pickupByBoxService", "按纸箱揽收服务要求"),

    ;

    private final String code;
    private final String desc;

    ServiceRequirementsEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ServiceRequirementsEnum of(String code) {
        return registry.get(code);
    }

    private static final Map<String, ServiceRequirementsEnum> registry = new HashMap();

    static {
        Iterator iterator = EnumSet.allOf(ServiceRequirementsEnum.class).iterator();
        while (iterator.hasNext()) {
            ServiceRequirementsEnum typeEnum = (ServiceRequirementsEnum) iterator.next();
            registry.put(typeEnum.getCode(), typeEnum);
        }
    }
}
