package cn.jdl.oms.express.domain.spec.dict;

/**
 * 上下文存储信息
 * @copyright    &copy;2023 JDL.CN All Right Reserved
 * <AUTHOR>
 * @date         2023/4/24
 * @version      1.0
 * @since        1.8
 */
public enum ContextInfoEnum {

    /**
     * 询价费用明细
     */
    ENQUIRY_FEE_INFOS("feeInfos", "询价费用明细"),

    /**
     * 补全营业厅标示
     */
    COMPLEMENT_BUSINESS_HALL_FLAG("complementBusinessHallFlag", "补全营业厅标示"),

    /**
     * 揽收站点/车队校验失败拒单标示
     */
    CHECK_PICKUP_STATION_FLAG("checkPickupStationFlag", "揽收站点/车队校验失败拒单标示"),

    /**
     * 若商家订单号为空，是否需要补全商家订单号
     */
    NEED_COMPLEMENT_CUSTOMER_ORDER_NO("needComplementCustomerOrderNo", "若商家订单号为空，是否需要补全商家订单号"),
    /** 支付结果，用于订单中心调用支付接口，存储支付接口返回结果，用作订单中心支付结果 */
    PAY_RESULT("PAY_RESULT", "支付结果"),
    /**用于存放支付结果的扩展信息，包含错误码和错误信息*/
    PAY_RESULT_EXT("PAY_RESULT_EXT", "支付结果的扩展信息"),

    /**
     * 起始站点地址
     * 存放根据 Shipment startStationNo 查出来的 AddressBasicPrimaryWSFacadeResponse
     */
    START_STATION_ADDRESS("startStationAddress", "起始站点地址"),
    /**
     * 是否合单打标
     */
    MERGE_ORDER("MERGE_ORDER", "已经合单"),
    /**
     * 是否整车询价
     */
    VEHICLE_INQUIRY("vehicleInquiry", "是否整车询价"),

    /** 异常中心挂异常 */
    EXCEPTION_INFO("EXCEPTION_INFO", "异常中心挂异常"),

    /**
     * 产品中心扩展补全辅助类数据
     */
    PRODUCT_EXTENSION_COMPLETER_DATA("productExtensionCompleterData", "产品中心扩展补全辅助类数据"),

    BATCH_CONSIGNEE_INFOS("batchConsigneeInfos", "集单串点收货人信息"),

    MODIFY_REPOSITORY_RESULT("modifyRepositoryResult", "修改持久化是否成功"),

    /**
     * 是否跳过询价
     * 0 - 否 1 - 是
     */
    SKIP_ENQUIRY("SKIP_ENQUIRY", "是否需要跳过台账"),

    /**
     * 改址一单到底
     * 0 - 否 1 - 是
     */
    READDRESS_1ORDER_2END("readdress1Order2End", "是否改址一单到底"),

    /**
     * 是否先款
     * 0 - 否 1 - 是
     */
    PAY_STAGE_ONLINE_PAYMENT("payStageOnlinePay", "支付环节-先款"),

    /**
     * 改址一单到底-初次改址
     * 0 - 否 1 - 是
     */
    READDRESS_1ORDER_2END_FIRST("readdress1Order2EndFirst", "是否改址一单到底的第一次改址"),

    /**
     * 是否需要支付
     * 0 - 否 1 - 是
     */
    READDRESS_NEED_PAY("readdressNeedPay", "需要支付"),

    /**
     * 是否需要退款
     * 0 - 否 1 - 是
     */
    READDRESS_NEED_REFUND("readdressNeedRefund", "需要退款"),

    /**
     * 需要支付的金额
     * @see
     */
    READDRESS_NEED_PAY_MONEY("readdressNeedPayMoney", "改址需要支付金额"),

    /**
     * 运单号校验是否成功
     * 1 - 成功；其他 - 失败
     */
    WAYBILL_NO_VALIDATE_SUCCESS("waybillNoValidateSuccess", "运单号校验成功"),

    /**
     * 需要审核的系统。目前只有 JDL_CRM
     *
     */
    VERIFY_SYSTEM("verifySystem", "审核系统"),

    /**
     * 需要审核的系统错误信息。
     *
     */
    VERIFY_SYSTEM_ERROR_MSG("verifySystemErrorMsg", "审核系统错误信息"),

    /**
     * 内部修改(目前包含下发)，会跳过非必要的能力点
     * 1 - 是；其他 - 否
     */
    INTERNAL_MODIFY("internalModify", "内部修改"),

    /**
     * 特殊流程，能力节点直接从敏感词到下发，跳过中间流程节点
     */
    SPECIAL_FLOW_FROM_SENSITIVE_WORDS_TO_ISSUE("specialFlowFromSensitiveWordsToIssue", "特殊流程，能力节点直接从敏感词到下发，跳过中间流程节点"),

    /**
     * 保险权益跳过
     */
    INSURANCE_SKIP("insuranceSkip", "跳过保险&权益"),

    /**
     * 特殊流程，能力节点直接从敏感词到持久化，跳过中间流程节点
     */
    SPECIAL_FLOW_FROM_SENSITIVE_WORDS_TO_REPOSITORY("specialFlowFromSensitiveWordsToRepository", "特殊流程，能力节点直接从敏感词到持久化，跳过中间流程节点"),

    /**
     * 拦截一单到底
     * 0 - 否 1 - 是
     */
    INTERCEPTION_THROUGH_ORDER_MODIFY("interceptionThroughOrderModify", "拦截一单到底修改"),

    /**
     * 快运寄付拦截一单到底修改
     * 0 - 否 1 - 是
     */
    FREIGHT_JF_INTERCEPTION_THROUGH_ORDER_MODIFY("freightJFInterceptionThroughOrderModify", "快运寄付拦截一单到底修改"),


    READDRESS_OR_FREIGHT_JF_INTERCEPTION_1ORDER_2END("readdressOrFreightJFInterception1Order2End","快递改址一单到底or快运寄付拦截一单到底"),

    /**
     * 收发管家实时预占模式
     */
    YISHOUFA("yishoufa", "收发管家"),

    /**
     * 原始业务身份
     */
    ORIGINAL_IDENTITY("originalIdentity", "原始业务身份"),

    /**
     * 是否打过互改标
     */
    HAS_MARKING_PRODUCT_EXCHANGE("hasMarkingProductExchange", "是否打过互改标"),

    /**
     * 业务身份修改类型：业务身份、业务类型、业务策略 修改类型
     * 0-全量修改；1-修改业务身份，2-修改业务类型，3-修改业务策略
     */
    MODIFY_BUSINESS_IDENTITY_TYPES("modifyBusinessIdentityTypes", "业务身份修改类型"),

    /**
     * 跳过计费写账标识
     * 0 - 否 1 - 是
     */
    SKIP_ENQUIRY_ORDER_BANK("skipEnquiryOrderBank", "跳过计费写账标识"),

    /**
     * 暂存服务单台账变更标识
     * 0 - 否 1 - 是
     */
    TEMP_STORAGE_ORDER_BANK_CLEAR_FLAG("tempStorageOrderBankClearFlag", "暂存服务单台账清空标识"),

    /**
     * 是否匹配产品中心暂存服务费
     * 0 - 否 1 - 是
     */
    TEMP_STORAGE_FEE_PRODUCT_MATCH_FLAG("tempStorageFeeProductMatchFlag", "是否匹配产品中心暂存服务费"),

    /**
     * 暂存服务费匹配成功
     * 0 - 否 1 - 是
     */
    TEMP_STORAGE_MATCH_SUCCESS("tempStorageMatchSuccess", "暂存服务费匹配成功"),

    /**
     * 芝麻代扣修改标识
     * 0 - 芝麻代扣改为非芝麻代扣， 1 - 非芝麻代扣改为芝麻代扣
     */
    PAYMENT_TYPE_ALIPAY_CHANGE_FLAG("paymentTypeAlipayChangeFlag", "芝麻代扣支付方式修改标识"),

    /**
     * 服务加操作场景，接单、取消、修改
     *
     */
    SERVICE_PLUS_OPERATE_SCENE("servicePlusOperateScene", "服务加操作场景"),

    /**
     * 加价策略标
     * * 0 - 否 1 - 是
     */
    ADDITION_PRICE_FLAG("additionPriceFlag", "加价策略标"),

    /**
     * 是否为融合 快运B2C
     * 1 - 是
     */
    IS_UNITED_FREIGHT_B2C("isUnitedFreightB2C", "是否为融合快运B2C"),

    /**
     * 以旧换新/送取同步修改操作类型
     * 1-取件单绑定派送单
     * 2-取件单解除绑定派送单
     * 3-派送单解除绑定取件单
     */
    DELIVERY_PICKUP_SYNC_BIND_OPERATE_TYPE("deliveryPickupSyncBindOperateType", "以旧换新/送取同步修改操作类型"),

    /**
     * 以旧换新/送取同步的取件单
     * 类型为ExpressOrderModel
     */
    DELIVERY_PICKUP_SYNC_PICKUP_ORDER("deliveryPickupSyncPickupOrder", "以旧换新/送取同步的取件单"),

    /**
     * 以旧换新/送取同步的派送单
     * 类型为ExpressOrderModel
     */
    DELIVERY_PICKUP_SYNC_DELIVERY_ORDER("deliveryPickupSyncDeliveryOrder", "以旧换新/送取同步的派送单"),

    /** 港澳&国际自定义托寄物 -- 先款服务询价单模式（寄付，简易->正式） */
    CUSTOMS_ONLINE_PAY("customsOnlinePay", "变更报关方式-在线支付"),
    /** 港澳&国际自定义托寄物 -- 先款服务询价单模式（寄付，简易->正式） */
    CUSTOMS_ONLINE_PAY_PENDING_MONEY("customsOnlinePayPendingMoney", "变更报关方式-在线支付-待支付金额"),
    /** 港澳&国际自定义托寄物 -- 退款模式(寄付，正式->简易) */
    CUSTOMS_REFUND("customsRefunds", "变更报关方式-退款"),
    /** 港澳&国际自定义托寄物 -- 先款服务询价单模式（到付） */
    CUSTOMS_NORMAL("customsNormal", "变更报关方式-正常"),

    /** 港澳&国际自定义托寄物 -- 先款服务询价单模式（寄付，取消服务单） */
    CUSTOMS_CANCEL("customsCancel", "取消报关服务单"),
    ;

    private final String code;
    private final String desc;

    ContextInfoEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
