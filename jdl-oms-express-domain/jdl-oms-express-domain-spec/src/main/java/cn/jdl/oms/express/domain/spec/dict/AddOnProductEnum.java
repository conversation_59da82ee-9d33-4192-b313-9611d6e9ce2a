package cn.jdl.oms.express.domain.spec.dict;

import java.util.Arrays;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 产品中心标准增值产品枚举类
 * @copyright    &copy;2023 JDL.CN All Right Reserved
 * <AUTHOR>
 * @date         2021/3/16
 * @version      1.0
 * @since        1.8
 */
public enum AddOnProductEnum {
    ZY_DSHK("ZY_DSHK", "众邮代收货款服务",null),
    ZY_BJFW("ZY_BJFW", "众邮保价服务",null),
    FEATHER_LETTER("ed-a-0001", "鸡毛信",92),
    JZD("ed-a-0008", "京尊达",35),
    INSURED("sc-a-0001", "增值服务保价产品编码",null),
    NEGOTIATION_REDELIVERY("ed-a-0005","协商再投",5),
    INSURED_VALUE_TOC("ed-a-0002", "普通保价",null),
    INSURED_PER_ORDER("ed-a-0003", "单单保",null),

    JDL_COD_TOC("ed-a-0009", "代收货款",null),
    JDL_COD_LAS("lg-a-0016", "代收货款", null),
    JDL_DIFFERENT_CITY_ADDRESS_LAS("lq-a-0007", "跨城改派", null),
    SIGN_RETURN_TOC("ed-a-0010", "签单返还",4),
    PACKAGE_SERVICE_TOC("ed-a-0011", "包装服务",72),
    PLASTIC_SEAL_PACKING("ed-a-0012","检查塑封包装",null),
    ORIGINAL_PACKING_LOSE("ed-a-0013","检查原包装（丢失）",null),
    ORIGINAL_PACKING_DAMAGED("ed-a-0014","检查原包装（破损）",null),
    ATTACHMENT("ed-a-0015","检查附件",null),
    GOODS_APPEARANCE("ed-a-0016","检查商品外观",null),
    USE_STATUS("ed-a-0017","检查使用情况",null),
    CHECK_SN("ed-a-0018","检查SN",null),
    J_ZHUN_DA("ed-a-0022", "京准达",113),
    PRIVACY_CONVERSATION("ed-a-0024","隐私通话",null),
    /**
     * 改址模式readdressMode：
     * 改址换单-changeOrder;改址一单-throughOrder;拦截一单-interceptionThroughOrder;
     * 拦截类型interceptType：
     * 同城拦截-sameCityIntercept;跨城拦截-crossCityIntercept;
     * 改址类型readdressType：
     * 同城跨站改址-acrossSite;跨城改址-acrossCity;同城同站改址-tearCodeBox;同城同站改址-sameSite;
     */
    READDRESS("ed-a-0031","改址服务",null),
    COOLER_BOX("ed-a-0035","保温箱",134),

    JDL_COD_TOB("fr-a-0001", "代收货款",null),
    INSURED_VALUE_TOB("fr-a-0002", "保价",null),
    PACKAGE_SERVICE_TOB("fr-a-0005", "包装服务",null),
    HEAVY_UPSTAIR_TOB("fr-a-0006", "重货上楼",null),
    SIGN_RETURN_TOB("fr-a-0007", "签单返还",null),
    DELIVERY_INTO_WAREHOUSE_TOB("fr-a-0008", "送货入仓",null),
    TEMPORARY_STORAGE_TOB("fr-a-0009", "暂存服务",null),

    DESIGNATED_SIGN("ed-a-0026","指定签收",null),
    PROOF_PICKTEAR_CODE("ed-a-0020","防撕码采集",null),
    SMILE_EXPRESS_SHEET("ed-a-0032","微笑面单",null),
    // FIXME 增值产品重复定义
    EXPRESS_CONSUMABLES("ed-a-0011","快递耗材",null),
    TE_AN_FU_WU("ed-a-0047","特安服务",null),
    JZS_FU_WU("ed-a-0079","京尊送",null),
    DA_BAO_FU_WU("ed-a-0052","打包服务",null),
    YUN_FEI_BAO("ed-a-0064","退回险",null),
    KUAI_DI_RU_CANG("ed-a-0045","快递入仓",null),
    BLACK_AND_WHITE_PRINT ("ed-a-0037","黑白打印",null),
    COLOR_PRINT("ed-a-0038","彩色打印",null),
    PICKUP_PICTURE("ed-a-0019","揽收拍照",null),
    DELIVERY_PICTURE("ed-a-0109", "派送拍照", null),
    MXLS("ed-a-0021","明细揽收",null),

    LL_ZZ_BJ("LL-ZZ-BJ","生鲜保价",null),
    LL_ZZ_DSHK("LL-ZZ-DSHK","生鲜代收货款",null),
    SXHDHCF("ll-a-0004","生鲜包装服务",null),
    LL_ZZ_QDFH("LL-ZZ-QDFH","生鲜签单返还",null),
    BOOK_DELIVERY("ed-a-0053","预约派送",null),
    INSURED_VALUE_FULL("ed-a-0072", "全额保",null),
    SURPRISE_DELIVERY("ed-a-0073", "惊喜送达",null),
    PICKUP_VERIFICATION("ed-a-0057", "验证揽收", null),
    /** 分批派送 */
    DELIVERY_IN_BATCHES("ed-a-0075", "分批派送", null),
    /**
     * 重货上楼产品编码
     */
    HEAVY_GOODS_UP_EXPRESS_PRODUCT_NO("lq-a-0012", "重货上楼产品编码", null),

    /**
     * 送装一体产品编码
     */
    DELIVERY_ASSEMBLE_PRODUCT_NO("lq-a-0002", "送装一体产品编码", null),

    /**
     * 大件二次上门安装服务
     */
    LAS_SECOND_ONSITE_ASSEMBLE_PRODUCT_NO("lq-a-0020", "大件二次上门安装服务", null),
    LAS_DISASSEMBLY_BLOCK("lq-a-0047", "大件拆木架", null),
    KD_CC_CZ_FJF("KDCCCC","快递超长超重附加费",null),
    INSURED_FR("fr-a-0057", "保价服务", null),
    PACKING_FR("fr-a-0058", "包装服务", null),
    OVER_WEIGHT_FR("fr-a-0059", "超长超重", null),

    CC_LL_SIGN_RETURN_TOC("ll-m-0013", "冷链签单返还",null),
    CC_MD_SIGN_RETURN_TOC("md-a-0023", "医药签单返还",null),
    CC_MD_COD_TOC("md-a-0027", "医药代收货款",null),
    CC_MD_DELIVERY_INTO_WAREHOUSE_TOB("md-a-0026", "医药送货入仓",null),
    CC_MD_PACKAGE_SERVICE_TOB("md-a-0025", "医药包装服务",null),
    CC_MD_INSURED("md-a-0022", "医药普通保价",null),
    CC_MD_DESIGNATED_SIGN("md-a-0024", "医药指定签收",null),
    CC_MD_NEGOTIATION_REDELIVERY("md-a-0030", "医药协商再投",null),


    CC_LL_NEGOTIATION_REDELIVERY("ll-a-0018", "冷链短信通知",null),
    CC_LL_INSURED("ll-a-0023", "冷链医药保价",null),
    CC_LL_COOLER_BOX("ll-a-0002", "冷链保温箱",null),
    CC_LL_THERMOMETER("ll-a-0003", "冷链温度计",null),
    CC_LL_ZS_INSURED("ll-a-0036", "冷链专送保价",null),
    CC_LL_SMILE_EXPRESS_SHEET("ll-f-0002", "冷链微笑面单",null),

    PEAK_SEASON_SURCHARGE("GF1006", "高峰期附加费", null),
    FUEL_SURCHARGE("FJF-RYF00001", "燃油附加费", null),
    FR_A_0011("fr-a-0011","整车阶梯保价",null),
    FR_A_0016("fr-a-0016","特快重货-保价",null),
    READDRESS_FREIGHT("fr-a-0049","快运改址",null),
    SPECIAL_FEE_PICKUP("fr-a-0047","特殊加收服务费",null),
    SPECIAL_FEE_DELIVERY("fr-a-0050","特殊加收-派送端",null),
    FIRST_COME_FIRST_SERVED("fr-a-0051","先到先送",null),
    PRINT_SIGN_BACK("fr-a-0052","配送签回单打印",null),
    VALIDATE_PICKUP("fr-a-0053","验证揽收-快运",null),
    VALIDATE_DELIVERY("fr-a-0054","验证签收-快运",null),
    PICTURE_PICKUP("fr-a-0055","揽收拍照-快运",null),
    PICTURE_DELIVERY("fr-a-0063","派送拍照-快运",null),
    WAIT_NOTICE_DELIVERY("fr-a-0044","等通知派送",null),
    QUICK_CLAIM("fr-a-0061","快速理赔",null),
    FRIGHT_BUBBLE_INSURED("fr-a-0062","特快泡货保价",null),
    FRIGHT_LOADING_CAR("fr-a-0031","装车服务-快运",null),
    FRIGHT_UNLOAD_CAR("fr-a-0032","卸车服务-快运",null),
    FRIGHT_TEST_EQUIPMENT("fr-a-0014","通电验机-快运",null),
    FRIGHT_CONTACT_DIRECTLY("fr-a-0070","自行联系-快运",null),
    FRIGHT_SCHEDULED_PICKUP("fr-a-0071","定时揽收-快运",null),
    FRIGHT_NIGHT_COLLECTION("fr-a-0072","夜间揽收-快运",null),
    FRIGHT_REMOVE_FRAME("fr-a-0074","拆木架-快运",null),
    FREIGHT_RECYCLE_PACKAGING("fr-a-0066","循环包装-快运",null),
    FREIGHT_A_0082("fr-a-0082","送装分离-快运",null),
    FREIGHT_PACKAGE_RETURN("fr-a-0092","包装返还",null),

    CC_B2B_MD_COD("ll-a-0024", "代收货款", null),
    CC_B2B_LL_DELIVERY_TO_WAREHOUSE("ll-a-0015", "生鲜送货入仓", null),
    CC_B2B_MD_DELIVERY_TO_WAREHOUSE("ll-a-0039", "医药送货入仓", null),
    CC_B2B_LL_HEAVY_UPSTAIRS("ll-a-0064", "生鲜重货上楼", null),
    CC_B2B_MD_HEAVY_UPSTAIRS("ll-a-0035", "医药重货上楼", null),
    CC_B2B_MD_PACKAGE_SERVICE("md-a-0004", "医药包装服务", null),
    CC_B2B_LL_INSURED_VALUE("ll-a-0061", "生鲜保价", null),
    CC_B2B_MD_INSURED_VALUE("ll-a-0023", "医药保价", null),
    CC_LL_SIGN_RETURN_TOB("ll-a-0014", "生鲜签单返还",null),
    CC_MD_SIGN_RETURN_TOB("ll-m-0013", "医药签单返还",null),

    CC_TO_DOOR_PICKUP_TOB("ll-a-0005", "上门揽收",null),
    CC_TO_DOOR_DELIVERY_TOB("ll-a-0006", "上门配送",null),
    CC_KD_STAGING_TOB("ll-a-0007", "卡班暂存增值服务",null),
    CC_DK_QUARANTINE_TICKET_TOB("ll-a-0009", "代开检疫票",null),
    CC_LOADING_CAR_TOB("ll-a-0010", "装车",null),
    CC_UNLOAD_CAR_TOB("ll-a-0011", "卸车",null),
    CC_BOX_BOX_INSURED_TOB("ll-a-0025", "箱箱保",null),
    CC_LOADING_UNLOAD_CAR_TOB("ll-a-0040", "装车、卸车",null),
    CC_STERILIZATION_TOB("ll-a-0047", "冷链消杀",null),
    CC_REST_RECEIVE_TOB("ll-a-0051", "安心收",null),
    CC_SMALL_INSURED_TOB("ll-a-0054", "小额保",null),
    CC_DELIVERY_TO_WAREHOUSE_TOB("ll-a-0060", "精准送仓",null),
    CC_MD_TO_DOOR_PICKUP_TOB("ll-a-0065", "装车",null),
    CC_MD_TO_DOOR_DELIVERY_TOB("ll-a-0066", "卸车",null),
    CC_FILE_TOB("ll-a-0069", "随货文件",null),
    CC_UNPACKING_HALF_RECEIVING_TOB("ll-a-0071", "拆箱半收",null),
    CC_SPRING_FESTIVAL_SURCHARGE_TOB("md-a-0002", "春节附加费",null),
    CC_IN_HOUSE_SERVICES_TOB("md-a-0006", "院内服务-医药",null),
    CC_ROLLING_STOCK_FEE_TOB("ll-a-0070", "整车压车费",null),
    CC_SMILE_EXPRESS_SHEET_TOB("ll-f-0002", "微笑面单",null),
    CHECK_PACKAGE("ed-f-0001", "检查外包装", null),
    /**
     * 代表快递条线里POP售后的耗材增值服务，后续其余承运商复用此编码
     */
    EXPRESS_PACKAGE_POP("ed-a-0080", "POP售后-极兔耗材", null),
    /**
     * 代表快递条线里POP售后的保价增值服务，后续其余承运商复用此编码
     */
    EXPRESS_GUARANTEE_POP("ed-a-0081", "POP售后-极兔保价", null),

    CONTRACT_RETURN_SIGNATURE_TO_B("sc-a-0006","合同物流-签单返还服务",null),
    CONTRACT_CAR_RETURN_SIGNATURE_TO_B("qh-a-0004","合同物流-汽车-签单返还服务",null),
    CONTROL_TEMP_DELIVERY("ll-a-0079","定温送-冷链卡班",null),
    KABAN_REDELIVERY("ll-a-0080", "卡班再次派送", null),

    CHECK_GOODS("ed-a-0077", "验货服务", null),
    UEP_CHECK_GOODS("ed-a-0083", "物流平台验货服务", null),
    EXPRESS_CONTACT_DIRECTLY("ed-a-0084","自行联系-快递",null),
    /**
     * 医药改址，改址类型readdressType-单选、必填 同城同站改址：sameSite 同城跨站改址：acrossSite
     */
    CC_MD_READDRESS("md-a-0034", "医药改址", null),
    EXPRESS_NIGHT_COLLECTION("ed-a-0085","夜间揽收--快递",null),

    CARBON_EMISSION_CALCULATION_EXPRESS("ed-a-0090","碳计算服务-快递",null),

    CARBON_EMISSION_CALCULATION_FREIGHT("fr-a-0076","碳排放计算-快运",null),

    BOOK_DELIVERY_FREIGHT("fr-a-0034","预约派送-快运",null),

    CUSTOM_CHECK_SIGN("ed-a-0088","自定义验证签收",null),

    ACSD_BJ("fr-a-0077","爱宠速递-保价",null),

    ACTIVATION_CHECK("ed-a-0098", "国补-激活校验", null),
    ACTIVATION_CHECK_FREIGHT("fr-a-0086", "国补-激活校验-快运", null),

    FJF_ZDZC("FJF-ZDZC0001", "站点暂存", null),
    LIVING_SEND("ed-a-0097", "活物寄", null),

    PROXY_SHIPPING_FEE_EXPRESS("ed-a-0095","代物流公司收运费-快递",null),

    PROXY_SHIPPING_FEE_FREIGHT("fr-a-0080","代物流公司收运费-快运",null),

    PROXY_COD_EXPRESS("ed-a-0094","代物流公司收货款-快递",null),

    PROXY_COD_FREIGHT("fr-a-0079","代物流公司收货款-快运",null),
    DEPPON_OVER_WEIGHT_EXPRESS("ed-a-0096","DP融合超长超重",null),
    DEPPON_OVER_WEIGHT_FREIGHT("fr-a-0081","DP融合超长超重",null),

    PACKAGING_SERVICES_INTL("isc-a-0031", "包装服务-国际", null),

    LAS_INFORMATION_COLLECTION_SERVICE("lq-a-0028", "大件信息采集服务", null),
    DISINFECT_FREIGHT("fr-a-0083", "医药消杀", null),
    DISINFECT_EXPRESS("ed-a-0103", "医药消杀-快递", null),
    FR_PACKING_SERVICE("fr-a-0033", "快运打包服务", null),
    FR_WAIT_SELLER_VERIFICATION("fr-a-0089", "等待商家验货-快运", null),
    EXPRESS__WAIT_SELLER_VERIFICATION("ed-a-0107", "等待商家验货-快递", null),
    FR_PACKING_SERVICE_NEW("fr-a-0091", "快运打包服务", null),
    ON_TIME_GUARANTEE("ed-a-0106","准时保", null),
    ;

    private String code;
    private String desc;
    /*外单增值服务打标位*/
    private Integer index;

    AddOnProductEnum(String code, String desc,Integer index) {
        this.code = code;
        this.desc = desc;
        this.index = index;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getIndex() {
        return index;
    }

    public static AddOnProductEnum of(String code) {
        return registry.get(code);
    }

    public static AddOnProductEnum ofByIndex(Integer index) {
        if(index == null){
            return null;
        }
        AddOnProductEnum productEnum =null;
        for(AddOnProductEnum  product:AddOnProductEnum.values()){
            if(product.getIndex()!=null&&product.getIndex().equals(index)){
                productEnum = product;
                break;
            }
        }
        return productEnum;
    }

    public static List<AddOnProductEnum> getCod(){
        return Arrays.asList(JDL_COD_TOC,LL_ZZ_DSHK,CC_MD_COD_TOC,JDL_COD_TOB, CC_B2B_MD_COD, PROXY_COD_EXPRESS, PROXY_COD_FREIGHT);
    }

    public static List<String> getCodCode(){
        return getCod().stream().map(AddOnProductEnum::getCode).collect(Collectors.toList());
    }

    private static Set<String> COD = new HashSet<>(Arrays.asList(
            JDL_COD_TOC.getCode(), LL_ZZ_DSHK.getCode(), CC_MD_COD_TOC.getCode(),
            JDL_COD_TOB.getCode(), CC_B2B_MD_COD.getCode(), PROXY_COD_EXPRESS.getCode(),
            PROXY_COD_FREIGHT.getCode()
    ));

    public static boolean isCodProduct(String productNo) {
        return COD.contains(productNo);
    }

    public static List<AddOnProductEnum> getInsuredValue(){
        return Arrays.asList(INSURED_VALUE_TOC,LL_ZZ_BJ,CC_MD_INSURED,CC_LL_INSURED,CC_LL_ZS_INSURED,FR_A_0011,
                FR_A_0016,INSURED_VALUE_TOB,CC_B2B_LL_INSURED_VALUE,CC_B2B_MD_INSURED_VALUE, CC_SMALL_INSURED_TOB, FRIGHT_BUBBLE_INSURED);
    }

    public static List<String> getInsuredValueCode(){
        return getInsuredValue().stream().map(AddOnProductEnum::getCode).collect(Collectors.toList());
    }

    public static List<AddOnProductEnum> getSignReturn(){
        return Arrays.asList(SIGN_RETURN_TOC,LL_ZZ_QDFH,CC_LL_SIGN_RETURN_TOC,CC_MD_SIGN_RETURN_TOC,SIGN_RETURN_TOB,CC_LL_SIGN_RETURN_TOB,CC_MD_SIGN_RETURN_TOB,CONTRACT_RETURN_SIGNATURE_TO_B,CONTRACT_CAR_RETURN_SIGNATURE_TO_B);
    }

    public static List<String> getSignReturnCode(){
        return getSignReturn().stream().map(AddOnProductEnum::getCode).collect(Collectors.toList());
    }

    public static List<AddOnProductEnum> getPackageService(){
        return Arrays.asList(PACKAGE_SERVICE_TOC,SXHDHCF,CC_MD_PACKAGE_SERVICE_TOB,PACKAGE_SERVICE_TOB,CC_B2B_MD_PACKAGE_SERVICE);
    }

    public static List<String> getPackageServiceCode(){
        return getPackageService().stream().map(AddOnProductEnum::getCode).collect(Collectors.toList());
    }

    public static List<AddOnProductEnum> getDeliveryToWarehouse(){
        return Arrays.asList(CC_B2B_LL_DELIVERY_TO_WAREHOUSE,CC_B2B_MD_DELIVERY_TO_WAREHOUSE);
    }

    public static List<String> getDeliveryToWarehouseCode(){
        return getDeliveryToWarehouse().stream().map(AddOnProductEnum::getCode).collect(Collectors.toList());
    }

    public static List<AddOnProductEnum> getPrecisionDeliveryToWarehouse(){
        return Arrays.asList(CC_DELIVERY_TO_WAREHOUSE_TOB);
    }

    public static List<String> getPrecisionDeliveryToWarehouseCode(){
        return getPrecisionDeliveryToWarehouse().stream().map(AddOnProductEnum::getCode).collect(Collectors.toList());
    }

    public static List<AddOnProductEnum> getHeavyUpstairs(){
        return Arrays.asList(CC_B2B_LL_HEAVY_UPSTAIRS,CC_B2B_MD_HEAVY_UPSTAIRS);
    }

    public static List<String> getHeavyUpstairsCode(){
        return getHeavyUpstairs().stream().map(AddOnProductEnum::getCode).collect(Collectors.toList());
    }

    public static List<AddOnProductEnum> getLoading(){
        return Arrays.asList(CC_LOADING_CAR_TOB,CC_MD_TO_DOOR_PICKUP_TOB);
    }

    public static List<String> getLoadingCode(){
        return getLoading().stream().map(AddOnProductEnum::getCode).collect(Collectors.toList());
    }

    public static List<AddOnProductEnum> getUnLoading(){
        return Arrays.asList(CC_MD_TO_DOOR_DELIVERY_TOB,CC_UNLOAD_CAR_TOB);
    }

    public static List<String> getUnLoadingCode(){
        return getUnLoading().stream().map(AddOnProductEnum::getCode).collect(Collectors.toList());
    }

    public static List<AddOnProductEnum> getLoadingAndUnLoading(){
        return Arrays.asList(CC_LOADING_UNLOAD_CAR_TOB);
    }

    public static List<String> getLoadingAndUnLoadingCode(){
        return getLoadingAndUnLoading().stream().map(AddOnProductEnum::getCode).collect(Collectors.toList());
    }

    public static List<AddOnProductEnum> getSmileExpressSheetProduct() {
        return Arrays.asList(CC_SMILE_EXPRESS_SHEET_TOB);
    }

    public static List<String> getSmileExpressSheetProductCode() {
        return getSmileExpressSheetProduct().stream().map(AddOnProductEnum::getCode).collect(Collectors.toList());
    }

    /**
     * 允许揽收后修改的产品枚举
     */
    public static List<AddOnProductEnum> getAllowedModifiedProduct(){
        return Arrays.asList(JDL_COD_TOC, LL_ZZ_DSHK, READDRESS, BOOK_DELIVERY, CC_MD_COD_TOC, EXPRESS_NIGHT_COLLECTION,
                CARBON_EMISSION_CALCULATION_FREIGHT, CARBON_EMISSION_CALCULATION_EXPRESS, ACTIVATION_CHECK, EXPRESS_CONTACT_DIRECTLY);
    }

    /**
     * 允许揽收后修改的产品编码
     */
    public static Set<String> getAllowedModifiedProductCode() {
        return getAllowedModifiedProduct().stream().map(AddOnProductEnum::getCode).collect(Collectors.toSet());
    }

    /**
     * 快运允许揽收后修改的产品枚举（月结、到付现结公共）
     */
    public static List<AddOnProductEnum> getFreightAllowedModifiedProduct(){
        return Arrays.asList(JDL_COD_TOB, SIGN_RETURN_TOB, HEAVY_UPSTAIR_TOB, DELIVERY_INTO_WAREHOUSE_TOB, READDRESS_FREIGHT,
                SPECIAL_FEE_PICKUP, SPECIAL_FEE_DELIVERY, FIRST_COME_FIRST_SERVED, FRIGHT_TEST_EQUIPMENT, FRIGHT_NIGHT_COLLECTION,
                CARBON_EMISSION_CALCULATION_FREIGHT, CARBON_EMISSION_CALCULATION_EXPRESS, BOOK_DELIVERY_FREIGHT, FREIGHT_RECYCLE_PACKAGING, FRIGHT_CONTACT_DIRECTLY,
                DISINFECT_FREIGHT);
    }

    /**
     * 快运允许揽收后修改的产品编码（月结、到付现结公共）
     */
    public static Set<String> getFreightAllowedModifiedProductCode() {
        return getFreightAllowedModifiedProduct().stream().map(AddOnProductEnum::getCode).collect(Collectors.toSet());
    }

    /**
     * 冷链B2B不允许询价的产品枚举
     */
    public static List<AddOnProductEnum> getCCB2BNotEnquiryProduct(){
        return Arrays.asList(CC_MD_NEGOTIATION_REDELIVERY, CC_LL_NEGOTIATION_REDELIVERY, CC_LL_SMILE_EXPRESS_SHEET, CC_REST_RECEIVE_TOB, CC_FILE_TOB, CC_ROLLING_STOCK_FEE_TOB, CC_UNPACKING_HALF_RECEIVING_TOB
        ,CC_LL_COOLER_BOX, CC_LL_THERMOMETER);
    }

    /**
     * 冷链B2B不允许询价的产品编码
     */
    public static Set<String> getCCB2BNotEnquiryProductCode() {
        return getCCB2BNotEnquiryProduct().stream().map(AddOnProductEnum::getCode).collect(Collectors.toSet());
    }

    /**
     * 快运允许揽收后修改的产品枚举
     */
    public static List<AddOnProductEnum> getNeedEnquiryProduct(){
        return Arrays.asList(HEAVY_UPSTAIR_TOB, DELIVERY_INTO_WAREHOUSE_TOB);
    }

    /**
     * 快运允许揽收后修改的产品编码
     */
    public static Set<String> getNeedEnquiryProductCodes() {
        return getNeedEnquiryProduct().stream().map(AddOnProductEnum::getCode).collect(Collectors.toSet());
    }

    // B2C询价跳过的产品（无需传给计费
    public static final Set<String> B2C_NON_ENQUIRY_PRODUCT = new HashSet<>();
    // b2c询价从入参获取的产品（询价场景过滤订单快照，并取入参）
    public static final Set<String> B2C_ENQUIRY_PRODUCT = new HashSet<>();
    static {
        // COD
        B2C_NON_ENQUIRY_PRODUCT.addAll(AddOnProductEnum.getCodCode());
        // 协商再投
        B2C_NON_ENQUIRY_PRODUCT.add(AddOnProductEnum.NEGOTIATION_REDELIVERY.getCode());
        B2C_NON_ENQUIRY_PRODUCT.add(AddOnProductEnum.CC_MD_NEGOTIATION_REDELIVERY.getCode());
        // 微笑面单
        B2C_NON_ENQUIRY_PRODUCT.add(AddOnProductEnum.SMILE_EXPRESS_SHEET.getCode());
        // 京碳惠
        B2C_NON_ENQUIRY_PRODUCT.add(AddOnProductEnum.CARBON_EMISSION_CALCULATION_EXPRESS.getCode());

        // 包装耗材
        B2C_ENQUIRY_PRODUCT.addAll(AddOnProductEnum.getPackageServiceCode());
        // 保温箱
        B2C_ENQUIRY_PRODUCT.add(AddOnProductEnum.COOLER_BOX.getCode());
        B2C_ENQUIRY_PRODUCT.add(AddOnProductEnum.CC_LL_COOLER_BOX.getCode());
    }

    /**
     * 判断是否是B2C业务跳过询价的产品
     * @param productNo
     * @return
     */
    public static boolean b2cSkipEnquiry(String productNo) {
        return B2C_NON_ENQUIRY_PRODUCT.contains(productNo);
    }

    private static final Map<String, AddOnProductEnum> registry = new HashMap<>();

    static {
        for (AddOnProductEnum typeEnum : EnumSet.allOf(AddOnProductEnum.class)) {
            registry.put(typeEnum.getCode(), typeEnum);
        }
    }

    /**
     * 改址增值服务
     */
    public static final Set<String> READDRESS_PRODUCT_CODE_SET = new HashSet<String>(){{
        add(READDRESS.getCode());
        add(READDRESS_FREIGHT.getCode());
        add(CC_MD_READDRESS.getCode());
    }};

    /**
     * 代物流公司收运费
     */
    public static final Set<String> PROXY_SHIPPING_FEE_CODE_SET = new HashSet<String>(){{
        add(PROXY_SHIPPING_FEE_EXPRESS.getCode());
        add(PROXY_SHIPPING_FEE_FREIGHT.getCode());
    }};
}
