package cn.jdl.oms.express.freight.extension.order;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.order.IOrderExtension;
import cn.jdl.oms.express.domain.infrs.acl.util.OriginalOrderModifyUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ProductClearPdqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.shared.common.constant.FlowConstants;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 退货单（拦截/拒收换单）下单成功删除原单产品
 */
@Slf4j
@Extension(code = ExpressOrderProduct.CODE)
public class FreightCreateOriginalOrderModifyExtension implements IOrderExtension {

    /**
     * UMP工具
     */
    @Resource
    private UmpUtil umpUtil;
    
    /**
     * 任务调度
     */
    @Resource
    private SchedulerService schedulerService;

    /**
     * 原单修改工具类
     */
    @Resource
    private OriginalOrderModifyUtil originalOrderModifyUtil;

    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = umpUtil.registerInfo(this.getClass().getName() + ".execute");
        try {
            ExpressOrderModel orderModel = context.getOrderModel();
            if (orderModel.getOrderSnapshot() == null) {
                return;
            }
            log.info("快运原单修改扩展点执行开始!");
            // 参考B2C，但是快运没有改址换单，仅处理退货单
            // 退货单后款订单
            if (OrderTypeEnum.RETURN_ORDER == orderModel.getOrderType()
                    && PaymentStageEnum.CASHONDELIVERY == orderModel.getFinance().getPaymentStage()) {
                // 退货单后款订单，只有需要删除的增值产品不为空，才发
                log.info("快运退货单后款订单异步清理原单增值服务执行开始!");
                List<String> clearProductNos = originalOrderModifyUtil.reverseOrReaddressDeleteOriginalProduct(orderModel);

                if (CollectionUtils.isNotEmpty(clearProductNos)) {
                    SchedulerMessage schedulerMessage = new SchedulerMessage();
                    ProductClearPdqMessageDto productClearPdqMessageDto = new ProductClearPdqMessageDto();
                    productClearPdqMessageDto.setOrderNo(orderModel.orderNo());
                    productClearPdqMessageDto.setOriginalOrderNo(orderModel.getOrderSnapshot().orderNo());
                    productClearPdqMessageDto.setRequestProfile(orderModel.requestProfile());
                    productClearPdqMessageDto.setBusinessIdentity(orderModel.getOrderBusinessIdentity());
                    productClearPdqMessageDto.setClearProductNos(clearProductNos);
                    schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(productClearPdqMessageDto));
                    schedulerMessage.setDtoClass(ProductClearPdqMessageDto.class);
                    schedulerService.addSchedulerTask(PDQTopicEnum.ORIGINAL_ORDER_MODIFY, schedulerMessage, FlowConstants.EXPRESS_ORDER_ORIGINAL_ORDER_MODIFY_FLOW_CODE);
                } else {
                    log.info("快运退货单后款订单，无需删除原单增值产品");
                }
            }
            log.info("快运退货单后款订单异步清理原单增值服务执行结束!");
        } catch (BusinessDomainException e) {
            Profiler.functionError(callerInfo);
            log.error("快运原单修改扩展点执行异常: {}", e.fullMessage());
            throw e;
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            log.error("快运原单修改扩展点执行异常", e);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.ORIGINAL_ORDER_MODIFY_FAIL).withCustom("快运原单修改扩展点执行异常");
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }
}