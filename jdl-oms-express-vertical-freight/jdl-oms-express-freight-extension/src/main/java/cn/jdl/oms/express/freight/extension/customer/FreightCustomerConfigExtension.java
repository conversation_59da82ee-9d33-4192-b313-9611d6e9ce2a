package cn.jdl.oms.express.freight.extension.customer;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CustomerConfig;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.customer.ICustomerConfigExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.BasicTraderResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.DeptResponse;
import cn.jdl.oms.express.domain.infrs.acl.util.BusinessSceneUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.SystemCallerUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.TrustSellerUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.WarehouseModeUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.SupplyChainDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.ContextInfoEnum;
import cn.jdl.oms.express.domain.spec.dict.OccupyModeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.DeptMarkEnum;
import cn.jdl.oms.express.shared.common.dict.TraderOperateStateEnum;
import cn.jdl.oms.express.shared.common.dict.TraderSignEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.dict.UnifiedSubErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.TraderSignUtils;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 客户配置校验扩展
 */
@Extension(code = ExpressOrderProduct.CODE)
public class FreightCustomerConfigExtension implements ICustomerConfigExtension {

    private static final Logger LOGGER = LoggerFactory.getLogger(FreightCustomerConfigExtension.class);

    /**
     * 客户配置信息
     */
    @Resource
    private CustomerConfigFacade customerConfigFacade;

    /**
     * 事业部信息
     */
    @Resource
    private CustomerFacade customerFacade;

    /**
     * 配置中心
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 事业部启用状态-2
     */
    private static final byte DEPARTMENT_STATUS_ENABLE = 2;

    @Override
    public void execute(ExpressOrderContext expressOrderContext) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("快运客户配置信息校验开始");
            // 账号信息校验
            checkBasicTraderInfo(expressOrderContext);
            // 事业部信息校验
            checkEbuInfo(expressOrderContext);
            LOGGER.info("快运客户配置信息校验结束");
        } catch (BusinessDomainException e) {
            LOGGER.error("快运客户配置信息失败", e.getMessage());
            throw e;
        } catch (Exception e) {
            LOGGER.error("快运客户配置信息校验异常", e);
            Profiler.functionError(callerInfo);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL, e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 账号信息校验
     */
    private void checkBasicTraderInfo(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel expressOrderModel = expressOrderContext.getOrderModel();

        CustomerConfig customerConfig = customerConfigFacade.getCustomerConfig(expressOrderContext);
        // 青龙业主号校验
        if (customerConfig == null) {
            LOGGER.error("客户配置信息校验失败，未查到相关客户信息");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0007.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0007.desc());
        }

        //设置信任商家标示
        TrustSellerUtil.setTrustCustomerWeightVolume(expressOrderModel, customerConfig);
        LOGGER.info("客户配置信息是否信任商家：" + TrustSellerUtil.isTrustWeightVolume(expressOrderModel));

        // 补全账号id
        expressOrderModel.getComplementModel().complementAccountId(this, customerConfig.getCustomerId());
        // 补全账号名称
        expressOrderModel.getComplementModel().complementAccountName(this, customerConfig.getCustomerName());

        if (OrderTypeEnum.RETURN_ORDER == expressOrderModel.getOrderType()) {
            LOGGER.info("客户配置信息校验，逆向单不检验客户信息");
            return;
        }

        //修改场景仓出库发货，不检验客户信息
        if (BusinessSceneEnum.MODIFY.getCode().equals(expressOrderModel.getOrderBusinessIdentity().getBusinessScene())
                && ModifySceneRuleUtil.isOutboundDelivery(expressOrderModel)) {
            LOGGER.info("修改场景仓出库发货，不检验客户信息");
            return;
        }

        if (ModifySceneRuleUtil.isPickupTransferStation(expressOrderModel)
                || ModifySceneRuleUtil.isNoTaskFinishCollect(expressOrderModel)
                || ModifySceneRuleUtil.isModifyOpMode(expressOrderModel)) {
            LOGGER.info("揽收转站、跨站截单、无任务揽收、修改运营模式策略,不检验客户信息");
            return;
        }

        if (ModifySceneRuleUtil.isModifyQuantity(expressOrderModel)
                && SystemCallerEnum.PDA == expressOrderModel.getChannel().getSystemCaller()) {
            LOGGER.info("终端修改包裹数策略,不检验客户信息");
            return;
        }

        //青龙业主号校验
        if (!TraderOperateStateEnum.normal.getCode().equals(customerConfig.getTraderOperateState())) {
            LOGGER.error("青龙业主号:{}状态异常，TraderOperateState:{}"
                    , expressOrderModel.getCustomer().getAccountNo()
                    , customerConfig.getTraderOperateState());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withCustom("客户配置信息校验失败,商家青龙业主号状态异常")
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.desc());
        }

        //云仓VMI订单校验
        if (expressUccConfigCenter.isClpsVmiCustomerConfigSwitch()) {
            checkCLPSVMI(expressOrderModel, customerConfig);
        }

        //若结算方式不是月结，则不进行商家配置校验
        if (expressOrderModel.getFinance() == null || SettlementTypeEnum.MONTHLY_PAYMENT != expressOrderModel.getFinance().getSettlementType()) {
            LOGGER.info("结算方式不是月结，则不进行商家配置校验");
            return;
        }

        if (SystemCallerUtil.currentIsSupplyOFC(expressOrderModel)
                && SupplyChainDeliveryOrderSignUtil.currentFlag(expressOrderModel)) {
            LOGGER.info("仓配接配，不校验月结账号");
            return;
        }

        //结算账户不为空,则通过结算账号校验账户是否开通月结，否则校验配送履约账号是否开通月结
        if (StringUtils.isNotBlank(expressOrderModel.getFinance().getSettlementAccountNo())) {
            LOGGER.info("有结算账号，SettlementAccountNo={}", expressOrderModel.getFinance().getSettlementAccountNo());
            //快运结算账号传的是事业部，需要通过事业部查到履约账号再校验
            //获取事业部信息
            DeptResponse deptInfo = customerFacade.getDept(expressOrderModel.getFinance().getSettlementAccountNo());
            if (deptInfo == null) {
                LOGGER.error("客户配置信息校验失败，未查到相关事业部信息");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0020.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0020.desc());
            }
            if (StringUtils.isBlank(deptInfo.getBdOwnerNo())) {
                LOGGER.error("客户配置信息校验失败，未查到相关事业部的青龙业主号");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0021.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0021.desc());
            }
            BasicTraderResponse basicTraderResponse = customerConfigFacade.getCustomerConfig(deptInfo.getBdOwnerNo());
            //青龙业主号校验
            if (!TraderOperateStateEnum.normal.getCode().equals(basicTraderResponse.getTraderOperateState())) {
                LOGGER.error("青龙业主号:{}状态异常，TraderOperateState:{}"
                        , expressOrderModel.getFinance().getSettlementAccountNo()
                        , basicTraderResponse.getTraderOperateState());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL)
                        .withCustom("客户配置信息校验失败,商家青龙业主号状态异常")
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.desc());
            }
            if (expressUccConfigCenter.isFreightValidateMonthlyPaymentSwitch()) {
                LOGGER.info("快运校验商家是否开通月结：校验");
                if (OrderTypeEnum.DELIVERY.equals(expressOrderModel.getOrderType()) && !SettlementTypeEnum.MONTHLY_PAYMENT.getTraderSignCode().contains(TraderSignUtils.getTraderSignCode(basicTraderResponse.getTraderSign(), TraderSignEnum.SETTLEMENT.getCode()))) {
                    LOGGER.error("结算方式校验失败,商家结算账户未开通月结结算方式");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL).withCustom("客户配置信息校验失败,结算方式校验失败")
                            .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.subCode())
                            .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.desc());
                }
            } else {
                LOGGER.info("快运校验商家是否开通月结：不校验");
            }

        } else {
            if (expressUccConfigCenter.isFreightValidateMonthlyPaymentSwitch()) {
                LOGGER.info("快运校验商家是否开通月结：校验");
                if (OrderTypeEnum.DELIVERY.equals(expressOrderModel.getOrderType()) && !SettlementTypeEnum.MONTHLY_PAYMENT.getTraderSignCode().contains(TraderSignUtils.getTraderSignCode(customerConfig.getTraderSign(), TraderSignEnum.SETTLEMENT.getCode()))) {
                    LOGGER.error("结算方式校验失败,商家结算账户未开通月结结算方式");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL).withCustom("客户配置信息校验失败,结算方式校验失败")
                            .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.subCode())
                            .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.desc());
                }
            } else {
                LOGGER.info("快运校验商家是否开通月结：不校验");
            }
        }
    }

    /**
     * 校验事业部信息
     */
    private void checkEbuInfo(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        if (SystemCallerUtil.currentIsSupplyOFC(orderModel) && SupplyChainDeliveryOrderSignUtil.currentFlag(orderModel)) {
            LOGGER.info("仓配接配，不校验事业部信息");
            return;
        }

        //获取事业部信息
        DeptResponse deptInfo = customerFacade.getDept(orderModel.getCustomer().getAccountNo2());
        if (deptInfo == null) {
            LOGGER.error("客户配置信息校验失败，未查到相关事业部信息");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0020.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0020.desc());
        }
        if (StringUtils.isBlank(orderModel.getCustomer().getAccountName2())) {
            orderModel.getComplementModel().complementAccountName2(this, deptInfo.getDeptName());
        }
        //产品：余继国，修改场景仓出库发货，不需要校验事业部信息，也不需要更新信任商家标识
        if (BusinessSceneEnum.MODIFY.getCode().equals(orderModel.getOrderBusinessIdentity().getBusinessScene())
                && ModifySceneRuleUtil.isOutboundDelivery(orderModel)) {
            LOGGER.info("修改场景仓出库发货，不需要校验事业部信息");
            return;
        }

        if (ModifySceneRuleUtil.isPickupTransferStation(orderModel)
                || ModifySceneRuleUtil.isNoTaskFinishCollect(orderModel)
                || ModifySceneRuleUtil.isModifyOpMode(orderModel)) {
            LOGGER.info("揽收转站、跨站截单、无任务揽收、修改运营模式策略,不检验事业部信息");
            return;
        }

        if (ModifySceneRuleUtil.isModifyQuantity(orderModel)
                && SystemCallerEnum.PDA == orderModel.getChannel().getSystemCaller()) {
            LOGGER.info("终端修改包裹数策略,不检验事业部信息");
            return;
        }

        // 校验逾期状态
        if (deptInfo.getExpireStatus()) {
            if (OrderTypeEnum.RETURN_ORDER == orderModel.getOrderType()) {
                LOGGER.info("逆向订单-不需要校验事业部逾期");
            } else {
                LOGGER.error("客户配置信息校验失败，事业部逾期");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0022.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0022.desc());
            }
        }
        // 下单校验启用状态
        if (BusinessSceneUtil.isCreate(expressOrderContext) && expressUccConfigCenter.isFreightValidateDepartmentStatusSwitch()) {
            if (OrderTypeEnum.RETURN_ORDER == orderModel.getOrderType()) {
                LOGGER.info("逆向订单-不需要校验事业部启用");
            } else if (DEPARTMENT_STATUS_ENABLE != deptInfo.getStatus()) {
                LOGGER.error("客户配置信息校验失败，事业部未启用");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0023.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0023.desc());
            }
        }
        //预收款校验
        if (orderModel.isFreightB2C()) {
            //快运B2C才进行预收款校验
            validateWithHoldingInAdvance(expressOrderContext, deptInfo);
        }
        //设置信任商家标示
        TrustSellerUtil.setTrustCustomerWeightVolume(orderModel, deptInfo);
        LOGGER.info("事业部信息是否信任商家：" + TrustSellerUtil.isTrustWeightVolume(orderModel));
        //是否整车询价放到上下文中
        expressOrderContext.putExtMaps(ContextInfoEnum.VEHICLE_INQUIRY.getCode(), deptInfo.getVehicleInquiry());
    }

    /**
     * 预收款校验
     *
     * @return
     */
    private void validateWithHoldingInAdvance(ExpressOrderContext context, DeptResponse deptInfo) {
        ExpressOrderModel orderModel = context.getOrderModel();
        if (BusinessSceneEnum.MODIFY.getCode().equals(orderModel.getOrderBusinessIdentity().getBusinessScene())) {
            LOGGER.info("修改场景-不需要预收款校验");
            return;
        }
        if (OrderTypeEnum.RETURN_ORDER == orderModel.getOrderType()) {
            LOGGER.info("逆向订单-不需要预收款校验");
            return;
        }
        // 仓配切配，systemCaller=SupplyOFC 并且 orderSign.supplyChainDelivery=1，跳过
        if (SystemCallerUtil.currentIsSupplyOFC(orderModel)
                && SupplyChainDeliveryOrderSignUtil.currentFlag(orderModel)) {
            LOGGER.info("渠道和订单标识校验通过，不需要预收款校验");
            return;
        }
        if (!Boolean.TRUE.equals(deptInfo.getAdvanceCollectionSeller())) {
            LOGGER.info("事业部：{}不是预收款商家，不需要预收款校验", orderModel.getCustomer().getAccountNo2());
            return;
        }
        if (deptInfo.getAdvanceInactiveAmount() != null) {
            BigDecimal estimateSurplusAmount = null;
            try {
                //查询预收款余额额度
                estimateSurplusAmount = customerFacade.queryAdvanceSurplusAnalysis(orderModel.getOperator(), orderModel.getCustomer().getAccountNo2());
            } catch (Exception e) {
                LOGGER.error("预收款校验信息查询异常，不拒单", e);
            }

            if (estimateSurplusAmount != null) {
                //预估可用余额是否小于事业部预收停用额，小于等于停用额拒单
                if (estimateSurplusAmount.longValue() <= deptInfo.getAdvanceInactiveAmount()) {
                    String message = String.format("您好，当前事业部对应的账户余额已不足，剩余%s元，请充值后下单，或更换其他事业部", estimateSurplusAmount.longValue());
                    LOGGER.error(message);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                            .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0026.subCode()).withCustom(message);
                }
            }
        } else {
            LOGGER.info("商家设置的预收停用额为空，不需要预收款校验");
        }

        if (orderModel.isFreightFTL()) {
            LOGGER.info("整车业务-跳过收发管家实时预占");
            return;
        }

        SettlementTypeEnum settlementType = orderModel.getFinance().getSettlementType();
        if (SettlementTypeEnum.MONTHLY_PAYMENT != settlementType) {
            // 非寄付月结 不处理
            LOGGER.info("非寄付月结,不获取预占模式");
            return;
        }

        // 获取预估运费
        Money estimateAmount = orderModel.getFinance().getEstimateAmount();
        if (null == estimateAmount || null == estimateAmount.getAmount()) {
            LOGGER.info("预估运费为空，不获取预占模式");
            return;
        }

        // 获取预占金额模式
        boolean isSupplyChain = SystemCallerUtil.currentIsSupplyOFC(orderModel)
                    && SupplyChainDeliveryOrderSignUtil.currentFlag(orderModel);
        if (!isSupplyChain) {
            // 只有非仓配接配获取
            Integer preoccupyMode = TraderSignUtils.getTraderSign(deptInfo.getDeptMark(), DeptMarkEnum.PREOCCUPY_MODE.getCode());
            if (null != preoccupyMode && !OccupyModeEnum.NON.getCode().equals(preoccupyMode)) {
                // 开通收发管家，存在预占模式
                LOGGER.info("预占模式为: {}", preoccupyMode);
                orderModel.complement().complementOccupyMode(this, preoccupyMode);
                // 上下文打标，用于判断是否需要执行预占能力
                context.putExtMaps(ContextInfoEnum.YISHOUFA.getCode(), preoccupyMode);
            }
        }
    }

    /**
     * 云仓VMI订单校验
     * 若识别是云仓VMI订单，则青龙业主号中的商家类型=零售服务内单账号且商家子类型必须为云仓VMI类型，不满足拒单；
     * 若识别不是云仓VMI订单，若青龙业主号同时满足：1）商家类型=零售服务内单账号；2）商家子类型=云仓VMI类型，拒单；
     */
    public void checkCLPSVMI(ExpressOrderModel expressOrderModel, CustomerConfig customerConfig) {
        if(!expressOrderModel.isFreightB2C()){
            LOGGER.info("只有B2C云仓VMI订单校验");
            return;
        }
        //只有接单场景校验
        if (!BusinessSceneEnum.CREATE.getCode().equals(expressOrderModel.getOrderBusinessIdentity().getBusinessScene())) {
            LOGGER.info("只有接单场景校验");
            return;
        }
        if (!(OrderTypeEnum.DELIVERY == expressOrderModel.getOrderType())) {
            LOGGER.info("只有正向单进行云仓VMI订单校验");
            return;
        }
        checkCLPSVMIConfig(expressOrderModel, customerConfig);
    }
}
