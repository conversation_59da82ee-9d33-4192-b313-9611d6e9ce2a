package cn.jdl.oms.express.lm.extension.b2c.customer;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CustomerConfig;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.customer.ICustomerConfigExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.AccountBookFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.AccountBookTranslator;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.TraderOperateStateEnum;
import cn.jdl.oms.express.shared.common.dict.TraderSignEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.dict.UnifiedSubErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.TraderSignUtils;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * LM-B2C-客户配置信息校验扩展点实现
 * @Version 1.0
 */
@Extension(code = ExpressOrderProduct.CODE)
public class LMB2CCustomerConfigExtension implements ICustomerConfigExtension {

    private static final Logger LOGGER = LoggerFactory.getLogger(LMB2CCustomerConfigExtension.class);

    /**
     * 客户配置信息
     */
    @Resource
    private CustomerConfigFacade customerConfigFacade;

    /**
     * 现结余额查询转换
     */
    @Resource
    private AccountBookTranslator accountBookTranslator;

    @Resource
    private AccountBookFacade accountBookFacade;

    /**
     * ucc
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * traderSign 标位1
     */
    private static final String SIGN_TYPE_1 = "1";

    /**
     * traderSign 标位0
     */
    private static final String SIGN_TYPE_0 = "0";

    /**
     * 隐私通话key
     */
    private static final String HIDE_PRIVACY_KEY = "hidePrivacyType";

    /**
     * 开箱验货key
     */
    private static final String UNPACKING_INSPECTION = "unpackingInspection";

    /**
     * 明细揽收key
     */
    private static final String PICKUP_DETAIL = "pickUpDetailType";

    /**
     * 商家类别：公司内部
     */
    private static final int IN_COMPANY = 1003;

    /**
     * 商家类别：分拣退货
     */
    private static final int SORT_RETURN = 1004;

    /**
     * 最大最小重量 TODO 需改为配置化
     */
    private final BigDecimal minTotalWeight = new BigDecimal(0);

    private final BigDecimal maxTotalWeight = new BigDecimal(9999000);

    /**
     * 预估金额范围 0~1000000
     */
    private static final BigDecimal MIN_ESTIMATE_AMOUNT = new BigDecimal(0);
    private static final BigDecimal MAX_ESTIMATE_AMOUNT = new BigDecimal(1000000);
    /**
     * 明细揽收-缺量揽收
     */
    private static final String PICKUP_DETAIL_2 = "2";

    /**
     * 明细揽收商品数量最大值
     */
    private static final BigDecimal MAX_TOTAL_GOODS_NUM = new BigDecimal(99999);

    /**
     * 商家子类别为SOP类型
     */
    private static final Integer SUB_TRADER_MOLD_SOP = 2001;

    /**
     * 渠道编码为售后单（0090001）
     */
    public static final String AFTER_SALE_CHANNEL_NO = "0090001";

    /**
     * 渠道编码为京东平台（0010001）
     */
    public static final String JD_CHANNEL_NO = "0010001";


    @Override
    public void execute(ExpressOrderContext expressOrderContext) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("LM-B2C-客户配置信息校验开始");
            ExpressOrderModel expressOrderModel = expressOrderContext.getOrderModel();
            String accountNo = expressOrderModel.getCustomer().getAccountNo();
            if (StringUtils.isNotBlank(accountNo)) {
                checkBasicTraderInfo(expressOrderContext);
            }
            LOGGER.info("LM-B2C-客户配置信息校验结束");
        } catch (Exception e) {
            LOGGER.error("LM-B2C-客户配置信息校验失败！", e);
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 功能:客户信息校验
     */
    private void checkBasicTraderInfo(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel expressOrderModel = expressOrderContext.getOrderModel();

        CustomerConfig customerConfig = customerConfigFacade.getCustomerConfig(expressOrderContext);

        // 青龙业主号校验
        if (customerConfig == null) {
            LOGGER.error("客户配置信息校验失败，未查到相关客户信息");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0007.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0007.desc());
        }
        // 补全账号id
        expressOrderModel.getComplementModel().complementAccountId(this, customerConfig.getCustomerId());
        // 补全账号名称
        expressOrderModel.getComplementModel().complementAccountName(this, customerConfig.getCustomerName());

        if (OrderTypeEnum.RETURN_ORDER == expressOrderModel.getOrderType()) {
            LOGGER.info("客户配置信息校验，逆向单不检验客户信息");
            return;
        }
        //青龙业主号校验
        if (!TraderOperateStateEnum.normal.getCode().equals(customerConfig.getTraderOperateState())) {
            LOGGER.error("青龙业主号:{}状态异常，TraderOperateState:{}"
                    , expressOrderModel.getCustomer().getAccountNo()
                    , customerConfig.getTraderOperateState());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withCustom("客户配置信息校验失败,商家青龙业主号状态异常")
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.desc());
        }

        //结算方式校验
        checkSettType(expressOrderModel, customerConfig);
    }

    /**
     * 功能: 结算方式校验
     * @param:
     * @return:
     * @throw:
     * @description:
     */
    private void checkSettType(ExpressOrderModel expressOrderModel, CustomerConfig customerConfig) {
        //修改场景未传不做校验
        if (BusinessSceneEnum.MODIFY.getCode().equals(expressOrderModel.getOrderBusinessIdentity().getBusinessScene())) {
            if (null == expressOrderModel.getFinance() || null == expressOrderModel.getFinance().getSettlementType()) {
                return;
            }
        }
        if (!expressOrderModel.getFinance().getSettlementType().getTraderSignCode().contains(Objects.requireNonNull(TraderSignUtils.getTraderSignCode(customerConfig.getTraderSign(), TraderSignEnum.SETTLEMENT.getCode())))) {
            LOGGER.error(String.format("客户配置信息校验失败,商家未开通%s结算方式", expressOrderModel.getFinance().getSettlementType().getDesc()));
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.subCode())
                    .withCustom(String.format("客户配置信息校验失败,商家未开通%s结算方式", expressOrderModel.getFinance().getSettlementType().getDesc()));
        }
    }
}
