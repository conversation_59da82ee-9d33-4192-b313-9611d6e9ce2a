## FEATURE:

[描述要开发的Java功能 - 具体说明功能需求、业务场景和技术要求]
1、回传接口支持传入配送信息中的expectPickupStartTime、expectPickupEndTime
2、b2c回传持久化扩展点中，调用持久化接口，支持传入expectPickupStartTime、expectPickupEndTime
注意：本需求不需要新增扩展点、不需要使用数据库
实现需要参考下面的功能提示词
## EXAMPLES:

[说明`core/examples/`文件夹中的相关示例，解释如何参考这些代码模式和最佳实践]

## DOCUMENTATION:

[列出开发过程中需要参考的文档资源：
- API文档链接
- 第三方库文档
- 企业内部文档
- 相关技术规范]

### 功能提示词



[其他考虑因素和特殊要求：
- 现有系统集成约束
- 性能要求
- 安全要求
- 企业中间件使用偏好
- AI开发时容易遗漏的要点]

## MIDDLEWARE HINTS:

[如果功能涉及特定中间件，可以提供关键词提示：
- 需要远程调用服务 → 会匹配JSF
- 需要消息队列处理 → 会匹配JMQ
- 需要缓存功能 → 会匹配JIMDB
- 需要配置管理 → 会匹配DUCC
- 需要日志记录 → 会匹配DongLog]