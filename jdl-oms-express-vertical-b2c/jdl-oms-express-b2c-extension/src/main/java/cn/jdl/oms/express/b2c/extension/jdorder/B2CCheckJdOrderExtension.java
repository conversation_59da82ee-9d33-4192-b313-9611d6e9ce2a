package cn.jdl.oms.express.b2c.extension.jdorder;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.JdOrderInfo;
import cn.jdl.oms.express.domain.dto.AddressInfoDto;
import cn.jdl.oms.express.domain.dto.ConsigneeInfoDto;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.jdorder.ICheckJdOrderExtension;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.jdorder.GetJdOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.jdorder.SOPBlackListFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.jdorder.GetJdOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.jdorder.SOPBlackListFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.jdorder.SOPBlackListFacadeResponse;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSignEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.horz.ext.jdorder.CheckJdOrderExtension;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.BusinessConstants;
import cn.jdl.oms.express.shared.common.constant.MagicCommonConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.SystemSubCallerEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.shared.common.utils.SendPayMapUtil;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;


/**
 * @Package: cn.jdl.oms.express.b2c.extension.jdorder
 * @ClassName: JdOrderCheckExtension
 * @Description: 零售订单校验及赋值
 * @Author: liufarui
 * @CreateDate: 2021/4/29
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version: V1.0
 */
@Extension(code = ExpressOrderProduct.CODE)
public class B2CCheckJdOrderExtension implements ICheckJdOrderExtension {
    private static final Logger LOGGER = LoggerFactory.getLogger(B2CCheckJdOrderExtension.class);

    @Resource
    private GetJdOrderFacade getJdOrderFacade;

    /**
     * state 完成
     */
    private static final int DONE = 30;

    /**
     * state 收款确认
     */
    private static final int RECEIPT_CONFIRMATION = 26;

    /**
     * 付款方式 - 代收货款「代收货款金额为0」
     */
    private static final int PAYMENT_TYPE_COLLECTION = 1;

    /**
     * 付款方式
     * PAYMENT_TYPE=1：代收货款「代收货款金额为0」
     * PAYMENT_TYPE=0：非代收货款「需要删除下单入参的产品增值服务中的代收货款增值服务」
     */
    private static final int PAYMENT_TYPE_NOT_COLLECTION = 0;

    /**
     * 快递运输
     */
    private static final Integer EXPRESS_DELIVERY = 70;

    /**
     * 顺丰快递
     */
    private static final Integer SF_DELIVERY = 69;

    /**
     * 收银台标识
     * 直接透传给OFC
     */
    private static final String SEND_PAY = "sendpay";

    private static final String SEND_PAY_MAP = "sendPayMap";

    /**
     * 配送方式
     * 直接透传给OFC
     */
    private static final String SHIPMENT_TYPE = "shipmentType";

    /**
     * idSopShipmentType
     * 直接透传给OFC
     */
    private static final String ID_SOP_SHIPMENT_TYPE = "idSopShipmentType";

    /**
     * shipType
     * 直接透传给OFC
     */
    private static final String SHIP_TYPE = "shipType";

    /**
     * paymentType
     * 直接透传给OFC
     */
    private static final String PAYMENT_TYPE = "paymentType";

    /**
     * 厂直订单有效且未完结标准
     */
    private static final List<Integer> EFFECTIVE_STATUS_FOR_DIRECT = Arrays.asList(7, 10, 16, 22);

    /**
     * UCC 配置「这里主要用到零售订单商家黑名单校验」
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * sop商家黑名单校验
     */
    @Resource
    private SOPBlackListFacade blackListFacade;

    /**
     * 商家基础资料查询
     */
    @Resource
    private CustomerConfigFacade customerConfigFacade;

    /**
     * 地址替换补齐下标阈值
     */
    private static final Integer JD_REPLACE_ADDRESS_CHECKRANGE = 5;

    @Resource
    private CheckJdOrderExtension checkJdOrderExtension;

    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        ExpressOrderModel orderModel = context.getOrderModel();
        try {
            LOGGER.info("零售订单校验及赋值扩展点执行开始");

            // 若为融合快运B2C，直接调快运扩展点
            if (UnitedB2CUtil.isUnitedFreightB2C(orderModel)) {
                checkJdOrderExtension.execute(context);
                return;
            }

            // 渠道编码不为京东平台，不做校验，在流程编排里面已经做了判断，这边只是做代码健壮性保护
            if (!BusinessConstants.JD_CHANNEL_NO.equals(orderModel.getChannel().getChannelNo())) {
                return;
            }

            if(OrderConstants.YES_VAL.equals(orderModel.getAttachment(OrderConstants.JD_ORDER_TRUST_FLAG))){
                LOGGER.info("零售订单信任标识=1跳过零售订单相关校验");
                return;
            }

            List<GetJdOrderFacadeResponse> jdOrderList = getJdOrderFacade.getJdOrderList(context);
            if (CollectionUtils.isEmpty(jdOrderList)) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL)
                        .withCustom("获取零售订单失败");
            }
            GetJdOrderFacadeResponse jdOrder = jdOrderList.get(0);
            SystemCallerEnum systemCaller = context.getOrderModel().getChannel().getSystemCaller();
            // 1 CLPS直接跳过 上游已经做了地址处理
            // 2 SUPPLY_OFC直接跳过，不做店铺校验，不做地址替换
            if (SystemCallerEnum.CLPS != systemCaller && SystemCallerEnum.SUPPLY_OFC != systemCaller) {

                if (jdOrder.isNeedCnAddress()) {

                    LOGGER.info("英文APP.跳过后续逻辑.orderId:{}", jdOrder.getOrderId());

                } else if (isPopConsolidation(jdOrder)) {

                    LOGGER.info("零售POP集运订单.跳过后续逻辑.orderId:{}", jdOrder.getOrderId());

                } else if (isTransitMode(jdOrder, context.getOrderModel())) {

                    LOGGER.info("中转模式.跳过后续逻辑.orderId:{}", jdOrder.getOrderId());

                } else if (isVcIntelOrder(jdOrder)) {
                    //厂直国际校验并置换地址
                    LOGGER.info("零售厂直国际订单orderId:{}", jdOrder.getOrderId());
                    fdcCheckAndReplaceAddress(context);
                } else if (isPassedOrder(jdOrder, context.getCustomerConfig().getTraderMold(), orderModel.getChannel().getChannelNo())) {

                    LOGGER.info("零售厂直订单or京东商城订单.跳过后续逻辑.orderId:{}", jdOrder.getOrderId());

                } else if (isJingXiOrder(jdOrder, context.getCustomerConfig().getSubTraderMold())) {

                    LOGGER.error("京喜订单校验失败：当subTraderMold == 100322 且 sendPay 997位 != 1时，拒单操作");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("京喜订单校验失败，当商家类型为京喜自营时仅支持京喜店铺订单");

                } else if (isZhengPinJianDingSendToJianDingCangOrder(jdOrder)) {

                    LOGGER.info("SendPayMap[624==1]判断为正品鉴定第一段发送到鉴定仓的单子，不进行地址和收件人信息替换.orderId:{}", jdOrder.getOrderId());

                } else if (isPaipaiQualityInspectionOrder(jdOrder)
                        && BatrixSwitch.applyByBoolean(BatrixSwitchKey.B2C_PAIPAI_CHECK_JD_ORDER_SWITCH)) {

                    LOGGER.info("SendPayMap[624==7]判断为拍拍质检订单，不进行地址和收件人信息替换.orderId:{}", jdOrder.getOrderId());

                } else if (OrderConstants.YES_VAL.equals(MapUtils.getString(orderModel.getOrderSign(), OrderSignEnum.GLOBAL_DIRECT_DELIVERY.getCode()))
                        && BatrixSwitch.applyByBoolean(BatrixSwitchKey.B2C_CHECK_JD_POP_SWITCH)) {

                    LOGGER.info("orderSign[globalDirectDelivery==1]判断为【全球售】订单，不进行地址和收件人信息替换.orderId:{}", jdOrder.getOrderId());

                } else if (isPersonalCustomization(jdOrder)
                        && BatrixSwitch.applyByBoolean(BatrixSwitchKey.PERSONAL_CUSTOMIZATION_CHECK_SWITCH)){

                    LOGGER.info("orderSign[sendPayMap 819=5 且 1243=1]判断为【】订单，不进行地址和收件人信息替换.orderId:{}", jdOrder.getOrderId());
                } else {
                    Integer traderMold = context.getCustomerConfig().getTraderMold();
                    // 出库商家ID
                    String outboundVenderId = null;
                    if (MapUtils.isNotEmpty(context.getOrderModel().getChannel().getExtendProps())) {
                        outboundVenderId = context.getOrderModel().getChannel().getExtendProps().get(AttachmentKeyEnum.OUTBOUND_VENDER_ID.getKey());
                    }
                    // 厂直商家 或者 非POP渠道，执行订单有效性校验并替换地址
                    if (OrderConstants.TRADER_MOLD_CHANGZHI.equals(traderMold)
                            || SystemCallerEnum.POP != systemCaller) {
                        // 校验sop黑名单里面是否有这个商家，无则返回
                        SOPBlackListFacadeRequest request = new SOPBlackListFacadeRequest();
                        request.setCustomerCode(context.getOrderModel().getCustomer().getAccountNo());
                        SOPBlackListFacadeResponse blackListFacadeBlackList = blackListFacade.isInBlackList(request);
                        if (!blackListFacadeBlackList.isInBlackList()) {
                            //pop 厂直 订单有效性校验
                            if (checkOrder(context, jdOrder, outboundVenderId)) {
                                //pop 厂直 地址置换
                                replaceAddress(context, jdOrder, outboundVenderId);
                            }
                        } else {
                            LOGGER.info("sop黑名单，跳过零售订单校验");
                        }
                    }
                }
            }
            // 订单数据补全
            completeModel(jdOrder, orderModel);
            LOGGER.info("零售订单校验及赋值扩展点执行结束");
        } catch (InfrastructureException infrastructureException) {
            Profiler.functionError(callerInfo);
            LOGGER.error("零售订单校验及赋值扩展点执行异常, traceId={}", orderModel.traceId(), infrastructureException);
            throw infrastructureException;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("零售订单校验及赋值扩展点执行异常, traceId={}", orderModel.traceId(), exception);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL, exception);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 厂直国际订单校验地址转换
     * @param context
     */
    private void fdcCheckAndReplaceAddress(ExpressOrderContext context) {
        List<JdOrderInfo> jdOrderInfoList = context.getJdOrderInfoList();
        JdOrderInfo jdOrderInfo = jdOrderInfoList.get(0);
        if (jdOrderInfo == null) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL)
                    .withCustom("零售厂直国际订单返回为空对象");
        }
        JdOrderInfo.VcIntelOrder vcIntelOrder = jdOrderInfo.getVcIntelOrder();
        if (vcIntelOrder == null) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL)
                    .withCustom("零售厂直国际订单返回为空对象");
        }
        if(StringUtils.isBlank(vcIntelOrder.getReceiveAddress())){
            LOGGER.error("零售厂直国际订单为空或详细地址为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL)
                    .withCustom("零售厂直国际订单为空或详细地址为空");
        }
        if (StringUtils.isBlank(vcIntelOrder.getReceiveMobile()) && StringUtils.isBlank(vcIntelOrder.getReceiveTel())){
            LOGGER.error("零售厂直国际订单查询返回的手机和电话都为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL)
                    .withCustom("零售厂直国际订单查询返回的手机和电话都为空");
        }
        LOGGER.info("零售厂直国际订单收件人信息:{}",JSONUtils.beanToJSONDefault(vcIntelOrder));
        // 状态校验
        jdOrderStatusCheck(jdOrderInfoList);
        // 地址转换
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        ConsigneeInfoDto consigneeInfoDto = new ConsigneeInfoDto();
        AddressInfoDto addressInfoDto = new AddressInfoDto();
        consigneeInfoDto.setConsigneeName(vcIntelOrder.getReceiveName());
        consigneeInfoDto.setConsigneePhone(vcIntelOrder.getReceiveTel());
        consigneeInfoDto.setConsigneeMobile(vcIntelOrder.getReceiveMobile());
        addressInfoDto.setProvinceNo(String.valueOf(vcIntelOrder.getProvinceId()));
        addressInfoDto.setProvinceName(null);
        addressInfoDto.setCityNo(String.valueOf(vcIntelOrder.getCityId()));
        addressInfoDto.setCityName(null);
        addressInfoDto.setCountyNo(String.valueOf(vcIntelOrder.getCountyId()));
        addressInfoDto.setCountyName(null);
        addressInfoDto.setTownNo(null);
        addressInfoDto.setTownName(null);
        addressInfoDto.setAddress(vcIntelOrder.getReceiveAddress());
        consigneeInfoDto.setAddressInfoDto(addressInfoDto);
        modelCreator.setConsigneeInfo(consigneeInfoDto);
        context.getOrderModel().getConsignee().complementJdOrderConsignee(modelCreator);
    }

    /**
     * 功能: 订单有效性校验
     *
     * @param: jdOrderList 从零售端查到的订单列表
     * @return: boolean
     * @throw: InfrastructureException
     * @description: 调零售订单中间件查询接口，返回结果yn=1且state不等于26（收款确认）和30（完成）则渠道订单号有效，否则为无效，
     * 若渠道订单号有多个，则需要循环遍历查询每个渠道订单号，全都为有效才通过有效性校验，否则拒单
     * @author: liufarui
     * @date: 2021/4/29 8:21 下午
     */
    private boolean checkValidate(List<GetJdOrderFacadeResponse> jdOrderList) {
        for (GetJdOrderFacadeResponse jdOrder : jdOrderList) {
            if (jdOrder == null) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL)
                        .withCustom("零售订单返回为空对象");
            }
            if (1 != jdOrder.getYn()
                    || DONE == jdOrder.getState()
                    || RECEIPT_CONFIRMATION == jdOrder.getState()) {
                LOGGER.error("订单校验失败，订单号：{}.", jdOrder.getOrderId());
                return false;
            }
        }
        return true;
    }

    /**
     * 功能: 订单数据补全
     *
     * @param: jdOrder 零售订单信息
     * @param: orderModel 订单中心纯配领域订单模型
     * @description: 渠道订单号通过有效性校验后，取零售订单中间件查询接口返回值里的sendpay、shipmenttype、paymenttype透传给OFC。
     * 若渠道订单号有多个，则取第一个渠道订单号的零售订单中间件查询结果
     * @author: liufarui
     * @date: 2021/4/29 8:59 下午
     */
    private void completeModel(GetJdOrderFacadeResponse jdOrder, ExpressOrderModel orderModel) {
        // 渠道订单号通过有效性校验后，取零售订单中间件查询接口返回值里的sendpay、shipmenttype、idSopShipmentType、shipType透传给OFC。
        String sendPay = jdOrder.getSendPay();
        String sendPayMap = jdOrder.getSendPayMap();
        Integer shipmentType = jdOrder.getShipmentType();
        Integer idSopShipmentType = jdOrder.getIdSopShipmentType();
        Integer shipType = jdOrder.getShipType();
        Integer paymentType = jdOrder.getPaymentType();
        LOGGER.info("零售订单有效性校验补齐sendPay:{}、sendPayMap:{}、shipmentType:{}、paymentType:{}",
                sendPay,sendPayMap,shipmentType,paymentType);
        orderModel.getExtendProps().put(SEND_PAY, sendPay);
        orderModel.getExtendProps().put(SHIPMENT_TYPE, shipmentType != null ? shipmentType.toString() : null);
        orderModel.getExtendProps().put(ID_SOP_SHIPMENT_TYPE, idSopShipmentType != null ? idSopShipmentType.toString() : null);
        orderModel.getExtendProps().put(SHIP_TYPE, shipType != null ? shipType.toString() : null);
        orderModel.getExtendProps().put(PAYMENT_TYPE, paymentType != null ? paymentType.toString() : null);

        //orderSign 源头直发
        if (StringUtils.isNotBlank(sendPayMap)){
            orderModel.getExtendProps().put(SEND_PAY_MAP, sendPayMap);
            if (SendPayMapUtil.isSourceDeliver(sendPayMap)){
                orderModel.putOrderSign(this, OrderSignEnum.ORIGIN_DIRECT.getCode(),"1");
            }
        }

        if (orderModel.getProductDelegate() != null
                && CollectionUtils.isNotEmpty(orderModel.getProductDelegate().getProducts())) {
            Product product = orderModel.getProductDelegate().getCodProduct();

            //孙志宇与张勇确认，零售支付方式为代收货款的，未下代收货款增值服务，不再补代收货款增值服务
            /*if (PAYMENT_TYPE_COLLECTION == jdOrder.getPaymentType()) {
                if (null == product) {
                    if (ProductEnum.isFreshMainProduct(orderModel.getProductDelegate().getMainProduct().getProductNo())){
                        product = orderModel.getProductDelegate().addProduct(AddOnProductEnum.LL_ZZ_DSHK);
                    }else {
                        product = orderModel.getProductDelegate().addProduct(AddOnProductEnum.JDL_COD_TOC);
                    }
                }

                if (null == product.getProductAttrs()) {
                    product.setProductAttrs(new HashMap<>());
                }
                // 若payment_type=1则给下单入参的产品增值服务中补全代收货款增值服务，代收货款金额为0
                if (!product.getProductAttrs().containsKey(AddOnProductAttrEnum.COD.getCode())) {
                    product.getProductAttrs().put(AddOnProductAttrEnum.COD.getCode(), "0");
                    LOGGER.info("payment_type=1,给下单的产品{}增值服务中补全代收货款增值服务,设置金额为0", JSONUtils.beanToJSONDefault(product));
                }
            }*/

            if (PAYMENT_TYPE_NOT_COLLECTION == paymentType && null != product) {
                // 若payment_type=0则删除下单入参的产品增值服务中的代收货款增值服务
                orderModel.getProductDelegate().removeProduct(product);
                LOGGER.info("payment_type=0,删除下单中的代收货款增值服务");
            }
        }

        // 地址赋值
        // 零售订单配送方式 & 非顺丰快递69 & 且非快递运输70
        if (!SF_DELIVERY.equals(shipmentType) && !EXPRESS_DELIVERY.equals(shipmentType)) {
            //shipmenttype不为69/70，idSopShipmentType不下发；
            orderModel.getExtendProps().remove(ID_SOP_SHIPMENT_TYPE);
            //零售地址
            String jdOrderAddress = jdOrder.getAddress();
            //上下文中地址【若流程中命中替换规则则是替换后的地址】
            String contextOrderAddress = orderModel.getConsignee().getAddress().getAddress();
            //赋值逻辑如下，idSopShipmentType不下发：
            //零售订单的收货地址为空，使上下文中的收件地址
            //零售订单的收货地址长度小于10，使用上下文中的收件地址
            //上下文中的收件地址长度小于6，使用零售订单的收货地址
            //上下文中的收件地址长度大于零售订单的收货地址，使用上下文中的收件地址
            //零售订单的收货地址包含上下文中收件地址字段的后六位内容，使用零售订单的收货地址
            //非以上情况，使用入参的收件地址兜底
            orderModel.getConsignee().getAddress().setAddress(makeUpToAddress(contextOrderAddress, jdOrderAddress, JD_REPLACE_ADDRESS_CHECKRANGE));
        }
        LOGGER.info("零售订单校验后收件人信息:{}",JSONUtils.beanToJSONDefault(orderModel.getConsignee()));
    }

    /**
     * 功能: 订单号校验
     *
     * @param: traderMold 商家类别
     * @param: systemCaller 调用方系统来源
     * @param: jdOrderInfoList 零售订单信息「会有多个订单信息」
     * @return: boolean
     * @description: 需校验的订单类型：非POP订单
     * @author: liufarui
     * @date: 2021/4/29 10:42 下午
     */
    private boolean checkOrderNo(ExpressOrderContext context) {
        SystemCallerEnum systemCaller = context.getOrderModel().getChannel().getSystemCaller();
        LOGGER.info("订单号校验开始：{}", systemCaller.getDesc());

        // POP订单直接返回
        if (SystemCallerEnum.POP == systemCaller) {
            return true;
        }

        List<GetJdOrderFacadeResponse> jdOrderList = getJdOrderFacade.getJdOrderList(context);
        if (CollectionUtils.isEmpty(jdOrderList)) {
            LOGGER.error("零售订单获取失败，交易客户订单号：{}.", context.getOrderModel().getCustomOrderNo());
            return false;
        }

        GetJdOrderFacadeResponse response = jdOrderList.get(0);
        if (StringUtils.isBlank(response.getAddress())){
            LOGGER.error("零售订单查询返回的详细地址为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL)
                    .withCustom("零售订单查询返回的详细地址为空");
        }
        if (StringUtils.isBlank(response.getMobile()) && StringUtils.isBlank(response.getPhone())){
            LOGGER.error("零售订单查询返回的手机和电话都为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL)
                    .withCustom("零售订单查询返回的手机和电话都为空");
        }
        // 校验处理规则：取零售订单中间件查询接口返回值里的收货人信息（姓名、手机号、座机、收货地址）
        // ，替换商家下单的收件人信息
        // ，同时置空收件人四级地址的ID和名称。
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        ConsigneeInfoDto consigneeInfoDto = new ConsigneeInfoDto();
        AddressInfoDto addressInfoDto = new AddressInfoDto();
        consigneeInfoDto.setConsigneeName(response.getCustomerName());
        consigneeInfoDto.setConsigneePhone(response.getPhone());
        consigneeInfoDto.setConsigneeMobile(response.getMobile());
        addressInfoDto.setProvinceNo(response.getProvince().toString());
        addressInfoDto.setProvinceName(null);
        addressInfoDto.setCityNo(response.getCity().toString());
        addressInfoDto.setCityName(null);
        addressInfoDto.setCountyNo(response.getCounty().toString());
        addressInfoDto.setCountyName(null);
        addressInfoDto.setTownNo(null);
        addressInfoDto.setTownName(null);
        addressInfoDto.setAddress(response.getAddress());
        consigneeInfoDto.setAddressInfoDto(addressInfoDto);
        modelCreator.setConsigneeInfo(consigneeInfoDto);

        context.getOrderModel().getConsignee().complementJdOrderConsignee(modelCreator);
        return true;
    }

    /**
     * 功能: 零售POP|厂直订单校验
     * @param: context
     * @return: boolean
     * @author: liqiang262
     * @date: 2022/8/23 10:42 上午
     */
    private boolean checkOrder(ExpressOrderContext context, GetJdOrderFacadeResponse jdOrder, String outboundVenderId) {
        LOGGER.info("零售POP|厂直订单校验开始 channelOrderNo:{}", context.getOrderModel().getChannel().getChannelOrderNo());
        Integer traderMold = context.getCustomerConfig().getTraderMold();
        List<JdOrderInfo> jdOrderList = context.getJdOrderInfoList();
        if (isJdVcOrder(traderMold, jdOrder, outboundVenderId)) {
            if (StringUtils.isBlank(context.getCustomerConfig().getExternalUniqueCode())) {
                LOGGER.error("零售厂直订单供应商简码为空,厂直商家请联系销售维护供应商简码，交易客户订单号：{}.", context.getOrderModel().getCustomOrderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL)
                        .withCustom("零售厂直订单供应商简码为空,厂直商家请联系销售维护供应商简码");
            }
            List<GetJdOrderFacadeResponse> jdVcOrderList = getJdOrderFacade.getJdVcOrderList(context);
            jdOrderList = context.getJdVcOrderInfoList();

            if (CollectionUtils.isEmpty(jdVcOrderList)) {
                // 通过 出库商家ID 二次查询匹配
                if (StringUtils.isNotBlank(outboundVenderId)) {
                    jdVcOrderList = getJdOrderFacade.getJdVcOrderListByOutboundVenderId(context, outboundVenderId);
                    jdOrderList = context.getJdVcOrderInfoList();
                }
            }

            if (CollectionUtils.isEmpty(jdVcOrderList)) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_GET_FAIL)
                        .withCustom("零售厂直订单查询失败");
            }
        }
        if (CollectionUtils.isEmpty(jdOrderList)) {
            LOGGER.error("零售POP|厂直订单获取失败，交易客户订单号：{}.", context.getOrderModel().getCustomOrderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL)
                    .withCustom("零售POP|厂直订单号校验失败");
        }
        JdOrderInfo jdOrderInfo = jdOrderList.get(0);
        if (jdOrderInfo == null) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL)
                    .withCustom("零售订单返回为空对象");
        }
        if (StringUtils.isBlank(jdOrderInfo.getAddress())){
            LOGGER.error("零售POP|厂直订单查询返回的详细地址为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL)
                    .withCustom("零售POP|厂直订单查询返回的详细地址为空");
        }
        if (StringUtils.isBlank(jdOrderInfo.getMobile()) && StringUtils.isBlank(jdOrderInfo.getPhone())){
            LOGGER.error("零售POP|厂直订单查询返回的手机和电话都为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL)
                    .withCustom("零售POP|厂直订单查询返回的手机和电话都为空");
        }
        //厂直
        if(isJdVcOrder(traderMold, jdOrder, outboundVenderId)){
            return checkJdVcOrder(context);
        }else{
            //非厂直
            return checkJdOrder(context);
        }
    }

    /**
     * 功能: 零售订单校验
     * @param: context
     * @return: boolean
     * 调零售订单中间件查询接口，返回结果yn=1且state不等于26（收款确认）和30（完成）则渠道订单号有效，否则为无效，
     * 若渠道订单号有多个，则需要循环遍历查询每个渠道订单号，全都为有效才通过有效性校验，否则拒单
     * @author: liqiang262
     * @date: 2022/48/23 10:42 上午
     */
    private boolean checkJdOrder(ExpressOrderContext context) {
        List<JdOrderInfo> jdOrderInfoList = context.getJdOrderInfoList();
        // 状态校验
        jdOrderStatusCheck(jdOrderInfoList);
        //渠道校验
        SystemCallerEnum systemCaller = context.getOrderModel().getChannel().getSystemCaller();
        if (context.getOrderModel().isB2C()) {
            if (SystemCallerEnum.ALLIN_PLATE.equals(systemCaller)) {
                LOGGER.info("渠道为一盘货跳过店铺校验");
                return true;
            }
        }
        // 零售自营跳过店铺校验
        if (SystemSubCallerEnum.RETAIL_DIRECT.getCode().equals(context.getOrderModel().getChannel().getSystemSubCaller())) {
            LOGGER.info("零售自营跳过店铺校验");
            return true;
        }
        // 跳过零售店铺校验标识为是，跳过店铺校验 todo 上线验证后去除开关默认执行
        String skipValidateVenderId = context.getOrderModel().getAttachment(AttachmentKeyEnum.SKIP_VALIDATE_VENDER_ID.getKey());
        if (OrderConstants.YES_VAL.equals(skipValidateVenderId)
                && BatrixSwitch.applyByBoolean(BatrixSwitchKey.SKIP_VALIDATE_VENDER_ID_SWITCH)) {
            LOGGER.info("跳过零售店铺校验标识为是，跳过店铺校验");
            return true;
        }
        // 出库商家ID
        String outboundVenderId = null;
        if (MapUtils.isNotEmpty(context.getOrderModel().getChannel().getExtendProps())) {
            outboundVenderId = context.getOrderModel().getChannel().getExtendProps().get(AttachmentKeyEnum.OUTBOUND_VENDER_ID.getKey());
        }
        //店铺校验
        boolean skipShopCheck = BatrixSwitch.applyByBoolean(BatrixSwitchKey.NOT_VC_ORDER_SKIP_SHOP_CHECK_SWITCH);
        for (JdOrderInfo jdOrder : jdOrderInfoList) {
            if (skipShopCheck) {
                LOGGER.info("非厂直订单跳过店铺校验开关开启");
                if (jdOrder.getOrderType() != null
                        && (18 == jdOrder.getOrderType() || 223 == jdOrder.getOrderType())) {
                    // 跳过店铺校验
                    LOGGER.info("非厂直零售订单{},订单类型{}跳过店铺校验", jdOrder.getOrderId(), jdOrder.getOrderType());
                } else {
                    // 利用订单号查询订单中间件查询出订单归属的商家ID与商家基础资料的的商家ID是否一致，不一致则判断订单中间件查询出订单归属的商家ID和随单下发的「出库商家ID」是否一致；
                    if (!jdOrder.getVenderId().equals(context.getCustomerConfig().getPopId())
                            && !jdOrder.getVenderId().equals(outboundVenderId)) {
                        LOGGER.error("所属京东商城店铺不在您的店铺列表中，请核对后重新尝试。订单校验失败，订单号：{}, venderId: {}, popId: {}, outboundVenderId: {}."
                                , jdOrder.getOrderId(), jdOrder.getVenderId(), context.getCustomerConfig().getPopId(), outboundVenderId);
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL)
                                .withCustom("所属京东商城店铺不在您的店铺列表中，请核对后重新尝试");
                    }
                }
            } else {
                LOGGER.info("非厂直订单跳过店铺校验开关关闭");
                // 利用订单号查询订单中间件查询出订单归属的商家ID与商家基础资料的的商家ID是否一致，不一致则判断订单中间件查询出订单归属的商家ID和随单下发的「出库商家ID」是否一致；
                if (!jdOrder.getVenderId().equals(context.getCustomerConfig().getPopId())
                        && !jdOrder.getVenderId().equals(outboundVenderId)) {
                    LOGGER.error("所属京东商城店铺不在您的店铺列表中，请核对后重新尝试。订单校验失败，订单号：{}, venderId: {}, popId: {}, outboundVenderId: {}."
                            , jdOrder.getOrderId(), jdOrder.getVenderId(), context.getCustomerConfig().getPopId(), outboundVenderId);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL)
                            .withCustom("所属京东商城店铺不在您的店铺列表中，请核对后重新尝试");
                }
            }
        }
        return true;
    }

    /**
     * 零售订单状态校验
     * @param jdOrderInfoList
     */
    private void jdOrderStatusCheck(List<JdOrderInfo> jdOrderInfoList){
        for (JdOrderInfo jdOrder : jdOrderInfoList) {
            if (1 != jdOrder.getYn()
                    || DONE == jdOrder.getState()
                    || RECEIPT_CONFIRMATION == jdOrder.getState()) {
                LOGGER.error("订单校验失败，订单号：{}.", jdOrder.getOrderId());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL)
                        .withCustom("订单有效性校验失败，商城订单无效或是已完成、收款确认状态");
            }
        }
    }

    /**
     * 功能: 零售普通厂直订单状态校验
     * @param: context
     * @return: boolean
     * @description: 需校验的订单类型：非POP订单
     * @author: liqiang262
     * @date: 2022/48/23 10:42 上午
     */
    private boolean checkJdVcOrder(ExpressOrderContext context) {
        List<JdOrderInfo> jdOrderInfoList = context.getJdVcOrderInfoList();
        for (JdOrderInfo jdOrder : jdOrderInfoList) {
            if (!EFFECTIVE_STATUS_FOR_DIRECT.contains(jdOrder.getState())) {
                LOGGER.info("校验订单状态-商城订单号:{}查询厂直订单状态orderState:{}无效或已完结",jdOrder.getOrderId(),jdOrder.getState());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.JD_ORDER_VALIDATE_FAIL)
                        .withCustom("商城订单号:"+jdOrder.getOrderId()+",厂直订单状态orderState:"+jdOrder.getState()+",无效或已完结");
            }
        }
        return true;
    }

    /**
     * 零售地址替换 pop 厂直
     * @param context
     */
    private void replaceAddress(ExpressOrderContext context, GetJdOrderFacadeResponse jdOrder, String outboundVenderId) {
        List<JdOrderInfo> jdOrderList = context.getJdOrderInfoList();
        Integer traderMold = context.getCustomerConfig().getTraderMold();
        if(isJdVcOrder(traderMold, jdOrder, outboundVenderId)){
            jdOrderList = context.getJdVcOrderInfoList();
        }
        JdOrderInfo jdOrderInfo = jdOrderList.get(0);
        // 校验处理规则：取零售查询接口返回值里的收货人信息（姓名、手机号、座机、收货地址）
        // ，替换商家下单的收件人信息
        // ，同时置空收件人四级地址的ID和名称。
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        ConsigneeInfoDto consigneeInfoDto = new ConsigneeInfoDto();
        AddressInfoDto addressInfoDto = new AddressInfoDto();
        consigneeInfoDto.setConsigneeName(jdOrderInfo.getCustomerName());
        consigneeInfoDto.setConsigneePhone(jdOrderInfo.getPhone());
        consigneeInfoDto.setConsigneeMobile(jdOrderInfo.getMobile());
        addressInfoDto.setProvinceNo(jdOrderInfo.getProvince().toString());
        addressInfoDto.setProvinceName(null);
        addressInfoDto.setCityNo(jdOrderInfo.getCity().toString());
        addressInfoDto.setCityName(null);
        addressInfoDto.setCountyNo(jdOrderInfo.getCounty().toString());
        addressInfoDto.setCountyName(null);
        addressInfoDto.setTownNo(null);
        addressInfoDto.setTownName(null);
        addressInfoDto.setAddress(jdOrderInfo.getAddress());
        consigneeInfoDto.setAddressInfoDto(addressInfoDto);
        modelCreator.setConsigneeInfo(consigneeInfoDto);
        context.getOrderModel().getConsignee().complementJdOrderConsignee(modelCreator);
    }

    /**
     * 判断是否厂直国际单，如果是需要进行地址数据替换。
     * <p>
     * sendPay 108=1/2/3，且 sendPay 124=7 且 orderType=18
     **/
    private boolean isVcIntelOrder(GetJdOrderFacadeResponse order) {

        if (order == null) {
            return false;
        }

        int orderType = order.getOrderType();
        String sendPay = order.getSendPay();
        if (orderType != 18 || !org.springframework.util.StringUtils.hasText(sendPay) || sendPay.length() < 124) {
            return false;
        }

        char c108 = sendPay.charAt(107);
        char c124 = sendPay.charAt(123);
        return c124 == '7'
                && (c108 == '1' || c108 == '2' || c108 == '3')
                ;
    }

    /**
     * 是否POP集运订单
     * <p>
     * PRD: https://joyspace.jd.com/pages/B4wTw24xa5CAH6n4yHYK
     *
     * @param order
     * @return
     */
    private boolean isPopConsolidation(GetJdOrderFacadeResponse order) {

        if (!expressUccConfigCenter.isNeedCheckPopConsolidation() || order == null) {
            return false;
        }

        // sendpay108=1 或2或3（代表主站售订单）且sendpay124=7 （代表集运订单）且orderType=22（代表POP）
        int orderType = order.getOrderType();
        String sendPay = order.getSendPay();

        if (orderType != 22 || StringUtils.isBlank(sendPay) || sendPay.length() < 124) {
            return false;
        }

        char c108 = sendPay.charAt(107);
        char c124 = sendPay.charAt(123);

        return c124 == '7' && (c108 == '1' || c108 == '2' || c108 == '3');
    }

    /**
     * 是否跳过后续逻辑(解密、替换电话地址等信息)
     *
     * @param order
     * @param traderMold
     * @param channelNo
     * @return
     */
    private boolean isPassedOrder(GetJdOrderFacadeResponse order, Integer traderMold, String channelNo) {
        /**
         * 目前厂直订单中，有一部分定制订单和厂直企配订单，定制订单无需进行解密，无需校验商城订单号，需要跳过逻辑；厂直企配业务不需要替换电话、地址等信息。
         * 当识别为厂直订单时（traderMold=1010），需要做如下判断：ordertype=18 且sendpay213位=1个人订单或2 企业订单直接跳过零售订单校验及地址替换；
         * 在京东商城的单子（channelNo=0010001），sendpay314=1或4时（厂直企配），跳过零售订单校验及地址替换；
         */

        if (order == null) {
            return false;
        }

        int orderType = order.getOrderType();
        String sendPay = order.getSendPay();

        LOGGER.info("零售厂直订单or京东商城订单判断是否跳过后续逻辑. orderId: {}, traderMold: {}, channelNo: {}, orderType: {}, sendPay: {}",
                order.getOrderId(), traderMold, channelNo, orderType, sendPay);

        boolean isPassed;

        if (OrderConstants.TRADER_MOLD_CHANGZHI.equals(traderMold)) {
            if (orderType != 18 || StringUtils.isBlank(sendPay) || sendPay.length() < 213) {
                return false;
            }
            char c213 = sendPay.charAt(212);
            isPassed = c213 == '1' || c213 == '2';
            if (isPassed) {
                return true;
            }
        }

        if (BusinessConstants.JD_CHANNEL_NO.equals(channelNo)) {
            if (StringUtils.isBlank(sendPay) || sendPay.length() < 314) {
                return false;
            }
            char c314 = sendPay.charAt(313);
            isPassed = c314 == '1' || c314 == '4';
            if (isPassed) {
                return true;
            }
        }

        return false;
    }

    /**
     * 判断是否中转模式
     * <p>
     * 先判断TransitType是否为1，否则判断sendpayMap 1086是否为1
     *
     * @param order
     * @param orderModel
     * @return
     */
    private boolean isTransitMode(GetJdOrderFacadeResponse order, ExpressOrderModel orderModel) {

        // 当「配送信息-物流中转仓=京东新疆中转仓」时，跳过零售校验中全部地址校验和替换逻辑；枚举值「1-京东新疆中转仓」
        if (MagicCommonConstants.STRING_1.equals(orderModel.getShipment().getTransitType())) {
            return true;
        }

        if (order == null) {
            return false;
        }

        if (SendPayMapUtil.isTransitMode(order.getSendPayMap())) {
            return true;
        }

        return false;
    }

    /**
     * 零售地址替换补偿
     * 赋值逻辑如下，idSopShipmentType不下发：
     * 零售订单的收货地址为空，使用上下文中的收件地址
     * 零售订单的收货地址长度小于10，使用上下文中的收件地址
     * 上下文中的收件地址长度小于6，使用零售订单的收货地址
     * 上下文中的收件地址长度大于零售订单的收货地址，使用上下文中的收件地址
     * 零售订单的收货地址包含上下文中收件地址字段的后六位内容，使用零售订单的收货地址
     * 非以上情况，使用上下文中的收件地址兜底
     * @param contextOrderAddress 上下文中地址
     * @param jdOrderAddress 零售地址
     * @param checkRange 5 地址替换补齐下标阈值
     * @return
     */
    public static String makeUpToAddress(String contextOrderAddress, String jdOrderAddress, Integer checkRange) {
        if (StringUtils.isEmpty(jdOrderAddress)){
            return contextOrderAddress;
        }
        if (jdOrderAddress.length() < 10){
            return contextOrderAddress;
        }
        if (contextOrderAddress.length() < checkRange + 1){
            return jdOrderAddress;
        }
        if (contextOrderAddress.length() >= jdOrderAddress.length()){
            return contextOrderAddress;
        }
        if (jdOrderAddress.contains(suffix(contextOrderAddress, checkRange))){
            return jdOrderAddress;
        }
        return contextOrderAddress;
    }

    /**
     * 获取地址后缀（忽略最后一个字符）
     * @param contextOrderAddress 上下问中的收货地址
     * @param checkRange 5 地址替换补齐下标阈值
     * @return
     */
    private static String suffix(String contextOrderAddress, Integer checkRange) {
        int length = contextOrderAddress.length();
        return contextOrderAddress.substring(length - checkRange - 1, length - 1);
    }

    /**
     * 判断是否命中京喜全托场景限制商家类型下单
     *
     * @param order
     * @param subTraderMold
     * @return
     */
    private boolean isJingXiOrder(GetJdOrderFacadeResponse order, Integer subTraderMold) {
        LOGGER.info("判断是否命中京喜订单. 开关switch: {}", expressUccConfigCenter.isB2cExpressJingXiQuanTuoCheckSwitch());

        if (!expressUccConfigCenter.isB2cExpressJingXiQuanTuoCheckSwitch()) {
            return false;
        }

        if (order == null) {
            return false;
        }

        LOGGER.info("判断是否命中京喜订单. orderId: {}, subTraderMold: {}, SendPayMap:{}", order.getOrderId(), subTraderMold, JSONUtils.beanToJSONDefault(order.getSendPayMap()));

        // 当subTraderMold == 100322 && sendPay 997位 != 1，做拒单处理
        return OrderConstants.SUB_TRADER_MOLD_JINGXIZIYING.equals(subTraderMold) && !SendPayMapUtil.isJingXi(order.getSendPayMap());
    }

    /**
     * SendPayMap[624==1]判断是否为正品鉴定第一段发送到鉴定仓的单子，不进行地址和收件人信息替换
     *
     * @param order
     * @return
     */
    private boolean isZhengPinJianDingSendToJianDingCangOrder(GetJdOrderFacadeResponse order) {
        LOGGER.info("快递B2C正品鉴定跳过地址替换，开关switch: {}", expressUccConfigCenter.isB2cZhengPinJianDingWaiveReplaceAddressSwitch());

        if (!expressUccConfigCenter.isB2cZhengPinJianDingWaiveReplaceAddressSwitch()) {
            return false;
        }

        if (order == null) {
            return false;
        }

        LOGGER.info("SendPayMap[624==1]判断是否为正品鉴定第一段发送到鉴定仓的单子. orderId: {}", order.getOrderId());

        return SendPayMapUtil.isZhengPinJianDingSendToJianDingCang(order.getSendPayMap());
    }

    /**
     * 判断是否拍拍质检订单

     */
    private boolean isPaipaiQualityInspectionOrder(GetJdOrderFacadeResponse order) {
        if (order == null) {
            return false;
        }
        return SendPayMapUtil.isPaipaiQualityInspectionOrder(order.getSendPayMap());
    }

    /**
     * 是否厂直订单
     * jdVcOrderValidDowngradeSwitch=true时【traderMold == 1010】
     * jdVcOrderValidDowngradeSwitch=false时【traderMold == 1010 && (orderType in (18,223) || null == outboundVenderId)】
     * @param traderMold
     * @param jdOrder
     * @return
     */
    private boolean isJdVcOrder(Integer traderMold, GetJdOrderFacadeResponse jdOrder, String outboundVenderId){
        if (!OrderConstants.TRADER_MOLD_CHANGZHI.equals(traderMold)) {
            return false;
        }
        if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.JD_VC_ORDER_VALID_DOWNGRADE_SWITCH)) {
            return true;
        }
        if (null == jdOrder.getOrderType()) {
            return false;
        }
        return StringUtils.isBlank(outboundVenderId) || BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.JD_VC_ORDER_ORDER_TYPE_LIST, String.valueOf(jdOrder.getOrderType()));
    }

    /**
     * 判断是服饰定制订单

     */
    private boolean isPersonalCustomization(GetJdOrderFacadeResponse order) {
        if (order == null) {
            return false;
        }
        return SendPayMapUtil.isPersonalCustomization(order.getSendPayMap());
    }
}
