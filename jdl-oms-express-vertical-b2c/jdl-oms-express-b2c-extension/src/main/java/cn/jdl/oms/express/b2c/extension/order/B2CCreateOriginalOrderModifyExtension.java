package cn.jdl.oms.express.b2c.extension.order;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.order.IOrderExtension;
import cn.jdl.oms.express.domain.infrs.acl.util.OriginalOrderModifyUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ProductClearPdqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.shared.common.constant.FlowConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.annotation.Resource;
import java.util.List;

/**
 * @Package: cn.jdl.oms.express.b2c.extension.order
 * @ClassName: B2CCreateOriginalOrderModifyExtension
 * @Description: 改址单下单成功需要清原单COD
 * @Author: liufarui
 * @CreateDate: 2021/6/7 6:37 下午
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version: V1.0
 */
@Extension(code = ExpressOrderProduct.CODE)
public class B2CCreateOriginalOrderModifyExtension implements IOrderExtension {

    private static final Logger LOGGER = LoggerFactory.getLogger(B2CCreateOriginalOrderModifyExtension.class);

    /**
     * 任务调度
     */
    @Resource
    private SchedulerService schedulerService;

    /**
     * 原单修改工具类
     */
    @Resource
    private OriginalOrderModifyUtil originalOrderModifyUtil;

    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            ExpressOrderModel orderModel = context.getOrderModel();
            if (orderModel.getOrderSnapshot() == null) {
                return;
            }
            LOGGER.info("b2c原单修改扩展点执行开始!");
            // 改址单后款订单 或者 退货单后款订单
            if (OrderTypeEnum.READDRESS == orderModel.getOrderType()
                    && PaymentStageEnum.CASHONDELIVERY == orderModel.getFinance().getPaymentStage()) {
                // 改址单后款订单都会发PDQ
                LOGGER.info("b2c改址单后款订单异步清理原单COD执行开始!");
                List<String> clearProductNos = originalOrderModifyUtil.reverseOrReaddressDeleteOriginalProduct(orderModel);

                SchedulerMessage schedulerMessage = new SchedulerMessage();
                ProductClearPdqMessageDto productClearPdqMessageDto = new ProductClearPdqMessageDto();
                productClearPdqMessageDto.setOrderNo(orderModel.orderNo());
                productClearPdqMessageDto.setOriginalOrderNo(orderModel.getOrderSnapshot().orderNo());
                productClearPdqMessageDto.setRequestProfile(orderModel.requestProfile());
                productClearPdqMessageDto.setBusinessIdentity(orderModel.getOrderBusinessIdentity());
                productClearPdqMessageDto.setClearProductNos(clearProductNos);
                schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(productClearPdqMessageDto));
                schedulerMessage.setDtoClass(ProductClearPdqMessageDto.class);
                schedulerService.addSchedulerTask(PDQTopicEnum.ORIGINAL_ORDER_MODIFY, schedulerMessage, FlowConstants.EXPRESS_ORDER_ORIGINAL_ORDER_MODIFY_FLOW_CODE);
                LOGGER.info("b2c改址单后款订单异步清理原单COD执行结束!");
            } else if (OrderTypeEnum.RETURN_ORDER == orderModel.getOrderType()
                    && PaymentStageEnum.CASHONDELIVERY == orderModel.getFinance().getPaymentStage()) {
                // 退货单后款订单，只有需要删除的增值产品不为空，才发
                LOGGER.info("b2c退货单后款订单异步清理原单增值服务执行开始!");
                List<String> clearProductNos = originalOrderModifyUtil.reverseOrReaddressDeleteOriginalProduct(orderModel);

                if (CollectionUtils.isNotEmpty(clearProductNos)) {
                    SchedulerMessage schedulerMessage = new SchedulerMessage();
                    ProductClearPdqMessageDto productClearPdqMessageDto = new ProductClearPdqMessageDto();
                    productClearPdqMessageDto.setOrderNo(orderModel.orderNo());
                    productClearPdqMessageDto.setOriginalOrderNo(orderModel.getOrderSnapshot().orderNo());
                    productClearPdqMessageDto.setRequestProfile(orderModel.requestProfile());
                    productClearPdqMessageDto.setBusinessIdentity(orderModel.getOrderBusinessIdentity());
                    productClearPdqMessageDto.setClearProductNos(clearProductNos);
                    schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(productClearPdqMessageDto));
                    schedulerMessage.setDtoClass(ProductClearPdqMessageDto.class);
                    schedulerService.addSchedulerTask(PDQTopicEnum.ORIGINAL_ORDER_MODIFY, schedulerMessage, FlowConstants.EXPRESS_ORDER_ORIGINAL_ORDER_MODIFY_FLOW_CODE);
                } else {
                    LOGGER.info("b2c退货单后款订单，无需删除原单增值产品");
                }
                LOGGER.info("b2c退货单后款订单异步清理原单增值服务执行结束!");
            } else {
                LOGGER.info("非改址单无需清理原单COD,b2c原单修改扩展点执行结束!");
            }
        } catch (BusinessDomainException e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("b2c原单修改扩展点执行业务异常: {}", e.fullMessage());
            throw e;
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("b2c原单修改扩展点执行异常", e);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.ORIGINAL_ORDER_MODIFY_FAIL).withCustom("b2c原单修改扩展点执行异常");
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }
}
