package cn.jdl.oms.express.b2c.extension.presort;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.PresortExtend;
import cn.jdl.oms.express.domain.bo.PresortResult;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.presort.IPresortExtension;
import cn.jdl.oms.express.domain.infrs.acl.baseHandler.presort.StationBaseHandler;
import cn.jdl.oms.express.domain.infrs.acl.facade.presort.PresortFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.presort.PresortBaseSiteFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.presort.PresortFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.presort.PresortFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.presort.PresortFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.presort.PresortRpcResultEnum;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ShipmentUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.SystemCallerUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.WarehouseModeUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.SupplyChainDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.WarehouseModeUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.SupplyChainDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PickupTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.vo.Shipment;
import cn.jdl.oms.express.domain.vo.modify.ChangedProperty;
import cn.jdl.oms.express.domain.vo.modify.ChangedPropertyDelegate;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.MaterialTurnoverEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Package: cn.jdl.oms.express.b2c.extension.presort
 * @ClassName: B2CModifyPresortExtension
 * @Description:
 * @Author: liufarui
 * @CreateDate: 2021/5/19 10:47 上午
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version: V1.0
 */
@Extension(code = ExpressOrderProduct.CODE)
public class B2CModifyPresortExtension implements IPresortExtension {
    private static final Logger LOGGER = LoggerFactory.getLogger(B2CModifyPresortExtension.class);

    @Resource
    private PresortFacade presortFacade;

    @Resource
    private PresortFacadeTranslator presortFacadeTranslator;

    /**
     * 预分拣场景 - 揽收
     */
    private static final Integer SCENE_TYPE_COLLECT = 1;

    /**
     * 预分拣场景 - 派送
     */
    private static final Integer SCENE_TYPE_DELIVER = 2;
    //605且站点id为-136时，属于疫情限售逻辑，此场景会由路由或预分拣返回指定话术，话术内容在overAreaReason
    private static final Integer PRESORT_SPECIAL_RESULT_STATUS = -136;

    /**
     * 预分拣标识初始化值
     */
    private static final Integer INIT_PRESORT_FLAG = 0;

    /**
     * 非超区
     */
    private static final String PRESORT_RESULT_SUCCESS = "1";

    //邮政限制揽收二级超区码
    private final static String PRESORT_DAWK_DELIVERY_LIMIT = "-253";

    @Resource
    private StationBaseHandler stationBaseHandler;

    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        ExpressOrderModel orderModel = context.getOrderModel();
        if (null == orderModel) {
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL).withCustom("订单中心纯配领域订单模型为空");
        }

        try {

            // B2C港澳关务数据的修改不涉及预分拣
            if (context.getOrderModel().isOnlyModifyCustoms()) {
                LOGGER.info("B2C港澳关务数据的修改不涉及预分拣--跳过预分拣");
                return;
            }

            LOGGER.info("修改订单预分拣B2C扩展点执行开始");
            ChangedPropertyDelegate changedPropertyDelegate = context.getChangedPropertyDelegate();
            // 前提条件：修改收寄件地址、产品、增值服务、站点信息、结算方式
            if (!ifNeedPresort(changedPropertyDelegate)) {
                // 不计算预分拣但是货品信息有修改，深度合作自提柜需求校验货品数量、重量、最长边
                if (changedPropertyDelegate.cargoHaveChange()) {
                    stationBaseHandler.validateHKSelfPickupLocker(orderModel);
                }
                return;
            }

            // 保存预分拣结果
            PresortResult presortResult = new PresortResult();
            PresortExtend presortExtend = new PresortExtend();
            String orderNo = orderModel.orderNo();

            // 快照
            ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();

            // 配送要求 - 快照
            Shipment snapshotShipment = orderSnapshot.getShipment();
            //materialTurnover
            String materialTurnover = orderModel.getShipment().getExtendProps(AttachmentKeyEnum.MATERIAL_TURNOVER.getKey());
            if (materialTurnover == null) {
                //当前单为空，去原单，特殊考虑上游清空，修改为空字符串的场景
                materialTurnover = snapshotShipment.getExtendProps(AttachmentKeyEnum.MATERIAL_TURNOVER.getKey());
            }
            if (MaterialTurnoverEnum.WARM_BOX.getCode().equals(materialTurnover)
                    || MaterialTurnoverEnum.MATERIAL_TURNOVER.getCode().equals(materialTurnover)) {
                if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.MATERIAL_TURNOVER_PRESORT_SWITCH)) {
                    LOGGER.info("物资周转为1或3时且开关开启，不执行预分拣校验，materialTurnover={}", materialTurnover);
                    return;
                }
            }
            // 仓配切配需求，deliveryPattern=1 或者 deliveryPattern=2 都需要做揽收派送预分拣
            // 如果修改传了该字段则替换成新的
            String deliveryPattern = orderModel.getShipment().isModifyContainsDeliveryPattern() ? (String) orderModel.getShipment().getShipmentExtendProp(OrderConstants.DELIVERY_PATTERN)
                    : (String) snapshotShipment.getShipmentExtendProp(OrderConstants.DELIVERY_PATTERN);


            // 是否计算揽派标志
            boolean startStationCalc = false;
            boolean endStationCalc = false;
            List<PresortFacadeRequest> requestList = new ArrayList<>(2);
            if (SystemCallerEnum.CSS == orderModel.getChannel().getSystemCaller()) {
                // TODO 哪里校验客服修改寄件地址只能在揽收前
                LOGGER.info("订单号：{},CSS客服系统修改收寄件地址-订单修改揽收站点预分拣计算开始", orderNo);
                Map<String, Boolean> map = cssModifyRequestBuild(context, orderModel, requestList);
                startStationCalc = map.get("startStationCalc");
                endStationCalc = map.get("endStationCalc");
            } else {
                LOGGER.info("订单号：{},订单修改揽收站点预分拣计算开始", orderNo);
                // 揽收站点 & 配送站点
                String startStationNo = orderModel.getShipment().getStartStationNo();
                String endStationNo = orderModel.getShipment().getEndStationNo();

                //https://joyspace.jd.com/pages/DkhoQR1lkqeClM9MNuCv
                //修改入参中不含始发站点：
                //若原单的揽收方式为自送，且始发站点不为空（可以同上校验站点有效性逻辑），则不再执行预分拣获取站点逻辑；
                if (null == startStationNo && PickupTypeEnum.SELF_DELIVERY == snapshotShipment.getPickupType()
                        && StringUtils.isNotBlank(snapshotShipment.getStartStationNo())) {
                    startStationNo = snapshotShipment.getStartStationNo();
                }
                //修改入参中不含目的站点：
                //若原单的派送方式为自提，且目的站点不为空（可以同上校验站点有效性），则不再执行预分拣获取站点逻辑；
                if (null == endStationNo && DeliveryTypeEnum.SELF_PICKUP == snapshotShipment.getDeliveryType() && StringUtils.isNotBlank(snapshotShipment.getEndStationNo())) {
                    endStationNo = snapshotShipment.getEndStationNo();
                }

                if (orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp()) {
                    LOGGER.info("订单号：{},揽收后修改，跳过揽收预分拣", orderNo);
                    // TODO 是否需要判断和揽收预分拣相关字段，如果传了清空，防止数据错误
                } else if (StringUtils.isNotBlank(startStationNo)) {
                    // 揽收站点不为空「一般自送，扫描小哥二维码下单的时候不为空」
                    PresortBaseSiteFacadeResponse presortBaseSiteFacadeResponse = presortFacade.getBaseSiteBySiteId(presortFacadeTranslator.toPresortBaseSiteFacadeRequest(startStationNo));

                    if (null != presortBaseSiteFacadeResponse
                            && startStationNo.equals(presortBaseSiteFacadeResponse.getSiteCode())) {
                        LOGGER.info("订单号:{}揽收站点:{}调用青龙基础资料校验通过", orderNo, startStationNo);
                        presortFacadeTranslator.complementShipmentStartStationInfo(orderModel, presortBaseSiteFacadeResponse.getSiteCode(), presortBaseSiteFacadeResponse.getSiteType(), presortBaseSiteFacadeResponse.getSiteName());
                    } else {
                        // 修改的时候，站点信息不为空，校验不通过直接修改失败
                        // 注：下单的时候，此处若是校验不通过会通过预分拣去寻找正确的站点信息并赋值
                        LOGGER.error("订单号:{}揽收站点:{}不为空，且调用青龙基础资料校验未通过，修改失败", orderNo, startStationNo);
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                                .withCustom(String.format("订单号:%s揽收站点:%s不为空，且调用青龙基础资料校验未通过，修改失败", orderNo, startStationNo));
                    }
                } else if (SystemCallerEnum.CLPS == orderSnapshot.getChannel().getSystemCaller()
                    && OrderConstants.DELIVERY_PATTERN_ONE.equals(deliveryPattern)) {
                    // CLPS来源（CLPS）的订单且为接货仓模式（deliveryPattern=1）不做揽收预分拣计算，
                    // 仓配揽收模式需要做揽收预分拣（deliveryPattern=2）
                    LOGGER.info("来源为CLPS的订单且为接货仓模式（deliveryPattern=1）不做揽收预分拣计算");
                } else if (SupplyChainDeliveryOrderSignUtil.flag(orderModel)
                        && WarehouseModeUtil.flag(orderModel)
                        && BatrixSwitch.applyByBoolean(BatrixSwitchKey.SUPPLY_CHAIN_DELIVERY_SKIP_START_STATION_PRESORT)) {
                    LOGGER.info("订单标识orderSign.supplyChainDelivery=1，且为接货仓模式（deliveryPattern=1）不做揽收预分拣计算");
                } else {
                    LOGGER.info("修改订单-订单号:{}未指定揽收站点，需要调用预分拣计算", orderNo);
                    startStationCalc = true;
                }

                // 派送站点不为空「一般自取的时候不为空」
                LOGGER.info("订单号：{},订单修改派件站点预分拣计算开始", orderNo);
                if (StringUtils.isNotBlank(endStationNo)) {
                    PresortBaseSiteFacadeResponse presortBaseSiteFacadeResponse = presortFacade.getBaseSiteBySiteId(presortFacadeTranslator.toPresortBaseSiteFacadeRequest(endStationNo));
                    if (null != presortBaseSiteFacadeResponse
                            && endStationNo.equals(presortBaseSiteFacadeResponse.getSiteCode())) {
                        LOGGER.info("订单号:{}派件站点:{}调用青龙基础资料校验通过", orderNo, endStationNo);
                        // 香港深度合作自提柜校验：目的站点是特殊自提柜时，卡控包裹数量、重量、最长边
                        stationBaseHandler.validateHKSelfPickupLocker(orderModel, presortBaseSiteFacadeResponse);
                        presortFacadeTranslator.complementShipmentEndStationInfo(orderModel, presortBaseSiteFacadeResponse.getSiteCode(), presortBaseSiteFacadeResponse.getSiteType(), presortBaseSiteFacadeResponse.getSiteName());
                    } else {
                        // 修改的时候，站点信息不为空，校验不通过直接修改失败
                        // 注：下单的时候，此处若是校验不通过会通过预分拣去寻找正确的站点信息并赋值
                        LOGGER.error("订单号:{}派件站点:{}不为空，且调用青龙基础资料校验未通过，修改失败", orderNo, endStationNo);
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                                .withCustom(String.format("订单号:%s派件站点:%s不为空，且调用青龙基础资料校验未通过，修改失败", orderNo, endStationNo));
                    }
                } else if (UnitedB2CUtil.isUnitedFreightB2C(orderModel)
                        && SystemCallerUtil.snapIsSupplyOFC(orderModel)
                        && WarehouseModeUtil.flag(orderModel)
                        && ShipmentUtil.isDeliveryTransportNetModeB(orderModel)

                ) {
                    // 【快运+冷链b2b+合同物流】仓配的零担且强B不做派送预分拣计算。此扩展点当前只有快运零担（非整车直达）使用，因此不判断零担
                    LOGGER.info("仓配订单，仓配的零担且强B不做派送预分拣计算");
                } else {
                    LOGGER.info("修改订单-订单号:{}未指定派送站点，需要调用预分拣计算", orderNo);
                    endStationCalc = true;
                }

                if (ModifySceneRuleUtil.isPickupTransferStation(context.getOrderModel())) {
                    LOGGER.info("揽收转站、跨站截单策略,不重新计算预分拣,只补全站点信息");
                    return;
                }

                if(startStationCalc){
                    PresortFacadeRequest startPresortFacadeRequest = presortFacadeTranslator.toB2CPresortModifyFacadeRequest(orderModel, SCENE_TYPE_COLLECT);
                    requestList.add(startPresortFacadeRequest);
                }

                if(endStationCalc){
                    PresortFacadeRequest endPresortFacadeRequest = presortFacadeTranslator.toB2CPresortModifyFacadeRequest(orderModel, SCENE_TYPE_DELIVER);
                    requestList.add(endPresortFacadeRequest);
                }
            }


            if (!requestList.isEmpty()) {
                LOGGER.info("修改订单-订单号:{}，开始调用预分拣计算", orderNo);
                Map<String, PresortFacadeResponse> presortFacadeResponseMap = presortFacade.batchComputePresort(requestList);
                if(MapUtils.isEmpty(presortFacadeResponseMap)){
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                            .withCustom(String.format("订单%s,修改订单派送站点预分拣计算失败", orderNo));
                }
                // 派送预分拣实际网络类型是否强B
                boolean isDeliveryTransportNetModeB = ShipmentUtil.isDeliveryTransportNetModeB(presortFacadeResponseMap.get(SCENE_TYPE_DELIVER.toString()));
                if (startStationCalc) {
                    PresortFacadeResponse presortFacadeResponse = presortFacadeResponseMap.get(SCENE_TYPE_COLLECT.toString());
                    if (PresortRpcResultEnum.PRESORT_RPC_RESULT_200.getCode().equals(presortFacadeResponse.getResultCode())) {
                        if (SupplyChainDeliveryOrderSignUtil.flag(orderModel)
                                && WarehouseModeUtil.flag(orderModel)
                                && isDeliveryTransportNetModeB
                                && UnitedB2CUtil.isUnitedFreightB2C(orderModel)
                                && BatrixSwitch.applyByBoolean(BatrixSwitchKey.PRESORT_SKIP_START_STATION_SWITCH)) {
                            LOGGER.info("订单标识为仓配订单，派送信息交付模式为1，派送预分拣实际网络类型为强B，不处理揽收预分拣");
                            return;
                        }
                        LOGGER.info("订单号:{}订单修改,揽收站点预分拣计算成功siteId:{}", orderNo, presortFacadeResponse.getSiteId());
                        computePresort(orderModel, presortResult, presortExtend, SCENE_TYPE_COLLECT, presortFacadeResponse);

                        // 如果揽收地址修改，则设置到修改项中
                        if (!String.valueOf(presortFacadeResponse.getSiteId()).equals(snapshotShipment.getStartStationNo())) {
                            ChangedProperty changedProperty = new ChangedProperty();
                            changedProperty.setOperateType(OperateTypeEnum.UPDATE);
                            changedProperty.setProperty(ModifyItemConfigEnum.START_STATION_NO.getField());
                            changedProperty.setItemCode(ModifyItemConfigEnum.START_STATION_NO.getCode());
                            changedProperty.setPropertyDesc(ModifyItemConfigEnum.START_STATION_NO.getDesc());
                            changedProperty.setOriginValue(snapshotShipment.getStartStationNo());
                            changedProperty.setModifyValue(String.valueOf(presortFacadeResponse.getSiteId()));
                            changedPropertyDelegate.addChangedProperties(changedProperty);
                        }
                    } else if (PresortRpcResultEnum.PRESORT_RPC_RESULT_605.getCode().equals(presortFacadeResponse.getResultCode())) {
                        LOGGER.info("订单:{}揽收站点预分拣计算超区resultMsg:{}", orderNo, presortFacadeResponse.getResultMessage());
                        if (presortFacadeResponse.getSiteId() != null && PRESORT_SPECIAL_RESULT_STATUS.equals(presortFacadeResponse.getSiteId())) {
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                                    .withSubCode(PresortRpcResultEnum.PRESORT_RPC_RESULT_605.getCode() + "" + PRESORT_SPECIAL_RESULT_STATUS)
                                    .withSubMessage(presortFacadeResponse.getOverAreaReason())
                                    .withCustom(String.format("预分拣信息校验失败,修改订单揽收站点预分拣计算失败 %s", presortFacadeResponse.getResultMessage()));
                        }else {
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                                    .withCustom(String.format("预分拣信息校验失败,修改订单揽收站点预分拣计算失败 %s", presortFacadeResponse.getResultMessage()))
                                    .withSubCode(presortFacadeResponse.getResultCode())
                                    .withSubMessage(presortFacadeResponse.getResultMessage());
                        }
                    } else {
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                                .withCustom(String.format("订单%s,修改订单揽收站点预分拣计算失败 %s", orderNo, presortFacadeResponse.getResultMessage()))
                                .withSubCode(presortFacadeResponse.getResultCode())
                                .withSubMessage(presortFacadeResponse.getResultMessage());
                    }
                }

                if (endStationCalc) {
                    PresortFacadeResponse presortFacadeResponse = presortFacadeResponseMap.get(SCENE_TYPE_DELIVER.toString());

                    if (PresortRpcResultEnum.PRESORT_RPC_RESULT_200.getCode().equals(presortFacadeResponse.getResultCode())) {
                        LOGGER.info("修改订单-订单号:{}派件站点预分拣计算成功siteId:{}", orderNo, presortFacadeResponse.getSiteId());
                        computePresort(orderModel, presortResult, presortExtend, SCENE_TYPE_DELIVER, presortFacadeResponse);

                        // 如果派送地址修改，则设置到修改项中
                        if (!String.valueOf(presortFacadeResponse.getSiteId()).equals(snapshotShipment.getEndStationNo())) {
                            ChangedProperty changedProperty = new ChangedProperty();
                            changedProperty.setOperateType(OperateTypeEnum.UPDATE);
                            changedProperty.setProperty(ModifyItemConfigEnum.END_STATION_NO.getField());
                            changedProperty.setItemCode(ModifyItemConfigEnum.END_STATION_NO.getCode());
                            changedProperty.setPropertyDesc(ModifyItemConfigEnum.END_STATION_NO.getDesc());
                            changedProperty.setOriginValue(snapshotShipment.getEndStationNo());
                            changedProperty.setModifyValue(String.valueOf(presortFacadeResponse.getSiteId()));
                            changedPropertyDelegate.addChangedProperties(changedProperty);
                        }
                    } else if (PresortRpcResultEnum.PRESORT_RPC_RESULT_605.getCode().equals(presortFacadeResponse.getResultCode())) {
                        LOGGER.info("订单:{}派送站点预分拣计算超区resultMsg:{}", orderNo, presortFacadeResponse.getResultMessage());
                        String secondLevelOverAreaCode = null;
                        if (presortFacadeResponse.getPresortRpcDto() != null) {
                            secondLevelOverAreaCode = presortFacadeResponse.getPresortRpcDto().getSecondLevelOverAreaCode() != null ?
                                    String.valueOf(presortFacadeResponse.getPresortRpcDto().getSecondLevelOverAreaCode()) : null;
                        }
                        if (presortFacadeResponse.getSiteId() != null && PRESORT_SPECIAL_RESULT_STATUS.equals(presortFacadeResponse.getSiteId())) {
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                                    .withSubCode(PresortRpcResultEnum.PRESORT_RPC_RESULT_605.getCode() + "" + PRESORT_SPECIAL_RESULT_STATUS)
                                    .withSubMessage(presortFacadeResponse.getOverAreaReason())
                                    .withCustom(String.format("预分拣信息校验失败,修改订单派送站点预分拣计算失败 %s", presortFacadeResponse.getResultMessage()));
                        } else if ((presortFacadeResponse.getPresortRpcDto() != null) && PRESORT_DAWK_DELIVERY_LIMIT.equals(secondLevelOverAreaCode)) {
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                                    .withCustom(String.format("预分拣信息校验失败,修改订单派送站点预分拣计算失败 %s", presortFacadeResponse.getResultMessage()))
                                    .withSubCode(PresortRpcResultEnum.PRESORT_RPC_RESULT_605.getCode() + "_" + PRESORT_DAWK_DELIVERY_LIMIT)
                                    .putExt(OrderConstants.SPECIAL_LIMIT_INFO, JSONUtils.beanToJSONDefault(presortFacadeResponse.getSpecialLimitInfoDTO()))
                                    .withSubMessage(presortFacadeResponse.getResultMessage());
                        }else {
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                                    .withCustom(String.format("预分拣信息校验失败,修改订单派送站点预分拣计算失败 %s", presortFacadeResponse.getResultMessage()))
                                    .withSubCode(presortFacadeResponse.getResultCode())
                                    .withSubMessage(presortFacadeResponse.getResultMessage());
                        }
                    } else {
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL)
                                .withCustom(String.format("订单%s,修改订单派送站点预分拣计算失败 %s", orderNo, presortFacadeResponse.getResultMessage()))
                                .withSubCode(presortFacadeResponse.getResultCode())
                                .withSubMessage(presortFacadeResponse.getResultMessage());
                    }
                }
                // 增值产品后置处理
                presortFacadeTranslator.postProcessAddOnProduct(context, presortFacadeResponseMap);
            }

            // 如果揽收/派送站点有变化，则补充到上下文订单模型中
            if (presortExtend.getStartStation() != null
                    || presortExtend.getEndStation() != null) {
                presortFacadeTranslator.complementPresort(orderModel, presortExtend);
            }
            context.setPresortResult(presortResult);
            LOGGER.info("修改订单,预分拣B2C扩展点执行结束");
        } catch (BusinessDomainException businessDomainException) {
            Profiler.functionError(callerInfo);
            LOGGER.error("修改预分拣B2C扩展点执行异常, traceId={}", orderModel.traceId(), businessDomainException);
            throw businessDomainException;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("修改预分拣B2C扩展点执行异常, traceId={}", orderModel.traceId(), exception);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.PRESORT_VALIDATE_FAIL, exception).withCustom("预分拣信息校验失败");
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 功能: 是否需要做预分拣
     *
     * @param: ChangedPropertyDelegate 更改的属性集合
     * @return: boolean
     * @throw:
     * @description: 以下几种情况需要做预分拣，否则直接跳过
     * 修改收寄件地址、产品、增值服务、站点信息、结算方式
     * @author: liufarui
     * @date: 2021/5/19 3:50 下午
     */
    private boolean ifNeedPresort(ChangedPropertyDelegate changedPropertyDelegate) {
        // 收件人地址是否发生变更
        return changedPropertyDelegate.consigneeAddressHaveChange()
                // 发件人地址是否发生变更
                || changedPropertyDelegate.consignorAddressHaveChange()
                // 产品信息是否发生变更
                || changedPropertyDelegate.productHaveChange()
                // 结算方式是否发生变更
                || changedPropertyDelegate.settlementTypeHaveChange()
                // 税金结算方式是否发生变更
                || changedPropertyDelegate.taxSettlementTypeHaveChange()
                // 起始站点信息是否发生变更
                || changedPropertyDelegate.startStationHaveChange()
                // 目的站点信息是否发生变更
                || changedPropertyDelegate.endStationHaveChange();
    }

    /**
     * 客服系统修改 触发预分拣校验入参
     * 客服系统只能修改收件地址或寄件地址 且目前客服系统不支持同时修改
     * 问题场景 接单 收寄件地址都进人工  修改单个地址调用预分拣时 揽收派送都校验则会响应超区拒单
     * 如接单收寄件地址都进人工 修改寄件地址为常规地址 同时进行了揽收派送双站点预分拣校验 派送站点会校验失败 响应超区 拒单
     * 现调整为寄件地址修改只做揽收站点校验；收件地址修改只做派送站点校验
     * 需要考虑别的业务线是否有类似场景；寄件地址是否肯定对应揽收站点  收件地址是否肯定对应派送站点(逆向，改址等场景)
     * @param context
     * @param orderModel
     * @param requestList
     * @return
     */
    private Map<String,Boolean> cssModifyRequestBuild (ExpressOrderContext context,ExpressOrderModel orderModel,List<PresortFacadeRequest> requestList){
        Map<String, Boolean> map = new HashMap<>();
        ChangedPropertyDelegate changedPropertyDelegate = context.getChangedPropertyDelegate();
        boolean consignorAddressHaveChange = changedPropertyDelegate.consignorAddressHaveChange();//发件地址变更
        boolean consigneeAddressHaveChange = changedPropertyDelegate.consigneeAddressHaveChange();//收件地址变更
        if(consignorAddressHaveChange){
            if (SupplyChainDeliveryOrderSignUtil.flag(orderModel)
                    && WarehouseModeUtil.flag(orderModel)
                    && BatrixSwitch.applyByBoolean(BatrixSwitchKey.SUPPLY_CHAIN_DELIVERY_SKIP_START_STATION_PRESORT)) {
                LOGGER.info("订单标识orderSign.supplyChainDelivery=1，且为接货仓模式（deliveryPattern=1）不做揽收预分拣计算");
            } else {
                PresortFacadeRequest startPresortFacadeRequest = presortFacadeTranslator.toPresortModifyFacadeRequest(orderModel, SCENE_TYPE_COLLECT);
                requestList.add(startPresortFacadeRequest);
            }
        }

        if(consigneeAddressHaveChange){
            if (UnitedB2CUtil.isUnitedFreightB2C(orderModel)
                    && SystemCallerUtil.snapIsSupplyOFC(orderModel)
                    && WarehouseModeUtil.flag(orderModel)
                    && ShipmentUtil.isDeliveryTransportNetModeB(orderModel)) {
                // 【快运+冷链b2b+合同物流】仓配的零担且强B不做派送预分拣计算。此扩展点当前只有快运零担（非整车直达）使用，因此不判断零担
                LOGGER.info("仓配订单，仓配的零担且强B不做派送预分拣计算");
            }
            PresortFacadeRequest endPresortFacadeRequest = presortFacadeTranslator.toPresortModifyFacadeRequest(orderModel, SCENE_TYPE_DELIVER);
            requestList.add(endPresortFacadeRequest);
        }
        map.put("startStationCalc",consignorAddressHaveChange);
        map.put("endStationCalc",consigneeAddressHaveChange);
        return map;
    }

    /**
     * 预分拣计算
     * 跟接单不同点
     * 1、只处理PRESORT_RPC_RESULT_200
     * 2、不补齐预分拣标识：修改场景不补齐，因为修改接口增加预分拣出参前未补齐。同时注意new PresortResult()时未赋值presortExtend.setPresortFlag(INIT_PRESORT_FLAG)
     */
    private void computePresort(ExpressOrderModel orderModel, PresortResult presortResult
            , PresortExtend presortExtend, int sceneType, PresortFacadeResponse presortResp) {

        //补齐预分拣扩展信息，需要下发给OFC
        if (SCENE_TYPE_COLLECT == sceneType) {
            presortFacadeTranslator.complementStartStation(orderModel, presortResp, presortExtend);
        } else if (SCENE_TYPE_DELIVER == sceneType) {
            presortFacadeTranslator.complementEndStation(orderModel, presortResp, presortExtend);

            // 补全接驳站点
            if(null != presortResp.getPresortRpcDto() && null != presortResp.getPresortRpcDto().getTransferPointId()){
                presortFacadeTranslator.complementEndTransferStation(orderModel, String.valueOf(presortResp.getPresortRpcDto().getTransferPointId()));
                presortResult.setEndTransferStationNo(String.valueOf(presortResp.getPresortRpcDto().getTransferPointId()));
            } else {
                //置为空
                presortFacadeTranslator.complementEndTransferStation(orderModel, "");
                presortResult.setEndTransferStationNo("");
            }
        }

        Integer resultCode = presortResp.getResultCode();
        Integer siteId = presortResp.getSiteId();
        Integer siteType = presortResp.getSiteType();
        String siteName = presortResp.getSiteName();

        if (PresortRpcResultEnum.PRESORT_RPC_RESULT_200.getCode().equals(resultCode)) {
            LOGGER.info("订单{}站点预分拣计算成功siteId:{}", (SCENE_TYPE_COLLECT == sceneType) ? "揽收" : "派送", siteId);

            if (SCENE_TYPE_COLLECT == sceneType) {
                presortResult.setStartStationNo(siteId.toString());
                presortResult.setStartStationName(siteName);
                presortResult.setStartStationType(siteType != null ? siteType.toString() : null);
                presortResult.setStartStationPresortResultType(PRESORT_RESULT_SUCCESS);
                presortExtend.getStartStation().setPresortResultType(PRESORT_RESULT_SUCCESS);
                if (presortResp.getPresortRpcDto() != null) {
                    //揽收路区
                    presortResult.setStartRoadArea(presortResp.getPresortRpcDto().getRoad());
                    //始发分拣中心ID
                    presortResult.setStartDmsId(presortResp.getPresortRpcDto().getDmsId() != null ?
                            String.valueOf(presortResp.getPresortRpcDto().getDmsId()) : null);
                    PresortFacadeResponse.PresortRpcDto presortRpcDto = presortResp.getPresortRpcDto();
                    //始发站点AOI路区
                    presortResult.setStartAoiCode(presortRpcDto.getAoiCode());
                    //始发站点物流模式
                    presortResult.setStartDeliveryType((presortRpcDto.getDeliveryType() == null) ? null : presortRpcDto.getDeliveryType().toString());
                    //始发站点超区编码
                    presortResult.setStartOverAreaCode((presortRpcDto.getSecondLevelOverAreaCode() == null) ? null : presortRpcDto.getSecondLevelOverAreaCode().toString());
                    //始发站点超区原因
                    presortResult.setStartOverAreaReason(presortRpcDto.getSecondLevelOverAreaValue());
                }
                presortFacadeTranslator.complementShipmentStartStationInfo(orderModel, siteId.toString(), siteType, siteName);
            } else if (SCENE_TYPE_DELIVER == sceneType) {
                presortResult.setEndStationNo(siteId.toString());
                presortResult.setEndStationName(siteName);
                presortResult.setEndStationType(siteType != null ? siteType.toString() : null);
                presortResult.setEndStationPresortResultType(PRESORT_RESULT_SUCCESS);
                if (presortResp.getPresortRpcDto() != null) {
                    //派送路区
                    presortResult.setEndRoadArea(presortResp.getPresortRpcDto().getRoad());
                    //目的分拣中心ID
                    presortResult.setEndDmsId(presortResp.getPresortRpcDto().getDmsId() != null ?
                            String.valueOf(presortResp.getPresortRpcDto().getDmsId()) : null);
                    PresortFacadeResponse.PresortRpcDto presortRpcDto = presortResp.getPresortRpcDto();
                    //目的站点AOI路区
                    presortResult.setEndAoiCode(presortRpcDto.getAoiCode());
                    //目的站点物流模式
                    presortResult.setEndDeliveryType((presortRpcDto.getDeliveryType() == null) ? null : presortRpcDto.getDeliveryType().toString());
                    //目的站点超区编码
                    presortResult.setEndOverAreaCode((presortRpcDto.getSecondLevelOverAreaCode() == null) ? null : presortRpcDto.getSecondLevelOverAreaCode().toString());
                    //目的站点超区原因
                    presortResult.setEndOverAreaReason(presortRpcDto.getSecondLevelOverAreaValue());
                }
                presortExtend.getEndStation().setPresortResultType(PRESORT_RESULT_SUCCESS);
                presortFacadeTranslator.complementShipmentEndStationInfo(orderModel, siteId.toString(), siteType, siteName);
            }
        }
    }
}
