package cn.jdl.oms.express.b2c.extension.customer;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CustomerConfig;
import cn.jdl.oms.express.domain.bo.NewCustomerConfig;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.customer.ICustomerConfigExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.AccountBookFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.AccountBookRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.AccountBookResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.AccountBookTranslator;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.SystemCallerUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.TrustSellerUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.SupplyChainDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.thread.ThreadPoolExecutorService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.ContextInfoEnum;
import cn.jdl.oms.express.domain.spec.dict.OccupyModeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.vo.Goods;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.ProductConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.TraderOperateStateEnum;
import cn.jdl.oms.express.shared.common.dict.TraderSignEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.dict.UnifiedSubErrorSpec;
import cn.jdl.oms.express.shared.common.dict.UnpackingInspectionEnum;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.DomainException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.shared.common.utils.TraderSignUtils;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

/**
 * B2C客户配置信息校验扩展点实现
 *
 * @author: lufahai
 * @Date: 2021/4/26
 * @Version 1.0
 */
@Extension(code = ExpressOrderProduct.CODE)
public class B2CCustomerConfigExtension implements ICustomerConfigExtension {

    private static final Logger LOGGER = LoggerFactory.getLogger(B2CCustomerConfigExtension.class);

    /**
     * 客户配置信息
     */
    @Resource
    private CustomerConfigFacade customerConfigFacade;

    /**
     * 事业部信息
     */
    @Resource
    private CustomerFacade customerFacade;

    /**
     * 现结余额查询转换
     */
    @Resource
    private AccountBookTranslator accountBookTranslator;

    @Resource
    private AccountBookFacade accountBookFacade;

    /**
     * ucc
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * traderSign 标位1
     */
    private static final String SIGN_TYPE_1 = "1";

    /**
     * traderSign 标位0
     */
    private static final String SIGN_TYPE_0 = "0";

    /**
     * 隐私通话key
     */
    private static final String HIDE_PRIVACY_KEY = "hidePrivacyType";

    /**
     * 开箱验货key
     */
    private static final String UNPACKING_INSPECTION = "unpackingInspection";

    /**
     * 明细揽收key
     */
    private static final String PICKUP_DETAIL = "pickUpDetailType";

    /**
     * 商家类别：公司内部
     */
    private static final int IN_COMPANY = 1003;

    /**
     * 商家类别：分拣退货
     */
    private static final int SORT_RETURN = 1004;

    /**
     * 最大最小重量 TODO 需改为配置化
     */
    private final BigDecimal minTotalWeight = new BigDecimal(0);

    private final BigDecimal maxTotalWeight = new BigDecimal(9999000);

    /**
     * 预估金额范围 0~1000000
     */
    private static final BigDecimal MIN_ESTIMATE_AMOUNT = new BigDecimal(0);
    private static final BigDecimal MAX_ESTIMATE_AMOUNT = new BigDecimal(1000000);
    /**
     * 明细揽收-缺量揽收
     */
    private static final String PICKUP_DETAIL_2 = "2";

    /**
     * 明细揽收商品数量最大值
     */
    private static final BigDecimal MAX_TOTAL_GOODS_NUM = new BigDecimal(99999);

    /**
     * 商家子类别为SOP类型
     */
    private static final Integer SUB_TRADER_MOLD_SOP = 2001;

    /**
     * 渠道编码为售后单（0090001）
     */
    public static final String AFTER_SALE_CHANNEL_NO = "0090001";

    /**
     * 渠道编码为京东平台（0010001）
     */
    public static final String JD_CHANNEL_NO = "0010001";

    /**
     * 商家子类别为京喜类型
     */
    private static final Integer SUB_TRADER_MOLD_JINGXI = 100322;


    @Resource
    private ThreadPoolExecutorService threadPoolExecutorService;


    @Override
    public void execute(ExpressOrderContext expressOrderContext) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("B2C客户配置信息校验开始：");
            ExpressOrderModel expressOrderModel = expressOrderContext.getOrderModel();
            String accountNo = expressOrderModel.getCustomer().getAccountNo();

            if (StringUtils.isNotBlank(accountNo)) {
                checkBasicTraderInfo(expressOrderContext);
            }
        } catch (Exception e) {
            LOGGER.error("B2C客户配置信息校验失败！", e);
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 功能:
     *
     * @param:
     * @return:
     * @throw:
     * @description:
     * @author: liufarui
     * @date: 2021/6/11 10:37
     */
    private void checkBasicTraderInfo(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel expressOrderModel = expressOrderContext.getOrderModel();
        // 等待两个Future完成
        CustomerConfig customerConfig;
        NewCustomerConfig newCustomerConfig = null;
        if (expressOrderModel.isProductBusinessMode125()) {
            try {
                // 使用CompletableFuture并行调用两个方法
                CompletableFuture<CustomerConfig> customerConfigFuture = CompletableFuture.supplyAsync(() ->
                        customerConfigFacade.getCustomerConfig(expressOrderContext), threadPoolExecutorService.getExecutorService()
                );

                CompletableFuture<NewCustomerConfig> newCustomerConfigFuture = CompletableFuture.supplyAsync(() ->
                        customerConfigFacade.getNewCustomerConfig(expressOrderContext), threadPoolExecutorService.getExecutorService()
                );
                // 获取customerConfig结果
                customerConfig = customerConfigFuture.get();

                // 如果需要newCustomerConfig，则获取其结果
                newCustomerConfig = newCustomerConfigFuture.get();
            } catch (InterruptedException | ExecutionException e) {
                Throwable cause = e.getCause(); // 获取真实异常
                if (cause instanceof DomainException) {
                    LOGGER.error("并行获取客户配置信息失败", ((DomainException) cause).fullMessage());
                    throw (DomainException) cause;
                }
                throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL, e)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0024.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0024.desc());
            }
        } else {
            customerConfig = customerConfigFacade.getCustomerConfig(expressOrderContext);
        }

        // 青龙业主号校验
        if (customerConfig == null) {
            LOGGER.error("客户配置信息校验失败，未查到相关客户信息");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0007.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0007.desc());
        }

        // 识别ordersign-unitedB2CFlag=1且主产品=快运的设置信任商家标识
        if (UnitedB2CUtil.isUnitedFreightB2C(expressOrderModel) && !expressOrderModel.isProductBusinessMode125()) {
            //设置信任商家标示
            TrustSellerUtil.setTrustCustomerWeightVolume(expressOrderModel, customerConfig);
            LOGGER.info("客户配置信息是否信任商家：{}", TrustSellerUtil.isTrustWeightVolume(expressOrderModel));
        }

        if (expressOrderModel.isProductBusinessMode125() && newCustomerConfig != null) {
            Map<String, NewCustomerConfig.ProductAttrInfo> productAttrInfoMap = newCustomerConfig.getProductAttrInfos();
            if (productAttrInfoMap != null) {
                NewCustomerConfig.ProductAttrInfo productAttrInfo = productAttrInfoMap.get(ProductConstants.BUSINESS_PRODUCT_ATTR_KEY);
                if (productAttrInfo != null) {
                    String billingWeightMethodStr = productAttrInfo.getBillingWeightMethod();
                    if (StringUtils.isNumeric(billingWeightMethodStr)) {
                        Integer billingWeightMethod = Integer.parseInt(billingWeightMethodStr);
                        expressOrderModel.getComplementModel().complementBillingWeightMethod(this, billingWeightMethod);
                    }
                    if (UnitedB2CUtil.isUnitedFreightB2C(expressOrderModel)) {
                        //设置信任商家标示
                        TrustSellerUtil.setNewTrustCustomerWeightVolume(expressOrderModel, billingWeightMethodStr);
                        LOGGER.info("客户配置信息是否信任商家：{}", TrustSellerUtil.isTrustWeightVolume(expressOrderModel));
                    }
                }
            }
        }

        // 补全账号id
        expressOrderModel.getComplementModel().complementAccountId(this, customerConfig.getCustomerId());
        // 补全账号名称
        expressOrderModel.getComplementModel().complementAccountName(this, customerConfig.getCustomerName());
        if (OrderTypeEnum.RETURN_ORDER == expressOrderModel.getOrderType()) {
            LOGGER.info("客户配置信息校验，逆向单不检验客户信息");
            return;
        }

        //修改场景仓出库发货，不检验客户信息
        if (BusinessSceneEnum.MODIFY.getCode().equals(expressOrderModel.getOrderBusinessIdentity().getBusinessScene())
                && ModifySceneRuleUtil.isTypeOfOutboundDelivery(expressOrderModel)) {
            LOGGER.info("修改场景仓出库发货，不检验客户信息");
            return;
        }

        if (ModifySceneRuleUtil.isPickupTransferStation(expressOrderModel)
                || ModifySceneRuleUtil.isNoTaskFinishCollect(expressOrderModel)
                || ModifySceneRuleUtil.isModifyOpMode(expressOrderModel)) {
            LOGGER.info("揽收转站、跨站截单、无任务揽收、修改运营模式策略,不检验客户信息");
            return;
        }

        if (ModifySceneRuleUtil.isModifyQuantity(expressOrderModel)
                && SystemCallerEnum.PDA == expressOrderModel.getChannel().getSystemCaller()) {
            LOGGER.info("终端修改包裹数策略,不检验客户信息");
            return;
        }

        //青龙业主号校验
        if (!TraderOperateStateEnum.normal.getCode().equals(customerConfig.getTraderOperateState())) {
            LOGGER.error("青龙业主号:{}状态异常，TraderOperateState:{}"
                    , expressOrderModel.getCustomer().getAccountNo()
                    , customerConfig.getTraderOperateState());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withCustom("客户配置信息校验失败,商家青龙业主号状态异常")
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.desc());
        }

        // 国际B2C不校验后续逻辑
        if (expressOrderModel.isIntlB2C()) {
            LOGGER.info("国际B2C，不检验客户信息后续逻辑");
            return;
        }

        // 散客挂月结校验，只有散客挂月结&&月结的情况下才会返回settCustomerConfig，否则为null
        CustomerConfig settCustomerConfig = checkIndividualType(expressOrderContext, expressOrderModel, customerConfig);

        //商家子类别为SOP且不为售后单校验
        checkJdOrder(expressOrderModel, customerConfig);

        // 隐私通话校验
        //checkHidePrivacy(expressOrderModel, customerConfig);

        // 订单总重量校验
        checkWeightInfo(expressOrderModel, customerConfig);

        // 线下协商运费校验
        checkEstimateAmount(expressOrderModel, customerConfig);

        // 开箱验货校验
        checkUnpackingInspection(expressOrderModel, customerConfig);

        // 明细揽收校验
        checkPickUpDetail(expressOrderModel, customerConfig);

        // 易收发校验
        checkEasySendReceive(expressOrderContext, customerConfig, settCustomerConfig);

        //云仓VMI订单校验
        if (expressUccConfigCenter.isClpsVmiCustomerConfigSwitch()) {
            checkCLPSVMI(expressOrderModel, customerConfig);
        }

        // 校验收件人是否允许改址
        if (expressOrderContext.isReaddressScene()) {
            consigneeReaddressCheck(expressOrderModel, expressOrderContext.getCustomerConfig());
        }
    }

    /**
     * 识别散客挂月结校验
     *
     * @param expressOrderContext,expressOrderModel,customerConfig
     * @param customerConfig
     */
    private CustomerConfig checkIndividualType(ExpressOrderContext expressOrderContext, ExpressOrderModel expressOrderModel, CustomerConfig customerConfig) {
        if (expressOrderModel.isProductBusinessMode125()) {
            LOGGER.info("125模式，不校验散客挂月结");
        }


        // 识别ordersign-unitedB2CFlag=1且主产品=快运此处逻辑走分支
        if (UnitedB2CUtil.isUnitedFreightB2C(expressOrderModel)) {
            checkFreightMonthlyPayment(expressOrderModel, customerConfig);
            return null;
        }

        CustomerConfig settCustomerConfig = null;

        //识别散客挂月结和结算方式，修改场景需要特殊处理：根据当前修改信息和快照数据一起识别
        ExpressOrderModel orderSnapshot = expressOrderContext.getOrderModel().getOrderSnapshot();
        String individualMsType = expressOrderModel.getAttachment(AttachmentKeyEnum.INDIVIDUAL_MS_TYPE.getKey());

        // 修改场景需要特殊处理：根据当前修改信息和快照数据一起识别
        if (StringUtils.isBlank(individualMsType) && BusinessSceneEnum.MODIFY.getCode().equals(expressOrderModel.getOrderBusinessIdentity().getBusinessScene())) {
            individualMsType = orderSnapshot.getAttachment(AttachmentKeyEnum.INDIVIDUAL_MS_TYPE.getKey());
        }
        SettlementTypeEnum settlementType = expressOrderModel.getFinance().getSettlementType();
        if (settlementType == null && BusinessSceneEnum.MODIFY.getCode().equals(expressOrderModel.getOrderBusinessIdentity().getBusinessScene())) {
            settlementType = orderSnapshot.getFinance().getSettlementType();
        }
        String settlementAccountNo = expressOrderModel.getFinance().getSettlementAccountNo();
        if (SettlementTypeEnum.MONTHLY_PAYMENT == settlementType
                && StringUtils.isBlank(settlementAccountNo)
                && BusinessSceneEnum.MODIFY.getCode().equals(expressOrderModel.getOrderBusinessIdentity().getBusinessScene())) {
            settlementAccountNo = orderSnapshot.getFinance().getSettlementAccountNo();
        }

        String customerAccountNo = expressOrderModel.getCustomer().getAccountNo();

        if (StringUtils.isNotBlank(individualMsType) && OrderConstants.YES_VAL.equals(individualMsType) &&
                SettlementTypeEnum.MONTHLY_PAYMENT == settlementType) {
            LOGGER.info("散客挂月结订单，结算方式为月结，校验结算账号有效性");
            if (!customerAccountNo.equals(settlementAccountNo)) {
                // 判断月结账号和履约账号是否一致,不一致重新查商家基础资料
                LOGGER.info("散客挂月结订单，判断月结账号和履约账号是否一致,不一致重新查商家基础资料");
                settCustomerConfig = customerConfigFacade.getCustomerConfigBySettlementAccountNo(settlementAccountNo);
            } else {
                settCustomerConfig = customerConfig;
            }
            //结算账号校验
            if (!TraderOperateStateEnum.normal.getCode().equals(settCustomerConfig.getTraderOperateState())) {
                LOGGER.error("青龙业主号:{}状态异常，TraderOperateState:{}"
                        , expressOrderModel.getFinance().getSettlementAccountNo()
                        , settCustomerConfig.getTraderOperateState());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL)
                        .withCustom("客户配置信息校验失败,商家青龙业主号状态异常")
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.desc());
            }
            if (!SettlementTypeEnum.MONTHLY_PAYMENT.getTraderSignCode().contains(TraderSignUtils.getTraderSignCode(settCustomerConfig.getTraderSign(), TraderSignEnum.SETTLEMENT.getCode()))) {
                LOGGER.error("结算方式校验失败,商家结算账户未开通月结结算方式");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL).withCustom("客户配置信息校验失败,结算方式校验失败")
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.desc());
            }
        }


        return settCustomerConfig;
    }

    /**
     * 快运月结账号校验
     *
     * @param expressOrderModel
     * @param customerConfig
     */
    private void checkFreightMonthlyPayment(ExpressOrderModel expressOrderModel, CustomerConfig customerConfig) {

        //若结算方式不是月结，则不进行商家配置校验。快运只校验月结，不考虑散客挂月结
        if (expressOrderModel.getFinance() == null || SettlementTypeEnum.MONTHLY_PAYMENT != expressOrderModel.getFinance().getSettlementType()) {
            LOGGER.info("结算方式不是月结，则不进行商家配置校验");
            return;
        }

        if (SystemCallerUtil.currentIsSupplyOFC(expressOrderModel)
                && SupplyChainDeliveryOrderSignUtil.currentFlag(expressOrderModel)) {
            LOGGER.info("仓配接配，不校验月结账号");
            return;
        }

        //结算账户不为空,则通过结算账号校验账户是否开通月结，否则校验配送履约账号是否开通月结
        if (StringUtils.isNotBlank(expressOrderModel.getFinance().getSettlementAccountNo())) {
            LOGGER.info("有结算账号，SettlementAccountNo={}", expressOrderModel.getFinance().getSettlementAccountNo());

            CustomerConfig settCustomerConfig = null;

            //识别月结结算方式，修改场景需要特殊处理：根据当前修改信息和快照数据一起识别
            ExpressOrderModel orderSnapshot = expressOrderModel.getOrderSnapshot();

            SettlementTypeEnum settlementType = expressOrderModel.getFinance().getSettlementType();
            if (settlementType == null && BusinessSceneEnum.MODIFY.getCode().equals(expressOrderModel.getOrderBusinessIdentity().getBusinessScene())) {
                settlementType = orderSnapshot.getFinance().getSettlementType();
            }
            String settlementAccountNo = expressOrderModel.getFinance().getSettlementAccountNo();
            if (SettlementTypeEnum.MONTHLY_PAYMENT == settlementType
                    && StringUtils.isBlank(settlementAccountNo)
                    && BusinessSceneEnum.MODIFY.getCode().equals(expressOrderModel.getOrderBusinessIdentity().getBusinessScene())) {
                settlementAccountNo = orderSnapshot.getFinance().getSettlementAccountNo();
            }

            String customerAccountNo = expressOrderModel.getCustomer().getAccountNo();

            LOGGER.info("结算方式为月结，校验结算账号有效性");
            if (!customerAccountNo.equals(settlementAccountNo)) {
                // 判断月结账号和履约账号是否一致,不一致重新查商家基础资料
                LOGGER.info("判断月结账号和履约账号是否一致,不一致重新查商家基础资料");
                settCustomerConfig = customerConfigFacade.getCustomerConfigBySettlementAccountNo(settlementAccountNo);
            } else {
                settCustomerConfig = customerConfig;
            }
            //结算账号校验
            if (!TraderOperateStateEnum.normal.getCode().equals(settCustomerConfig.getTraderOperateState())) {
                LOGGER.error("青龙业主号:{}状态异常，TraderOperateState:{}"
                        , expressOrderModel.getFinance().getSettlementAccountNo()
                        , settCustomerConfig.getTraderOperateState());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL)
                        .withCustom("客户配置信息校验失败,商家青龙业主号状态异常")
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.desc());
            }
            if (!SettlementTypeEnum.MONTHLY_PAYMENT.getTraderSignCode().contains(TraderSignUtils.getTraderSignCode(settCustomerConfig.getTraderSign(), TraderSignEnum.SETTLEMENT.getCode()))) {
                LOGGER.error("结算方式校验失败,商家结算账户未开通月结结算方式");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL).withCustom("客户配置信息校验失败,结算方式校验失败")
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.desc());
            }

        } else {
            if (expressUccConfigCenter.isFreightValidateMonthlyPaymentSwitch()) {
                LOGGER.info("快运校验商家是否开通月结：校验");
                if (OrderTypeEnum.DELIVERY.equals(expressOrderModel.getOrderType()) && !SettlementTypeEnum.MONTHLY_PAYMENT.getTraderSignCode().contains(TraderSignUtils.getTraderSignCode(customerConfig.getTraderSign(), TraderSignEnum.SETTLEMENT.getCode()))) {
                    LOGGER.error("结算方式校验失败,商家结算账户未开通月结结算方式");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL).withCustom("客户配置信息校验失败,结算方式校验失败")
                            .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.subCode())
                            .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.desc());
                }
            } else {
                LOGGER.info("快运校验商家是否开通月结：不校验");
            }
        }

    }

    /**
     * 功能: 易收发校验
     *
     * @param:
     * @return:
     * @throw:
     * @description:
     * @author: liufarui
     * @date: 2021/6/11 10:42
     */
    private void checkEasySendReceive(ExpressOrderContext context, CustomerConfig customerConfig, CustomerConfig settCustomerConfig) {
        ExpressOrderModel expressOrderModel = context.getOrderModel();
        //修改场景
        if (BusinessSceneEnum.MODIFY.getCode().equals(expressOrderModel.getOrderBusinessIdentity().getBusinessScene())) {
            if (null == expressOrderModel.getFinance() || null == expressOrderModel.getFinance().getSettlementType()) {
                LOGGER.info("跳过易收发校验：修改场景，未传settlementType");
                return;
            }
            // 仓配切配，systemCaller=SupplyOFC 并且 orderSign.supplyChainDelivery=1，跳过。取快照校验
            if (SystemCallerUtil.currentIsSupplyOFC(expressOrderModel.getOrderSnapshot())
                    && SupplyChainDeliveryOrderSignUtil.currentFlag(expressOrderModel.getOrderSnapshot())) {
                LOGGER.info("跳过易收发校验：修改场景，渠道和订单标识校验通过");
                return;
            }
        }
        //接单场景
        if (BusinessSceneEnum.CREATE.getCode().equals(expressOrderModel.getOrderBusinessIdentity().getBusinessScene())) {
            // 仓配切配，systemCaller=SupplyOFC 并且 orderSign.supplyChainDelivery=1，跳过
            if (SystemCallerUtil.currentIsSupplyOFC(expressOrderModel)
                    && SupplyChainDeliveryOrderSignUtil.currentFlag(expressOrderModel)) {
                LOGGER.info("跳过易收发校验：接单场景，渠道和订单标识校验通过");
                return;
            }
        }
        String individualMsType = expressOrderModel.getAttachment(AttachmentKeyEnum.INDIVIDUAL_MS_TYPE.getKey());
        String settlementAccountNo = expressOrderModel.getFinance().getSettlementAccountNo();
        //散客挂月结
        //https://joyspace.jd.com/pages/6dmKZyonLKQMwTQPbwDT
        if ("1".equals(individualMsType) && StringUtils.isNotBlank(settlementAccountNo)) {
            if (null == settCustomerConfig) {
                settCustomerConfig = customerConfigFacade.getCustomerConfigBySettlementAccountNo(settlementAccountNo);
            }
            checkEasySendReceiveExt(context, settCustomerConfig, settlementAccountNo);
        } else {
            checkEasySendReceiveExt(context, customerConfig, expressOrderModel.getCustomer().getAccountNo());
        }
    }


    /**
     * 易收发扩展
     * 散客挂月结使用结算账号
     *
     * @param context 上下文
     * @param customerConfig 客户配置信息
     * @param accountNo 结算账户
     */
    private void checkEasySendReceiveExt(ExpressOrderContext context, CustomerConfig customerConfig, String accountNo) {
        ExpressOrderModel expressOrderModel = context.getOrderModel();
        if (!SIGN_TYPE_1.equals(TraderSignUtils.getTraderSignCode(customerConfig.getTraderSign(), TraderSignEnum.AGENCY_TYPE.getCode()))) {
            LOGGER.info("商家未开通易收发服务，不做易收发校验。traderSign第{}位值为{}", TraderSignEnum.AGENCY_TYPE.getCode(), TraderSignUtils.getTraderSignCode(customerConfig.getTraderSign(), TraderSignEnum.AGENCY_TYPE.getCode()));
            return;
        }

        SettlementTypeEnum settlementType = expressOrderModel.getFinance().getSettlementType();
        if (!settlementType.getTraderSignCode().contains(Objects.requireNonNull(TraderSignUtils.getTraderSignCode(customerConfig.getTraderSign(), TraderSignEnum.SETTLEMENT.getCode())))) {
            LOGGER.error(String.format("客户配置信息校验失败,商家未开通%s结算方式", settlementType.getDesc()));
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.subCode())
                    .withCustom(String.format("客户配置信息校验失败,商家未开通%s结算方式", settlementType.getDesc()));
        }

        //预收款停用金额
        Integer preChargeMoney = customerConfig.getPreChargeMoney();
        if (null == preChargeMoney) {
            // 预收款停用金额为空 报错异常 发自定义告警
            UmpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_PRE_CHARGE_MONEY_FAIL
                    , accountNo + "预收款停用金额为空"
                    , expressOrderModel.requestProfile()
                    , expressOrderModel.businessIdentity()
                );
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0027.desc())
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0027.subCode());
        }
        //现结余额查询
        AccountBookRequest request = accountBookTranslator.toAccountBookRequest(customerConfig, accountNo, expressOrderModel);
        AccountBookResponse response = accountBookFacade.querySingleAccountBookAmount(request);
        if (new BigDecimal(preChargeMoney).compareTo(response.getResultData()) >= 0) {
            LOGGER.error("客户配置信息校验失败，余额为{}，停用金额为{}，余额小于等于停用金额", preChargeMoney, response.getResultData());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0009.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0009.desc());
        }

        if (SettlementTypeEnum.MONTHLY_PAYMENT != settlementType) {
            // 非寄付月结 不处理
            LOGGER.info("非寄付月结,不获取预占模式");
            return;
        }

        Money estimateAmount = expressOrderModel.getFinance().getEstimateAmount();
        if (null == estimateAmount || null == estimateAmount.getAmount()) {
            LOGGER.info("预估运费为空，不获取预占模式");
            return;
        }

        // 获取预占金额模式
        boolean isSupplyChain = SystemCallerUtil.currentIsSupplyOFC(expressOrderModel)
                    && SupplyChainDeliveryOrderSignUtil.currentFlag(expressOrderModel);
        if (!isSupplyChain) {
            // 只有非仓配接配获取
            Integer preoccupyMode = TraderSignUtils.getTraderSign(customerConfig.getTraderSign(), TraderSignEnum.PREOCCUPY_MODE.getCode());
            if (null != preoccupyMode && !OccupyModeEnum.NON.getCode().equals(preoccupyMode)) {
                // 开通收发管家，存在预占模式
                LOGGER.info("预占模式为: {}", preoccupyMode);
                expressOrderModel.complement().complementOccupyMode(this, preoccupyMode);
                // 判断是否需要预占
                context.putExtMaps(ContextInfoEnum.YISHOUFA.getCode(), preoccupyMode);
            }
        }

    }

    /**
     * 功能: 校验隐私通话
     *
     * @param:
     * @return:
     * @throw:
     * @description: 不传值时，若商家开通此服务，则补齐此服务
     * @author: liufarui
     * @date: 2021/6/11 10:42
     */
    private void checkHidePrivacy(ExpressOrderModel expressOrderModel, CustomerConfig customerConfig) {
        //修改场景未传或移除隐私通话不做校验
        if (BusinessSceneEnum.MODIFY.getCode().equals(expressOrderModel.getOrderBusinessIdentity().getBusinessScene())
                && (expressOrderModel.getShipment().getServiceRequirements() == null || StringUtils.isBlank(expressOrderModel.getShipment().getServiceRequirements().get(HIDE_PRIVACY_KEY)))) {
            return;
        }
        //商家开通隐私通话，但订单未传隐私通话相关值，需要补全信息
        if (SIGN_TYPE_1.equals(TraderSignUtils.getTraderSignCode(customerConfig.getTraderSign(), TraderSignEnum.PRIVACY_CONVERSATION.getCode()))) {
            if (!expressOrderModel.getShipment().serviceRequirementsContainsKey(HIDE_PRIVACY_KEY)) {
                expressOrderModel.getShipment().serviceRequirementsPut(HIDE_PRIVACY_KEY, SIGN_TYPE_1);
            }
        } else {
            //订单传了隐私通话相关值，商家未开通隐私通话，拒单
            if (expressOrderModel.getShipment().serviceRequirementsContainsKey(HIDE_PRIVACY_KEY)) {
                LOGGER.error("客户配置信息校验失败，未开通隐私通话服务");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0010.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0010.desc());
            }
        }
    }

    /**
     * 功能: 校验订单总重量
     *
     * @param:
     * @return:
     * @throw:
     * @description: 公司内部商家和分拣退货组商家不校验
     * @author: liufarui
     * @date: 2021/6/11 10:42
     */
    private void checkWeightInfo(ExpressOrderModel orderModel, CustomerConfig customerConfig) {
        // 公司内部商家和分拣退货组商家不校验总重量
        if (IN_COMPANY == customerConfig.getTraderMold() || SORT_RETURN == customerConfig.getTraderMold()) {
            return;
        }
        //接单已经在基本信息校验处理 不再重复操作
        if (BusinessSceneEnum.CREATE.getCode().equals(orderModel.getOrderBusinessIdentity().getBusinessScene())) {
            return;
        }
        //修改场景未传不做校验
        if (BusinessSceneEnum.MODIFY.getCode().equals(orderModel.getOrderBusinessIdentity().getBusinessScene())) {
            if (null == orderModel.getCargoDelegate().totalCargoWeight()) {
                return;
            }
        }

        BigDecimal totalWeight = orderModel.getCargoDelegate().totalCargoWeight();
        List<String> cargoWeightSystemCallerList = expressUccConfigCenter.getCargoWeightSystemCallerList();
        String orderSnapshotSystemCaller = orderModel.getOrderSnapshot().getChannel().getSystemCaller().getCode();
        //订单接单时是JOS｜CLPS来源订单货品重量可操作修改为0
        if (CollectionUtils.isNotEmpty(cargoWeightSystemCallerList) && cargoWeightSystemCallerList.contains(orderSnapshotSystemCaller)) {
            if (minTotalWeight.compareTo(totalWeight) > 0 || maxTotalWeight.compareTo(totalWeight) < 0) {
                LOGGER.error("客户配置信息校验失败，订单总重量为{}，订单总重量需大于{}且小于{}", totalWeight, minTotalWeight, maxTotalWeight);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0011.subCode())
                        .withCustom("客户配置信息校验失败，订单总重量需大于" + minTotalWeight + "且小于" + maxTotalWeight);
            }
        } else {
            if (minTotalWeight.compareTo(totalWeight) >= 0 || maxTotalWeight.compareTo(totalWeight) < 0) {
                LOGGER.error("客户配置信息校验失败，订单总重量为{}，订单总重量需大于{}且小于{}", totalWeight, minTotalWeight, maxTotalWeight);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0011.subCode())
                        .withCustom("客户配置信息校验失败，订单总重量需大于" + minTotalWeight + "且小于" + maxTotalWeight);
            }
        }

    }

    /**
     * 功能: 线下协商运费校验
     *
     * @param:
     * @return:
     * @throw:
     * @description: 开通线下协商运费时，订单上的预估金额要大于0且小于1000000
     * @author: liufarui
     * @date: 2021/6/11 10:42
     */
    private void checkEstimateAmount(ExpressOrderModel expressOrderModel, CustomerConfig customerConfig) {
        //修改场景下，如果没有对线下协商运单预估金额修改，则不做校验
        if (BusinessSceneEnum.MODIFY.getCode().equals(expressOrderModel.getOrderBusinessIdentity().getBusinessScene())) {
            if (null == expressOrderModel.getFinance()
                    || null == expressOrderModel.getFinance().getEstimateAmount()
                    || null == expressOrderModel.getFinance().getEstimateAmount().getAmount()) {
                return;
            }
        }
        if (SIGN_TYPE_1.equals(TraderSignUtils.getTraderSignCode(customerConfig.getTraderSign(),
                TraderSignEnum.OFFLINE_CONSULT_FREIGHT.getCode()))) {
            if (null == expressOrderModel.getFinance()
                    || null == expressOrderModel.getFinance().getEstimateAmount()
                    || null == expressOrderModel.getFinance().getEstimateAmount().getAmount()
                    || MIN_ESTIMATE_AMOUNT.compareTo(expressOrderModel.getFinance().getEstimateAmount().getAmount()) >= 0
                    || MAX_ESTIMATE_AMOUNT.compareTo(expressOrderModel.getFinance().getEstimateAmount().getAmount()) <= 0) {
                LOGGER.error("客户配置信息校验失败，开通线下协商运费时，订单预估金额需大于{}且小于{}", MIN_ESTIMATE_AMOUNT, MAX_ESTIMATE_AMOUNT);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withCustom("客户配置信息校验失败,开通线下协商运费时,订单预估金额需大于" + MIN_ESTIMATE_AMOUNT + "且小于" + MAX_ESTIMATE_AMOUNT);
            }
        }
    }

    /**
     * 功能: 开箱验货校验
     *
     * @param:
     * @return:
     * @throw:
     * @description:
     * @author: liufarui
     * @date: 2021/6/11 10:43
     */
    private void checkUnpackingInspection(ExpressOrderModel expressOrderModel, CustomerConfig customerConfig) {

        // 上游没有传开箱验货值，直接返回不校验
        if (expressOrderModel.getShipment().getServiceRequirements() == null
                || StringUtils.isBlank(expressOrderModel.getShipment().getServiceRequirements().get(UNPACKING_INSPECTION))) {
            return;
        }
        String unpackingInspectionStr = expressOrderModel.getShipment().getServiceRequirements().get(UNPACKING_INSPECTION);
        // 上游传的值没有被成功解析，拒单
        UnpackingInspectionEnum unpackingInspectionEnum = UnpackingInspectionEnum.fromCode(Integer.parseInt(unpackingInspectionStr));
        if (null == unpackingInspectionEnum) {
            LOGGER.error("客户配置信息校验失败，开箱验货校验失败，传值不准确");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL).withCustom("客户配置信息校验失败,开箱验货校验失败，传值不准确");
        }

        // 暂时不支持随心验
        if (UnpackingInspectionEnum.RANDOM_CHECK == unpackingInspectionEnum) {
            LOGGER.error("客户配置信息校验失败，开箱验货校验失败，不支持随心验");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL).withCustom("客户配置信息校验失败,开箱验货校验失败，不支持随心验");
        }

        // 订单传了开箱验货服务，但商家未开通开箱验货服务，拒单
        String traderSignCode = TraderSignUtils.getTraderSignCode(customerConfig.getTraderSign(), TraderSignEnum.UNPACKING_INSPECTION.getCode());
        if (StringUtils.isBlank(traderSignCode)
                || SIGN_TYPE_0.equals(traderSignCode)) {
            LOGGER.error("客户配置信息校验失败，开箱验货标位获取为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL).withCustom("客户配置信息校验失败，开箱验货标位获取为空");
        }

        // 订单下发开箱验货服务，但是下发列表里面不包含从商家基础资料中获取到的标位服务
        if (!unpackingInspectionEnum.getTradeSignCode().contains(traderSignCode)) {
            LOGGER.error("客户配置信息校验失败，开箱验货校验失败，下发开箱服务: {} 不包含标位: {}", unpackingInspectionEnum.getDesc(), traderSignCode);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL)
                    .withCustom(String.format("客户配置信息校验失败,开箱验货校验失败，下发开箱服务: %s 不包含标位: %s", unpackingInspectionEnum.getDesc(), traderSignCode));
        }
    }

    /**
     * 功能: 明细揽收校验
     *
     * @param:
     * @return:
     * @throw:
     * @description:
     * @author: liufarui
     * @date: 2021/6/11 10:43
     */
    private void checkPickUpDetail(ExpressOrderModel expressOrderModel, CustomerConfig customerConfig) {
        // 非缺量揽收不校验
        if (!PICKUP_DETAIL_2.equals(expressOrderModel.getShipment().serviceRequirementsGetValue(PICKUP_DETAIL))) {
            LOGGER.info("明细揽收为非缺量揽收，不做校验");
            return;
        }

        // 缺量揽收时，商家未开通明细揽收服务，拒单
        if (SIGN_TYPE_0.equals(TraderSignUtils.getTraderSignCode(customerConfig.getTraderSign(), TraderSignEnum.PICKUP_DETAIL.getCode()))) {
            LOGGER.error("客户配置信息校验失败，明细揽收校验失败");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0012.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0012.desc());
        }

        List<Goods> goodsList = (List<Goods>) expressOrderModel.getGoodsDelegate().getGoodsList();

        // 缺量揽收允许商品为空
        if (CollectionUtils.isEmpty(goodsList)) {
            return;
        }

        // 明细商品总数量
        BigDecimal totalGoodsNum = BigDecimal.ZERO;

        // 明细中商品编码、商品名称、商品数量不能为空
        for (Goods goods : goodsList) {
            if (StringUtils.isBlank(goods.getGoodsNo())) {
                LOGGER.error("客户配置信息校验失败，明细揽收校验失败，商品编码为空！");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0013.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0013.desc());
            }
            if (StringUtils.isBlank(goods.getGoodsName())) {
                LOGGER.error("客户配置信息校验失败，明细揽收校验失败，商品名称为空！");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0014.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0014.desc());
            }
            if (goods.getGoodsQuantity() == null) {
                LOGGER.error("客户配置信息校验失败，明细揽收校验失败，商品数量为空！");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0015.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0015.desc());
            }
            if (goods.getGoodsQuantity().getValue() == null || StringUtils.isBlank(goods.getGoodsQuantity().getUnit())) {
                LOGGER.error("客户配置信息校验失败，明细揽收校验失败，商品数量值或单位为空！");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0016.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0016.desc());
            } else {
                totalGoodsNum = totalGoodsNum.add(goods.getGoodsQuantity().getValue());
            }
        }

        // 明细商品总数量不能大于99999
        if (MAX_TOTAL_GOODS_NUM.compareTo(totalGoodsNum) < 0) {
            LOGGER.error("客户配置信息校验失败，明细揽收校验失败，商品总数量大于{}", MAX_TOTAL_GOODS_NUM);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0017.subCode())
                    .withCustom("客户配置信息校验失败,明细揽收校验失败，商品总数量大于" + MAX_TOTAL_GOODS_NUM);
        }
    }

    /**
     * 子类别为SOP时校验
     *
     * @param expressOrderModel
     * @param customerConfig
     */
    private void checkJdOrder(ExpressOrderModel expressOrderModel, CustomerConfig customerConfig) {
        //修改场景不校验
        if (BusinessSceneEnum.MODIFY.getCode().equals(expressOrderModel.getOrderBusinessIdentity().getBusinessScene())) {
            return;
        }
        // systemCaller=SupplyOFC需要跳过
        if (SystemCallerUtil.currentIsSupplyOFC(expressOrderModel)) {
            LOGGER.info("systemCaller=SupplyOFC，不执行渠道校验");
            return;
        }

        if (needCheckSubTraderMold(customerConfig) && !AFTER_SALE_CHANNEL_NO.equals(expressOrderModel.getChannel().getChannelNo())) {
            if (expressOrderModel.getExtendProps() != null && StringUtils.isNotBlank(expressOrderModel.getExtendProps().get(AttachmentKeyEnum.CUSTOM_EXTEND_MSG.getKey()))) {
                Map<String, String> customExtendMsg = JSONUtils.jsonToMap(expressOrderModel.getExtendProps().get(AttachmentKeyEnum.CUSTOM_EXTEND_MSG.getKey()));
                String skipCheckSopChannelNo = customExtendMsg.get(AttachmentKeyEnum.SKIP_CHECK_SOP_CHANNEL_NO.getKey());
                if (OrderConstants.YES_VAL.equals(skipCheckSopChannelNo)) {
                    LOGGER.info("skipCheckSopChannelNo={},跳过渠道校验", skipCheckSopChannelNo);
                    return;
                }
            }
            if (!JD_CHANNEL_NO.equals(expressOrderModel.getChannel().getChannelNo())) {
                LOGGER.error("客户配置信息校验失败，商家子类别为SOP或京喜类型且渠道编码不为售后单时，渠道编码必须为京东平台订单");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0018.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0018.desc());
            }
            if (CollectionUtils.isNotEmpty(expressOrderModel.getProductDelegate().getCodProducts()) &&
                    expressOrderModel.getChannel().getChannelOrderNo().split(",").length > 1) {
                LOGGER.error("客户配置信息校验失败，商家子类别为SOP或京喜类型,渠道编码不为售后单且存在代收货款时，渠道订单号存在多个");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0019.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0019.desc());
            }
        }
    }

    /**
     * 是否校验商家账号：SubTraderMold
     */
    private boolean needCheckSubTraderMold(CustomerConfig customerConfig) {
        if (SUB_TRADER_MOLD_SOP.equals(customerConfig.getSubTraderMold())) {
            return true;
        }

        if (SUB_TRADER_MOLD_JINGXI.equals(customerConfig.getSubTraderMold())) {
            LOGGER.info("subTraderMold命中京喜商家账号订单");
            if (expressUccConfigCenter.isB2cExpressJingXiQuanTuoCheckSwitch()) {
                LOGGER.info("subTraderMold命中京喜商家账号订单, 开关开启");
                return true;
            } else {
                LOGGER.info("subTraderMold命中京喜商家账号订单, 开关关闭");
                return false;
            }
        }
        return false;
    }

    /**
     * 云仓VMI订单校验
     * 若识别是云仓VMI订单，则青龙业主号中的商家类型=零售服务内单账号且商家子类型必须为云仓VMI类型，不满足拒单；
     * 若识别不是云仓VMI订单，若青龙业主号同时满足：1）商家类型=零售服务内单账号；2）商家子类型=云仓VMI类型，拒单；
     */
    public void checkCLPSVMI(ExpressOrderModel expressOrderModel, CustomerConfig customerConfig) {
        if(!expressOrderModel.isB2C()){
            LOGGER.info("只有B2C进行云仓VMI订单校验");
            return;
        }
        //只有接单场景校验
        if (!BusinessSceneEnum.CREATE.getCode().equals(expressOrderModel.getOrderBusinessIdentity().getBusinessScene())) {
            LOGGER.info("只有接单场景校验");
            return;
        }
        if (!(OrderTypeEnum.DELIVERY == expressOrderModel.getOrderType())) {
            LOGGER.info("只有正向单进行云仓VMI订单校验");
            return;
        }
        checkCLPSVMIConfig(expressOrderModel, customerConfig);
    }
}
