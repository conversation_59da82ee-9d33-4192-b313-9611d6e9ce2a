package cn.jdl.oms.express.b2c.extension.basic;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.basic.IBasicInfoExtension;
import cn.jdl.oms.express.domain.infrs.acl.baseHandler.basicInfo.CargoValidBaseHandler;
import cn.jdl.oms.express.domain.infrs.acl.baseHandler.basicInfo.ShipmentValidBaseHandler;
import cn.jdl.oms.express.domain.infrs.acl.facade.basic.BasicInfoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.basic.BasicInfoFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.basic.BasicInfoFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.basic.BasicInfoFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.util.CargoWaybillCodeRuleUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.SystemCallerUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.SupplyChainDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.cache.BasicCache;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.CancelStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.IdentityTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.InitiatorTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.InterceptTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PickupTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.spec.dict.VolumeTypeEnum;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Cargo;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.Goods;
import cn.jdl.oms.express.domain.vo.ProductDelegate;
import cn.jdl.oms.express.domain.vo.Quantity;
import cn.jdl.oms.express.domain.vo.Shipment;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.BusinessConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.ExpressOrderStatusCustomEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyMarkEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.ModifyMarkUtil;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.util.Arrays;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
/**
 * @version v1.0
 * @ProjectName: jdl-oms-express
 * @Package: cn.jdl.oms.express.b2c.extension.basic
 * @ClassName: CreateBasicInfoExtension
 * @Description: 接单基本信息校验b2c垂直扩展点实现
 * @Author: xuezhiguo5
 * @CreateDate: 2021/4/27
 * @Since JDK 1.8
 */
@Extension(code = ExpressOrderProduct.CODE)
public class B2CCreateBasicInfoExtension implements IBasicInfoExtension {
    private static final Logger LOGGER = LoggerFactory.getLogger(B2CCreateBasicInfoExtension.class);

    /**
     * 基础信息校验
     */
    @Resource
    private BasicInfoFacade basicInfoFacade;

    /**
     * 基本信息校验的转换器
     */
    @Resource
    private BasicInfoFacadeTranslator basicInfoFacadeTranslator;

    /**
     * ucc
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 详细地址的长度
     */
    private static final Integer ADDRESS_LENGTH = 200;

    /**
     * 寄件人的身份证的长度
     */
    private static final Integer CONSIGNOR_ID_NO_LENGTH_OLD = 15;

    /**
     * 寄件人的身份证的长度
     */
    private static final Integer CONSIGNOR_ID_NO_LENGTH_NEW = 18;

    /**
     * 运费金额最大值
     */
    private static final Integer FINANCE_AMOUNT = 1000000;

    /**
     * 货品数量的最小值
     */
    private static final Integer CARGO_QUANTITY_MIN = 0;

    /**
     * 货品数量的最大值
     */
    private static final Integer CARGO_QUANTIFY = 50000;

    /**
     * 寄件人手机号码和电话号码最大长度
     */
    private static final int CONSIGNOR_PHONE_AND_MOBILE_MAX_LENGTH = 30;

    /**
     * 校验体积最大值
     */
    private static final BigDecimal VOLUME_MAX_VALUE = new BigDecimal("9999000000");

    /**
     * 校验重量最大值
     */
    private static final BigDecimal WEIGHT_MAX_VALUE = new BigDecimal("9999000");

    /**
     * 商家指定信息长度
     */
    public static final Integer SELF_DEFINE_INFO_LENGTH = 500;

    /**
     * 末级部门编码长度限制
     */
    private static final int DEPARTMENT_NO_BOUND = 50;

    /**
     * 商家逆向退货场景指定退货地址必传类型
     */
    private static final String RETURN_TYPE = "11";

    /**
     * 接单时解析并校验货品信息中的运单号
     */
    @Resource
    private CargoWaybillCodeRuleUtil cargoWaybillCodeRuleUtil;

    @Resource
    private BasicCache basicCache;

    @Resource
    private CargoValidBaseHandler cargoValidBaseHandler;

    /**
     * 派送信息校验
     */
    @Resource
    private ShipmentValidBaseHandler shipmentValidBaseHandler;

    /**
     * b2c接单基础信息校验扩展点
     *
     * @param context 领域上下文
     * @throws AbilityExtensionException
     * <AUTHOR>
     */
    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("b2c接单基本信息校验扩展点执行开始");
            ExpressOrderModel orderModel = context.getOrderModel();
            this.basicInfoValid(orderModel);
            //收寄件信息校验
            this.consignValid(orderModel);
            //货物信息校验
            this.cargosValid((List<Cargo>) orderModel.getCargoDelegate().getCargoList(), orderModel.getChannel(), orderModel);
            //渠道编码信息校验
            this.channelValid(orderModel);
            //补充-商品信息校验
            this.goodsValid(orderModel);
            //补充-派送方式校验
            this.shipmentValid(orderModel);
            //补充-财务信息校验
            this.financeValid(orderModel);
            // 产品信息校验
            this.productsValid(orderModel);
            // 改址单基本信息校验
            if (OrderTypeEnum.READDRESS == orderModel.getOrderType()) {
                LOGGER.info("b2c接单改址单基本信息校验扩展点执行开始");
                readdressValid(orderModel);
                LOGGER.info("b2c接单改址单基本信息校验扩展点执行结束");
            }
            //扩展信息校验
            this.extendsValid(orderModel);
            //退货信息校验
            this.returnInfoValid(orderModel);
            // 解决方案校验
            this.businessSolutionInfoValid(orderModel);
            LOGGER.info("b2c接单基本信息校验扩展点执行结束");
        } catch (InfrastructureException infrastructureException) {
            Profiler.functionError(callerInfo);
            LOGGER.error("b2c接单基本信息校验扩展点执行异常, traceId={}", context.getOrderModel().traceId(), infrastructureException);
            throw infrastructureException;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("b2c接单基本信息校验扩展点执行异常, traceId={}", context.getOrderModel().traceId(), exception);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL, exception);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * b2c扩展点基本信息校验
     *
     * @param orderModel
     */
    private void basicInfoValid(ExpressOrderModel orderModel) {
        if (OrderTypeEnum.RETURN_ORDER != orderModel.getOrderType() && orderModel.getShipment().getPickupType() == null) {
            LOGGER.error("B2C订单接单的基本信息-配送信息的揽收方式为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("B2C订单接单的基本信息-配送信息的揽收方式为空");
        }
        if (null == orderModel.getOrderSubType()) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("B2C订单接单的基本信息-订单子类型为空");
        }
    }

    /**
     * 产品信息校验
     * @param orderModel
     */
    private void productsValid(ExpressOrderModel orderModel) {
        if (null == orderModel.getProductDelegate() || orderModel.getProductDelegate().isEmpty()) {
            return;
        }

        if (null != orderModel.getProductDelegate().ofProductNo(AddOnProductEnum.DELIVERY_IN_BATCHES.getCode())) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("B2C订单接单的基本信息-不合规产品:分批派送");
        }

    }

    /**
     * 退货信息校验
     * @param orderModel
     */
    private void returnInfoValid(ExpressOrderModel orderModel){
        //商家逆向场景指定退货地址
        if (orderModel.getReturnInfoVo() != null &&
                RETURN_TYPE.equals(orderModel.getReturnInfoVo().getReturnType())) {
            Consignee consignee = orderModel.getReturnInfoVo().getConsignee();
            if (StringUtils.isBlank(consignee.getConsigneeName())
                    || (StringUtils.isBlank(consignee.getConsigneePhone())  && StringUtils.isBlank(consignee.getConsigneeMobile()))
                    || consignee.getAddress() == null
                    || StringUtils.isBlank(consignee.getAddress().getAddress())) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.APPOINT_RETURN_INFO_LOSS)
                        .withCustom("B2C订单接单的基本信息-指定退货地址信息不全");
            }
        }
    }

    /**
     * 扩展信息校验
     * @param orderModel
     */
    private void extendsValid(ExpressOrderModel orderModel) {
        String selfDefineInfo = orderModel.getAttachment(AttachmentKeyEnum.SELF_DEFINE_INFO.getKey());
        if (StringUtils.isNotBlank(selfDefineInfo) && selfDefineInfo.length() > SELF_DEFINE_INFO_LENGTH) {
            LOGGER.error("B2C订单接单的基本信息-商家自定义信息长度大于500");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("B2C订单接单的基本信息-商家自定义信息长度大于500");
        }

        if (StringUtils.length(orderModel.getAttachment(AttachmentKeyEnum.DEPARTMENT_NO.getKey())) > DEPARTMENT_NO_BOUND) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("下单人末级部门编码超长");
        }
    }

    /**
     * b2c收寄件信息校验
     *
     * @param orderModel
     * @throws DomainAbilityException
     */
    private void consignValid(ExpressOrderModel orderModel) throws Exception {

        Consignor consignor = orderModel.getConsignor();
        Consignee consignee = orderModel.getConsignee();
        Channel channel = orderModel.getChannel();

        if (null == consignor) {
            LOGGER.error("寄件人为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("寄件人为空");
        } else {
            // 发件人电话和手机不能同时为空
            if (StringUtils.isBlank(consignor.getConsignorPhone()) && StringUtils.isBlank(consignor.getConsignorMobile())) {
                LOGGER.error("寄件人电话和手机同时为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("寄件人的电话和手机同时为空");
            }
        }

        if (StringUtils.isNotBlank(consignor.getConsignorPhone())
                && consignor.getConsignorPhone().length() > CONSIGNOR_PHONE_AND_MOBILE_MAX_LENGTH) {
            LOGGER.error("接单发货人电话号码超出最大长度{}", CONSIGNOR_PHONE_AND_MOBILE_MAX_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("发货人电话号超长");
        }
        if (StringUtils.isNotBlank(consignor.getConsignorMobile())
                && consignor.getConsignorMobile().length() > CONSIGNOR_PHONE_AND_MOBILE_MAX_LENGTH) {
            LOGGER.error("接单发货人手机号码超出最大长度{}", CONSIGNOR_PHONE_AND_MOBILE_MAX_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("发货人手机号超长");
        }
        if (consignor.getConsignorIdType() != null
                && !IdentityTypeEnum.ID_CARD.getCode().equals(consignor.getConsignorIdType().getCode())
                && consignor.getConsignorIdNo() != null
                && (consignor.getConsignorIdNo().length() < 6 || consignor.getConsignorIdNo().length() > 20)) {
            LOGGER.error("接单基本信息校验活动能力开始执行,证件号不能小于6位或者大于20位");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("证件号不能小于6位或者大于20位");
        }

        if (null == consignee) {
            LOGGER.error("收件人为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("收件人为空");
        } else {
            if (StringUtils.isBlank(consignee.getConsigneePhone()) && StringUtils.isBlank(consignee.getConsigneeMobile())) {
                if (!BusinessConstants.JD_CHANNEL_NO.equals(channel.getChannelNo())) {
                    LOGGER.error("收件人电话和手机同时为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("收件人的电话和手机同时为空");
                }

            }
        }

        if (StringUtils.isBlank(consignor.getConsignorName())) {
            LOGGER.error("接单基本信息校验活动能力开始执行,发货人姓名为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("发货人姓名为空");
        }

        if (StringUtils.isBlank(consignee.getConsigneeName())) {
            LOGGER.error("接单基本信息校验活动能力开始执行,收货人姓名为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("收货人姓名为空");
        }

        //校验第二点：证件类型为身份证或者户口薄时，需要验证证件号码的长度为15位或者为18位
        if (consignor.getConsignorIdType() != null && (consignor.getConsignorIdType() == IdentityTypeEnum.ID_CARD ||
                consignor.getConsignorIdType() == IdentityTypeEnum.HOUSEHOLD_REGISTER)) {
            if (consignor.getConsignorIdNo() == null
                    || (consignor.getConsignorIdNo().length() != CONSIGNOR_ID_NO_LENGTH_OLD
                    && consignor.getConsignorIdNo().length() != CONSIGNOR_ID_NO_LENGTH_NEW)) {
                LOGGER.error("寄件人的身份证号码不等于15位或者18位");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("寄件人的身份证号码不等于15位或者18位");
            }
        }

        if(null != consignor.getConsignorIdType() && StringUtils.isBlank(consignor.getConsignorIdNo())){
            LOGGER.error("B2C接单基本信息校验,寄件人证件号不能为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("寄件人证件号不能为空");
        }

        if(null != consignee.getConsigneeIdType() && StringUtils.isBlank(consignee.getConsigneeIdNo())){
            LOGGER.error("B2C接单基本信息校验,收件人证件号不能为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("收件人证件号不能为空");
        }


        boolean isUnitedFreightB2C = UnitedB2CUtil.isUnitedFreightB2C(orderModel);

        //寄件人地址信息校验
        addressValid(consignor.getAddress(), InitiatorTypeEnum.CONSIGNOR.getDesc(), isUnitedFreightB2C);

        //收货人地址信息校验
        addressValid(consignee.getAddress(), InitiatorTypeEnum.CONSIGNEE.getDesc(), isUnitedFreightB2C);

        // 国际B2C正向单
        if (orderModel.isIntlB2CExpress()) {

            // 收件人邮编必填
            if (StringUtils.isBlank(consignee.getConsigneeZipCode())) {
                LOGGER.error("接单基本信息校验,国际B2C,收件人邮编必填");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("收件人邮编必填");
            }

            // 收件人行政区编码必填
            if (StringUtils.isBlank(consignee.getAddress().getRegionNo())) {
                LOGGER.error("接单基本信息校验,国际B2C,收件人行政区编码必填");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("收件人行政区编码必填");
            }

        }

    }

    /**
     * 地址信息校验
     *
     * @param address
     * @param contact
     * @return
     */
    private void addressValid(Address address, String contact, boolean isUnitedFreightB2C) {
        if (address == null) {
            LOGGER.error("接单基本信息校验活动能力开始执行,{}地址信息为空", contact);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("%s地址信息为空", contact));
        }
        if (StringUtils.isBlank(address.getAddress())) {
            LOGGER.error("接单基本信息校验活动能力开始执行,{}详细地址为空", contact);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("%s详细地址为空", contact));
        }
        if (address.getAddress().length() > ADDRESS_LENGTH) {
            LOGGER.error("{}详细地址长度不能超过{}", contact, ADDRESS_LENGTH);
            // 此处开关默认false不校验，无印港澳放开到350，若无问题下线开关直接删除校验逻辑即可
            BatrixSwitch.applyDefNotExecute(BatrixSwitchKey.ADDRESS_LENGTH_VALIDATION, (bTrue) -> {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom(contact + "详细地址长度不能超过" + ADDRESS_LENGTH);
            });
        }

        // 识别ordersign-unitedB2CFlag=1且主产品=快运，校验地址是否围栏信任fenceTrusted非空
        if (isUnitedFreightB2C && address.getFenceTrusted() == null) {
            LOGGER.error("接单基本信息校验活动能力开始执行,{}地址是否围栏信任为空", contact);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(String.format("%s地址是否围栏信任为空", contact));

        }

    }


    /**
     * b2c货品信息校验
     *
     * @param cargos
     * @param orderModel
     * @throws DomainAbilityException
     */
    private void cargosValid(List<Cargo> cargos, Channel channel, ExpressOrderModel orderModel) throws DomainAbilityException {
        if (CollectionUtils.isNotEmpty(cargos)) {
            cargos.forEach(cargo -> {
                if (cargo == null) {
                    LOGGER.error("接单基本信息校验活动能力开始执行,货品信息为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品信息为空");
                }
                Quantity cargoQuantity = cargo.getCargoQuantity();
                if (null == cargoQuantity) {
                    LOGGER.error("货品为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品为空");
                } else if (null == cargoQuantity.getValue()) {
                    LOGGER.error("货品的数量值为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品的数量值为空");
                } else if (null == cargoQuantity.getUnit()) {
                    LOGGER.error("货品的数量单位为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品的数量单位为空");
                }
                if (StringUtils.isBlank(cargo.getCargoName())) {
                    LOGGER.error("接单基本信息校验活动能力开始执行,货品名称为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品名称为空");
                }
                if (cargo.getCargoWeight() == null) {
                    LOGGER.error("接单基本信息校验活动能力开始执行,货品重量信息为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品重量信息为空");
                }
                if (cargo.getCargoWeight().getUnit() == null) {
                    LOGGER.error("接单基本信息校验活动能力开始执行,货品重量单位为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品重量单位为空");
                }
                if (cargo.getCargoWeight().getValue() == null) {
                    LOGGER.error("接单基本信息校验活动能力开始执行,货品重量为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品重量为空");
                }
                // 体积判空校验
                if (cargo.getCargoVolume() == null) {
                    LOGGER.error("接单基本信息校验活动能力开始执行,货品体积信息为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("货品体积信息为空");
                }
                if (cargo.getCargoVolume().getUnit() == null) {
                    LOGGER.error("接单基本信息校验活动能力开始执行,货品体积单位为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("货品体积单位为空");
                }
                if (cargo.getCargoVolume().getValue() == null) {
                    LOGGER.error("接单基本信息校验活动能力开始执行,货品体积为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("货品体积为空");
                }

                List<String> cargoWeightSystemCallerList = expressUccConfigCenter.getCargoWeightSystemCallerList();
                //JOS｜CLPS来源订单货品重量可以为0
                if (CollectionUtils.isNotEmpty(cargoWeightSystemCallerList) && cargoWeightSystemCallerList.contains(channel.getSystemCaller().getCode())) {
                    if (BigDecimal.ZERO.compareTo(cargo.getCargoWeight().getValue()) > 0
                            || WEIGHT_MAX_VALUE.compareTo(cargo.getCargoWeight().getValue()) <= 0) {
                        LOGGER.error("接单基本信息校验活动能力开始执行,渠道:{}订单货品重量需大于等于" + BigDecimal.ZERO + "且小于" + WEIGHT_MAX_VALUE + "千克",channel.getSystemCaller().getCode());
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom("订单货品重量需大于等于" + BigDecimal.ZERO + "且小于" + WEIGHT_MAX_VALUE + "千克");
                    }
                } else {
                    if (BigDecimal.ZERO.compareTo(cargo.getCargoWeight().getValue()) >= 0
                        || WEIGHT_MAX_VALUE.compareTo(cargo.getCargoWeight().getValue()) <= 0) {
                        LOGGER.error("接单基本信息校验活动能力开始执行,货品重量需大于" + BigDecimal.ZERO + "且小于" + WEIGHT_MAX_VALUE + "千克");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom("货品重量需大于" + BigDecimal.ZERO + "且小于" + WEIGHT_MAX_VALUE + "千克");
                    }
                }

                // 校验体积
                HashSet<String> cargoVolumeSystemCallerSet = expressUccConfigCenter.getCargoVolumeSystemCallerSet();

                /*BigDecimal volumeValue = this.getVolumeValue(cargo.getCargoVolume());
                if (CollectionUtils.isNotEmpty(cargoVolumeSystemCallerSet) && cargoVolumeSystemCallerSet
                    .contains(channel.getSystemCaller().getCode())) {
                    if (BigDecimal.ZERO.compareTo(volumeValue) > 0 || VOLUME_MAX_VALUE.compareTo(volumeValue) <= 0) {
                        LOGGER.error(
                            "接单基本信息校验活动能力开始执行,渠道:{}订单货品体积需大于等于" + BigDecimal.ZERO + "且小于" + VOLUME_MAX_VALUE + "立方厘米",
                            channel.getSystemCaller().getCode());
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("订单货品体积需大于等于" + BigDecimal.ZERO + "且小于" + VOLUME_MAX_VALUE + "立方厘米");
                    }
                } else {
                    if (BigDecimal.ZERO.compareTo(volumeValue) >= 0 || VOLUME_MAX_VALUE.compareTo(volumeValue) <= 0) {
                        LOGGER.error("接单基本信息校验活动能力开始执行,货品体积需大于" + BigDecimal.ZERO + "且小于" + VOLUME_MAX_VALUE + "立方厘米");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("货品体积需大于" + BigDecimal.ZERO + "且小于" + VOLUME_MAX_VALUE + "立方厘米");
                    }
                }*/

                String maxValue = expressUccConfigCenter.getFreightCargoVolumeMax();// 100000001
                BigDecimal volumeValue = this.getVolumeValue(cargo.getCargoVolume());
                if (new BigDecimal("0").compareTo(volumeValue) > 0
                        || volumeValue.compareTo(new BigDecimal(maxValue)) >= 0) {
                    LOGGER.error("接单基本信息校验活动能力开始执行,货品体积需大于等于0且小于{}立方厘米", volumeValue);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom(String.format("货品体积需大于等于0且小于%s立方厘米", maxValue));
                }

                if (BigDecimal.ZERO.equals(cargoQuantity.getValue())) {
                    List<String> list = expressUccConfigCenter.getCargoQuantitySystemCallerList();
                    if (CollectionUtils.isNotEmpty(list) && !list.contains(channel.getSystemCaller().getCode())) {
                        LOGGER.error("货品的数量为零，并且渠道:{}不属于特定的渠道来源",channel.getSystemCaller().getCode());
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom("货品的数量为零，并且渠道来源不属于特定的渠道来源");
                    }
                }

                //补充校验 货品的数量不能小于零或者大于50000
                if (cargoQuantity.getValue().intValue() < CARGO_QUANTITY_MIN
                        || cargoQuantity.getValue().intValue() > CARGO_QUANTIFY) {
                    LOGGER.error("B2C接单基本信息校验，货品数量小于等于" + CARGO_QUANTITY_MIN + "或大于" + CARGO_QUANTIFY);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("货品数量小于等于" + CARGO_QUANTITY_MIN + "或大于" + CARGO_QUANTIFY);
                }

                //补充校验 附件的业务类型和附件路径是否为空
                if (cargo.getAttachments() != null) {
                    cargo.getAttachments().forEach(attachment -> {
                        if (StringUtils.isEmpty(attachment.getAttachmentType()) || StringUtils.isEmpty(attachment.getAttachmentUrl())) {
                            LOGGER.error("附件的业务类型和附件路径不能为空");
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                    .withCustom("附件的业务类型或者附件路径为空");
                        }
                    });
                }
            });
        }

        // 根据包裹号解析并校验运单号
        cargoWaybillCodeRuleUtil.validate(orderModel);

        // 港澳电商标快校验
        cargoValidBaseHandler.hkmoValidCargoWeightForDSTH(orderModel);
    }

    /**
     * 体积转换值转换
     *
     * @param volume
     * @return
     */
    private BigDecimal getVolumeValue(Volume volume) {
        //默认单位是立方厘米，如果不是则转成立方厘米
        if (VolumeTypeEnum.DM3.getCode().equals(volume.getUnit().getCode())) {
            return volume.getValue().multiply(new BigDecimal("1000"));
        }
        if (VolumeTypeEnum.M3.getCode().equals(volume.getUnit().getCode())) {
            return volume.getValue().multiply(new BigDecimal("1000000"));
        }
        return volume.getValue();
    }

    /**
     * b2c渠道信息校验
     *
     * @param: orderModel
     * @url: https://cf.jd.com/pages/viewpage.action?pageId=40635335
     */
    private void channelValid(ExpressOrderModel orderModel) throws DomainAbilityException {
        if (StringUtils.isBlank(orderModel.getChannel().getChannelNo())) {
            LOGGER.error("B2C订单接单的基本信息-渠道信息的渠道编码为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("B2C订单接单的基本信息-渠道信息的渠道编码为空");
        }

        if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.BASIC_CACHE_SWITCH)) {
            if (!basicCache.validChannelNo(orderModel.getChannel().getChannelNo())) {
                LOGGER.error("校验渠道编码不在配置范围内，校验失败");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("校验渠道编码不在配置范围内，校验失败");
            }
        } else {
            BasicInfoFacadeRequest basicInfoFacadeRequest = basicInfoFacadeTranslator.toBasicInfoFacadeRequest(orderModel);
            //调青龙接口进行校验
            BasicInfoFacadeResponse basicInfoFacadeResponse = basicInfoFacade.getBasicData(basicInfoFacadeRequest);
            boolean judgeChannelNo = false;
            for (String channelNo : basicInfoFacadeResponse.getBaseDataList()) {
                if (channelNo.equals(orderModel.getChannel().getChannelNo())) {
                    judgeChannelNo = true;
                    break;
                }
            }
            if (!judgeChannelNo) {
                LOGGER.error("校验渠道编码不在配置范围内，校验失败");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("校验渠道编码不在配置范围内，校验失败");
            }
        }

    }

    /**
     * b2c商品信息校验
     *
     * @param orderModel
     */
    private void goodsValid(ExpressOrderModel orderModel) throws DomainAbilityException {

        if (CollectionUtils.isNotEmpty(orderModel.getGoodsDelegate().getGoodsList())) {
            List<Goods> goodsList = (List<Goods>) orderModel.getGoodsDelegate().getGoodsList();
            goodsList.forEach(goods -> {
                if (null == goods) {
                    return;
                }
                if (StringUtils.isBlank(goods.getGoodsName())) {
                    LOGGER.error("商品的名称为空，校验失败");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("商品的名称为空，校验失败");
                }
                if (this.isQuantityEmpty(goods.getGoodsQuantity())) {
                    LOGGER.error("商品的数量(值，单位)为空，校验失败");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("商品的数量(值，单位)为空，校验失败");
                }
            });
        }

    }

    /**
     * b2c派送、揽收方式校验
     *
     * @param orderModel
     */
    private void shipmentValid(ExpressOrderModel orderModel) throws DomainAbilityException {

        // 配送信息：必填
        Shipment shipment = orderModel.getShipment();
        if (shipment == null) {
            LOGGER.error("订单接单的基本信息-配送信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("订单接单的基本信息-配送信息为空");
        }

        if (orderModel.getShipment().getDeliveryType() != null) {
            if (orderModel.getShipment().getDeliveryType() != DeliveryTypeEnum.TO_DOOR &&
                    orderModel.getShipment().getDeliveryType() != DeliveryTypeEnum.SELF_PICKUP) {
                LOGGER.error("派送方式不包含送货上门或者自提");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("派送方式不包含送货上门或者自提");
            }
            if (orderModel.getShipment().getDeliveryType() == DeliveryTypeEnum.SELF_PICKUP
                    && StringUtils.isBlank(orderModel.getShipment().getEndStationNo())) {
                LOGGER.error("派送方式为自提,目的站点编码不能为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("派送方式为自提,目的站点编码不能为空");
            }
        }
        if (orderModel.getShipment().getPickupType() != null
                && orderModel.getShipment().getPickupType() == PickupTypeEnum.SELF_DELIVERY
                && StringUtils.isBlank(orderModel.getShipment().getStartStationNo())) {
            if (!orderModel.getProductDelegate().getMainProduct().getProductNo().equals(ProductEnum.HSD.getCode())) {
                LOGGER.error("主产品非函速达且揽收方式为自送，始发站点编码不能为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("主产品非函速达且揽收方式为自送，始发站点编码不能为空");
            }
        }

        // 识别ordersign-unitedB2CFlag=1且主产品=快运的校验派送方式非空
        if (UnitedB2CUtil.isUnitedFreightB2C(orderModel) && shipment.getDeliveryType() == null) {
            LOGGER.error("快运订单接单的基本信息-派送方式为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("快运订单接单的基本信息-派送方式为空");
        }

        // 期望取件开始时间：揽收方式为上门取件且不包含增值产品自行联系，或者快运整车，必填
        Date expectPickupStartTime = shipment.getExpectPickupStartTime();
        // 期望取件结束时间：揽收方式为上门取件且不包含增值产品自行联系，或者快运整车，必填
        Date expectPickupEndTime = shipment.getExpectPickupEndTime();
        if (SystemCallerUtil.currentIsSupplyOFC(orderModel)
                && SupplyChainDeliveryOrderSignUtil.currentFlag(orderModel)) {
            LOGGER.info("供应链OFC-仓配接配，不校验取件时间");
        } else {

            // 是否需要校验期望取件时间。识别ordersign-unitedB2CFlag=1且主产品=快运的，揽收方式为上门取件且不包含增值产品自行联系，或者快运整车
            boolean needCheckExpectPickupTime = UnitedB2CUtil.isUnitedFreightB2C(orderModel)
                    && ((isOnSitePick(shipment.getPickupType()) && !hasSpecialProduct(orderModel)) || orderModel.isFreightFTL());

            // 期望取件开始时间：需要识别ordersign-unitedB2CFlag=1且主产品=快运的，揽收方式为上门取件且不包含增值产品自行联系，或者快运整车，必填
            if (needCheckExpectPickupTime
                    && expectPickupStartTime == null) {
                LOGGER.error("订单接单的基本信息-期望取件开始时间为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("订单接单的基本信息-期望取件开始时间为空");
            }
            // 期望取件结束时间：需要识别ordersign-unitedB2CFlag=1且主产品=快运的，揽收方式为上门取件且不包含增值产品自行联系，或者快运整车，必填
            if (needCheckExpectPickupTime
                    && expectPickupEndTime == null) {
                LOGGER.error("订单接单的基本信息-期望取件结束时间为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("订单接单的基本信息-期望取件结束时间为空");
            }
            /* 以下逻辑 CreateBasicInfoAbility 中已有
            // 期望取件开始时间不能早于当前时间
            if (expectPickupStartTime != null) {
                Date now = new Date();
                if (expectPickupStartTime.before(now)) {
                    LOGGER.error("订单接单的基本信息-期望取件开始时间不能早于当前时间");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("订单接单的基本信息-期望取件开始时间不能早于当前时间");
                }
            }

            // 期望取件开始时间不能晚于期望取件结束时间
            if (expectPickupStartTime != null && expectPickupEndTime != null
                    && expectPickupStartTime.after(expectPickupEndTime)) {
                LOGGER.error("订单接单的基本信息-期望取件开始时间不能晚于期望取件结束时间");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("订单接单的基本信息-期望取件开始时间不能晚于期望取件结束时间");
            }

            // 期望送达开始时间、期望送达结束时间：非必填，开始时间不能晚于结束时间
            Date expectDeliveryStartTime = shipment.getExpectDeliveryStartTime();
            Date expectDeliveryEndTime = shipment.getExpectDeliveryEndTime();
            if (expectDeliveryStartTime != null && expectDeliveryEndTime != null
                    && expectDeliveryStartTime.after(expectDeliveryEndTime)) {
                LOGGER.error("订单接单的基本信息-期望送达开始时间不能晚于期望送达结束时间");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("订单接单的基本信息-期望送达开始时间不能晚于期望送达结束时间");
            }
            */
        }

        // 港澳电商标快校验
        shipmentValidBaseHandler.hkmoValidPickupByBoxServiceForDSTH(orderModel);
    }

    /**
     * 判断是否上门取件
     */
    private boolean isOnSitePick(PickupTypeEnum pickupType) {
        return pickupType != null && PickupTypeEnum.ON_SITE_PICK.getCode().equals(pickupType.getCode());
    }

    /**
     * 判断是否有自行联系
     */
    private boolean hasSpecialProduct(ExpressOrderModel orderModel) {
        return orderModel != null
                && orderModel.getProductDelegate() != null
                && orderModel.getProductDelegate().ofProductNo(AddOnProductEnum.FRIGHT_CONTACT_DIRECTLY.getCode()) != null;
    }


    private void financeValid(ExpressOrderModel orderModel) throws DomainAbilityException {
        if (orderModel.getFinance().getEstimateAmount() != null
                && null != orderModel.getFinance().getEstimateAmount().getAmount()
                && new BigDecimal(FINANCE_AMOUNT).compareTo(orderModel.getFinance().getEstimateAmount().getAmount()) <= 0) {
            LOGGER.error("当前的运费的值为: {}, 预估运费大于等于{}.", orderModel.getFinance().getEstimateAmount().getAmount(), FINANCE_AMOUNT);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("预估运费大于等于一百万");
        }

        SettlementTypeEnum settlementType = orderModel.getFinance().getSettlementType();
        if (null == settlementType) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("结算方式不可以为空");
        }
        // 改址单，新单仅支持修改成到付现结和月结
        // 改址单放开结算方式校验

        //结算账号改成必填
        /*if (StringUtils.isBlank(orderModel.getFinance().getSettlementAccountNo())){
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("结算账号不可以为空");
        }*/

        // 识别ordersign-unitedB2CFlag=1且主产品=快运的逆向单结算方式只允许月结
        if (OrderTypeEnum.RETURN_ORDER.getCode().equals(orderModel.getOrderType().getCode())
                && UnitedB2CUtil.isUnitedFreightB2C(orderModel)
                && (SettlementTypeEnum.CASH_ON_PICK == settlementType || SettlementTypeEnum.CASH_ON_DELIVERY == settlementType)) {

            LOGGER.error("快运B2C逆向单结算方式只允许月结");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("快运B2C逆向单结算方式只允许月结");
        }

    }

    /**
     * 功能: B2C改址单 - 原始订单合法性信息
     *
     * @param:
     * @return:
     * @throw:
     * @description: 订单类型是改址单的时候走这条
     * @author: liufarui
     * @date: 2021/5/24 9:35 下午
     */
    private void readdressValid(ExpressOrderModel orderModel) throws DomainAbilityException {
        SettlementTypeEnum settlementType = orderModel.getFinance().getSettlementType();
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if (null == orderSnapshot) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("改址单类型，原单信息不可以为空");
        }
        // TODO liupinxun 黑名单规则校验，等优化完，做成可配置话
        // readdressBlackValid(orderModel);

        // 原单关联单类型中已经存在改址单；
        if (orderSnapshot.getRefOrderInfoDelegate().getRefOrderInfoByType(RefOrderTypeEnum.READDRESS.getCode()) != null) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("已经存在改址单,不允许重新生成改址单");
        }

        // 原始订单状态不能是取消成功、取消中的订单
        CancelStatusEnum cancelStatus = orderSnapshot.getCancelStatus();
        if (CancelStatusEnum.CANCEL_SUCCESS == cancelStatus) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("已取消的订单不能生成改址单");
        }
        if (CancelStatusEnum.CANCELLING == cancelStatus) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("取消中的订单不能生成改址单");
        }
        // 拦截中和拦截成功的订单不能生成改址单
        // 但是拦截失败的订单不做限制
        if (InterceptTypeEnum.INTERCEPTING == orderSnapshot.getInterceptType()
                || InterceptTypeEnum.INTERCEPT_SUCCESS == orderSnapshot.getInterceptType()) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(orderSnapshot.getInterceptType().getDesc() + "状态的订单不能生成改址单");
        }

        // 原始订单改址黑名单校验
        // 原单产品服务
        ProductDelegate delegate = orderSnapshot.getProductDelegate();
        if (null == delegate) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("原始订单产品服务不可以为空");
        }

        // 原单结算方式
        SettlementTypeEnum snapshotSettlementType = orderSnapshot.getFinance().getSettlementType();
        if (null == snapshotSettlementType) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("原始订单结算方式不可以为空");
        }

        // 原单状态
        Integer customStatus = orderSnapshot.getCustomStatus();
        if (null == customStatus) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("原始订单状态不可以为空");
        }

        // 原单订单类型
        OrderTypeEnum orderType = orderSnapshot.getOrderType();
        if (null == orderType) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("原始订单类型不可以为空");
        }

        // 原单站点类型（原单揽收站点类型不是三方；前端校验接口；（前端上游控制））

        // 京尊达增值服务不支持修改
        /*if (delegate.ofProductNo(AddOnProductEnum.JZD.getCode()) != null) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("京尊达增值服务不允许生成改址单");
        }*/
        // 京准达、特惠送、特快送、特快次晨、微小件、特惠包裹、同城当日达支持修改地址，其他的不支持修改。
        // 注：和文卿确认，同城当日达和特快次晨不属于产品类型，这边不做判断。
        /*if (!(delegate.ofProductNo(AddOnProductEnum.J_ZHUN_DA.getCode()) != null
                || delegate.ofProductNo(ProductEnum.THS.getCode()) != null
                || delegate.ofProductNo(ProductEnum.TKS.getCode()) != null
                || delegate.ofProductNo(ProductEnum.KDWXJ.getCode()) != null
                || delegate.ofProductNo(ProductEnum.THBG.getCode()) != null)) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("此类产品不允许生成改址单");
        }*/

        // 原始订单到付的订单不允许生成改址单
        // 原始订单寄付且有代收货款增值服务的订单不允许生成改址单
        if (SettlementTypeEnum.CASH_ON_PICK == snapshotSettlementType && CollectionUtils.isNotEmpty(delegate.getCodProducts())) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("原单寄付且有代收货款增值服务的订单不允许生成改址单");
        }

        // 原始订单到付且先款改址单未询价订单不允许生成改址单
        if (SettlementTypeEnum.CASH_ON_DELIVERY == snapshotSettlementType
                && PaymentStageEnum.ONLINEPAYMENT == orderModel.getFinance().getPaymentStage()
                && (null == orderSnapshot.getFinance().getEnquiryStatus()
                || EnquiryStatusEnum.INQUIRED.getCode() > orderSnapshot.getFinance().getEnquiryStatus().getCode())) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("原始订单到付且先款改址单未询价,不允许生成改址单");
        }

        // 原订单类型：逆向单、改址单
        if (OrderTypeEnum.READDRESS == orderType
                || OrderTypeEnum.RETURN_ORDER == orderType) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("逆向单或者改址单不允许生成改址单");
        }

        /* 去除校验原单渠道编码为0010001时，改址新单拒单的逻辑；
        if (JD_CHANNEL_NO.equals(orderSnapshot.getChannel().getChannelNo())) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("销售平台是京东平台的订单不允许生成改址单");
        }
        */

        // 原订单状态：拒收、妥投、已下发、异常终止、已取消 不允许生成改址单
        //与张勇、王传宇、孙志宇确认，派送中不允许改址卡控由快递卡控，订单中心不在卡控派送中的状态不允许生成改址单
        if (ExpressOrderStatusCustomEnum.notAllowReaddress(customStatus)) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom(ExpressOrderStatusCustomEnum.ofCustomOrderStatus(customStatus).getDesc() + "状态的订单不允许生成改址单");
        }

        //供应链OFC来源仓配快递不支持换单改址，仅支持结算方式=月结进行一单到底改址
        SystemCallerEnum systemCaller = orderSnapshot.getChannel().getSystemCaller();
        if ( SystemCallerEnum.SUPPLY_OFC == systemCaller && SupplyChainDeliveryOrderSignUtil.snapFlag(orderModel)) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("B2C订单接单的基本信息-供应链OFC来源仓配快递不支持换单改址");
        }

        //改址新单原单只要修改过代收货款或收件人信息都不让生成新的改址单
        // 获取原单标记位
        String modifyMark = null;
        if (orderSnapshot.getExtendProps() != null) {
            modifyMark = orderSnapshot.getExtendProps().get(AttachmentKeyEnum.MODIFY_MARK.getKey());
        }
        if (StringUtils.isBlank(modifyMark)) {
            modifyMark = ModifyMarkUtil.getInitMark();
        }
        String originCODSign = ModifyMarkUtil.getPositionMark(modifyMark, ModifyMarkEnum.COD.getPosition());
        String originConsigneeSign = ModifyMarkUtil.getPositionMark(modifyMark, ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getPosition());
        if (ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getSign().equals(originConsigneeSign)) {
            LOGGER.info("原单收件人信息已经修改，不能生成新的改址单");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.MODIFY_VALIDATE_FAIL_CONSIGNEE).withCustom("原单收件人信息已经修改，不能生成新的改址单");
        }

        // 改址单指定原单结算方式校验
        // 扩展字段
        Map<String, String> extendProps = orderModel.getFinance().getExtendProps();
        // 修改原单的结算方式为
        String originSettlementType = MapUtils.isNotEmpty(extendProps) ? extendProps.get(AttachmentKeyEnum.ORIGIN_SETTLEMENT_TYPE.getKey()) : null;

        if (StringUtils.isNotBlank(originSettlementType) && !StringUtils.isNumeric(originSettlementType)){
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("原单结算方式originSettlementType有误");
        }

        Integer originSettlementTypeIntVal = StringUtils.isNotBlank(originSettlementType) ? Integer.parseInt(originSettlementType) : null;

        // 原单不是到付现结 无需操作原单结算方式
        if (SettlementTypeEnum.CASH_ON_DELIVERY != snapshotSettlementType){
            if(null != originSettlementTypeIntVal){
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("原单非到付，不允许操作原单结算方式originSettlementType");
            }
        } else {
            // 只有到付原单的【寄付】改址单需要指定原单的结算方式
            if (SettlementTypeEnum.CASH_ON_PICK != settlementType
                    && SettlementTypeEnum.MONTHLY_PAYMENT != settlementType
                    && null != originSettlementTypeIntVal){
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("原单到付改址单非寄付，不允许操作原单结算方式originSettlementType");
            }
            if (SettlementTypeEnum.CASH_ON_PICK == settlementType
                    && null != originSettlementTypeIntVal
                    && !Arrays.contains(new int []{SettlementTypeEnum.CASH_ON_PICK.getCode(),SettlementTypeEnum.CASH_ON_DELIVERY.getCode()}
                    , originSettlementTypeIntVal)){
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("到付原单的改址寄付现结新单，originSettlementType操作有误，只支持[1：寄付现结,2：到付现结]");
            }
            if (SettlementTypeEnum.MONTHLY_PAYMENT == settlementType
                    && null != originSettlementTypeIntVal
                    && !Arrays.contains(new int []{SettlementTypeEnum.MONTHLY_PAYMENT.getCode(),SettlementTypeEnum.CASH_ON_DELIVERY.getCode()}
                    , originSettlementTypeIntVal)){
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("到付原单的改址寄付月结新单，originSettlementType操作有误，只支持[2：到付现结,3：寄付月结]");
            }
        }

    }

    /**
     * 判断数量对象是否为空
     * @param quantity
     * @return
     */
    private boolean isQuantityEmpty(Quantity quantity) {
        return null == quantity
            || null == quantity.getValue()
            || StringUtils.isBlank(quantity.getUnit());
    }

    /**
     * 结算方式是否是：到付现结或月结
     *
     * @param settlementType 结算方式
     * @Return boolean
     * @author: lishuo147
     * @date: 2023/09/07 13:52
     */
    public boolean isCashOnDeliveryOrMonthlyPayment(SettlementTypeEnum settlementType){
        return SettlementTypeEnum.MONTHLY_PAYMENT == settlementType ||
            SettlementTypeEnum.MONTHLY_PAYMENT_DELIVERY == settlementType ||
            SettlementTypeEnum.CASH_ON_DELIVERY == settlementType;
    }

    /**
     * 解决方案校验
     */
    private void businessSolutionInfoValid(ExpressOrderModel orderModel) {

        // 识别ordersign-unitedB2CFlag=1且主产品=快运，校验解决方案信息非空
        if (UnitedB2CUtil.isUnitedFreightB2C(orderModel) && orderModel.getBusinessSolution() == null) {
            LOGGER.error("快运订单接单的基本信息-解决方案信息为空");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("快运订单接单的基本信息-解决方案信息为空");
        }
    }

}
