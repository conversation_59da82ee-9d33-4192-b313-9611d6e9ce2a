package cn.jdl.oms.express.b2c.extension.basic;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.basic.IBasicInfoExtension;
import cn.jdl.oms.express.domain.infrs.acl.baseHandler.basicInfo.CargoValidBaseHandler;
import cn.jdl.oms.express.domain.infrs.acl.baseHandler.basicInfo.DeliveryPickupSyncBaseHandler;
import cn.jdl.oms.express.domain.infrs.acl.baseHandler.basicInfo.ShipmentValidBaseHandler;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.SupplyChainDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.IdentityTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PickupTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderExtendTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.spec.model.ICargo;
import cn.jdl.oms.express.domain.vo.Cargo;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.ReturnInfoVo;
import cn.jdl.oms.express.domain.vo.Shipment;
import cn.jdl.oms.express.freight.extension.constant.FreightLengthConstants;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.FinanceConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.DomainAbilityException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.DateUtils;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static cn.jdl.oms.express.shared.common.dict.ReturnTypeEnum.TO_DESIGNATED_ADDRESS;

/**
 * @Package: cn.jdl.oms.express.b2c.extension.basic
 * @ClassName: B2CModifyBasicInfoExtension
 * @Description:
 * @Author: liufarui
 * @CreateDate: 2021/5/18 11:33 下午
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version: V1.0
 */
@Extension(code = ExpressOrderProduct.CODE)
public class B2CModifyBasicInfoExtension implements IBasicInfoExtension {

    private static final Logger LOGGER = LoggerFactory.getLogger(B2CModifyBasicInfoExtension.class);

    /**
     * 送取同步校验
     */
    @Resource
    private DeliveryPickupSyncBaseHandler deliveryPickupSyncBaseHandler;

    /**
     * 报警
     */
    @Resource
    private UmpUtil umpUtil;

    @Resource
    private CargoValidBaseHandler cargoValidBaseHandler;

    /**
     * 派送信息校验
     */
    @Resource
    private ShipmentValidBaseHandler shipmentValidBaseHandler;

    /**
     * 手机、电话长度
     */
    public static final int PHONE_AND_MOBILE_MAX_LENGTH = 30;

    /**
     * 包裹号生成规则
     */
    private static final String PACKAGE_NUMBER_RULE = "packageNumberRule";

    /**
     * 包裹号生成规则 - 自定义
     */
    private static final String PACKAGE_NUMBER_RULE_CUSTOM = "CUSTOM";

    /**
     * 包裹号生成规则 - 零包裹
     */
    private static final String PACKAGE_NUMBER_RULE_TOTAL_ZERO = "TOTAL_ZERO";

    private static Set<String> ALLOWED_REF_ORDER_TYPE = new HashSet<>(Arrays.asList(
            RefOrderExtendTypeEnum.THIRD_WAYBILL_NO.getCode()
            , RefOrderExtendTypeEnum.REVERSE_WAYBILL_NO.getCode()
    ));

    /**
     * 功能: 订单修改信息校验
     *
     * @param: context 领域上下文
     * @throw: AbilityExtensionException
     * @description:
     * @author: liufarui
     * @date: 2021/5/18 11:34 下午
     */
    @Override
    public void execute(ExpressOrderContext context) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("订单修改基本信息校验扩展点执行开始");
            this.cargoInfoValid(context);
            this.consigneeValid(context.getOrderModel());
            this.consignorValid(context.getOrderModel());
            this.shipmentValid(context.getOrderModel());
            this.channelValid(context.getOrderModel());
            //逆向单,改址单,不允许执行修改流程
            // update liujiangwai1 [抖音加密] 改址单放开修改卡控
            ExpressOrderModel snapshot = context.getOrderModel().getOrderSnapshot();
            if (snapshot != null && OrderTypeEnum.RETURN_ORDER == snapshot.getOrderType()) {
                // 如果原单是港澳订单,则允许修改逆向单
                if (snapshot.isHKMO()) {
                    LOGGER.info("B2C-港澳订单允许修改逆向单");
                } else {
                    String modifySceneRule = ModifySceneRuleUtil.getModifySceneRule(context.getOrderModel());
                    //逆向单只允许修改打印次数或者碳排放计算
                    if ((context.getChangedPropertyDelegate().getChangedProperties().size() != 1
                            || !context.getChangedPropertyDelegate().propertyHasChange(ModifyItemConfigEnum.ORDER_PRINT_TIMES))
                            && !ModifySceneRuleUtil.isCarbonEmissionCalculation(modifySceneRule)
                    ) {
                        LOGGER.error("订单修改信息校验活动能力开始执行,订单类型:{},不允许修改", JSONUtils.beanToJSONDefault(snapshot.getOrderType()));
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).
                            withCustom(String.format("订单类型: %s ,不允许修改", JSONUtils.beanToJSONDefault(snapshot.getOrderType())));
                    }
                }
            }
            this.returnInfoValid(context.getOrderModel());
            this.refOrderValid(context.getOrderModel());
            // 财务信息校验
            this.financeValid(context.getOrderModel());
            try {
                deliveryPickupSyncBaseHandler.validateModify(context);
            } catch (Exception e) {
                umpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_DELIVERY_PICKUP_SYNC_VALIDATE_MODIFY_FAIL_ALARM, "送取同步修改校验失败", context.getOrderModel().traceId());
                throw e;
            }
            LOGGER.info("订单修改基本信息校验扩展点执行结束");
        } catch (InfrastructureException infrastructureException) {
            Profiler.functionError(callerInfo);
            LOGGER.error("订单修改基本信息校验扩展点执行异常, traceId={},exception:{}", context.getOrderModel().traceId(), infrastructureException);
            throw infrastructureException;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("订单修改基本信息校验扩展点执行异常, traceId={},exception:{}", context.getOrderModel().traceId(), exception);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL, exception);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 退货信息校验
     * @param orderModel
     */
    private void returnInfoValid(ExpressOrderModel orderModel){
        if (null == orderModel.getReturnInfoVo()) {
            return;
        }
        ReturnInfoVo returnInfoVo = orderModel.getReturnInfoVo();
        ReturnInfoVo originReturnInfo = orderModel.getOrderSnapshot().getReturnInfoVo();
        //商家逆向场景指定退货地址
        if (TO_DESIGNATED_ADDRESS.getCode().equals(returnInfoVo.getReturnType())) {
            // 修改基本信息校验-退货类型为退回到指定地址，指定退货地址信息不全
            Consignee consignee = orderModel.getReturnInfoVo().getConsignee();
            if(consignee == null){
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.APPOINT_RETURN_INFO_LOSS)
                        .withCustom("修改基本信息校验-退货类型为退回到指定地址，指定退货地址信息不能为空");
            }
            if (StringUtils.isBlank(consignee.getConsigneeName())
                    || (StringUtils.isBlank(consignee.getConsigneePhone())  && StringUtils.isBlank(consignee.getConsigneeMobile()))
                    || consignee.getAddress() == null
                    || StringUtils.isBlank(consignee.getAddress().getAddress())) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.APPOINT_RETURN_INFO_LOSS)
                        .withCustom("修改基本信息校验-退货类型为退回到指定地址，指定退货地址信息不全");
            }
        } else if (originReturnInfo != null) {
            if (TO_DESIGNATED_ADDRESS.getCode().equals(originReturnInfo.getReturnType())) {
                Consignee consignee = orderModel.getReturnInfoVo().getConsignee();
                if (consignee != null) {
                    if(consignee.getConsigneeName() != null && StringUtils.isBlank(consignee.getConsigneeName())){
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.APPOINT_RETURN_INFO_LOSS)
                                .withCustom("修改基本信息校验-原单退货类型为退回到指定地址，指定退货地址信息-收件人姓名不可清空");
                    }
                    if (consignee.getAddress() != null
                            && consignee.getAddress().getAddress() != null
                            && StringUtils.isBlank(consignee.getAddress().getAddress())) {
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.APPOINT_RETURN_INFO_LOSS)
                                .withCustom("修改基本信息校验-原单退货类型为退回到指定地址，指定退货地址信息-收件地址不可清空");
                    }
                    String consigneePhone = consignee.getConsigneePhone() != null ? consignee.getConsigneePhone() : originReturnInfo.getConsignee().getConsigneePhone();
                    String consigneeMobile = consignee.getConsigneeMobile() != null ? consignee.getConsigneeMobile() : originReturnInfo.getConsignee().getConsigneeMobile();
                    if (StringUtils.isBlank(consigneePhone) && StringUtils.isBlank(consigneeMobile) ) {
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.APPOINT_RETURN_INFO_LOSS)
                                .withCustom("修改基本信息校验-原单退货类型为退回到指定地址，修改后的收件人手机号和电话不能都为空");
                    }
                }
            }
        }
    }

    /**
     * 功能: B2C修改货品数量校验
     *
     * @param: ExpressOrderContext 上下文model信息
     * @throw: BusinessDomainException
     * @description: 揽收后如果修改货品信息则货品的长、宽、高和货品数量、均不能为空；如果为空则提示错误：长、宽、高、货品数量均不能为空；
     * @author: liufarui
     * @date: 2021/5/18 11:44 下午
     */
    private void cargoInfoValid(ExpressOrderContext context) throws DomainAbilityException {
        ExpressOrderModel expressOrderModel = context.getOrderModel();

        // 港澳电商标快校验：改成ed-m-0059但是没改cargo，也得限制cargoWeight，所以提前到cargoDelegate=null前
        cargoValidBaseHandler.hkmoValidCargoWeightForDSTH(expressOrderModel);

        if (null == expressOrderModel.getCargoDelegate()
                || expressOrderModel.getCargoDelegate().isEmpty()) {
            return;
        }
        if (context.getChangedPropertyDelegate().propertyHasChange(ModifyItemConfigEnum.CARGO)) {
            for (ICargo iCargo : expressOrderModel.getCargoDelegate().getCargoList()) {
                Cargo cargo = (Cargo) iCargo;
                if (CollectionUtils.isNotEmpty(cargo.getAttachments())) {
                    cargo.getAttachments().forEach(attachment -> {
                        if (StringUtils.isEmpty(attachment.getAttachmentType()) || StringUtils.isEmpty(attachment.getAttachmentUrl())) {
                            LOGGER.error("附件的业务类型和附件路径不能为空");
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                    .withCustom("附件的业务类型或者附件路径为空");
                        }
                    });
                }
                if (UnitedB2CUtil.isUnitedFreightB2C(expressOrderModel)) {
                    // 零包裹下单包裹号校验
                    validatePackageNumberRule(cargo);
                }
            }
        }
    }

    /**
     * 收件人信息校验
     * @param orderModel
     */
    private void consigneeValid(ExpressOrderModel orderModel) {
        Consignee consignee = orderModel.getConsignee();
        if(consignee == null){
            return;
        }

        if (StringUtils.isNotBlank(consignee.getConsigneeMobile())
                && consignee.getConsigneeMobile().length() > PHONE_AND_MOBILE_MAX_LENGTH) {
            LOGGER.error("修改收货人手机号码超出最大长度{}", PHONE_AND_MOBILE_MAX_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("收货人手机号超长");
        }
        if (StringUtils.isNotBlank(consignee.getConsigneePhone())
                && consignee.getConsigneePhone().length() > PHONE_AND_MOBILE_MAX_LENGTH) {
            LOGGER.error("修改收货人电话号码超出最大长度{}", PHONE_AND_MOBILE_MAX_LENGTH);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("修改收电话号超长");
        }

        if(!BatrixSwitch.applyByBoolean(BatrixSwitchKey.B2C_MODIFY_CONSIGNEE_AND_CONSIGNOR_CHECK)){
            return;
        }
        // 收货人姓名：接单必填，修改可以不填，长度限制
        Optional.ofNullable(consignee.getConsigneeName()).ifPresent(consigneeName -> {
            if (StringUtils.isBlank(consigneeName)) {
                LOGGER.error("修改基本信息校验-收件人姓名为空");
                UmpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_B2C_MODIFY_CONSIGNEE_AND_CONSIGNOR_CHECK_FAIL_ALARM
                        , "修改基本信息校验-收件人姓名为空, orderNo:" + orderModel.orderNo()
                        , orderModel.requestProfile(), orderModel.getBusinessIdentity());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("修改基本信息校验-收件人姓名为空");
            }
        });

        String consigneeMobile = getModifyConsigneeMobile(orderModel);
        String consigneePhone = getModifyConsigneePhone(orderModel);
        if (StringUtils.isBlank(consigneeMobile) && StringUtils.isBlank(consigneePhone)) {
            LOGGER.error("修改基本信息校验-修改后收货人手机和收货人电话同时为空");
            UmpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_B2C_MODIFY_CONSIGNEE_AND_CONSIGNOR_CHECK_FAIL_ALARM
                    , "修改基本信息校验-修改后收货人手机和收货人电话同时为空, orderNo:" + orderModel.orderNo()
                    , orderModel.requestProfile(), orderModel.getBusinessIdentity());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("修改基本信息校验-修改后收货人手机和收货人电话同时为空");
        }

        // 收货人证件类型、收货人证件号码：非必填，按收货人证件类型是否有值校验
        IdentityTypeEnum consigneeIdType = consignee.getConsigneeIdType();
        // 证件类型有值，校验收货人证件号码
        if (consigneeIdType != null) {
            Optional.ofNullable(consignee.getConsigneeIdNo()).ifPresent(consigneeIdNo -> {
                // 必填
                if (StringUtils.isBlank(consigneeIdNo)) {
                    LOGGER.error("修改基本信息校验-收货人证件号码为空");
                    UmpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_B2C_MODIFY_CONSIGNEE_AND_CONSIGNOR_CHECK_FAIL_ALARM
                            , "修改基本信息校验-收货人证件号码为空, orderNo:" + orderModel.orderNo()
                            , orderModel.requestProfile(), orderModel.getBusinessIdentity());
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("修改基本信息校验-收货人证件号码为空");
                }
                // 长度校验
                if (consigneeIdNo.length() < OrderConstants.ID_NO_LENGTH_MIN) {
                    LOGGER.error("修改基本信息校验-证件号不能小于6位");
                    UmpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_B2C_MODIFY_CONSIGNEE_AND_CONSIGNOR_CHECK_FAIL_ALARM
                            , "修改基本信息校验-证件号不能小于6位, orderNo:" + orderModel.orderNo()
                            , orderModel.requestProfile(), orderModel.getBusinessIdentity());
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("修改基本信息校验-证件号不能小于6位");
                }
                // 证件类型为身份证或者户口薄时，需要验证证件号码的长度为15位或者为18位
                if ((consigneeIdType == IdentityTypeEnum.ID_CARD || consigneeIdType == IdentityTypeEnum.HOUSEHOLD_REGISTER)
                        && (!OrderConstants.ID_CARD_LENGTH_OLD.equals(consigneeIdNo.length()) && !OrderConstants.ID_CARD_LENGTH_NEW.equals(consigneeIdNo.length()))) {
                    LOGGER.error("修改基本信息校验-收货人的身份证号码不等于15位或者18位");
                    UmpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_B2C_MODIFY_CONSIGNEE_AND_CONSIGNOR_CHECK_FAIL_ALARM
                            , "修改基本信息校验-收货人的身份证号码不等于15位或者18位, orderNo:" + orderModel.orderNo()
                            , orderModel.requestProfile(), orderModel.getBusinessIdentity());
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("修改基本信息校验-收货人的身份证号码不等于15位或者18位");
                }
            });
        }
    }

    /**
     * 发件人信息校验
     *
     * @param orderModel
     */
    private void consignorValid(ExpressOrderModel orderModel) {
        // 收货人：接单必填，修改可以不填
        Consignor consignor = orderModel.getConsignor();
        if (consignor == null) {
            return;
        }

        if(!BatrixSwitch.applyByBoolean(BatrixSwitchKey.B2C_MODIFY_CONSIGNEE_AND_CONSIGNOR_CHECK)){
            return;
        }

        // 发货人姓名：接单必填，修改可以不填，长度限制
        Optional.ofNullable(consignor.getConsignorName()).ifPresent(consignorName -> {
            if (StringUtils.isBlank(consignorName)) {
                LOGGER.error("修改基本信息校验-发货人姓名为空");
                UmpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_B2C_MODIFY_CONSIGNEE_AND_CONSIGNOR_CHECK_FAIL_ALARM
                        , "发货人姓名为空, orderNo:" + orderModel.orderNo()
                        , orderModel.requestProfile(), orderModel.getBusinessIdentity());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("修改基本信息校验-发货人姓名为空");
            }
        });

        // 发货人手机、发货人电话：二选一必填，长度校验
        String consignorMobile = getModifyConsignorMobile(orderModel);
        String consignorPhone = getModifyConsignorPhone(orderModel);
        if (consignorMobile != null && StringUtils.isBlank(consignorMobile)
                && consignorPhone != null && StringUtils.isBlank(consignorPhone)) {
            LOGGER.error("修改基本信息校验-发货人手机和发货人电话同时为空");
            UmpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_B2C_MODIFY_CONSIGNEE_AND_CONSIGNOR_CHECK_FAIL_ALARM
                    , "发货人手机和发货人电话同时为空, orderNo:" + orderModel.orderNo()
                    , orderModel.requestProfile(), orderModel.getBusinessIdentity());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("修改基本信息校验-发货人手机和发货人电话同时为空");
        }

        // 发货人证件类型、发货人证件号码：非必填，按发货人证件类型是否有值校验
        IdentityTypeEnum consignorIdType = consignor.getConsignorIdType();
        // 证件类型有值，校验发货人证件号码
        if (consignorIdType != null) {
            Optional.ofNullable(consignor.getConsignorIdNo()).ifPresent(consignorIdNo -> {
                // 必填
                if (StringUtils.isBlank(consignorIdNo)) {
                    LOGGER.error("修改基本信息校验-发货人证件号码为空");
                    UmpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_B2C_MODIFY_CONSIGNEE_AND_CONSIGNOR_CHECK_FAIL_ALARM
                            , "发货人证件号码为空, orderNo:" + orderModel.orderNo()
                            , orderModel.requestProfile(), orderModel.getBusinessIdentity());
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("修改基本信息校验-发货人证件号码为空");
                }
                // 长度校验
                if (consignorIdNo.length() < OrderConstants.ID_NO_LENGTH_MIN) {
                    LOGGER.error("修改基本信息校验-证件号不能小于6位");
                    UmpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_B2C_MODIFY_CONSIGNEE_AND_CONSIGNOR_CHECK_FAIL_ALARM
                            , "证件号不能小于6位, orderNo:" + orderModel.orderNo()
                            , orderModel.requestProfile(), orderModel.getBusinessIdentity());
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("证件号不能小于6位");
                }
                // 证件类型为身份证或者户口薄时，需要验证证件号码的长度为15位或者为18位
                if ((consignorIdType == IdentityTypeEnum.ID_CARD || consignorIdType == IdentityTypeEnum.HOUSEHOLD_REGISTER)
                        && (!OrderConstants.ID_CARD_LENGTH_OLD.equals(consignorIdNo.length()) && !OrderConstants.ID_CARD_LENGTH_NEW.equals(consignorIdNo.length()))) {
                    LOGGER.error("修改基本信息校验-身份证号码不等于15位或者18位");
                    UmpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_B2C_MODIFY_CONSIGNEE_AND_CONSIGNOR_CHECK_FAIL_ALARM
                            , "身份证号码不等于15位或者18位, orderNo:" + orderModel.orderNo()
                            , orderModel.requestProfile(), orderModel.getBusinessIdentity());
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("身份证号码不等于15位或者18位");
                }
            });
        }
    }

    /**
     * b2c派送、揽收方式校验
     *
     * @param orderModel
     */
    private void shipmentValid(ExpressOrderModel orderModel) throws DomainAbilityException {
        // 港澳电商标快校验：当前单没有pickupByBoxService，则快照必须有pickupByBoxService，所以提前到shipment为null判断前
        shipmentValidBaseHandler.hkmoValidPickupByBoxServiceForDSTH(orderModel);

        Shipment shipment = orderModel.getShipment();
        if (null == shipment) {
            return;
        }
        DeliveryTypeEnum deliveryType = shipment.getDeliveryType();
        if (deliveryType != null) {
            if (deliveryType != DeliveryTypeEnum.TO_DOOR &&
                    deliveryType != DeliveryTypeEnum.SELF_PICKUP) {
                LOGGER.error("派送方式不包含送货上门或者自提");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("派送方式不包含送货上门或者自提");
            }
            if (deliveryType == DeliveryTypeEnum.SELF_PICKUP
                    && StringUtils.isBlank(shipment.getEndStationNo())){
                LOGGER.error("派送方式为自提,目的站点编码不能为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("派送方式为自提,目的站点编码不能为空");
            }
        }

        PickupTypeEnum pickupType = shipment.getPickupType();
        if (PickupTypeEnum.SELF_DELIVERY == pickupType
                && StringUtils.isBlank(shipment.getStartStationNo())){
            if (!orderModel.getProductDelegate().getMainProduct().getProductNo().equals(ProductEnum.HSD.getCode())){
                LOGGER.error("主产品非函速达且揽收方式为自送，始发站点编码不能为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("主产品非函速达且揽收方式为自送，始发站点编码不能为空");
            }
        }
        // 揽收方式为直送分拣时校验始发分拣中心id
        if (PickupTypeEnum.SELF_DELIVERY_DMS == pickupType) {
            if (StringUtils.isBlank(shipment.getStartCenterNo())) {
                LOGGER.error("揽收方式为直送分拣时，始发分拣中心id(startCenterNo)字段不能为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("揽收方式为直送分拣中心时，始发分拣中心id字段不能为空");
            }
        }

        if(UnitedB2CUtil.isUnitedFreightB2C(orderModel)){
            // 揽收方式为上门取件且不包含增值产品自行联系时，期望取件开始时间、期望取件结束时间必填
            if (shipment.getPickupType() != null
                    && PickupTypeEnum.ON_SITE_PICK.getCode().equals(shipment.getPickupType().getCode())
                    && !hasSpecialProduct(orderModel)) {
                Date expectPickupStartTime = shipment.getExpectPickupStartTime();
                Date expectPickupEndTime = shipment.getExpectPickupEndTime();
                if (expectPickupStartTime == null || DateUtils.isZeroDateTime(expectPickupStartTime)) {
                    LOGGER.error("快运订单修改的基本信息-期望取件开始时间在揽收方式为上门取件时不能为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("快运订单修改的基本信息-期望取件开始时间在揽收方式为上门取件时不能为空");
                }
                if (expectPickupEndTime == null || DateUtils.isZeroDateTime(expectPickupEndTime)) {
                    LOGGER.error("快运订单修改的基本信息-期望取件结束时间在揽收方式为上门取件时不能为空");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("快运订单修改的基本信息-期望取件结束时间在揽收方式为上门取件时不能为空");
                }

                // 期望送达开始时间、期望送达结束时间：非必填，修改时传值，需要校验
                if (shipment.getExpectDeliveryStartTime() != null && shipment.getExpectDeliveryEndTime() != null) {
                    // 修改前后的值，开始时间不能晚于结束时间
                    Date expectDeliveryStartTime = shipment.getExpectDeliveryStartTime();
                    Date expectDeliveryEndTime = shipment.getExpectDeliveryEndTime();
                    if (expectDeliveryStartTime.after(expectDeliveryEndTime)) {
                        LOGGER.error("快运订单修改的基本信息-期望送达开始时间不能晚于期望送达结束时间");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom("快运订单修改的基本信息-期望送达开始时间不能晚于期望送达结束时间");
                    }
                }

            }
        }
    }

    /**
     * 关联单据校验
     * @param orderModel
     */
    private void refOrderValid(ExpressOrderModel orderModel) {
        Optional.ofNullable(orderModel.getRefOrderInfoDelegate())
                .ifPresent(refOrderDelegate -> {
                    if (MapUtils.isNotEmpty(refOrderDelegate.getExtendProps())) {
                        refOrderDelegate.getExtendProps().keySet().forEach(orderType -> {
                            if (!ALLOWED_REF_ORDER_TYPE.contains(orderType)) {
                                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                        .withCustom("关联单据信息传参非法");
                            }
                        });
                    }
                });
    }

    /**
     * 财务信息校验
     *
     * @param orderModel 订单修改信息
     */
    private void financeValid(ExpressOrderModel orderModel) {
        Finance finance = orderModel.getFinance();
        if (null == finance) {
            return;
        }
        if (MapUtils.isNotEmpty(finance.getExtendProps())) {
            if (null != orderModel.getOrderSnapshot() && orderModel.getOrderSnapshot().isJG12123()) {
                if (OrderConstants.YES_VAL.equals(finance.getExtendProps().get(FinanceConstants.CUSTOMER_BILLING_ORDER_MARK))) {
                    if (!finance.getExtendProps().containsKey(FinanceConstants.CUSTOMER_BILLING_AMOUNT)) {
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                .withCustom("商家自计费模式-商家支付单-商家计算金额为空");
                    } else {
                        try {
                            String customerAmtStr = finance.getExtendProps().get(FinanceConstants.CUSTOMER_BILLING_AMOUNT);
                            new BigDecimal(customerAmtStr).setScale(2, RoundingMode.HALF_UP);
                        } catch (Exception e) {
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                    .withCustom("商家自计费模式-商家支付单-商家计算金额格式错误");
                        }
                    }
                    if (finance.getExtendProps().containsKey(FinanceConstants.CUSTOMER_MERGE_PAY_AMOUNT)) {
                        BigDecimal customerMergePayAmt;
                        try {
                            String customerMergePayAmtStr = finance.getExtendProps().get(FinanceConstants.CUSTOMER_MERGE_PAY_AMOUNT);
                            customerMergePayAmt = new BigDecimal(customerMergePayAmtStr).setScale(2, RoundingMode.HALF_UP);
                        } catch (Exception e){
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                    .withCustom("商家自计费模式-商家支付单-合并支付金额格式错误");
                        }
                        if (customerMergePayAmt.compareTo(BigDecimal.ZERO) <= 0) {
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                    .withCustom("商家自计费模式-商家支付单-合并支付金额必须大于0");
                        }
                    }
                }
            }
        }

        // 预估费用：非必填，合法性校验
        Optional.ofNullable(finance.getEstimateAmount()).ifPresent(estimateAmount -> {
            if (new BigDecimal(FreightLengthConstants.ESTIMATE_AMOUNT_MAX).compareTo(estimateAmount.getAmount()) <= 0) {
                LOGGER.error("当前的运费的值为: {}, 预估运费大于等于{}.", orderModel.getFinance().getEstimateAmount().getAmount(), FreightLengthConstants.ESTIMATE_AMOUNT_MAX);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("预估运费大于等于一百万");
            }
        });
    }

    /**
     * 渠道信息校验
     */
    private void channelValid(ExpressOrderModel orderModel){
        // 渠道信息：必填
        Channel snapChannel = orderModel.getOrderSnapshot().getChannel();
        Channel channel = orderModel.getChannel();
        SystemCallerEnum systemCaller = snapChannel.getSystemCaller();
        //供应链OFC来源仓配快递非月结不支持改址一单到底
        if (SystemCallerEnum.SUPPLY_OFC == systemCaller && SupplyChainDeliveryOrderSignUtil.snapFlag(orderModel)) {
            if (orderModel.isReaddress1Order2End() && !orderModel.getOrderSnapshot().isPickupOrDeliveryMonthlyPayment()) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("B2C订单修改的基本信息校验-供应链OFC来源仓配快递非月结不支持改址一单到底");
            }
        }

        if (UnitedB2CUtil.isUnitedFreightB2C(orderModel)) {
            // 渠道操作时间：必填
            if (channel.getChannelOperateTime() == null) {
                LOGGER.error("快运订单修改的基本信息-渠道信息的渠道操作时间为空");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("快运订单修改的基本信息-渠道信息的渠道操作时间为空");
            }
        }
    }

    /**
     * 判断是否有自行联系
     * 备注：
     * 1、不需要在基本信息校验判断是新增或者删除，只需要有这个产品。新增删除卡控在修改白名单。
     * 2、当前单的产品在修改信息比对后，已包括快照产品
     */
    private boolean hasSpecialProduct(ExpressOrderModel orderModel) {
        return orderModel != null
                && orderModel.getProductDelegate() != null
                && orderModel.getProductDelegate().ofProductNo(AddOnProductEnum.FRIGHT_CONTACT_DIRECTLY.getCode()) != null;
    }


    /**
     * 零包裹下单包裹号校验
     */
    private void validatePackageNumberRule(Cargo cargo) {
        if (cargo == null) {
            return;
        }
        String cargoNo = cargo.getCargoNo();
        Map<String, String> extendProps = cargo.getExtendProps();
        if (MapUtils.isEmpty(extendProps)) {
            return;
        }
        String packageNumberRule = extendProps.get(PACKAGE_NUMBER_RULE);
        if (StringUtils.isBlank(packageNumberRule)) {
            return;
        }

        if (PACKAGE_NUMBER_RULE_CUSTOM.equals(packageNumberRule)
                && StringUtils.isBlank(cargoNo)) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("包裹号规则为自定义(packageNumberRule=CUSTOM)，包裹号(cargoNo)不能为空");
        }

        if (PACKAGE_NUMBER_RULE_TOTAL_ZERO.equals(packageNumberRule)
                && StringUtils.isNotBlank(cargoNo)) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("包裹号规则为零包裹(packageNumberRule=TOTAL_ZERO)，包裹号(cargoNo)必须为空");
        }
    }

    /**
     * 修改场景获取收货人手机
     */
    private String getModifyConsigneeMobile(ExpressOrderModel orderModel) {
        // 当前单可以用空字符代表清空，不需要判断isNotEmpty或者isNotBlank
        if (orderModel.getConsignee() != null
                && orderModel.getConsignee().getConsigneeMobile() != null) {
            return orderModel.getConsignee().getConsigneeMobile();
        }
        // 再取原单
        if (orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().getConsignee() != null) {
            return orderModel.getOrderSnapshot().getConsignee().getConsigneeMobile();
        }
        return null;
    }

    /**
     * 修改场景获取收货人电话
     */
    private String getModifyConsigneePhone(ExpressOrderModel orderModel) {
        // 当前单可以用空字符代表清空，不需要判断isNotEmpty或者isNotBlank
        if (orderModel.getConsignee() != null
                && orderModel.getConsignee().getConsigneePhone() != null) {
            return orderModel.getConsignee().getConsigneePhone();
        }
        // 再取原单
        if (orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().getConsignee() != null) {
            return orderModel.getOrderSnapshot().getConsignee().getConsigneePhone();
        }
        return null;
    }

    /**
     * 修改场景获取收货人手机
     */
    private String getModifyConsignorMobile(ExpressOrderModel orderModel) {
        // 当前单可以用空字符代表清空，不需要判断isNotEmpty或者isNotBlank
        if (orderModel.getConsignor() != null
                && orderModel.getConsignor().getConsignorMobile() != null) {
            return orderModel.getConsignor().getConsignorMobile();
        }
        // 再取原单
        if (orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().getConsignor() != null) {
            return orderModel.getOrderSnapshot().getConsignor().getConsignorMobile();
        }
        return null;
    }

    /**
     * 修改场景获取收货人电话
     */
    private String getModifyConsignorPhone(ExpressOrderModel orderModel) {
        // 当前单可以用空字符代表清空，不需要判断isNotEmpty或者isNotBlank
        if (orderModel.getConsignor() != null
                && orderModel.getConsignor().getConsignorPhone() != null) {
            return orderModel.getConsignor().getConsignorPhone();
        }
        // 再取原单
        if (orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().getConsignor() != null) {
            return orderModel.getOrderSnapshot().getConsignor().getConsignorPhone();
        }
        return null;
    }



}
