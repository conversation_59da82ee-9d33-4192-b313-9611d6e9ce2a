package cn.jdl.oms.express.b2c.extension.white;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.white.IWhiteExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.BasicTraderResponse;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.DpDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.SupplyChainDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductAttrEnum;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.InitiatorTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.spec.model.ICargo;
import cn.jdl.oms.express.domain.utils.ModifyProductUtil;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Cargo;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.ProductDelegate;
import cn.jdl.oms.express.domain.vo.modify.ChangedProperty;
import cn.jdl.oms.express.domain.vo.modify.ChangedPropertyDelegate;
import cn.jdl.oms.express.freight.extension.white.FreightModifyWhiteExtension;
import cn.jdl.oms.express.domain.vo.modify.ModifyProduct;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.BusinessConstants;
import cn.jdl.oms.express.shared.common.constant.ModifySceneRuleConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.ShipmentConstants;
import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyMarkEnum;
import cn.jdl.oms.express.shared.common.dict.MunicipalityEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.ReaddressMarkEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.ModifyMarkUtil;
import com.jd.matrix.sdk.annotation.Extension;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName B2CModifyWhiteExtension
 * @Description  修改项校验
 * <AUTHOR>
 * @Date 2021/5/13 17:14
 * @ModifyDate 2021/5/13 17:15
 * @Version 1.0
 */
@Extension(code = ExpressOrderProduct.CODE)
public class B2CModifyWhiteExtension implements IWhiteExtension {

    private static final Logger LOGGER = LoggerFactory.getLogger(B2CModifyWhiteExtension.class);

    /**
     * 商家类别：纯外单
     */
    private static final int TRADERMOLD_CWD = 1002;

    /**
     * 快递-代收货款编码
     */
    private static final String COD_PRODUCT_NO = "ed-a-0009";

    /**
     * 快递-代收货款编码集合
     */
    private static final HashSet<String> EXPRESS_COD_NO_SET = new HashSet<>();

    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    @Resource
    private CustomerConfigFacade customerConfigFacade;

    @Resource
    private FreightModifyWhiteExtension freightModifyWhiteExtension;

    static {
        EXPRESS_COD_NO_SET.add("ed-a-0009");
        EXPRESS_COD_NO_SET.add("LL-ZZ-DSHK");
    }

    // 政府补贴订单状态常量
    private static final String GOV_SUBSIDY_INITIATE_RESHOOT_STATUS = "4";  // 发起补拍状态
    private static final String GOV_SUBSIDY_MERCHANT_APPROVED_STATUS = "7"; // 商家审核通过状态


    @Override
    public void execute(ExpressOrderContext expressOrderContext) throws AbilityExtensionException {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        if(UnitedB2CUtil.isUnitedFreightB2C(orderModel)){
            freightModifyWhiteExtension.execute(expressOrderContext);
            return;
        }
        ExpressOrderModel snapshot = orderModel.getOrderSnapshot();
        // 更改的属性集合
        ChangedPropertyDelegate changedPropertyDelegate = expressOrderContext.getChangedPropertyDelegate();
        // 订单当前状态
        OrderStatusEnum currentOrderStatus = orderModel.getOrderSnapshot().getOrderStatus().getOrderStatus();
        // 订单号
        String orderNo = orderModel.orderNo();

        // 获取原单标记位
        String modifyMark = null;


        if (ModifySceneRuleUtil.isNoTaskFinishCollect(orderModel)) {
            //无任务揽收不执行扩展点校验，统一在ability校验
            return;
        }

        // 校验国际b2c订单修改逻辑
        if (orderModel.isIntlB2C()) {
            validateIntlB2CModify(orderModel, snapshot, changedPropertyDelegate);
            return;
        }

        if (orderModel.getOrderSnapshot().getExtendProps() != null) {
            modifyMark = orderModel.getOrderSnapshot().getExtendProps().get(AttachmentKeyEnum.MODIFY_MARK.getKey());
        }

        if (StringUtils.isBlank(modifyMark)) {
            modifyMark = ModifyMarkUtil.getInitMark();
        }

        String majorProductNo = orderModel.getOrderSnapshot().getProductDelegate().getMajorProductNo();
        if (ProductEnum.JDBS.getCode().equals(majorProductNo)) {
            LOGGER.error("帮送订单不允许修改");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("帮送订单不允许修改");
        }

        if (BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.MODIFY_ORDER_BLACK_MAIN_PRODUCT_LIST, majorProductNo)) {
            LOGGER.error("主产品不允许修改：{}", majorProductNo);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("主产品不允许修改");
        }

        // TODO 重构产品维度的校验代码
        // TODO 是否也可以用上面逻辑 校验 当前单和快照的主产品不允许修改
        if (changedPropertyDelegate.productHaveChange()) {
            String modifiedMainProduct = orderModel.getProductDelegate().getMajorProductNo();
            if (StringUtils.isNotBlank(modifiedMainProduct)
                    && BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.MODIFY_ORDER_BLACK_MAIN_PRODUCT_LIST, modifiedMainProduct)) {
                LOGGER.error("修改后主产品不允许修改：{}", majorProductNo);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("修改后主产品不允许修改");
            }
        }

        if (changedPropertyDelegate.propertyHasChange(ModifyItemConfigEnum.MATERIAL_TURNOVER)) {
            if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.B2C_MATERIAL_TURNOVER_NOT_ALLOW_MODIFY_SWITCH)) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("物资周转不允许修改");
            }
        }
        // 如果是港澳订单
        if (orderModel.getOrderSnapshot().isHKMO()) {
            // 订单类型
            String orderType = (null != orderModel.getOrderType()) ? orderModel.getOrderType().getCode() : null;
            // 修改策略为"仅修改报关信息"
            if (orderModel.isOnlyModifyCustoms()) {
                // 如果修改的内容不在白名单中则不允许修改
                validateOnlyModifyFields(changedPropertyDelegate, orderModel);
                // 如果删除字段不在白名单中则不允许修改
                validateOnlyModifyClearFields(orderModel);
            } else if (OrderTypeEnum.RETURN_ORDER.getCode().equals(orderType)) {
                // 如果是逆向单, 则修改策略必填 "仅修改报关信息"
                LOGGER.error("港澳逆向单修改时修改策略必填--仅修改报关信息, orderNo: {} ", expressOrderContext.getOrderModel().orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("港澳逆向单修改时修改策略必填--仅修改报关信息");
            }
        }

        // 获取修改策略
        String modifySceneRule = ModifySceneRuleUtil.getModifySceneRule(orderModel);

        // 是否 仅修改多地址审核状态
        if (ModifySceneRuleUtil.isOnlyModifyMultiAddressVerifyStatus(modifySceneRule)) {
            // 校验 仅修改多地址审核状态
            validateOnlyModifyMultiAddressVerifyStatus(orderModel, changedPropertyDelegate);
        }

        // 处理 内部修改策略
        if (ModifySceneRuleUtil.isPartOfInternalModify(modifySceneRule)) {
            // 目前 仅校验 修改/清除收发货人信息
            validateInternalModify(modifySceneRule, orderModel, changedPropertyDelegate);
            return;
        }

        // 仅修改收件人联系方式（姓名、电话、手机）
        if (ModifySceneRuleUtil.isOnlyModifyConsigneeContactInformation(modifySceneRule)) {
            // 校验仅修改收件人联系方式
            validateOnlyModifyConsigneeContactInformation(modifySceneRule, orderModel, changedPropertyDelegate);
        }

        // 修改联系方式
        if (ModifySceneRuleUtil.isModifyContactInformation(modifySceneRule)) {
            validateModifyContactInformation(modifySceneRule, changedPropertyDelegate, orderModel);
        }

        // 仓出库发货/特殊仓出库发货
        if (ModifySceneRuleUtil.isTypeOfOutboundDelivery(orderModel)) {
            validateOutboundDelivery(orderModel);
            return;
        }

        // 仅修改补签标识
        if (ModifySceneRuleUtil.isModifyReSignFlag(modifySceneRule)) {
            validateModifyReSignFlag(changedPropertyDelegate);
            return;
        }

        // 仅修改国补审核状态
        if (ModifySceneRuleConstants.MODIFY_GUO_BU_STATUS.equals(modifySceneRule)) {
            if (changedPropertyDelegate.getChangedProperties().size() != 1
                    || !changedPropertyDelegate.propertyHasChange(ModifyItemConfigEnum.GOV_SUBSIDY_APPROVAL_STATUS)) {
                LOGGER.error("修改策略为仅修改国补审核状态，只能修改国补审核状态");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).
                        withCustom("修改策略为仅修改国补审核状态，只能修改国补审核状态");
            }

            // 判断时间
            Date channelOperateTime = orderModel.getChannel().getChannelOperateTime();
            long opTime = channelOperateTime == null ? System.currentTimeMillis() : channelOperateTime.getTime();
            String lastOpTimeStr = snapshot.getAttachment(AttachmentKeyEnum.GOV_SUBSIDY_STATUS_UPDATE_TIME.getKey());
            if (StringUtils.isNotBlank(lastOpTimeStr)) {
                long lastOpTime = Long.parseLong(lastOpTimeStr);
                if (opTime <= lastOpTime) {
                    LOGGER.error("修改审核状态：操作时间{}晚于订单上次操作时间{}，请重新发起", opTime, lastOpTime);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).
                            withCustom("修改审核状态：操作时间晚于订单上次操作时间，请重新发起");
                }
            }
            // 记录更新时间
            orderModel.putAttachment(AttachmentKeyEnum.GOV_SUBSIDY_STATUS_UPDATE_TIME.getKey(), String.valueOf(opTime));

            // 校验国补状态
            String govSubsidyStatus = orderModel.getAttachment(AttachmentKeyEnum.GOV_SUBSIDY_APPROVAL_STATUS.getKey());
            String snapGovSubsidyStatus= snapshot.getAttachment(AttachmentKeyEnum.GOV_SUBSIDY_APPROVAL_STATUS.getKey());

            if (GOV_SUBSIDY_INITIATE_RESHOOT_STATUS.equals(govSubsidyStatus) &&
                    GOV_SUBSIDY_MERCHANT_APPROVED_STATUS.equals(snapGovSubsidyStatus)) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).
                        withCustom("修改审核状态：商家审核通过不允许更新为发起补拍");
            }

            if(GOV_SUBSIDY_INITIATE_RESHOOT_STATUS.equals(snapGovSubsidyStatus) &&
                    GOV_SUBSIDY_MERCHANT_APPROVED_STATUS.equals(govSubsidyStatus)){
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).
                        withCustom("修改审核状态：发起补拍不允许更新为商家审核通过");
            }
        }

        // 修改项基本信息校验-期望提货开始时间、期望送达结束时间不能早于系统当前时间
        Date currentDate = new Date();
        if (changedPropertyDelegate.propertyHasChange(ModifyItemConfigEnum.EXPECT_PICKUP_START_TIME)
                && orderModel.getShipment().getExpectPickupStartTime().getTime() < currentDate.getTime()) {
            LOGGER.info("期望提货开始时间不能早于当前时间. orderNo: {}", orderNo);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("期望提货开始时间不能早于当前时间");
        }

        if (changedPropertyDelegate.propertyHasChange(ModifyItemConfigEnum.EXPECT_DELIVERY_END_TIME)
                && orderModel.getShipment().getExpectDeliveryEndTime().getTime() < currentDate.getTime()) {
            LOGGER.info("期望送达结束时间不能早于当前时间. orderNo: {}", orderNo);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("期望送达结束时间不能早于当前时间");
        }

        // 怕对线上产生影响，本次不做这个功能，先注释掉
        // 结算方式发生变更
//        if (changedPropertyDelegate.settlementTypeHaveChange()) {
//            // 若原单为散客挂月结（extendProps-individualMsType为1），不允许修改结算方式
//            String individualMsType = orderModel.getOrderSnapshot().getAttachment(AttachmentKeyEnum.INDIVIDUAL_MS_TYPE.getKey());
//            if ("1".equals(individualMsType)) {
//                LOGGER.info("原单为散客挂月结,不允许修改结算方式. orderNo: {}", orderNo);
//                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("原单为散客挂月结,不允许修改结算方式");
//            }
//        }

        //揽收后能否改代收货款或收件人信息的判断逻辑扩大至两项改过一项就不让改,分如下场景
        // 1、两项都未修改，可以修改一项，也可以同时修改两项
        // 2、如果有一项修改过，则不能同时再次修改两项，也不能再次修改另外的一项
        String originCodSign = ModifyMarkUtil.getPositionMark(modifyMark, ModifyMarkEnum.COD.getPosition());
        String originConsigneeSign = ModifyMarkUtil.getPositionMark(modifyMark, ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getPosition());
        //修改项基本信息校验-产品信息发生变更
        if (changedPropertyDelegate.productHaveChange()) {
            // 获取修改后的产品
            List<ModifyProduct> modifyProductList = ModifyProductUtil.getModifyProduct(changedPropertyDelegate);
            List<String> modifyProductCodeList = modifyProductList.stream().map(ModifyProduct::getProductNo).collect(Collectors.toList());

            // 卡控揽收后不能修改的产品
            if (orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp()) {
                // 基本白名单
                Set<String> allWhiteList = AddOnProductEnum.getAllowedModifiedProductCode();

                // 特殊白名单：拦截一单到底
                if (orderModel.isInterceptionThroughOrderModify()) {
                    List<String> whiteList = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.INTERCEPT_READDRESS_AFTER_PICKUP_ALLOW_MODIFY_PRODUCT_WHITE_LIST,",");
                    allWhiteList.addAll(whiteList);
                }

                // 特殊白名单：德邦一单到底（德邦落地配目前都是寄付月结）
                if (DpDeliveryOrderSignUtil.flag(orderModel)) {
                    List<String> whiteList = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.DP_DELIVERY_AFTER_PICK_UP_PRODUCT_WHITE_LIST,",");
                    allWhiteList.addAll(whiteList);
                }

                // 揽收后【结算方式=月结】支持修改【送货入仓】
                if (orderModel.getOrderSnapshot().isPickupOrDeliveryMonthlyPayment()) {
                    allWhiteList.add(AddOnProductEnum.KUAI_DI_RU_CANG.getCode());
                }

                // 揽收后【结算方式!=寄付现结】支持修改【消杀】
                if (!orderModel.getOrderSnapshot().isCashOnPick()) {
                    allWhiteList.add(AddOnProductEnum.DISINFECT_EXPRESS.getCode());
                }

                // 特殊白名单：拒收一单到底
                if (orderModel.isRejectionOrder()) {
                    List<String> whiteList = BatrixSwitch.obtainListByUccKey(BatrixSwitchKey.REJECT_READDRESS_ALLOW_MODIFY_PRODUCT_WHITE_LIST,",");
                    allWhiteList.addAll(whiteList);
                }

                List<String> illegalProductCodeList = modifyProductList.stream()
                        .map(ModifyProduct::getProductNo)
                        .filter(productNo -> !allWhiteList.contains(productNo))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(illegalProductCodeList)) {
                    String forbidDesc = "揽收后修改，不允许修改的产品:" + ProductDelegate.buildAddOnProductInfo(illegalProductCodeList);
                    LOGGER.error(forbidDesc);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(forbidDesc);
                }

                // 校验有修改的商品能否新增或者删除
                validateProductInsertOrDelete(orderModel, modifyProductList);
            }

            // 「激活校验」增值产品 只允许在「派送前」修改
            if (modifyProductCodeList.contains(AddOnProductEnum.ACTIVATION_CHECK.getCode()) &&
                    !orderModel.getOrderSnapshot().getOrderStatus().isBeforeOrderDelivery()) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("派送后不支持修改增值产品「激活校验」ed-a-0098");
            }

            if (DpDeliveryOrderSignUtil.flag(orderModel)) {
                LOGGER.info("德邦落地配，不卡控修改代物流公司收货款");
            } else {
                // 如果修改了代收货款增值产品
                // 订单标识-是否仓配快递，取原单
                boolean isSupplyChainDelivery = SupplyChainDeliveryOrderSignUtil.snapFlag(orderModel);
                for (String codCode : AddOnProductEnum.getCodCode()) {
                    if (modifyProductCodeList.contains(codCode)) {
                        Product codProduct = orderModel.getProductDelegate().ofProductNo(codCode);
                        //新增代收货款增值服务需要判断是否是生鲜和非生鲜互改
                        if (codProduct.getOperateType() == OperateTypeEnum.INSERT) {
                            // 原单的代收货款金额
                            Product originCod = orderModel.getOrderSnapshot().getProductDelegate().getCodProduct();
                            if(originCod == null){
                                if (orderModel.getOrderSnapshot().getOrderStatus().isBeforePickedUp()) {
                                    LOGGER.info("揽收前支持【新增代收货款增值服务】");
                                    continue;
                                } else {
                                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("揽收后不支持新增代收货款增值服务");
                                }
                            }
                            String targetMainProductCode = orderModel.getProductDelegate().getMajorProductNo();
                            String originMainProductCode = orderModel.getOrderSnapshot().getProductDelegate().getMajorProductNo();
                            //主产品为非生鲜改为生鲜或生鲜改为非生鲜允许新增代收货款
                            if ((ProductEnum.isFreshMainProduct(targetMainProductCode) && !ProductEnum.isFreshMainProduct(originMainProductCode))
                                    || (!ProductEnum.isFreshMainProduct(targetMainProductCode) && ProductEnum.isFreshMainProduct(originMainProductCode))) {
                                LOGGER.info("主产品为非生鲜改为生鲜或生鲜改为非生鲜允许新增代收货款");
                                // TODO 此开关下掉之后，原方法mainProductChargeResolve可以同步删除
                                if (expressUccConfigCenter.isAllowCodModifyTwoTimesSwitch() && EXPRESS_COD_NO_SET.contains(codCode)) {
                                    LOGGER.info("INSERT-COD多次修改开关开启,codCode:{}", codCode);
                                    modifyMark = mainProductChargeResolveV2(orderModel, codProduct, originCod, orderNo, originCodSign, modifyMark, isSupplyChainDelivery);
                                } else {
                                    LOGGER.info("INSERT-COD多次修改开关关闭,codCode:{}", codCode);
                                    modifyMark = mainProductChargeResolve(codProduct,originCod,orderModel.getOrderSnapshot().getChannel().getChannelNo(),
                                            orderNo,orderModel.getCustomer().getAccountNo(),orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp(),
                                            originCodSign,originConsigneeSign,modifyMark, isSupplyChainDelivery);
                                }
                            }
                        }else if(codProduct.getOperateType() == OperateTypeEnum.UPDATE){
                            //入参的代收货款金额
                            BigDecimal modifyCod = new BigDecimal(codProduct.getProductAttrs().get(AddOnProductAttrEnum.COD.getCode()));
                            //原代收货款金额
                            Product originProduct = orderModel.getOrderSnapshot().getProductDelegate().getCodProduct();
                            BigDecimal originCod = new BigDecimal(originProduct.getProductAttrs().get(AddOnProductAttrEnum.COD.getCode()));

                            //销售平台为京东平台
                            if (BusinessConstants.JD_CHANNEL_NO.equals(orderModel.getOrderSnapshot().getChannel().getChannelNo())
                                    && modifyCod.compareTo(originCod) != 0) {
                                if (isSupplyChainDelivery) {
                                    LOGGER.info("isSupplyChainDelivery=true，支持修改代收货款金额");
                                } else {
                                    LOGGER.info("京东平台的订单不支持修改代收货款金额,orderNo:{}", orderNo);
                                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("京东平台的订单不支持修改代收货款金额");
                                }
                            }

                            //获取商家配置信息
                            BasicTraderResponse basicTraderResponse = customerConfigFacade.getCustomerConfig(orderModel.getCustomer().getAccountNo());
                            //商家类型为纯外单
                            if (TRADERMOLD_CWD == basicTraderResponse.getTraderMold()) {
                                if(modifyCod.compareTo(BigDecimal.ZERO) < 0){
                                    LOGGER.info("商家类型为纯外单时,修改代收货款要大于等于0,orderNo:{}", orderNo);
                                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("商家类型为纯外单时,修改代收货款要大于等于0");
                                }
                            }else{
                                if(modifyCod.compareTo(BigDecimal.ZERO) <= 0){
                                    LOGGER.info("商家类型非纯外单时,修改代收货款要大于0,orderNo:{}", orderNo);
                                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("商家类型非纯外单时,修改代收货款要大于0");
                                }
                            }
                            //揽收后的校验规则
                            if(orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp()){
                                //是否修改过
                                if (ModifyMarkEnum.COD.getSign().equals(originCodSign)) {
                                    LOGGER.info("代收货款信息已经修改，不支持再次修改代收货款,orderNo:{}", orderNo);
                                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.MODIFY_VALIDATE_FAIL_COD).withCustom("代收货款信息已经修改，不支持再次修改代收货款");
                                }
                                //当代收货款金额发生变化时（增加代收货款比对开关，后续可能去掉）
                                if (expressUccConfigCenter.isModifyCodSwitch() && originCod.compareTo(modifyCod) != 0) {
                                    //不能从小改大
                                    if (modifyCod.compareTo(originCod) > 0) {
                                        LOGGER.info("修改后代收货款金额大于修改前代收货款金额,orderNo:{}", orderNo);
                                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改后代收货款金额大于修改前代收货款金额，不支持修改");
                                    }
                                }

                                if (expressUccConfigCenter.isAllowCodModifyTwoTimesSwitch() && EXPRESS_COD_NO_SET.contains(codCode)) {
                                    LOGGER.info("UPDATE-COD多次修改开关开启,codCode:{}", codCode);
                                    // 记录COD修改次数并更新修改标记
                                    modifyMark = recordCodModifyTimesAndUpdateModifyMark(orderModel, modifyMark);
                                } else {
                                    LOGGER.info("UPDATE-COD多次修改开关关闭,codCode:{}", codCode);
                                    //更新标位
                                    modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.COD.getPosition(), ModifyMarkEnum.COD.getSign());
                                }
                            }
                        }else if(codProduct.getOperateType() == OperateTypeEnum.DELETE){
                            //销售平台为京东平台
                            if (BusinessConstants.JD_CHANNEL_NO.equals(orderModel.getOrderSnapshot().getChannel().getChannelNo())) {
                                LOGGER.info("京东平台的订单不支持删除代收货款,orderNo:{}", orderNo);
                                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("京东平台的订单不支持删除代收货款");
                            }
                            //揽收后的校验规则
                            if(orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp()){
                                //是否修改过
                                if (ModifyMarkEnum.COD.getSign().equals(originCodSign)) {
                                    LOGGER.info("代收货款信息已经修改，不支持删除代收货款,orderNo:{}", orderNo);
                                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.MODIFY_VALIDATE_FAIL_COD).withCustom("代收货款信息已经修改，不支持删除代收货款");
                                }

                                if (expressUccConfigCenter.isAllowCodModifyTwoTimesSwitch() && EXPRESS_COD_NO_SET.contains(codCode)) {
                                    LOGGER.info("DELETE-COD多次修改开关开启,codCode:{}", codCode);
                                    // 记录COD修改次数并更新修改标记
                                    modifyMark = recordCodModifyTimesAndUpdateModifyMark(orderModel, modifyMark);
                                } else {
                                    LOGGER.info("DELETE-COD多次修改开关关闭,codCode:{}", codCode);
                                    //更新标位
                                    modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.COD.getPosition(), ModifyMarkEnum.COD.getSign());
                                }
                            }
                        }
                    }
                }
            }


            // 订单结算方式
            SettlementTypeEnum settlementType = snapshot.getFinance().getSettlementType();
            if(changedPropertyDelegate.settlementTypeHaveChange()){
                // 如果结算方式变更则取最新的修改后的结算方式
                settlementType = orderModel.getFinance().getSettlementType();
            }

            // 校验能否修改产品
            for(ChangedProperty changedProperty:changedPropertyDelegate.getChangedProperties()){
                // 共享送主产品，不能修改
                if(ModifyItemConfigEnum.PRODUCT_NO.getField().equals(changedProperty.getProperty()) &&
                        (OperateTypeEnum.INSERT == changedProperty.getOperateType() && ProductEnum.GXS.getCode().equals(changedProperty.getModifyValue()) ||
                                OperateTypeEnum.DELETE == changedProperty.getOperateType() && ProductEnum.GXS.getCode().equals(changedProperty.getOriginValue()))){
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("共享送和其他产品不能相互修改");
                }
                // 自行联系-快递：不支持从无改有，支持从有改无。仅支持揽收前修改
                if(ModifyItemConfigEnum.PRODUCT_NO.getField().equals(changedProperty.getProperty()) &&
                        OperateTypeEnum.INSERT == changedProperty.getOperateType() && AddOnProductEnum.EXPRESS_CONTACT_DIRECTLY.getCode().equals(changedProperty.getModifyValue())){
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("自行联系-快递(ed-a-0084)不能新增");
                }
                // 百川流程，国补增值服，揽收后修改
                // 寄付 不能从有到无 不能从无到有  不能从A到B
                // 到付 不能从有到无 不能从无到有 不能从A到B
                // https://joyspace.jd.com/pages/G0hyCRMoGagEM96u9gtC
                if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.GB_B2C_MODIFY_VALID_SWITCH)) {//国补B2C修改校验开关
                    if (orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp() //揽收后
                            && ModifyItemConfigEnum.PRODUCT_NO.getField().equals(changedProperty.getProperty()) //变更项-产品
                            && (AddOnProductEnum.ACTIVATION_CHECK.getCode().equals(changedProperty.getModifyValue())
                            || AddOnProductEnum.ACTIVATION_CHECK.getCode().equals(changedProperty.getOriginValue()))//变更项-产品-国补
                    ) {
                        //订单结算方式为到付现结，寄付现结不允许修改
                        if (SettlementTypeEnum.CASH_ON_DELIVERY == settlementType
                                || SettlementTypeEnum.CASH_ON_PICK == settlementType) {
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("现结单，揽收后，国补激活(ed-a-0098)不能修改");
                        }
                    }
                }
            }
        }

        // 校验必须删除的产品是否删除（不放上述changedPropertyDelegate.productHaveChange()内原因：卡控必须删除但是没修改增值产品）
        validateProductMustDelete(orderModel, orderModel.getOrderSnapshot());

        //收件人发生修改 并且 不是仅修改收件人联系方式 并且 不是修改联系方式 港澳同城改址以上游传改址产品为准
        if ((changedPropertyDelegate.consigneeHaveChange() || isHKMOReaddress(orderModel))
                && !ModifySceneRuleUtil.isOnlyModifyConsigneeContactInformation(orderModel)
                && !ModifySceneRuleUtil.isModifyContactInformation(orderModel)) {
            if(orderModel.getOrderSnapshot().getProductDelegate().ofProductNo(ProductEnum.TSSTC.getCode()) != null){
                LOGGER.info("特瞬送同城不支持修改收件人信息: {}", orderNo);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("特瞬送同城不支持修改收件人地址");
            }else if(orderModel.getOrderSnapshot().getProductDelegate().ofProductNo(ProductEnum.TCSP.getCode()) != null){
                LOGGER.info("同城速配不支持修改收件人信息: {}", orderNo);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("同城速配不支持修改收件人地址");
            }else if(currentOrderStatus == OrderStatusEnum.CUSTOMER_SIGNED
                    || currentOrderStatus == OrderStatusEnum.PARTIAL_SIGNED
                    || currentOrderStatus == OrderStatusEnum.CUSTOMER_REJECTED){
                if (currentOrderStatus == OrderStatusEnum.CUSTOMER_REJECTED && orderModel.isRejectionOrder()) {
                    LOGGER.info("拒收一单到底，拒收状态允许修改收件人信息");
                } else {
                    LOGGER.info("妥投和拒收状态下不支持修改收件人信息: {}", orderNo);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("妥投和拒收状态下不支持修改收件人地址");
                }
            }

            if (orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp()){
                // 揽收后
                if (orderModel.isReaddress1Order2End()){
                    // 原单月结 不允许收件人发起改址
                    if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.MONTH_SETTLE_READDRESS_VALID_SWITCH)) {
                        if (SettlementTypeEnum.MONTHLY_PAYMENT == orderModel.getOrderSnapshot().getFinance().getSettlementType()
                                && InitiatorTypeEnum.CONSIGNEE == orderModel.getInitiatorType()
                                && !orderModel.isCashOnDelivery()
                                && !orderModel.isSameSiteReaddress()){
                            LOGGER.error("原单月结收件人只能到付改址");
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("原单月结收件人只能到付改址");
                        }
                    } else {
                        LOGGER.info("月结改址校验开关关闭");
                    }
                    //客服系统、订单标识-德邦落地配允许多次修改收件人信息
                    if (SystemCallerEnum.CSS == orderModel.getChannel().getSystemCaller()
                            || SystemCallerEnum.JDL_ISC == orderModel.getChannel().getSystemCaller()
                            || DpDeliveryOrderSignUtil.flag(orderModel)) {
                        // 收件人地址发生变更 一单到底修改，客服系统来源、订单标识-德邦落地配 不超过上限
                        String readdressTimes = Optional.ofNullable(orderModel.getOrderSnapshot().getAttachment(OrderConstants.READDRESS_TIMES)).orElse("0");
                        int readdressTimesVal = Integer.parseInt(readdressTimes);
                        if (OrderConstants.READDRESS_TIMES_MAX <= readdressTimesVal){
                            LOGGER.error("客服系统来源发起的改址，最多只能操作{}次,",OrderConstants.READDRESS_TIMES_MAX);
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.MODIFY_VALIDATE_FAIL_CONSIGNEE).withCustom("客服系统来源发起的改址，最多只能操作"+OrderConstants.READDRESS_TIMES_MAX+"次");
                        }
                    } else {
                        // 判断标位
                        if (ReaddressMarkEnum.RESULT_SUCCESS.getCode().equals(originConsigneeSign)
                                && !modifyIDInfoWhite(changedPropertyDelegate,orderModel)
                        ) {
                            LOGGER.info("收件人信息已经修改，不支持再次修改收件人信息: {}", orderNo);
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.MODIFY_VALIDATE_FAIL_CONSIGNEE).withCustom("收件人信息已经修改，不支持再次修改");
                        }
                    }

                    if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.READDRESS_JF_VALID_SWITCH)) {
                        // 校验 改址一单到底不支持寄付后款
                        if (SettlementTypeEnum.CASH_ON_PICK == orderModel.getFinance().getSettlementType()
                                && PaymentStageEnum.CASHONDELIVERY == orderModel.getFinance().getPaymentStage()) {
                            LOGGER.info("改址一单到底，不支持寄付后款改址: {}", orderNo);
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                                    .withCustom("改址一单到底，不支持寄付后款改址");
                        }
                    }
                } else {
                    // 判断标位
                    if (ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getSign().equals(originConsigneeSign)) {
                        if (modifyIDInfoWhite(changedPropertyDelegate,orderModel)
                                || ModifySceneRuleUtil.isCssCustomsAudit(modifySceneRule))
                        {
                            LOGGER.info("特殊策略，允许修改收件人信息: {}", orderNo);
                        } else {
                            LOGGER.info("收件人信息已经修改，不支持再次修改收件人信息: {}", orderNo);
                            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.MODIFY_VALIDATE_FAIL_CONSIGNEE)
                                    .withCustom("收件人信息已经修改，不支持再次修改");
                        }
                    }

                    //揽收后需要校验一级地址（省）、二级地址（市）是否变更
                    Address originAddress = orderModel.getOrderSnapshot().getConsignee().getAddress();
                    if (expressOrderContext.getChangedPropertyDelegate().propertyHasChange(ModifyItemConfigEnum.CONSIGNEE_PROVINCE_NO_GIS)
                            && MunicipalityEnum.isMunicipality(originAddress.getProvinceNoGis())) {
                        LOGGER.info("直辖市一级地址不支持修改: {}", orderNo);
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("直辖市一级地址不支持修改");
                    } else if (expressOrderContext.getChangedPropertyDelegate().propertyHasChange(ModifyItemConfigEnum.CONSIGNEE_CITY_NO_GIS)
                            && !MunicipalityEnum.isMunicipality(originAddress.getProvinceNoGis())) {
                        LOGGER.info("非直辖市二级地址不支持修改: {}", orderNo);
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("非直辖市二级地址不支持修改");
                    }
                    // 更新标位 TODO ModifyMarkEnum -> ReaddressMarkEnum
                    modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getPosition(), ModifyMarkEnum.CONSIGNEE_ADDRESS_AFTER_PICK_UP.getSign());
                }
            }

        }

        //揽收后如果修改货品信息则货品的长、宽、高、货品数量、重量均不能为空
        if(orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp()
                && expressOrderContext.getChangedPropertyDelegate().propertyHasChange(ModifyItemConfigEnum.CARGO)){
            for (ICargo iCargo : orderModel.getCargoDelegate().getCargoList()) {
                Cargo cargo = (Cargo) iCargo;
                if (null == cargo.getCargoDimension()
                        || null == cargo.getCargoDimension().getHeight()
                        || null == cargo.getCargoDimension().getLength()
                        || null == cargo.getCargoDimension().getWidth()
                        || null == cargo.getCargoQuantity()
                        || null == cargo.getCargoQuantity().getValue()
                        || null == cargo.getCargoWeight()
                        || null == cargo.getCargoWeight().getValue()) {
                    LOGGER.info("修改订单货品信息时长宽高货品数量均不能为空,orderNo:{}", orderModel.orderNo());
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).
                            withCustom("修改订单货品信息时长、宽、高、货品数量均不能为空");
                }
            }
        }

        //无接触收货方式或指定地点发生修改
        if (changedPropertyDelegate.contactlessTypeHaveChange()) {
            if (!orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp()) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("无接触收货方式必须是在订单揽收后才可以设置");
            }

            //是否修改过无接触收货
            String originSign = ModifyMarkUtil.getPositionMark(modifyMark, ModifyMarkEnum.CONTACTLESS.getPosition());
            if (ModifyMarkEnum.CONTACTLESS.getSign().equals(originSign)) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("无接触收货方式已经发生过修改，不支持再次修改");
            }

            //是否修改过派送方式
            originSign = ModifyMarkUtil.getPositionMark(modifyMark, ModifyMarkEnum.DELIVERY_TYPE.getPosition());
            if (ModifyMarkEnum.DELIVERY_TYPE.getSign().equals(originSign)) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("派送方式已经发生过修改，不支持再次修改");
            }

            //更新标位
            modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.CONTACTLESS.getPosition(), ModifyMarkEnum.CONTACTLESS.getSign());
        }

        //派送方式发生修改
        if (changedPropertyDelegate.deliveryTypeHaveChange() && orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp()) {
            //是否修改过派送方式
            String originSign = ModifyMarkUtil.getPositionMark(modifyMark, ModifyMarkEnum.DELIVERY_TYPE.getPosition());
            if (ModifyMarkEnum.DELIVERY_TYPE.getSign().equals(originSign)) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("派送方式已经发生过修改，不支持再次修改");
            }
            //更新标位
            modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.DELIVERY_TYPE.getPosition(), ModifyMarkEnum.DELIVERY_TYPE.getSign());
        }

        //将更新后的标位放入上下文中
        changedPropertyDelegate.setModifyMark(modifyMark);
        //更新订单信息上的modifyMark
        orderModel.putAttachment(AttachmentKeyEnum.MODIFY_MARK.getKey(), modifyMark);


        //todo readdress1order2endV2 一单到底-路由运营模式允许通过配送域扩展字段修改
        //改址一单到底引入
        //下单时带了路由运营模式，然后发生改址，改址时会通过订单存储的运营模式调用产品中心，产品中心调用路由
        //结果：因为地址变更导致原有运营模式不适配，路由校验失败，产品校验失败，改址失败拒单
        //fixme 产品层面是不是可以考虑下单不下运营模式，按路由返回的确定模式去运营
        if(StringUtils.isNotBlank(orderModel.getShipment().getExtendProps(ShipmentConstants.OPERATION_MODE))){
            if(null == orderModel.getProductDelegate().getMainProduct()){
                orderModel.getProductDelegate().addMainProduct((Product) orderModel.getOrderSnapshot().getProductDelegate().getMainProduct());
            }
            Map<String,String> appendExt = new HashMap<>();
            appendExt.put(ShipmentConstants.OPERATION_MODE, orderModel.getShipment().getExtendProps(ShipmentConstants.OPERATION_MODE));
            appendExt.put(AttachmentKeyEnum.OPERATE_TYPE.getKey(), String.valueOf(OperateTypeEnum.UPDATE.getCode()));
            orderModel.getProductDelegate().appendMainProductExt(appendExt);
        } else {
            if(orderModel.isReaddress1Order2End()){
                if(null == orderModel.getProductDelegate().getMainProduct()){
                    orderModel.getProductDelegate().addMainProduct((Product) orderModel.getOrderSnapshot().getProductDelegate().getMainProduct());
                    Map<String,String> appendExt = new HashMap<>();
                    appendExt.put(AttachmentKeyEnum.OPERATE_TYPE.getKey(), String.valueOf(OperateTypeEnum.UPDATE.getCode()));
                    orderModel.getProductDelegate().appendMainProductExt(appendExt);
                }
                orderModel.getProductDelegate().removeMainProductExt(ShipmentConstants.OPERATION_MODE);
            }
        }
        // 修改项白名单校验 todo 渠道维度加开关，保运营
        // 渠道来源
        /*String modifySource = orderModel.getChannel().getSystemCaller().getCode();
        if (expressUccConfigCenter.isB2CWhiteNoCheck(modifySource)) {
            LOGGER.info("{}来源白名单配置校验关闭！",modifySource);
            return;
        }*/

    }

    /**
     * 记录COD修改次数并更新修改标记
     *
     * @param orderModel
     * @param modifyMark
     * @return
     */
    private String recordCodModifyTimesAndUpdateModifyMark(ExpressOrderModel orderModel, String modifyMark) {

        // 月结COD允许修改2次
        if (SettlementTypeEnum.MONTHLY_PAYMENT == orderModel.getOrderSnapshot().getFinance().getSettlementType()
                || SettlementTypeEnum.MONTHLY_PAYMENT_DELIVERY == orderModel.getOrderSnapshot().getFinance().getSettlementType()) {
            LOGGER.info("月结订单，记录COD修改次数，更新modifyMark");

            // 记录COD修改次数并在修改次数达到2次时更新标位
            Map<String, String> snapshotExtendProps = orderModel.getOrderSnapshot().getExtendProps();
            if (MapUtils.isEmpty(snapshotExtendProps)) {
                LOGGER.info("月结订单，第一次修改COD");
                // 记录COD修改次数
                orderModel.getExtendProps().put(OrderConstants.COD_MODIFY_TIMES, "1");
            } else {
                //
                String codModifyTimes = snapshotExtendProps.get(OrderConstants.COD_MODIFY_TIMES);
                if (StringUtils.isBlank(codModifyTimes)) {
                    LOGGER.info("月结订单，第一次修改COD");
                    // 记录COD修改次数
                    orderModel.getExtendProps().put(OrderConstants.COD_MODIFY_TIMES, "1");
                } else {
                    //
                    Integer codModifyTimesInt = Integer.valueOf(codModifyTimes);
                    codModifyTimesInt++;
                    LOGGER.info("月结订单，第[{}]次修改COD", codModifyTimesInt);
                    // 记录COD修改次数
                    orderModel.getExtendProps().put(OrderConstants.COD_MODIFY_TIMES, String.valueOf(codModifyTimesInt));
                    if (codModifyTimesInt >= OrderConstants.COD_MAX_MODIFY_TIMES) {
                        LOGGER.info("月结订单，第[{}]次修改COD，更新标位", codModifyTimesInt);
                        //更新标位
                        modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.COD.getPosition(), ModifyMarkEnum.COD.getSign());
                    }
                }
            }

        } else {
            LOGGER.info("非月结订单，不记录COD修改次数，只更新modifyMark");
            //更新标位
            modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.COD.getPosition(), ModifyMarkEnum.COD.getSign());
        }

        return modifyMark;
    }

    private String mainProductChargeResolve(Product codProduct, Product originCod, String channelNo, String orderNo, String accountNo,
                                            boolean isAfterPickedUp, String originCODSign, String originConsigneeSign, String modifyMark, boolean isSupplyChainDelivery){
        String mark = modifyMark;
        BigDecimal targetShouldPayMoney = new BigDecimal(codProduct.getProductAttrs().get(AddOnProductAttrEnum.COD.getCode()));
        BigDecimal originShouldPayMoney = new BigDecimal(originCod.getProductAttrs().get(AddOnProductAttrEnum.COD.getCode()));
        if (targetShouldPayMoney.compareTo(originShouldPayMoney) != 0){
            //销售平台为京东平台
            if (isSupplyChainDelivery) {
                LOGGER.info("isSupplyChainDelivery=true，支持修改代收货款金额");
            } else {
                if (BusinessConstants.JD_CHANNEL_NO.equals(channelNo)) {
                    LOGGER.info("京东平台的订单不支持修改代收货款金额,orderNo:{}", orderNo);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRODUCT_VALIDATE_FAIL).withCustom("京东平台的订单不支持修改代收货款金额");
                }
            }
            //获取商家配置信息
            BasicTraderResponse basicTraderResponse = customerConfigFacade.getCustomerConfig(accountNo);
            //商家类型为纯外单
            if (TRADERMOLD_CWD == basicTraderResponse.getTraderMold()) {
                if(targetShouldPayMoney.compareTo(BigDecimal.ZERO) < 0){
                    LOGGER.info("商家类型为纯外单时,修改代收货款要大于等于0,orderNo:{}", orderNo);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("商家类型为纯外单时,修改代收货款要大于等于0");
                }
            }else{
                if(targetShouldPayMoney.compareTo(BigDecimal.ZERO) <= 0){
                    LOGGER.info("商家类型非纯外单时,修改代收货款要大于0,orderNo:{}", orderNo);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("商家类型非纯外单时,修改代收货款要大于0");
                }
            }
            //揽收后的校验规则
            if(isAfterPickedUp){
                //是否修改过
                if (ModifyMarkEnum.COD.getSign().equals(originCODSign)) {
                    LOGGER.info("代收货款信息已经修改，不支持再次修改代收货款,orderNo:{}", orderNo);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.MODIFY_VALIDATE_FAIL_COD)
                            .withCustom("代收货款信息已经修改，不支持再次修改代收货款");
                }
                //当代收货款金额发生变化时（增加代收货款比对开关，后续可能去掉）
                if (expressUccConfigCenter.isModifyCodSwitch() && targetShouldPayMoney.compareTo(originShouldPayMoney) > 0) {
                    //不能从小改大
                    LOGGER.info("修改后代收货款金额大于修改前代收货款金额,orderNo:{}", orderNo);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("修改后代收货款金额大于修改前代收货款金额，不支持修改");
                }
                //更新标位
                mark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.COD.getPosition(), ModifyMarkEnum.COD.getSign());
            }
        }
        return mark;
    }

    private String mainProductChargeResolveV2(ExpressOrderModel orderModel, Product codProduct, Product originCod,
                                              String orderNo, String originCODSign, String modifyMark, boolean isSupplyChainDelivery) {

        String channelNo = orderModel.getOrderSnapshot().getChannel().getChannelNo();
        String accountNo = orderModel.getCustomer().getAccountNo();
        boolean isAfterPickedUp = orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp();

        String mark = modifyMark;
        BigDecimal targetShouldPayMoney = new BigDecimal(codProduct.getProductAttrs().get(AddOnProductAttrEnum.COD.getCode()));
        BigDecimal originShouldPayMoney = new BigDecimal(originCod.getProductAttrs().get(AddOnProductAttrEnum.COD.getCode()));
        if (targetShouldPayMoney.compareTo(originShouldPayMoney) != 0) {
            //销售平台为京东平台
            if (isSupplyChainDelivery) {
                LOGGER.info("isSupplyChainDelivery=true，支持修改代收货款金额");
            } else {
                if (BusinessConstants.JD_CHANNEL_NO.equals(channelNo)) {
                    LOGGER.info("京东平台的订单不支持修改代收货款金额,orderNo:{}", orderNo);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PRODUCT_VALIDATE_FAIL).withCustom("京东平台的订单不支持修改代收货款金额");
                }
            }
            //获取商家配置信息
            BasicTraderResponse basicTraderResponse = customerConfigFacade.getCustomerConfig(accountNo);
            //商家类型为纯外单
            if (TRADERMOLD_CWD == basicTraderResponse.getTraderMold()) {
                if (targetShouldPayMoney.compareTo(BigDecimal.ZERO) < 0) {
                    LOGGER.info("商家类型为纯外单时,修改代收货款要大于等于0,orderNo:{}", orderNo);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("商家类型为纯外单时,修改代收货款要大于等于0");
                }
            } else {
                if (targetShouldPayMoney.compareTo(BigDecimal.ZERO) <= 0) {
                    LOGGER.info("商家类型非纯外单时,修改代收货款要大于0,orderNo:{}", orderNo);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("商家类型非纯外单时,修改代收货款要大于0");
                }
            }
            //揽收后的校验规则
            if (isAfterPickedUp) {
                //是否修改过
                if (ModifyMarkEnum.COD.getSign().equals(originCODSign)) {
                    LOGGER.info("代收货款信息已经修改，不支持再次修改代收货款,orderNo:{}", orderNo);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.MODIFY_VALIDATE_FAIL_COD)
                            .withCustom("代收货款信息已经修改，不支持再次修改代收货款");
                }
                //当代收货款金额发生变化时（增加代收货款比对开关，后续可能去掉）
                if (expressUccConfigCenter.isModifyCodSwitch() && targetShouldPayMoney.compareTo(originShouldPayMoney) > 0) {
                    //不能从小改大
                    LOGGER.info("修改后代收货款金额大于修改前代收货款金额,orderNo:{}", orderNo);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                            .withCustom("修改后代收货款金额大于修改前代收货款金额，不支持修改");
                }
                //更新标位
                mark = recordCodModifyTimesAndUpdateModifyMark(orderModel, modifyMark);
            }
        }
        return mark;
    }


    /**
     * 校验修改字段是否在白名单中
     * @param changedPropertyDelegate
     * @param orderModel
     */
    private void validateOnlyModifyFields(ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        Map<String, ChangedProperty> changedPropertyMap = changedPropertyDelegate.getChangedPropertyMap();
        if (changedPropertyMap == null) {
            return;
        }
        for (String code: changedPropertyMap.keySet()) {
            // 如果修改内容不在白名单中则不允许修改
            if (!expressUccConfigCenter.isInExpressHKMModifyWhite(code)) {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("在策略为仅修改报关数据时 ");
                if (null != changedPropertyMap.get(code)) {
                    stringBuilder.append(changedPropertyMap.get(code).getPropertyDesc());
                }
                stringBuilder.append(" 修改内容不在白名单中");
                LOGGER.info("{}，orderNo:{}", stringBuilder.toString(), orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(stringBuilder.toString());
            }
        }
    }

    /**
     * 校验删除字段是否在白名单中
     * @param orderModel
     */
    private void validateOnlyModifyClearFields(ExpressOrderModel orderModel) {
        List<String> clearFields = orderModel.getClearFields();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(clearFields)) {
            return;
        }
        for (String code: clearFields) {
            // 如果删除字段不在白名单中则不允许修改
            if (!expressUccConfigCenter.isInExpressHKMModifyWhite(code)) {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append("在策略为仅修改报关数据时 ");
                stringBuilder.append(code);
                stringBuilder.append(" 修改内容不在白名单中");
                LOGGER.info("{}，orderNo:{}", stringBuilder.toString(), orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(stringBuilder.toString());
            }
        }
    }

    /**
     * 仅修改报关数据策略
     * 允许多次修改收件人证件信息
     */
    private boolean modifyIDInfoWhite(ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel){
        return orderModel.isOnlyModifyCustoms() && changedPropertyDelegate.onlyConsigneeIDInfoHaveChange();
    }

    /**
     * 校验 内部修改
     *
     * @param modifySceneRule
     * @param orderModel
     * @param changedPropertyDelegate
     */
    private void validateInternalModify(String modifySceneRule, ExpressOrderModel orderModel, ChangedPropertyDelegate changedPropertyDelegate) {

        // 目前 仅校验 修改/清除收发货人信息
        if (ModifySceneRuleConstants.INTERNAL_MODIFY_CONSIGNOR_AND_CONSIGNEE_INFO.equals(modifySceneRule)) {
            if (!changedPropertyDelegate.onlyConsignorAndConsigneeInfoChanged()) {
                LOGGER.error("修改策略为内部修改，其他信息不允许修改，orderNo:{}", orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为内部修改，其他信息不允许修改");
            }
        }
        //只允许修改逆向单
        if (ModifySceneRuleConstants.INTERNAL_MODIFY_RETURN_ORDER_INFO.equals(modifySceneRule)) {
            if (!orderModel.getOrderSnapshot().isReverseOrder()) {
                LOGGER.error("修改策略为internalModifyReturnOrderInfo，只允许修改逆向单");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为internalModifyReturnOrderInfo，只允许修改逆向单");
            }
        }

    }

    /**
     * 校验 仅修改多地址审核状态
     */
    private void validateOnlyModifyMultiAddressVerifyStatus(ExpressOrderModel orderModel, ChangedPropertyDelegate changedPropertyDelegate) {

        // 不允许其他信息修改
        if (!changedPropertyDelegate.onlyMultiAddressVerifyStatusChanged()) {
            LOGGER.info("修改策略为仅修改多地址审核状态，其他信息不允许修改，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为仅修改多地址审核状态，其他信息不允许修改");
        }

    }

    /**
     * 校验仅修改收件人联系方式
     */
    private void validateOnlyModifyConsigneeContactInformation(String modifySceneRule, ExpressOrderModel orderModel, ChangedPropertyDelegate changedPropertyDelegate) {
        // 修改黑名单
        modifyBlacklistModifyContactInformation(modifySceneRule, changedPropertyDelegate, orderModel);

        // 不允许未修改
        if (!changedPropertyDelegate.consigneeContactInformationHaveChange()) {
            LOGGER.info("修改策略为仅修改收件人联系方式，收件人姓名电话手机未发生变化，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为仅修改收件人联系方式，收件人姓名电话手机未发生变化");
        }

        // 不允许修改收件人其他信息
        if (changedPropertyDelegate.consigneeHaveChangeIgnoreContactInformation()) {
            LOGGER.info("修改策略为仅修改收件人联系方式，仅允许修改姓名电话手机，不允许修改其他收件信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为仅修改收件人联系方式，仅允许修改姓名电话手机，不允许修改其他收件信息");
        }
    }

    /**
     * 修改黑名单：校验仅修改收件人联系方式
     */
    private void modifyBlacklistModifyContactInformation(String modifySceneRule, ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        String modifySceneRuleDesc = null;
        if (ModifySceneRuleUtil.isOnlyModifyConsigneeContactInformation(modifySceneRule)) {
            modifySceneRuleDesc = "仅修改收件人联系方式";
        } else {
            modifySceneRuleDesc = "修改联系方式";
        }

        // 仅修改收件人联系方式，不允许修改发货信息
        if (ModifySceneRuleUtil.isOnlyModifyConsigneeContactInformation(modifySceneRule)) {
            if (changedPropertyDelegate.consignorHaveChange()) {
                LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改发货信息，orderNo:{}", orderModel.orderNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改发货信息");
            }
        }
        if (changedPropertyDelegate.cargoHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改货品信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改货品信息");
        }
        if (changedPropertyDelegate.goodsHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改商品信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改商品信息");
        }
        if (changedPropertyDelegate.financeHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改交易费用信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改交易费用信息");
        }
        /*if (changedPropertyDelegate.shipmentHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改配送信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改配送信息");
        }*/
        if (changedPropertyDelegate.promotionHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改营销信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改营销信息");
        }
        if (changedPropertyDelegate.agreementInfosHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改协议信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改协议信息");
        }
        if (changedPropertyDelegate.productHaveChange()) {
            LOGGER.info("修改策略为"+ modifySceneRuleDesc +"，不允许修改产品信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为"+ modifySceneRuleDesc +"，不允许修改产品信息");
        }
    }

    /**
     * 修改联系方式（姓名、电话、手机）
     */
    private void validateModifyContactInformation(String modifySceneRule, ChangedPropertyDelegate changedPropertyDelegate, ExpressOrderModel orderModel) {
        // 修改黑名单
        modifyBlacklistModifyContactInformation(modifySceneRule, changedPropertyDelegate, orderModel);

        // 发件人、收件人姓名电话手机不允许未修改
        if (!changedPropertyDelegate.consignorContactInformationHaveChange() &&
                !changedPropertyDelegate.consigneeContactInformationHaveChange()) {
            LOGGER.info("修改策略为修改联系方式，发件人、收件人姓名电话手机未发生变化，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为修改联系方式，发件人、收件人姓名电话手机未发生变化");
        }

        // 不允许修改发件人、收件人其他信息
        if (changedPropertyDelegate.consignorHaveChangeIgnoreContactInformation()
                || changedPropertyDelegate.consigneeHaveChangeIgnoreContactInformation()) {
            LOGGER.info("修改策略为修改联系方式，仅允许修改发件人、收件人姓名电话手机，不允许修改其他发货信息、收货信息，orderNo:{}", orderModel.orderNo());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("修改策略为修改联系方式，仅允许修改发件人、收件人姓名电话手机，不允许修改其他发货信息、收货信息");
        }
    }

    /**
     * 仓出库发货
     */
    private void validateOutboundDelivery(ExpressOrderModel orderModel) {
        // 目前无卡控
        LOGGER.info("仓出库发货，修改白名单无特殊校验");
    }

    /**
     * 仅修改补签标识
     */
    private void validateModifyReSignFlag(ChangedPropertyDelegate changedPropertyDelegate) {
        // 未修改不校验
        if (changedPropertyDelegate == null
                || changedPropertyDelegate.getChangedProperties() == null
                || changedPropertyDelegate.getChangedProperties().isEmpty()) {
            return;
        }
        if (changedPropertyDelegate.getChangedProperties().size() != 1
                || !changedPropertyDelegate.propertyHasChange(ModifyItemConfigEnum.RE_SIGN_FLAG)) {
            LOGGER.error("修改策略为仅修改补签标识，只能修改是否发起补签(reSignFlag)");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).
                    withCustom("修改策略为仅修改补签标识，只能修改是否发起补签(reSignFlag)");
        }
    }

    /**
     * 揽收后修改，校验有修改的产品能否新增或者删除
     */
    private void validateProductInsertOrDelete(ExpressOrderModel orderModel, List<ModifyProduct> modifyProductList) {
        if (CollectionUtils.isEmpty(modifyProductList)) {
            return;
        }

        Set<String> insertBlacklist = new HashSet<>();
        Set<String> deleteBlacklist = new HashSet<>();

        // 拒收一单到底
        if (orderModel.isRejectionOrder()) {
            // 代收货款：不能新增
            insertBlacklist.addAll(AddOnProductEnum.getCodCode());
            // 国补激活校验：不能删除
            deleteBlacklist.add(AddOnProductEnum.ACTIVATION_CHECK.getCode());
        }

        // 自行联系不能从有到无
        insertBlacklist.add(AddOnProductEnum.EXPRESS_CONTACT_DIRECTLY.getCode());

        if (insertBlacklist.isEmpty() && deleteBlacklist.isEmpty()) {
            return;
        }

        for (ModifyProduct modifyProduct : modifyProductList) {
            if (insertBlacklist.contains(modifyProduct.getProductNo())
                    && OperateTypeEnum.INSERT == modifyProduct.getOperateType()) {
                LOGGER.error("{}增值产品揽收后不能新增", modifyProduct.getProductNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom(modifyProduct.getProductNo() + "增值产品揽收后不能新增");
            }
            if (deleteBlacklist.contains(modifyProduct.getProductNo())
                    && OperateTypeEnum.DELETE == modifyProduct.getOperateType()) {
                LOGGER.error("{}增值产品揽收后不能删除", modifyProduct.getProductNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom(modifyProduct.getProductNo() + "增值产品揽收后不能删除");
            }
        }
    }

    /**
     * 校验必须删除的产品是否删除
     */
    private void validateProductMustDelete(ExpressOrderModel orderModel, ExpressOrderModel orderSnapshot) {
        // 拒收一单到底，如果原单有代收货款，拒收一单修改必须删除（validateProductInsertOrDelete已卡控不能新增）
        if (orderModel.isRejectionOrder()
                && orderSnapshot != null
                && orderSnapshot.getProductDelegate() != null
                && orderSnapshot.getProductDelegate().getCodProduct() != null) {
            Product codProductModify = orderModel.getProductDelegate() != null ? orderModel.getProductDelegate().getCodProduct() : null;
            if (codProductModify == null || OperateTypeEnum.DELETE != codProductModify.getOperateType()) {
                String forbidDesc = "拒收一单到底，不允许存在代收货款，必须删除已有的增值服务" +  orderSnapshot.getProductDelegate().getCodProduct().getProductNo();
                LOGGER.error(forbidDesc);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom(forbidDesc);
            }
        }
    }
    /**
     * 校验国际b2c修改
     */
    private void validateIntlB2CModify(ExpressOrderModel orderModel, ExpressOrderModel snapshot, ChangedPropertyDelegate changedPropertyDelegate) {
        SystemCallerEnum systemCallerEnum = orderModel.getChannel().getSystemCaller();
        if (snapshot.getOrderStatus().isAfterPickedUp()) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("揽收后不允许修改");
        }

        if (!SystemCallerEnum.PDA.equals(systemCallerEnum)) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("揽收前只允许终端修改");
        }

        if (changedPropertyDelegate.getChangedProperties().size() > 1 || !changedPropertyDelegate.productHaveChange()) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("揽收前只允许终端修改产品");
        }

        List<String> modifyProductCodeList = new ArrayList<>();
        for (ChangedProperty changedProperty : changedPropertyDelegate.getChangedProperties()) {
            if (!ModifyItemConfigEnum.PRODUCT_NO.getField().equals(changedProperty.getProperty())) {
                continue;
            }
            if (StringUtils.isNotBlank(changedProperty.getModifyValue())) {
                modifyProductCodeList.add(changedProperty.getModifyValue());
            } else {
                modifyProductCodeList.add(changedProperty.getOriginValue());
            }
        }

        if (modifyProductCodeList.size() != 1) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("揽收前只允许终端修改包装增值服务");
        }

        // 检查修改的属性是否为产品编号或包装服务
        String packagingServicesIntlCode = AddOnProductEnum.PACKAGING_SERVICES_INTL.getCode();
        if (!packagingServicesIntlCode.equals(modifyProductCodeList.get(0))) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("揽收前只允许终端修改包装增值服务isc-a-0031");
        }
    }

    /**
     * 判断是否港澳改址修改请求
     * 港澳同城改址以上游传改址产品为准
     * 快照是港澳订单 并且 当前单有改址增值服务
     */
    private boolean isHKMOReaddress(ExpressOrderModel orderModel) {
        return orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().isHKMO()
                && orderModel.getProductDelegate() != null
                && orderModel.getProductDelegate().hasReaddressProduct();
    }
}