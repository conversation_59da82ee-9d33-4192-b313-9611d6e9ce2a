package cn.jdl.oms.express.worker.scheduler.coupon;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.PromotionInfo;
import cn.jdl.oms.core.model.TicketInfo;
import cn.jdl.oms.express.domain.infrs.acl.facade.coupon.CouponFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.issue.ModifyIssueFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.ModifyOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.coupon.CouponInfoRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.coupon.CouponInfoResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.coupon.CouponUseRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.BusinessIdentityFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.PromotionFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.TicketFacade;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.CouponReleaseMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ModifyIssueMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ModifyRepositoryMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import cn.jdl.oms.express.shared.common.constant.FlowConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.CouponStatusEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldValueEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.SystemSubCallerEnum;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.worker.scheduler.AbstractSchedulerHandler;
import com.jd.paq.core.Message;
import com.jd.paq.plugin.IMessage;
import com.jd.paq.plugin.PDQClientException;
import com.jd.paq.plugin.Result;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 优惠券释放
 */
public class CouponReleaseHandler extends AbstractSchedulerHandler {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(CouponReleaseHandler.class);

    @Resource
    private CouponFacade couponFacade;

    @Resource
    private ModifyOrderFacade modifyOrderFacade;

    /**
     * 任务调度
     */
    @Resource
    private SchedulerService schedulerService;

    /**
     * 修改下发服务
     */
    @Resource
    private ModifyIssueFacade modifyIssueFacade;

    /**
     * 订单详情查询
     */
    @Resource
    private GetOrderFacade getOrderFacade;

    /**
     * 重试最大次数
     */
    private final static int MAX_RETRY_TIMES = 10;

    /**
     * @param iMessage 调度任务数据（业务数据、任务主题Topic）
     * @Description 单任务处理：资源释放
     * <AUTHOR>
     * @createDate 2021/3/20 6:54 下午
     */
    @Override
    public Result execute(IMessage iMessage) throws PDQClientException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_WORKER_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        //任务调度执行结果对象根据实际的执行结果定义返回具体的业务结果
        Result result = new Result(Result.SUCCESS);
        result.setCode(1);
        try {
            // 设置消息体为重试消息
            if (iMessage instanceof Message) {
                int retryTime = ((Message) iMessage).getRedriveCount();
                if (retryTime >= MAX_RETRY_TIMES) {
                    LOGGER.info("任务调度【" + iMessage.getTopic() + "】,重试次数超过" + MAX_RETRY_TIMES + "次,暂停重试");
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }
                String iMessageContent = iMessage.getMessageBody();
                PDQTopicEnum pdqTopicEnum = PDQTopicEnum.getByTopic(iMessage.getTopic());
                if (null == pdqTopicEnum) {
                    LOGGER.info("任务调度【" + iMessage.getTopic() + "】,未匹配到任务队列,暂停重试");
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                LOGGER.info("开始执行优惠券释放任务调度topic:{},消息体:{}",pdqTopicEnum,iMessageContent);
                // 重试消息体
                SchedulerMessage schedulerMessage = JSONUtils.jsonToBean(
                        iMessageContent, SchedulerMessage.class);
                if (null == schedulerMessage) {
                    LOGGER.info("任务调度【" + iMessage.getTopic() + "】,场景业务数据对象不存在,暂停重试");
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                CouponReleaseMessageDto messageDto = (CouponReleaseMessageDto) JSONUtils.jsonToBean(schedulerMessage.getDtoJson(), schedulerMessage.getDtoClass());
                if (messageDto == null) {
                    LOGGER.info("任务调度【" + iMessage.getTopic() + "】,场景业务数据对象不存在,暂停重试");
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }

                //查询订单详情
                GetOrderFacadeResponse getOrderFacadeResponse = getOrderFacade.getOrder(convertRequestProfile(messageDto.getRequestProfile().getTraceId()), convertGetOrderRequest(messageDto.getOrderNo()));

                CouponUseRequest request = new CouponUseRequest();
                request.setBusinessUnit(messageDto.getBusinessIdentity().getBusinessUnit());
                request.setTraceId(messageDto.getRequestProfile().getTraceId());
                request.setCouponNo(messageDto.getCouponNo());
                request.setOrderCreatorNo(messageDto.getOrderCreatorNo());
                request.setOrderCreateTime(messageDto.getOrderCreateTime());
                request.setUseAmount(messageDto.getUseAmount());
                request.setWaybillNo(messageDto.getWaybillNo());
                request.setUse(false);
                request.setCouponExecuteType(messageDto.getCouponExecuteType());
                //优惠券来源
                request.setCouponSource(messageDto.getCouponSource());
                //平台订单号
                request.setPlatformOrderNo(messageDto.getPlatformOrderNo());
                //流向
                request.setStartFlowDirection(messageDto.getStartFlowDirection());
                request.setEndFlowDirection(messageDto.getEndFlowDirection());
                //寄件人手机号
                if(StringUtils.isNotBlank(messageDto.getFromUserTel())){
                    request.setFromUserTel(messageDto.getFromUserTel());
                }
                //收件人手机号
                if(StringUtils.isNotBlank(messageDto.getToUserTel())){
                    request.setToUserTel(messageDto.getToUserTel());
                }
                //查询并设置优惠券子类型
                CouponInfoResponse couponInfoResponse = queryCouponInfo(request);
                request.setCouponSubStyle(couponInfoResponse.getCouponSubStyle());
                boolean rest = couponFacade.useCoupon(request);
                if (!rest) {
                    LOGGER.info("任务调度【" + iMessage.getTopic() + "】,释放优惠券失败,暂停重试");
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                //修改、取消释放时需要修改数据库中优惠券状态，标为删除
                if(messageDto.isModifySomeData()){
                    ModifyOrderFacadeRequest facadeRequest = this.toModifyOrderFacadeRequest(messageDto);
                    try{
                        modifyOrderFacade.modifyOrder(messageDto.getRequestProfile(),facadeRequest);
                        LOGGER.info("修改数据库状态成功");
                    }catch (Exception e){
                        SchedulerMessage scheduler = new SchedulerMessage();
                        //持久化消息
                        ModifyRepositoryMessageDto modifyRepositoryMessageDto = new ModifyRepositoryMessageDto();
                        modifyRepositoryMessageDto.setModifyOrderFacadeRequest(facadeRequest);
                        modifyRepositoryMessageDto.setRequestProfile(messageDto.getRequestProfile());
                        modifyRepositoryMessageDto.setBusinessIdentity(messageDto.getBusinessIdentity());
                        scheduler.setDtoJson(JSONUtils.beanToJSONDefault(modifyRepositoryMessageDto));
                        scheduler.setDtoClass(ModifyRepositoryMessageDto.class);
                        schedulerService.addSchedulerTask(PDQTopicEnum.MODIFY_REPOSITORY_RETRY, scheduler,
                                FlowConstants.EXPRESS_ORDER_COUPON_FLOW_CODE);
                        LOGGER.error("修改数据库状态失败，发pdq重试");
                    }
                }

                if (messageDto.isReleaseTicketPending()) {
                    LOGGER.info("回传异步释放优惠券，下发OFC messageDto:{}", JSONUtils.beanToJSONDefault(messageDto));
                    issueOfcModifyCoupon(messageDto, getOrderFacadeResponse);
                }
            }
        } catch (Exception e) {
            result.setCode(Result.SYSTEMERROR);
            LOGGER.error("优惠券释放任务调度执行异常,再次重试", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        return result;

    }

    /**
     * 修改下发OFC入参转换并调用
     *
     * @param messageDto
     * @return
     */
    private void issueOfcModifyCoupon(CouponReleaseMessageDto messageDto, GetOrderFacadeResponse getOrderFacadeResponse) {
        //下发OFC优惠券入参
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = new ModifyIssueFacadeRequest();
        List<TicketInfo> ticketInfoList = new ArrayList<>();

        TicketInfo ticketInfo = new TicketInfo();
        ticketInfo.setTicketNo(messageDto.getCouponNo());
        Map<String, String> extendProps = new HashMap<>();
        extendProps.put("operateType", OperateTypeEnum.DELETE.getCode().toString());
        ticketInfo.setExtendProps(extendProps);
        ticketInfoList.add(ticketInfo);

        PromotionInfo promotionInfo = new PromotionInfo();
        promotionInfo.setTicketInfos(ticketInfoList);
        modifyIssueFacadeRequest.setPromotionInfo(promotionInfo);

        modifyIssueFacadeRequest.setRequestProfile(messageDto.getRequestProfile());
        //渠道信息
        ChannelInfo channelInfo = new ChannelInfo();
        channelInfo.setChannelOperateTime(new Date());
        channelInfo.setSystemCaller(SystemCallerEnum.EXPRESS_OMS.getCode());
        channelInfo.setSystemSubCaller(SystemSubCallerEnum.JDL_OMS.getCode());
        modifyIssueFacadeRequest.setChannelInfo(channelInfo);
        //操作人
        modifyIssueFacadeRequest.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        modifyIssueFacadeRequest.setOrderNo(messageDto.getOrderNo());
        modifyIssueFacadeRequest.setCustomOrderNo(getOrderFacadeResponse.getCustomOrderNo());

        Map<String, String> modifiedFields = new HashMap<>();
        modifiedFields.put(ModifiedFieldEnum.TICKET_INFOS.getCode(), ModifiedFieldValueEnum.ALL_DELETE.getCode());

        modifyIssueFacadeRequest.setModifiedFields(modifiedFields);
        //业务身份
        BusinessIdentity businessIdentity = generateBusinessIdentity(getOrderFacadeResponse.getBusinessIdentity(), messageDto);
        modifyIssueFacadeRequest.setBusinessIdentity(businessIdentity);

        try{
            modifyIssueFacade.modifyIssue(modifyIssueFacadeRequest, messageDto.getBusinessIdentity());
        } catch (Exception e) {
            LOGGER.error("CouponReleaseHandler修改下发异常,执行PDQ重试", e);
            SchedulerMessage schedulerMessage = new SchedulerMessage();
            ModifyIssueMessageDto modifyIssueMessageDto = new ModifyIssueMessageDto();
            modifyIssueMessageDto.setBusinessIdentity(convertBusinessIdentity(getOrderFacadeResponse.getBusinessIdentity()));
            modifyIssueMessageDto.setOrderNo(messageDto.getOrderNo());
            modifyIssueMessageDto.setCustomOrderNo(getOrderFacadeResponse.getCustomOrderNo());
            modifyIssueMessageDto.setRequestProfile(messageDto.getRequestProfile());
            modifyIssueMessageDto.setModifyIssueFacadeRequest(modifyIssueFacadeRequest);

            schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(modifyIssueMessageDto));
            schedulerMessage.setDtoClass(ModifyIssueMessageDto.class);
            schedulerService.addSchedulerTask(PDQTopicEnum.MODIFY_ISSUE_RETRY, schedulerMessage,
                    FlowConstants.EXPRESS_ORDER_MODIFY_ISSUE_FLOW_CODE);
        }
        LOGGER.info("CouponReleaseHandler下发OFC修改优惠券完成");
    }

    private OrderBusinessIdentity convertBusinessIdentity(BusinessIdentityFacade businessIdentity) {
        OrderBusinessIdentity orderBusinessIdentity = new OrderBusinessIdentity();
        orderBusinessIdentity.setBusinessUnit(businessIdentity.getBusinessUnit());
        orderBusinessIdentity.setBusinessType(businessIdentity.getBusinessType());
        orderBusinessIdentity.setBusinessScene(businessIdentity.getBusinessScene());
        orderBusinessIdentity.setBusinessStrategy(businessIdentity.getBusinessStrategy());
        orderBusinessIdentity.setFulfillmentUnit(businessIdentity.getFulfillmentUnit());
        return orderBusinessIdentity;
    }

    private BusinessIdentity generateBusinessIdentity(BusinessIdentityFacade businessIdentityFacade, CouponReleaseMessageDto messageDto) {
        BusinessIdentity businessIdentity = new BusinessIdentity();
        businessIdentity.setBusinessUnit(businessIdentityFacade.getBusinessUnit());
        businessIdentity.setBusinessType(businessIdentityFacade.getBusinessType());
        businessIdentity.setBusinessScene(businessIdentityFacade.getBusinessScene());
        businessIdentity.setFulfillmentUnit(businessIdentityFacade.getFulfillmentUnit());
        businessIdentity.setBusinessStrategy(businessIdentityFacade.getBusinessStrategy());

        OrderBusinessIdentity orderBusinessIdentity = new OrderBusinessIdentity();
        orderBusinessIdentity.setBusinessUnit(businessIdentityFacade.getBusinessUnit());
        orderBusinessIdentity.setBusinessType(businessIdentityFacade.getBusinessType());
        orderBusinessIdentity.setBusinessScene(businessIdentityFacade.getBusinessScene());
        orderBusinessIdentity.setFulfillmentUnit(businessIdentityFacade.getFulfillmentUnit());
        orderBusinessIdentity.setBusinessStrategy(businessIdentityFacade.getBusinessStrategy());

        messageDto.setBusinessIdentity(orderBusinessIdentity);
        return businessIdentity;
    }

    private ModifyOrderFacadeRequest toModifyOrderFacadeRequest(CouponReleaseMessageDto messageDto){
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        Map<String, String> modifiedFields = new HashMap<>();
        modifiedFields.put(ModifiedFieldEnum.TICKET_INFOS.getCode(), ModifiedFieldValueEnum.INCREMENT_UPDATE.getCode());
        request.setOrderNo(messageDto.getOrderNo());
        PromotionFacade promotion = new PromotionFacade();
        List<TicketFacade> tickets = new ArrayList<>();
        TicketFacade ticketFacade = new TicketFacade();
        ticketFacade.setTicketNo(messageDto.getCouponNo());
        ticketFacade.setCouponStatus(CouponStatusEnum.ALLOW_ROLLBACK_YES.getCode());
        ticketFacade.setOperateType(OperateTypeEnum.DELETE);
        tickets.add(ticketFacade);
        promotion.setTickets(tickets);
        request.setPromotion(promotion);
        return request;
    }

    /**
     * 查询优惠券
     */
    private CouponInfoResponse queryCouponInfo(CouponUseRequest couponUseRequest) {
        CouponInfoRequest request = new CouponInfoRequest();
        request.setTraceId(couponUseRequest.getTraceId());
        request.setCouponNo(couponUseRequest.getCouponNo());
        request.setOrderCreatorNo(couponUseRequest.getOrderCreatorNo());
        request.setCouponSource(couponUseRequest.getCouponSource());
        request.setPlatformOrderNo(couponUseRequest.getPlatformOrderNo());
        //流向
        request.setStartFlowDirection(couponUseRequest.getStartFlowDirection());
        request.setEndFlowDirection(couponUseRequest.getEndFlowDirection());
        if(StringUtils.isNotBlank(couponUseRequest.getFromUserTel())){
            request.setFromUserTel(couponUseRequest.getFromUserTel());
        }
        if(StringUtils.isNotBlank(couponUseRequest.getToUserTel())){
            request.setToUserTel(couponUseRequest.getToUserTel());
        }
        return couponFacade.queryInfo(request);
    }

    private RequestProfile convertRequestProfile(String traceId) {
        RequestProfile requestProfile = new RequestProfile();
        requestProfile.setExt(null);
        requestProfile.setLocale("zh_CN");
        requestProfile.setTenantId("1000");
        requestProfile.setTimeZone("GMT+8");
        requestProfile.setTraceId(traceId);
        return requestProfile;
    }

    private GetOrderFacadeRequest convertGetOrderRequest(String orderNo) {
        GetOrderFacadeRequest request = new GetOrderFacadeRequest();
        request.setOrderNo(orderNo);
        return request;
    }
}
