package cn.jdl.oms.express.worker.message.handler;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.dto.BusinessIdentityDto;
import cn.jdl.oms.express.domain.dto.CostInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.ProductInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.flow.compare.CompareFlowNode;
import cn.jdl.oms.express.domain.flow.order.OrderFieldDataNotifyFlowNode;
import cn.jdl.oms.express.domain.flow.product.ProductMappingFlowNode;
import cn.jdl.oms.express.domain.flow.repository.OrderRepositoryFlowNode;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderNoApiFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.waybill.FreightWayBillQueryFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.waybill.WayBillQueryFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderExistsRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderModelCreatorTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CustomsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ProductFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.repository.OrderDataNotifyTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.waybill.WayBillQueryRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.waybill.WayBillQueryResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.waybill.WaybillByChoiceResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.waybill.WaybillChoiceProductFeeDetail;
import cn.jdl.oms.express.domain.infrs.acl.util.FinanceUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedBusinessIdentityUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.es.orderflow.ExpressOrderFlowService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.entity.CommonDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.handler.ExpressAbstractHandler;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderDataFlowDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.waybill.MarkUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.waybill.WaybillInfoDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.waybill.WaybillInfoMappingUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisClient;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisLockFactory;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.lock.LockEntry;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductAttrEnum;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AdministrativeRegionEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.spec.dict.WarmLayerEnum;
import cn.jdl.oms.express.domain.utils.ContextInfoUtil;
import cn.jdl.oms.express.domain.utils.OrderDataFieldEnum;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.MagicCommonConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.BusinessTypeEnum;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldValueEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyMarkEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AntiConcurrentException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.JMQRetryException;
import cn.jdl.oms.express.shared.common.mdc.MDCTraceConstants;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.shared.common.utils.ModifyMarkUtil;
import cn.jdl.oms.express.worker.message.util.DataSyncAntiConcurrentUtil;
import com.jd.ldop.center.api.waybill.dto.ExtendMessageDTO;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

/**
 * @ProjectName：jdl-oms-express-horz-infrastructure
 * @Package： cn.jdl.oms.express.horz.infrs.ohs.locals.message.handler
 * @ClassName: WaybillInfoUpdateHandler
 * @Description: 运单信息更新消息处理类
 * https://joyspace.jd.com/pages/UfRBhiJOzRIXvzCtnHX9
 * 基本主要步骤逻辑
 * 1.根据外单号寻找订单详情数据
 * 2.根据外单号和订单详情相关数据获取外单详情数据
 * 3.把外单详情对象Model转化成订单Model
 * 4.对比订转化订单Model和之前获取的订单详情数据
 * 5.存储对比之后的订单数据
 * @Author： yangxiangrong5
 * @CreateDate 2021/4/12 20:03
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
public class WaybillInfoUpdateHandler extends ExpressAbstractHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(WaybillInfoUpdateHandler.class);

    /**
     * 订单详情查询
     */
    @Resource
    private GetOrderFacade getOrderFacade;

    /**
     * 运单详情查询
     */
    @Resource
    private WayBillQueryFacade wayBillQueryFacade;

    /**
     * 快运运单详情查询
     */
    @Resource
    private FreightWayBillQueryFacade freightWayBillQueryFacade;

    /**
     * 对比flow节点
     */
    @Resource
    private CompareFlowNode compareFlowNode;

    /**
     * 持久化flow接单
     */
    @Resource
    private OrderRepositoryFlowNode orderRepositoryFlowNode;

    @Resource
    private IRedisLockFactory redisLockFactory;

    @Resource
    private IRedisClient redisClient;

    @Resource
    private GetOrderModelCreatorTranslator modelCreatorTranslator;

    /**
     * 判断订单是否存在服务
     */
    @Resource
    private GetOrderNoApiFacade getOrderNoApiFacade;

    //Ucc配置
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    @Resource
    private WaybillInfoMappingUtil waybillInfoMappingUtil;

    @Resource
    private OrderFieldDataNotifyFlowNode orderFieldDataNotifyFlowNode;

    /** 订单数据流水转换器 */
    @Resource
    private OrderDataNotifyTranslator orderDataNotifyTranslator;

    /** 纯配订单jmq服务生产者 */
    @Resource
    private ExpressOrderFlowService expressOrderFlowService;

    /**
     * 部署环境
     */
    @Value("${express.order.run.environment}")
    private String environment;


    @Resource
    private ProductMappingFlowNode productMappingFlowNode;

    @Resource
    private UmpUtil umpUtil;

    /** POP售后数据同步字段 */
    private static final List<OrderDataFieldEnum> POP_FIELDS = Arrays.asList(
            OrderDataFieldEnum.EXPECT_PICKUP_START_TIME,
            OrderDataFieldEnum.EXPECT_PICKUP_END_TIME
    );

    /**
     * 校验并发锁工具
     */
    @Resource
    private DataSyncAntiConcurrentUtil dataSyncAntiConcurrentUtil;

    /**
     * 设置 业务身份修改类型。0-全量修改；1-修改业务身份，2-修改业务类型，3-修改业务策略
     */
    private static final Set<Integer> MODIFY_BUSINESS_IDENTITY_TYPES = Arrays.asList(0).stream().collect(Collectors.toSet());

    @Override
    public boolean handle(CommonDto commonDto) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".handle"
                , UmpKeyConstants.JDL_OMS_WORKER_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        String traceId = "WaybillInfoUpdateHandler" + System.currentTimeMillis();
        WaybillInfoDto waybilldto = (WaybillInfoDto) commonDto;
        LOGGER.info("运单信息变更开始:traceId={},开始运单号={}", traceId, waybilldto.getWaybillCode());
        Lock lock = null;
        GetOrderFacadeResponse getOrderFacadeResponse = null;
        try {
            //运单号非空校验
            if (StringUtils.isBlank(waybilldto.getWaybillCode())) {
                LOGGER.info("运单信息变更消息,运单号为空，不处理");
                return true;
            }
            //修改来源类型:1是订单中心,则直接消费掉
            if (waybilldto.getModifyDataType() != null && 1 == waybilldto.getModifyDataType()) {
                LOGGER.debug("运单信息变更消息:modifyDataType={},不处理", waybilldto.getModifyDataType());
                return true;
            }
            //修改来源数据是否在黑名单中，若是则直接消费掉
            if (waybilldto.getModifyDataSource() != null && expressUccConfigCenter.isModifyDataSyncSourceBlack(String.valueOf(waybilldto.getModifyDataSource()))) {
                LOGGER.debug("运单信息变更消息:modifyDataSource={},不处理", waybilldto.getModifyDataSource());
                return true;
            }
            GetOrderExistsRequest getOrderExistsRequest = new GetOrderExistsRequest();
            getOrderExistsRequest.setCustomOrderNo(waybilldto.getWaybillCode());
            boolean existOrder = getOrderNoApiFacade.existsOrder(buildRequest(traceId), getOrderExistsRequest);
            if (!existOrder) {
                LOGGER.debug("运单信息变更消息:traceId={},waybillCode={},非百川的单子直接过滤", traceId, waybilldto.getWaybillCode());
                return true;
            }
            //获取订单信息数据
            getOrderFacadeResponse = getOrderInfo(traceId, waybilldto.getWaybillCode());
            // 融合业务身份转换成真实业务身份
            UnitedBusinessIdentityUtil.convertOrderToReal(getOrderFacadeResponse, true);
            //订单中心没对应数据直接消费掉
            if (getOrderFacadeResponse == null || getOrderFacadeResponse.getOrderNo() == null || null == getOrderFacadeResponse.getBusinessIdentity()) {
                LOGGER.info("运单信息变更消息:waybillCode={},订单中心没对应数据直接消费掉", waybilldto.getWaybillCode());
                return true;
            }
            // 环境判断
            String orderEnv = getOrderFacadeResponse.getEnvironment();
            if (StringUtils.isNotBlank(orderEnv)) {
                if (!environment.equals(orderEnv)) {
                    LOGGER.info("订单数据与运行环境不一致,环境{}消费到环境{}的订单{}mq，忽略", environment, orderEnv, getOrderFacadeResponse.getOrderNo());
                    return true;
                }
            }

            for (ProductFacade product : getOrderFacadeResponse.getProducts()) {
                if (ProductEnum.JDBS.getCode().equals(product.getProductNo())) {
                    LOGGER.error("京东帮送不同步");
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_JDBS_WB_UPDATE_FAIL, "京东帮送同步消息, tid: " + traceId);
                    return  true;
                }
            }

            LOGGER.debug("获取订单详情信息:waybillCode={},response={}", waybilldto.getWaybillCode(), JSONUtils.beanToJSONDefaultLazy(getOrderFacadeResponse));

            //不是 快递/快运/冷链 直接消费掉
            if (!BusinessUnitEnum.CN_JDL_C2C.getCode().equals(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit())
                    && !BusinessUnitEnum.CN_JDL_B2C.getCode().equals(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit())
                    && !BusinessUnitEnum.CN_JDL_C2B.getCode().equals(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit())
                    && !BusinessUnitEnum.CN_JDL_CC_B2C.getCode().equals(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit())
                    && !BusinessUnitEnum.CN_JDL_FREIGHT_SERVICE.getCode().equals(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit())
                    && !BusinessUnitEnum.CN_JDL_FREIGHT_CONSUMER.getCode().equals(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit())
                    && !BusinessUnitEnum.CN_JDL_CC_B2B.getCode().equals(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit())
            ) {
                LOGGER.info("{}运单信息变更消息:traceId={},waybillCode={},不是C2C|B2C|C2B|快运｜B网营业厅|CCB2B直接消费掉", getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit(), traceId, waybilldto.getWaybillCode());
                return true;
            }

            //整车业务不需要数据同步
            if (BusinessTypeEnum.FTL_DIRECT.getCode().equals(getOrderFacadeResponse.getBusinessIdentity().getBusinessType())) {
                LOGGER.info("整车业务不需要数据同步,以订单数据为准");
                return true;
            }

            if (BusinessTypeEnum.READDRESS_EXPRESS.getCode().equals(getOrderFacadeResponse.getBusinessIdentity().getBusinessType())) {
                LOGGER.info("{}运单信息变更消息: {}直接消费掉", waybilldto.getWaybillCode(), getOrderFacadeResponse.getBusinessIdentity().getBusinessType());
                return true;
            }

            if(isHKMO(getOrderFacadeResponse.getCustomsFacade())){
                LOGGER.info("港澳订单-直接消费掉,traceId:{},waybillCode:{},customs:{},", traceId, waybilldto.getWaybillCode(),JSONUtils.beanToJSONDefault(getOrderFacadeResponse.getCustomsFacade()));
                return true;
            }

            for (ProductFacade product : getOrderFacadeResponse.getProducts()) {
                if (BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.SKIP_WAYBILL_UPDATE_MAIN_PRODUCT_LIST, product.getProductNo())) {
                    LOGGER.info("{}产品-直接消费", product.getProductNo());
                    return true;
                }
            }

            // FIXME liujiangwai1 这个代码走不到吧
            if(BusinessUnitEnum.CN_JDL_INTL_C2C.getCode().equals(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit())) {
                LOGGER.info("国际C2C订单-直接消费掉,traceId:{},waybillCode:{},customs:{},", traceId, waybilldto.getWaybillCode(),JSONUtils.beanToJSONDefault(getOrderFacadeResponse.getCustomsFacade()));
                return true;
            }

            //订单状态已取消了不消费
            if (OrderStatusEnum.CANCELED.getCode().equals(getOrderFacadeResponse.getOrderStatus())) {
                LOGGER.info("{}运单信息变更消息:traceId={},waybillCode={},订单状态为取消直接消费掉", getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit(), traceId, waybilldto.getWaybillCode());
                return true;
            }

            //增加监控打点-是否有终端切百川标识，仍在数据同步
            Map<String, String> channelExt = getOrderFacadeResponse.getChannel().getExtendProps();
            if (MapUtils.isNotEmpty(channelExt)) {
                if (OrderConstants.YES_VAL.equals(channelExt.get(AttachmentKeyEnum.TERMINAL_MODIFY_FLAG.getKey()))) {
                    umpUtil.registerInfoEnd(this.getClass().getName() + ".terminalModifySync");
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_TERMINAL_MODIFY_SYNC_ALARM, "终端修改切百川，仍触发了数据同步监控,运单号=" + waybilldto.getWaybillCode());
                    LOGGER.info("该订单存在终端修改切百川标识，仍触发了数据同步，orderNo={},waybillCode={}", getOrderFacadeResponse.getOrderNo(), waybilldto.getWaybillCode());
                }
            }

            //添加并发锁
            String keyPrefix = getOrderFacadeResponse.getTenantId() +
                    ":" + getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit() +
                    ":" + getOrderFacadeResponse.getBusinessIdentity().getBusinessType();
            LockEntry lockEntry = new LockEntry(waybilldto.getWaybillCode(), 10, TimeUnit.SECONDS);
            lockEntry.withPrefix(keyPrefix);
            lock = redisLockFactory.create(redisClient, lockEntry);
            if (!lock.tryLock()) {
                LOGGER.info("运单信息变更消息:waybillCode={},获取锁失败!", waybilldto.getWaybillCode());
                throw new AntiConcurrentException(UnifiedErrorSpec.BasisOrder.WAYBILL_UPDATE_INFO_FAIL).withCustom("获取锁失败!");
            }
            //是否正在进行状态回传，若是则优先处理回传
            String callBackLockKey = keyPrefix + ":" + BusinessSceneEnum.CALLBACK.getCode() + ":" + getOrderFacadeResponse.getOrderNo();
            if (redisClient.get(callBackLockKey) != null) {
                LOGGER.info("{}运单信息变更消息:traceId={},waybillCode={},正在进行状态回传，优先进行状态回传，暂不处理数据同步!", getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit(), traceId, waybilldto.getWaybillCode());
                throw new AntiConcurrentException(UnifiedErrorSpec.BasisOrder.WAYBILL_UPDATE_INFO_FAIL).withCustom("正在进行状态回传!");
            }

            // 校验各种并发锁，避免持久化锁冲突（需要按业务身份配置）
            dataSyncAntiConcurrentUtil.validate(getOrderFacadeResponse);
            // 将getOrderFacadeResponse转回原始业务身份
            UnitedBusinessIdentityUtil.convertOrderToOriginal(getOrderFacadeResponse);
            UnitedBusinessIdentityUtil.clearOrderOriginalIdentity();
            //请求运单数据组装
            WayBillQueryRequest wayBillQueryFacadeRequest = new WayBillQueryRequest();
            wayBillQueryFacadeRequest.setCustomerCode(getOrderFacadeResponse.getCustomer() != null ? getOrderFacadeResponse.getCustomer().getAccountNo() : null);
            wayBillQueryFacadeRequest.setUserPin(getOrderFacadeResponse.getOperator());
            wayBillQueryFacadeRequest.setDeliveryId(waybilldto.getWaybillCode());
            if (StringUtils.isEmpty(wayBillQueryFacadeRequest.getCustomerCode()) || StringUtils.isEmpty(wayBillQueryFacadeRequest.getUserPin()) || StringUtils.isEmpty(wayBillQueryFacadeRequest.getDeliveryId())) {
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_WORKER_WAYBILL_UPDATE_INFO
                        , System.currentTimeMillis()
                        , getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit() + "运单信息变更链路追踪ID:"
                                + MDC.get(MDCTraceConstants.TRACEID)
                                + ", 运单号:"
                                + wayBillQueryFacadeRequest.getDeliveryId()
                                + ", 当前运单请求数据缺失:"
                                + JSONUtils.beanToJSONDefault(wayBillQueryFacadeRequest));
                LOGGER.error("当前运单请求数据缺失,wayBillQueryFacadeRequest:{}", JSONUtils.beanToJSONDefault(wayBillQueryFacadeRequest));
                return false;
            }
            LOGGER.debug("运单信息变更消息:获取运单详情数据请求参数={}", JSONUtils.beanToJSONDefaultLazy(wayBillQueryFacadeRequest));
            //获取运单数据
            WayBillQueryResponse wayBillQueryResponse = null;
            // 产品互改快运改快递时，按目前逻辑，走订单原身份（快运）查询
            // 融合c2c订单，因为接单可能是快运，也可能是快递，所以需要先按所属人查一次，再按照操作人查一次
            if (isFreight(getOrderFacadeResponse) || isUnitedC2C(getOrderFacadeResponse)) {
                wayBillQueryResponse = freightWayBillQueryFacade.queryWaybillDetail(getOrderFacadeResponse, waybilldto.getWaybillCode());
            } else {
                wayBillQueryResponse = wayBillQueryFacade.queryWaybillDetail(wayBillQueryFacadeRequest);
            }

            if (wayBillQueryResponse != null) {
                LOGGER.debug("运单信息变更消息:wayBillQueryResponse={}", JSONUtils.beanToJSONDefaultLazy(wayBillQueryResponse));
                // 把运单数据model转订单model
                Map<String, ProductFacade> productFacadeMap = new HashMap<>();
                List<ProductFacade> productFacadeList = getOrderFacadeResponse.getProducts();
                if (productFacadeList != null) {
                    for (ProductFacade productFacade : productFacadeList) {
                        if (StringUtils.isNotEmpty(productFacade.getProductNo()) && MapUtils.isNotEmpty(expressUccConfigCenter.getSpecialHandlerAddedProduct()) && expressUccConfigCenter.getSpecialHandlerAddedProduct().containsKey(productFacade.getProductNo())) {
                            productFacadeMap.put(productFacade.getProductNo(), productFacade);
                        }
                    }
                    LOGGER.info("同步运单数据特殊处理增值服务编码:productFacadeMap={}", JSONUtils.beanToJSONDefault(productFacadeMap));
                }
                //把订单详情转订单ExpressOrderModel
                ExpressOrderModelCreator orderDetailModelCreator = modelCreatorTranslator.toExpressOrderModelCreator(getOrderFacadeResponse);
                orderDetailModelCreator.setClearFields(new ArrayList<>());
                // 有了 snapShotModel 之后，不再使用 getOrderFacadeResponse 做逻辑
                ExpressOrderModel snapShotModel = new ExpressOrderModel(orderDetailModelCreator);
                // transferOrder过程中，会有按不同业务身份走不同的逻辑。因此产品互改时要先判断 wayBillQueryResponse 如果是产品互改，将 snapShotModel 的业务身份及类型改为目标业务身份及类型。
                boolean hasReset = resetSnapshotBusinessIdentity(wayBillQueryResponse, snapShotModel);
                // 把snapShot更新为真实业务身份
                OrderBusinessIdentity orderBusinessIdentity = UnitedBusinessIdentityUtil.convertModelToReal(snapShotModel);

                ExpressOrderModelCreator billExpressOrderModelCreator = waybillInfoMappingUtil.transferOrder(wayBillQueryResponse, productFacadeMap, snapShotModel, hasReset);
                if (snapShotModel.isFreight() || UnitedB2CUtil.isUnitedFreightB2C(snapShotModel)) {
                    // 快运订单 删除cargoInfo -- cargo不再做数据同步，运营实操修改同步到履约信息
                    billExpressOrderModelCreator.setCargoInfos(null);
                    billExpressOrderModelCreator.getModifiedFields().remove(ModifiedFieldEnum.CARGO_INFOS.getCode());
                } else {
                    // 其余业务删除履约信息
                    billExpressOrderModelCreator.setFulfillmentInfo(null);
                }
                LOGGER.debug("构建后订单信息:billExpressOrderModelCreator={}", JSONUtils.beanToJSONDefaultLazy(billExpressOrderModelCreator));
                //todo C2B微信小程序商家取件补全向多方收费信息
                if (BusinessUnitEnum.CN_JDL_C2B.getCode().equals(snapShotModel.getOrderBusinessIdentity().getBusinessUnit())) {
                    LOGGER.info("C2B微信小程序商家取件补全向多方收费信息，waybillCode:{}", waybilldto.getWaybillCode());
                    c2bWxPickCostInfo(billExpressOrderModelCreator, waybilldto);
                }
                supplementData(snapShotModel, billExpressOrderModelCreator, waybilldto.getWaybillSign());
                // 再把snapShotModel和billExpressOrderModelCreator更新为原始业务身份
                /**************************start调用订单对比能力*********************************/
                LOGGER.info("运单信息变更消息:组装的运单数据={},订单系统原数据={}",
                        JSONUtils.beanToJSONDefault(billExpressOrderModelCreator),
                        JSONUtils.beanToJSONDefault(getOrderFacadeResponse));

                //把运单数据model转creator->订单ExpressOrderModel
                ExpressOrderModel billDetailOrderModel = new ExpressOrderModel(billExpressOrderModelCreator);
                //把数据库订单数据放入快照参数，跟运单订单详情对比
                billDetailOrderModel.assignSnapshot(snapShotModel);
                //补全原单的增值产品（协商再投、微笑面单、单单保）
                completeProduct(billDetailOrderModel, waybilldto, wayBillQueryResponse);
                billDetailOrderModel.withRequestProfile(buildRequest(traceId));
                // OrderBusinessIdentity放入context
                OrderBusinessIdentity businessIdentity = new OrderBusinessIdentity();
                businessIdentity.setBusinessType(billExpressOrderModelCreator.getBusinessIdentity().getBusinessType());
                businessIdentity.setBusinessUnit(billExpressOrderModelCreator.getBusinessIdentity().getBusinessUnit());
                businessIdentity.setBusinessScene(billExpressOrderModelCreator.getBusinessIdentity().getBusinessScene());
                // RequestProfile放入context
                RequestProfile requestProfile = new RequestProfile();
                requestProfile.setTraceId(traceId);
                requestProfile.setTimeZone("GMT+8");
                requestProfile.setLocale("zh_CN");
                requestProfile.setTenantId("1000");
                ExpressOrderContext expressOrderContext = new ExpressOrderContext(businessIdentity, requestProfile, businessIdentity.getBusinessScene());
                expressOrderContext.setOrderModel(billDetailOrderModel);
                expressOrderContext.setSkipRepositoryRetry(true);
                // 如果是融合业务身份，给上下文加上融合业务身份标识，用于流程图内的身份转换
                if (snapShotModel.isUnitedIdentity()) {
                    // 2、将原始业务身份保存至上下文。只在未保存时才保存至上下文，不做覆盖。
                    if (ContextInfoUtil.getOriginalIdentity(expressOrderContext) == null) {
                        if (orderBusinessIdentity != null) {
                            OrderBusinessIdentity originalIdentity = orderBusinessIdentity.copy();
                            ContextInfoUtil.saveOriginalIdentity(expressOrderContext, originalIdentity);
                        }
                    }
                    expressOrderContext.getOrderModel().assignIsUnitedIdentity();
                }
                if (hasReset) {
                    // 设置 业务身份和业务类型和业务策略 修改类型
                    ContextInfoUtil.setupModifyBusinessIdentityTypes(expressOrderContext, MODIFY_BUSINESS_IDENTITY_TYPES);

                    // 互改打点
                    CallerInfo exchangeCallerInfo = Profiler.registerInfo(this.getClass().getName() + ".exchange"
                            , UmpKeyConstants.JDL_OMS_WORKER_EXPRESS_APP_CODE
                            , UmpKeyConstants.METHOD_ENABLE_HEART
                            , UmpKeyConstants.METHOD_ENABLE_TP);

                    Profiler.registerInfoEnd(exchangeCallerInfo);
                }
                //流程初始化
                InputMessage inputMessage = new InputMessage(expressOrderContext);
                // 订单信息比对
                compareFlowNode.call(inputMessage);
                // 更新主产品变更来源
                if(expressOrderContext.getOrderModel().isC2C()){
                    if(expressOrderContext.getChangedPropertyDelegate().mainProductHaveChange()){
                        // 当「modifyDataSource=2」时，将「主产品变更来源 赋值为 终端修改-1」
                        if (MagicCommonConstants.NUM_2.equals(waybilldto.getModifyDataSource())) {
                            expressOrderContext.getOrderModel().putAttachment(OrderConstants.MAIN_PRODUCT_CHANGED_SOURCE, MagicCommonConstants.STRING_1);
                        }
                    }
                }

                LOGGER.info("运单信息变更消息:orderNo={},要持久化的数据={}", getOrderFacadeResponse.getOrderNo()
                        , JSONUtils.beanToJSONDefaultLazy(inputMessage));

                //更新代收货款标位
                MarkUtil markUtil = new MarkUtil(wayBillQueryResponse.getOrderMark());

                String modifyMark = null;

                if (snapShotModel.getExtendProps() != null) {
                    modifyMark = snapShotModel.getExtendProps().get(AttachmentKeyEnum.MODIFY_MARK.getKey());
                }

                if (StringUtils.isBlank(modifyMark)) {
                    modifyMark = ModifyMarkUtil.getInitMark();
                }

                if ("9".equals(String.valueOf(markUtil.charAt(8)))) {
                    if (BatrixSwitch.applyByContainsOrAll(BatrixSwitchKey.WAYBILL_INFO_COD_TIMES_BUSINESS_UNIT_LIST, getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit())) {
                        LOGGER.info("运单数据同步-业务身份[{}]开启[COD修改标记不同步]", getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit());
                    } else {
                        LOGGER.info("运单信息缓存代收货款标为开始");
                        modifyMark = ModifyMarkUtil.updateMark(modifyMark, ModifyMarkEnum.COD.getPosition(), ModifyMarkEnum.COD.getSign());
                        LOGGER.info("运单信息缓存代收货款标为结束");
                    }
                }
                billDetailOrderModel.getExtendProps().put(AttachmentKeyEnum.MODIFY_MARK.getKey(), modifyMark);

                if (redisClient.get(callBackLockKey) != null) {
                    LOGGER.info("运单信息变更消息:waybillCode={},正在进行状态回传，优先进行状态回传，暂不处理数据同步!",
                            waybilldto.getWaybillCode());
                    throw new AntiConcurrentException(UnifiedErrorSpec.BasisOrder.WAYBILL_UPDATE_INFO_FAIL).withCustom("正在进行状态回传!");
                }

                //上门接货退货场景特殊处理
                if (isPickReturn(wayBillQueryResponse.getOrderStatusId())) {
                    LOGGER.info("命中上门接货退货场景");
                    //若优惠券清空则不更新优惠券
                    if (expressOrderContext.getOrderModel().getModifiedFields() != null
                            && ModifiedFieldValueEnum.ALL_DELETE.getCode().equals(expressOrderContext.getOrderModel().getModifiedFields().get(ModifiedFieldEnum.TICKET_INFOS.getCode()))) {
                        LOGGER.info("上门接货退货场景，优惠券清空不处理优惠券");
                        expressOrderContext.getOrderModel().getModifiedFields().remove(ModifiedFieldEnum.TICKET_INFOS.getCode());
                        expressOrderContext.getOrderModel().getPromotion().setTickets(null);
                    }
                    //若积分为0，则不更新积分
                    if (expressOrderContext.getOrderModel().getFinance().getPoints() != null
                            && expressOrderContext.getOrderModel().getFinance().getPoints().getRedeemPointsQuantity() != null
                            && expressOrderContext.getOrderModel().getFinance().getPoints().getRedeemPointsQuantity().getValue() != null
                            && expressOrderContext.getOrderModel().getFinance().getPoints().getRedeemPointsQuantity().getValue().intValue() == 0) {
                        LOGGER.info("上门接货退货场景，积分为0不处理积分");
                        expressOrderContext.getOrderModel().getFinance().setPoints(null);
                    }
                }

                // 持久化前 更新 新产品。
                productMappingFlowNode.call(inputMessage);

                boolean needSendMultiPartiesTotalAmounts = false;
                //短期方案,快运C2C德邦到付订单,若结算方式或者折后金额变更,则发送数据流水,通知DP
                if (BatrixSwitch.applyByBoolean(BatrixSwitchKey.SYNC_DATA_DP_SEND_MULTI_PARTIES_TOTAL_AMOUNTS_SWITCH)) {
                    if (snapShotModel.isFreightC2C()
                            && snapShotModel.getOrderStatus().isAfterPickedUp()
                            && SettlementTypeEnum.CASH_ON_DELIVERY == snapShotModel.getFinance().getSettlementType()
                            && snapShotModel.isJDLToDPDelivery()
                    ) {
                        //折后金额发生变化
                        if (expressOrderContext.getChangedPropertyDelegate().settlementTypeHaveChange()
                                || expressOrderContext.getChangedPropertyDelegate().discountAmountHaveChange()) {
                            LOGGER.info("快运C2C德邦到付订单,结算方式或者折后金额发生变更,重新计算多方计费总额");
                            needSendMultiPartiesTotalAmounts = true;
                            //京东转德邦，需要按结算方式、支付环节纬度计算多方计费总额
                            List<CostInfo> costInfoList = FinanceUtil.computeMPTotalAmountsGroupSettleAndStage(expressOrderContext.getOrderModel(), expressOrderContext.getOrderModel().getOrderSnapshot(), null, false, false);
                            expressOrderContext.getOrderModel().complement().complementMultiPartiesTotalAmounts(this, costInfoList);
                        }
                    }
                }

                orderRepositoryFlowNode.call(inputMessage);

                if (needSendMultiPartiesTotalAmounts && CollectionUtils.isNotEmpty(expressOrderContext.getOrderModel().getFinance().getMultiPartiesTotalAmounts())) {
                    LOGGER.info("快运C2C德邦到付订单,结算方式或者折后金额发生变更,发送多方计费金额变更通知");
                    //发送多方计费金额变更通知
                    OrderDataFlowDto dto = orderDataNotifyTranslator.toGeneralOrderDataFlowDto(expressOrderContext.getOrderModel(), OrderConstants.NO_VAL, OrderDataFieldEnum.MULTI_PARTIES_TOTAL_AMOUNTS);
                    if (null != dto) {
                        expressOrderFlowService.sendOrderDataRecordMq(dto);
                    }
                }

                // pop售后单数据变更消息通知
                if (this.isPopAfsOrder(expressOrderContext)) {
                    if (expressOrderContext.getChangedPropertyDelegate().pickupTimeHaveChange()) {
                        LOGGER.info("预约取件时间变更，发送数据同步消息");
                        OrderDataFlowDto popMsgDto = orderDataNotifyTranslator.toModifyOrderDataFlowDto(expressOrderContext, POP_FIELDS);
                        // 如果dto为null说明需要同步的字段没有变更
                        if (null != popMsgDto) {
                            expressOrderFlowService.sendPopAfsInfoUpdateMq(popMsgDto);
                        }
                    }
                }

                ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
                if(expressOrderContext.getOrderModel().isC2C()){
                    SystemCallerEnum systemCallerEnum = orderModel.getChannel().getSystemCaller();
                    if(expressOrderContext.getChangedPropertyDelegate().mainProductHaveChange()){
                        if (MagicCommonConstants.NUM_2.equals(waybilldto.getModifyDataSource())) {
                            //主产品变更需要使用订单修改记录，传expressOms无法区分是否是终端进行修改的，后续终端计数分析可能会使用，systemCaller不会落库
                            orderModel.getChannel().setSystemCaller(SystemCallerEnum.PDA);
                        }
                        LOGGER.info("主产品变更，发送数据流水消息");
                        orderFieldDataNotifyFlowNode.call(inputMessage);
                    }
                    //上面systemCaller变更，以防后面有别的节点会使用这个字段，所以再改回来
                    orderModel.getChannel().setSystemCaller(systemCallerEnum);
                }
                if (UnitedBusinessIdentityUtil.isUnitedIdentity(expressOrderContext)) {
                    UnitedBusinessIdentityUtil.convertToOriginal(expressOrderContext);
                }
                /**************************end调用订单对比能力*********************************/
                return true;
            } else {
                Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_WORKER_WAYBILL_UPDATE_INFO
                        , System.currentTimeMillis()
                        , getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit()
                                + ","
                                + "查询运单数据为空,运单号码为:" + waybilldto.getWaybillCode());
                LOGGER.error("查询运单数据为空,运单号码为:{}", waybilldto.getWaybillCode());
                return false;
            }
        } catch (BusinessDomainException e) {
            if (e.getDooErrorSpec() == UnifiedErrorSpec.BasisOrder.ORDER_INFO_NO_CHANGE) {
                LOGGER.error("订单信息未发生改变");
                return true;
            }
            LOGGER.error("运单信息变更处理业务异常，BusinessDomainException={}", e.fullMessage());
            throw new JMQRetryException(UnifiedErrorSpec.BasisOrder.WAYBILL_UPDATE_INFO_FAIL).withCustom("运单信息变更处理业务异常");
        } catch (AntiConcurrentException e) {
            LOGGER.info("运单信息变更处理并发锁异常，{}", e.fullMessage());
            throw new JMQRetryException(UnifiedErrorSpec.BasisOrder.WAYBILL_UPDATE_INFO_FAIL).withCustom("运单信息变更处理并发锁异常");
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("运单信息变更处理可用率异常,运单信息变更处理异常", e);
            throw new JMQRetryException(UnifiedErrorSpec.BasisOrder.WAYBILL_UPDATE_INFO_FAIL).withCustom("运单信息变更处理异常");
        } finally {
            try {
                if (lock != null) {
                    lock.unlock();
                }
            }catch (Exception e){
                LOGGER.error("redis锁释放失败",e);
            }
            Profiler.registerInfoEnd(callerInfo);
        }

    }

    /**
     * 判断是否是pop售后单
     * @param context
     * @return
     */
    private boolean isPopAfsOrder(ExpressOrderContext context) {
        if (context.getOrderModel().isC2B()) {
            // snapshot存储订单数据快照
            ExpressOrderModel snapshot = context.getOrderModel().getOrderSnapshot();
            String customerOrderNo = snapshot.getChannel().getCustomerOrderNo();
            if (!customerOrderNo.startsWith("UEP")) {
                return false;
            }
            // todo 是否校验长度
            LOGGER.info("POP售后订单");
            return true;
        }
        return false;
    }


    /**
     * 补全原单的增值产品
     * @param model
     */
    private void completeProduct(ExpressOrderModel model, WaybillInfoDto waybillDto, WayBillQueryResponse wayBillQueryResponse) {
        if ((BusinessUnitEnum.CN_JDL_B2C.getCode().equals(model.getBusinessIdentity().getBusinessUnit()) && !UnitedB2CUtil.isUnitedFreightB2C(model))
                || BusinessUnitEnum.CN_JDL_O2O_B.getCode().equals(model.getBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_C2B.getCode().equals(model.getBusinessIdentity().getBusinessUnit())) {

            if (null != model.getOrderSnapshot().getProductDelegate().ofProductNo(AddOnProductEnum.SMILE_EXPRESS_SHEET.getCode())) {
                Product product = model.getOrderSnapshot().getProductDelegate().ofProductNo(AddOnProductEnum.SMILE_EXPRESS_SHEET.getCode());
                product.setParentNo(model.getProductDelegate().getMajorProductNo());
                model.getProductDelegate().addProduct(product);
            }
            if (null != model.getOrderSnapshot().getProductDelegate().ofProductNo(AddOnProductEnum.NEGOTIATION_REDELIVERY.getCode())) {
                Product product = model.getOrderSnapshot().getProductDelegate().ofProductNo(AddOnProductEnum.NEGOTIATION_REDELIVERY.getCode());
                product.setParentNo(model.getProductDelegate().getMajorProductNo());
                model.getProductDelegate().addProduct(product);
            }
            //todo 考虑单单保价 和 普通保价 互斥问题  现在单单保不作为保价增值服务
            /*if (waybillDto.getPriceProtectMoney() == null || waybillDto.getPriceProtectMoney().compareTo((double)0) < 0){
                if (null != model.getOrderSnapshot().getProductDelegate().ofProductNo(AddOnProductEnum.INSURED_VALUE_TOC.getCode())) {
                    Product product = model.getOrderSnapshot().getProductDelegate().ofProductNo(AddOnProductEnum.INSURED_VALUE_TOC.getCode());
                    product.setParentNo(model.getProductDelegate().getMajorProductNo());
                    model.getProductDelegate().addProduct(product);
                }
            }*/

            //签单返还补齐要素
            Product signReturnProduct = model.getOrderSnapshot().getProductDelegate().getSignReturnProduct();
            if (null != signReturnProduct) {
                Product product = model.getProductDelegate().getSignReturnProduct();
                if (null != product){
                    if(StringUtils.isNotBlank(signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_TYPE.getCode()))){
                        product.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_TYPE.getCode(),signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_TYPE.getCode()));
                    }
                    if(StringUtils.isNotBlank(signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_SIGN_TYPE.getCode()))){
                        product.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_SIGN_TYPE.getCode(),signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_SIGN_TYPE.getCode()));
                    }
                    if(StringUtils.isNotBlank(signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_DOC_POSITION.getCode()))){
                        product.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_DOC_POSITION.getCode(),signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_DOC_POSITION.getCode()));
                    }
                    if(StringUtils.isNotBlank(signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_SAMPLE_PIC.getCode()))){
                        product.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_SAMPLE_PIC.getCode(),signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_SAMPLE_PIC.getCode()));
                    }
                    if(StringUtils.isNotBlank(signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_REQUEST.getCode()))){
                        product.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_REQUEST.getCode(),signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_REQUEST.getCode()));
                    }
                }
            }

            //保温箱补全要素
            if (null != model.getProductDelegate().ofProductNo(AddOnProductEnum.COOLER_BOX.getCode())) {
                Product coolerBoxProduct = model.getOrderSnapshot().getProductDelegate().ofProductNo(AddOnProductEnum.COOLER_BOX.getCode());
                if (null != coolerBoxProduct) {
                    // FIXME ofProductNo 不为null addProduct也是一个返回结果吧
                    Product product = model.getProductDelegate().addProduct(AddOnProductEnum.COOLER_BOX);
                    Map<String,String> productAttrs = product.getProductAttrs();
                    if (productAttrs == null){
                        productAttrs = new HashMap<>();
                    }
                    productAttrs.putAll(coolerBoxProduct.getProductAttrs());
                    product.setProductAttrs(productAttrs);
                }
            }

            //包装耗材以原单的为准
            if (null != model.getOrderSnapshot().getProductDelegate().getPackageServiceProduct()) {
                Product product = model.getOrderSnapshot().getProductDelegate().getPackageServiceProduct();
                // 产品互改模式快运改快递时，原单可能有快运的包装服务，需剔除
                if (!product.getProductNo().equals(AddOnProductEnum.PACKAGE_SERVICE_TOB.getCode())) {
                    product.setParentNo(model.getProductDelegate().getMajorProductNo());
                    model.getProductDelegate().addProduct(product);
                }
            }

            //B2C揽收修改外单数据时，外单没有附件信息及扩展信息----基于原单货品信息构建货品，此逻辑可去掉
        }else if(BusinessUnitEnum.CN_JDL_CC_B2C.getCode().equals(model.getBusinessIdentity().getBusinessUnit())){
            if (null != model.getOrderSnapshot().getProductDelegate().ofProductNo(AddOnProductEnum.CC_LL_SMILE_EXPRESS_SHEET.getCode())) {
                Product product = model.getOrderSnapshot().getProductDelegate().ofProductNo(AddOnProductEnum.CC_LL_SMILE_EXPRESS_SHEET.getCode());
                product.setParentNo(model.getProductDelegate().getMajorProductNo());
                model.getProductDelegate().addProduct(product);
            }
            if (null != model.getOrderSnapshot().getProductDelegate().ofProductNo(AddOnProductEnum.CC_MD_NEGOTIATION_REDELIVERY.getCode())) {
                Product product = model.getOrderSnapshot().getProductDelegate().ofProductNo(AddOnProductEnum.CC_MD_NEGOTIATION_REDELIVERY.getCode());
                product.setParentNo(model.getProductDelegate().getMajorProductNo());
                model.getProductDelegate().addProduct(product);
            }

            //签单返还补齐要素
            Product signReturnProduct = model.getOrderSnapshot().getProductDelegate().getSignReturnProduct();
            if (null != signReturnProduct) {
                Product product = model.getProductDelegate().getSignReturnProduct();
                if (null != product){
                    if(StringUtils.isNotBlank(signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_TYPE.getCode()))){
                        product.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_TYPE.getCode(),signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_TYPE.getCode()));
                    }
                    if(StringUtils.isNotBlank(signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_SIGN_TYPE.getCode()))){
                        product.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_SIGN_TYPE.getCode(),signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_SIGN_TYPE.getCode()));
                    }
                    if(StringUtils.isNotBlank(signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_DOC_POSITION.getCode()))){
                        product.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_DOC_POSITION.getCode(),signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_DOC_POSITION.getCode()));
                    }
                    if(StringUtils.isNotBlank(signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_SAMPLE_PIC.getCode()))){
                        product.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_SAMPLE_PIC.getCode(),signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_SAMPLE_PIC.getCode()));
                    }
                    if(StringUtils.isNotBlank(signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_REQUEST.getCode()))){
                        product.getProductAttrs().put(AddOnProductAttrEnum.RE_RECEIVE_REQUEST.getCode(),signReturnProduct.getProductAttrs().get(AddOnProductAttrEnum.RE_RECEIVE_REQUEST.getCode()));
                    }
                }
            }

            //保温箱补全要素
            if (null != model.getProductDelegate().ofProductNo(AddOnProductEnum.CC_LL_COOLER_BOX.getCode())) {
                Product coolerBoxProduct = model.getOrderSnapshot().getProductDelegate().ofProductNo(AddOnProductEnum.CC_LL_COOLER_BOX.getCode());
                if (null != coolerBoxProduct) {
                    Product product = model.getProductDelegate().addProduct(AddOnProductEnum.CC_LL_COOLER_BOX);
                    Map<String,String> productAttrs = product.getProductAttrs();
                    if (productAttrs == null){
                        productAttrs = new HashMap<>();
                    }
                    productAttrs.putAll(coolerBoxProduct.getProductAttrs());
                    product.setProductAttrs(productAttrs);
                }
            }

            //包装耗材以原单的为准
            if (null != model.getOrderSnapshot().getProductDelegate().getPackageServiceProduct()) {
                Product product = model.getOrderSnapshot().getProductDelegate().getPackageServiceProduct();
                product.setParentNo(model.getProductDelegate().getMajorProductNo());
                model.getProductDelegate().addProduct(product);
            }
        }

        //非生鲜温层为common时，同步为空。提取至外部静态常量
        if (!FRESH_PRODUCT_NOS.contains(model.getProductDelegate().getMainProduct().getProductNo())) {
            if (WarmLayerEnum.COMMON == model.getShipment().getWarmLayer()) {
                model.getShipment().setWarmLayer(WarmLayerEnum.DEFAULT);
            }
        }
    }

    private static final Set<String> FRESH_PRODUCT_NOS = new HashSet<String>() {{
        add(ProductEnum.SXTK.getCode());
        add(ProductEnum.SXTH.getCode());
        add(ProductEnum.SXZS.getCode());
        add(ProductEnum.CC_LLZS.getCode());
        add(ProductEnum.CC_YYZS.getCode());
        add(ProductEnum.CC_YYLL.getCode());
    }};

    /**
     * 是否接货退货场景的状态
     *
     * @param orderStatusId 外单订单状态
     * @return
     */
    private boolean isPickReturn(Integer orderStatusId) {
        if (orderStatusId == null) {
            return false;
        }
        Integer waybillStatusId = orderStatusId - 10000;
        //UCC配置,
        return expressUccConfigCenter.isPickReturnWaybillStatus(String.valueOf(waybillStatusId));
    }

    /**
     * 产品互改场景，重置快照的业务身份
     *
     * @param wayBillQueryResponse
     * @param snapshotModel
     */
    private boolean resetSnapshotBusinessIdentity(WayBillQueryResponse wayBillQueryResponse, ExpressOrderModel snapshotModel) {

        ExtendMessageDTO extendMessageDTO = wayBillQueryResponse.getExtendMessageDTO();
        if (extendMessageDTO != null && extendMessageDTO.getChangeProductMode() != null) {
            LOGGER.info("产品互改，changeProductMode: {}", extendMessageDTO.getChangeProductMode());
            // 产品互改模式: 1-快运改快递，2-快递改快运。保持与运单一致
            if (MagicCommonConstants.NUM_1.equals(extendMessageDTO.getChangeProductMode())) {

                OrderBusinessIdentity snapshotBusinessIdentity = snapshotModel.getOrderBusinessIdentity();
                if (snapshotBusinessIdentity == null) {
                    snapshotBusinessIdentity = new OrderBusinessIdentity();
                }
                // 快运B2C -> 快递B2C
                if (BusinessUnitEnum.CN_JDL_FREIGHT_SERVICE.getCode().equals(snapshotBusinessIdentity.getBusinessUnit())) {

                    snapshotBusinessIdentity.setBusinessUnit(BusinessUnitEnum.CN_JDL_B2C.getCode());
                    snapshotBusinessIdentity.setBusinessType(BusinessTypeEnum.EXPRESS.getCode());
                    snapshotBusinessIdentity.setBusinessStrategy("BOpenExpress");
                    snapshotModel.resetOrderBusinessIdentity(snapshotBusinessIdentity);

                    return true;
                }

            }

        }

        return false;

    }

    /**
     * @Description 补充组装的订单数据
     * <AUTHOR>
     * @Date 13:48 2021/5/15
     * @Param getOrderFacadeResponse
     * @Param billExpressOrderModelCreator
     * @Return void
     * @Throws
     **/
    private void supplementData(ExpressOrderModel snapShotModel, ExpressOrderModelCreator billExpressOrderModelCreator, String waybillSign) {
        //设置订单号
        billExpressOrderModelCreator.setOrderNo(snapShotModel.orderNo());
        //设置订单ID
        //billExpressOrderModelCreator.setOrderId(getOrderFacadeResponse.getOrderId());
        //业务身份
        BusinessIdentityDto businessIdentityDto = billExpressOrderModelCreator.getBusinessIdentity();
        if (businessIdentityDto == null) {
            businessIdentityDto = new BusinessIdentityDto();
            businessIdentityDto.setBusinessUnit(snapShotModel.getOrderBusinessIdentity().getBusinessUnit());
            businessIdentityDto.setBusinessType(snapShotModel.getOrderBusinessIdentity().getBusinessType());
            businessIdentityDto.setBusinessStrategy(snapShotModel.getOrderBusinessIdentity().getBusinessStrategy());
        }
        businessIdentityDto.setBusinessScene(BusinessSceneEnum.MODIFY.getCode());
        billExpressOrderModelCreator.setBusinessIdentity(businessIdentityDto);

        //B2C协商再投次数通过回传更新，需要过滤掉;微笑面单需由上游传，不应同步，需要过滤掉
        if (BusinessUnitEnum.CN_JDL_B2C.getCode().equals(snapShotModel.getOrderBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_O2O_B.getCode().equals(snapShotModel.getOrderBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_C2B.getCode().equals(snapShotModel.getOrderBusinessIdentity().getBusinessUnit())){
            List<ProductInfoDto> products = billExpressOrderModelCreator.getProducts().stream()
                    //过滤掉协商再投、微笑面单
                    .filter(productInfoDto ->
                            !AddOnProductEnum.NEGOTIATION_REDELIVERY.getCode().equals(productInfoDto.getProductNo())
                            //&& !AddOnProductEnum.INSURED_VALUE_TOC.getCode().equals(productInfoDto.getProductNo())
                            //&& !AddOnProductEnum.INSURED_PER_ORDER.getCode().equals(productInfoDto.getProductNo())
                            && !AddOnProductEnum.SMILE_EXPRESS_SHEET.getCode().equals(productInfoDto.getProductNo())
                    ).collect(Collectors.toList());
            billExpressOrderModelCreator.setProducts(products);
            LOGGER.info("B2C业务，过滤掉协商再投、微笑面单，products{}",billExpressOrderModelCreator.getProducts());
        } else if(BusinessUnitEnum.CN_JDL_CC_B2C.getCode().equals(snapShotModel.getOrderBusinessIdentity().getBusinessUnit())){
            List<ProductInfoDto> products = billExpressOrderModelCreator.getProducts().stream()
                    //过滤掉协商再投、微笑面单
                    .filter(productInfoDto ->
                            !AddOnProductEnum.CC_MD_NEGOTIATION_REDELIVERY.getCode().equals(productInfoDto.getProductNo())
                                    && !AddOnProductEnum.CC_LL_SMILE_EXPRESS_SHEET.getCode().equals(productInfoDto.getProductNo())
                    ).collect(Collectors.toList());
            billExpressOrderModelCreator.setProducts(products);
            LOGGER.info("CCB2C业务，过滤掉协商再投、微笑面单，products{}",billExpressOrderModelCreator.getProducts());
        }
        //给扩展字段赋waybillsign,用于修改处理时，判断是否修改相关标识（是否是揽收后修改，是否是揽收后修改过代收货款，是否是揽收后修改收件人信息）
        Map<String,String> map = billExpressOrderModelCreator.getExtendProps();
        if (MapUtils.isEmpty(map)) {
            map = new HashMap<>();
        }
        String ignoreModifyFlag = OrderConstants.IGNORE_MODIFY_FLAG_VALUE;
        if (StringUtils.isNotBlank(waybillSign)){
            LOGGER.info("运单揽收前或揽收后数据变化发起修改，waybillSign:{}",waybillSign);
            //由于终端不切订单中心，终端揽收完成时称重量方通过外单修改（揽收完成是揽收前的动作--揽收前修改），不经过订单中心；
            //外单修改调运单修改，此时修改请求经过运单处理后会发出运单变化消息，订单中心监听运单变更消息调外单信息更新到订单中心；
            //在处理运单消息变更的过程中，订单的状态可能已经回传成揽收后，
            //因此如果只判断订单状态是揽收后，会导致揽收后修改的标识记录有误（因为终端的修改是揽收前，订单中心的状态被回传更新为揽收后）
            //与外单及订单中心产品（严飞、王维金确认后），同步外单数据更新时，
            // 判断运单标位waybillSign（第8位和第103位为0表示揽收前修改）的第8位和第103位是否为0，
            //如果为0，则为揽收前修改，如果不是，则为揽收后修改
            if (!OrderConstants.NOT_IGNORE_MODIFY_FLAG_VALUE.equals(String.valueOf(waybillSign.charAt(8-1)))
                    && !OrderConstants.NOT_IGNORE_MODIFY_FLAG_VALUE.equals(String.valueOf(waybillSign.charAt(103-1)))){
                LOGGER.info("运单揽收后修改");
                ignoreModifyFlag = OrderConstants.NOT_IGNORE_MODIFY_FLAG_VALUE;
            }
        }
        map.put("ignoreModifyFlag",ignoreModifyFlag);
        billExpressOrderModelCreator.setExtendProps(map);
    }

    private static RequestProfile buildRequest(String tradeId) {
        RequestProfile requestProfile = new RequestProfile();
        requestProfile.setExt(null);
        requestProfile.setLocale("zh_CN");
        requestProfile.setTenantId("1000");
        requestProfile.setTimeZone("GMT+8");
        requestProfile.setTraceId(tradeId);
        return requestProfile;
    }

    /**
     * @param tradeId
     * @param refOrderNo
     * @return
     */
    private GetOrderFacadeResponse getOrderInfo(String tradeId, String refOrderNo) {
        RequestProfile requestProfile = buildRequest(tradeId);
        GetOrderFacadeRequest facadeRequest = new GetOrderFacadeRequest();
        facadeRequest.setCustomOrderNo(refOrderNo);
        return getOrderFacade.getOrder(requestProfile, facadeRequest);
    }


    /**
     * 判断是否港澳订单
     * @return
     */
    public boolean isHKMO(CustomsFacade customsFacade) {
        // 获取跨境报关信息，始发目的流向存在与否，判断是否是港澳订单。若始发流向目的流向都是大陆，不是港澳订单。
        return Optional.ofNullable(customsFacade)
                .filter(customsVo -> null != customsVo.getStartFlowDirection()
                        && null != customsVo.getEndFlowDirection()
                        && !(AdministrativeRegionEnum.CN == customsVo.getStartFlowDirection() && AdministrativeRegionEnum.CN == customsVo.getEndFlowDirection()))
                .isPresent();
    }

    /**
     * C2B微信小程序商家取件补全向多方收费信息
     * @param billExpressOrderModelCreator
     * @param waybilldto
     */
    private void c2bWxPickCostInfo(ExpressOrderModelCreator billExpressOrderModelCreator, WaybillInfoDto waybilldto){
        WaybillByChoiceResponse waybillByChoiceResponse = wayBillQueryFacade.queryWaybillByChoice(waybilldto.getWaybillCode());
        if(null != waybillByChoiceResponse
                && CollectionUtils.isNotEmpty(waybillByChoiceResponse.getProductFeeDetails())){
            FinanceInfoDto financeInfo = billExpressOrderModelCreator.getFinanceInfo();
            financeInfo = null != financeInfo ? financeInfo : new FinanceInfoDto();
            List<CostInfoDto> costInfoDtos = financeInfo.getCostInfoDtos();
            if(CollectionUtils.isEmpty(costInfoDtos)){
                costInfoDtos = new ArrayList<>(waybillByChoiceResponse.getProductFeeDetails().size());
            }
            for (WaybillChoiceProductFeeDetail productFeeDetail : waybillByChoiceResponse.getProductFeeDetails()) {
                CostInfoDto costInfoDto = new CostInfoDto();
                //费用项编码
                costInfoDto.setCostNo(productFeeDetail.getProductFeeCode());
                //费用项名称
                costInfoDto.setCostName(productFeeDetail.getProductFeeName());
                //收费方
                costInfoDto.setChargingSource(productFeeDetail.getChargingSource());
                //收费方结算账号
                costInfoDto.setSettlementAccountNo(productFeeDetail.getSettlementCode());
                costInfoDtos.add(costInfoDto);
            }
            financeInfo.setCostInfoDtos(costInfoDtos);
            LOGGER.info("C2B微信小程序商家取件补全向多方收费信息，waybillCode:{},financeInfo:{}",waybilldto.getWaybillCode(),JSONUtils.beanToJSONDefault(financeInfo));
            billExpressOrderModelCreator.setFinanceInfo(financeInfo);
        }
    }

    /**
     * 判断业务身份是否快运
     */
    private boolean isFreight(GetOrderFacadeResponse getOrderFacadeResponse) {
        return getOrderFacadeResponse != null
                && getOrderFacadeResponse.getBusinessIdentity() != null
                && StringUtils.isNotBlank(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit())
                && (BusinessUnitEnum.CN_JDL_FREIGHT_SERVICE.getCode().equals(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_FREIGHT_CONSUMER.getCode().equals(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit()));
    }

    private boolean isUnitedC2C(GetOrderFacadeResponse getOrderFacadeResponse) {
        return getOrderFacadeResponse != null
                && getOrderFacadeResponse.getBusinessIdentity() != null
                && StringUtils.isNotBlank(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit())
                && (BusinessUnitEnum.CN_JDL_UNITED_C2C.getCode().equals(getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit()));
    }
}