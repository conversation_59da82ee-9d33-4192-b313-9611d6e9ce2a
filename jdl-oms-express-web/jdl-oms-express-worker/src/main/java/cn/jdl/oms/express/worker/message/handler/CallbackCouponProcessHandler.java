package cn.jdl.oms.express.worker.message.handler;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.PromotionInfo;
import cn.jdl.oms.core.model.TicketInfo;
import cn.jdl.oms.express.c2c.infrs.acl.pl.orderbank.C2COrderBankFacadeTranslator;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.infrs.acl.facade.coupon.CouponFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.issue.ModifyIssueFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderNoApiFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.ModifyOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.coupon.CouponInfoRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.coupon.CouponInfoResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.coupon.CouponUseRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderExistsRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderModelCreatorTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.PromotionFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.TicketFacade;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.entity.CommonDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.handler.ExpressAbstractHandler;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.CallbackCouponProcessMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ModifyIssueMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ModifyRepositoryMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.vo.Discount;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.FinanceDetail;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import cn.jdl.oms.express.domain.vo.Ticket;
import cn.jdl.oms.express.shared.common.constant.FlowConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.CouponExecuteTypeEnum;
import cn.jdl.oms.express.shared.common.dict.CouponStatusEnum;
import cn.jdl.oms.express.shared.common.dict.DiscountTypeEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldValueEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.SystemSubCallerEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.JMQRetryException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.alibaba.fastjson.JSONObject;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 回传优惠券处理
 *
 * @author: wangsong16
 * @Date: 2025/8/12
 */
public class CallbackCouponProcessHandler extends ExpressAbstractHandler {

    private static final Logger log = LoggerFactory.getLogger(CallbackCouponProcessHandler.class);

    private static final String TENANT_ID = "1000";
    private static final String LOCALE = "zh_CN";
    private static final String TIME_ZONE = "GMT+8";

    /**
     * 订单详情查询
     */
    @Resource
    private GetOrderFacade getOrderFacade;

    /**
     * 部署环境
     */
    @Value("${express.order.run.environment}")
    private String environment;

    /**
     * 判断订单是否存在服务
     */
    @Resource
    private GetOrderNoApiFacade getOrderNoApiFacade;

    /**
     * 转换器
     */
    @Resource
    private C2COrderBankFacadeTranslator c2cOrderBankFacadeTranslator;

    /**
     * 订单详情model转换
     */
    @Resource
    private GetOrderModelCreatorTranslator orderModelCreatorTranslator;

    /**
     * 修改下发服务
     */
    @Resource
    private ModifyIssueFacade modifyIssueFacade;

    /**
     * 任务调度
     */
    @Resource
    private SchedulerService schedulerService;

    @Resource
    private CouponFacade couponFacade;

    @Resource
    private ModifyOrderFacade modifyOrderFacade;

    /**
     * @param
     * @return
     * @throws
     * @throws
     * @Description 回传优惠券处理的MQ消息
     * <AUTHOR>
     */
    @Override
    public boolean handle(CommonDto commonDto) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".handle"
                , UmpKeyConstants.JDL_OMS_WORKER_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        log.info("回传优惠券处理消息开始：{}", commonDto);
        CallbackCouponProcessMessageDto dto = (CallbackCouponProcessMessageDto) commonDto;
        try {
            //解析业务单号-订单号
            String orderNo = dto.getOrderNo();

            if (StringUtils.isBlank(orderNo)) {
                log.info("未解析出运单号，不执行！！！");
                return true;
            }

            //判断订单是否存在服务
            GetOrderExistsRequest getOrderExistsRequest = new GetOrderExistsRequest();
            getOrderExistsRequest.setOrderNo(orderNo);
            boolean existOrder = getOrderNoApiFacade.existsOrder(convertRequestProfile(dto.getMessageId()), getOrderExistsRequest);
            if (!existOrder) {
                log.info("非百川的单子直接过滤,traceId:{}, 订单号:{}", dto.getMessageId(), orderNo);
                return true;
            }
            //查询订单详情
            GetOrderFacadeResponse getOrderFacadeResponse = getOrderFacade.getOrder(convertRequestProfile(dto.getMessageId()), convertGetOrderRequest(orderNo));
            log.info("根据订单号查询订单信息出参：{}", getOrderFacadeResponse);
            if (getOrderFacadeResponse == null || StringUtils.isBlank(getOrderFacadeResponse.getOrderNo())) {
                log.info("根据订单号查询订单信息为空，messageId:{}", dto.getMessageId());
                return true;
            }
            String orderEnv = getOrderFacadeResponse.getEnvironment();
            if (StringUtils.isNotBlank(orderEnv)) {
                if (!environment.equals(orderEnv)) {
                    log.info("订单数据与运行环境不一致,环境{}消费到环境{}的订单{}mq，忽略", environment, orderEnv, getOrderFacadeResponse.getOrderNo());
                    return true;
                }
            }

            RequestProfile profile = new RequestProfile();
            profile.setLocale(LOCALE);
            profile.setTenantId(TENANT_ID);
            profile.setTimeZone(TIME_ZONE);
            profile.setTraceId(Long.toString(System.currentTimeMillis()));
            ExpressOrderModel model = new ExpressOrderModel(orderModelCreatorTranslator.toExpressOrderModelCreator(getOrderFacadeResponse)).withRequestProfile(profile);


            List<Ticket> tickets = model.getPromotion().getTickets();
            if (CollectionUtils.isNotEmpty(tickets)) {
                for (Ticket ticket : tickets) {
                    // 优先判断CouponStatus == 5（待回滚）
                    if (CouponStatusEnum.DELETE_PENDING.getCode().equals(ticket.getCouponStatus())) {

                        // 回传场景的优惠券释放前置校验: 费用明细不存在
                        if (!existCoupon(model.getFinance())) {
                            log.info("订单中的当前优惠券满足释放条件：费用明细不存在 & 优惠券状态为待回滚，ticket:{}, 原单Finance:{}",
                                    JSONUtils.beanToJSONDefault(ticket), JSONUtils.beanToJSONDefault(model.getFinance()));

                            CouponUseRequest request = new CouponUseRequest();
                            request.setBusinessUnit(model.getBusinessIdentity().getBusinessUnit());
                            request.setTraceId(profile.getTraceId());
                            request.setCouponNo(ticket.getTicketNo());
                            request.setOrderCreatorNo(dto.getOperator());
                            request.setOrderCreateTime(model.getOperateTime());
                            request.setUseAmount(ticket.getTicketUseAmount().getAmount());
                            request.setWaybillNo(model.getRefOrderInfoDelegate().getWaybillNo());
                            request.setUse(false);
                            request.setCouponExecuteType(CouponExecuteTypeEnum.MODIFY.getCode());
                            //优惠券来源
                            request.setCouponSource(ticket.getTicketSource());
                            //平台订单号
                            request.setPlatformOrderNo(model.getRefOrderInfoDelegate().getEnquiryOrderNo());
                            //流向
                            request.setStartFlowDirection(getStartFlowDirection(model));
                            request.setEndFlowDirection(getEndFlowDirection(model));
                            //查询并设置优惠券子类型
                            CouponInfoResponse couponInfoResponse = queryCouponInfo(request);
                            request.setCouponSubStyle(couponInfoResponse.getCouponSubStyle());
                            boolean rest = couponFacade.useCoupon(request);
                            if (!rest) {
                                log.error("释放优惠券失败, request:{}", JSONObject.toJSONString(request));
                                throw new JMQRetryException(UnifiedErrorSpec.BasisOrder.COUPON_RELEASE_FAIL).withCustom("释放优惠券失败");
                            }
                            ModifyOrderFacadeRequest facadeRequest = this.toModifyOrderFacadeRequest(model.orderNo(), ticket.getTicketNo());
                            try {
                                modifyOrderFacade.modifyOrder(profile, facadeRequest);
                                log.info("修改数据库状态成功");
                            } catch (Exception e) {
                                SchedulerMessage scheduler = new SchedulerMessage();
                                //持久化消息
                                ModifyRepositoryMessageDto modifyRepositoryMessageDto = new ModifyRepositoryMessageDto();
                                modifyRepositoryMessageDto.setModifyOrderFacadeRequest(facadeRequest);
                                modifyRepositoryMessageDto.setRequestProfile(profile);
                                modifyRepositoryMessageDto.setBusinessIdentity(model.getOrderBusinessIdentity());
                                scheduler.setDtoJson(JSONUtils.beanToJSONDefault(modifyRepositoryMessageDto));
                                scheduler.setDtoClass(ModifyRepositoryMessageDto.class);
                                schedulerService.addSchedulerTask(PDQTopicEnum.MODIFY_REPOSITORY_RETRY, scheduler,
                                        FlowConstants.EXPRESS_ORDER_COUPON_FLOW_CODE);
                                log.error("修改数据库状态失败，发pdq重试");
                            }
                        }

                        // 回传场景的优惠券【仅下发】前置校验: 费用明细存在
                        else if (existCoupon(model.getFinance())) {
                            log.info("回传场景的优惠券【仅下发】前置校验：费用明细存在 & CouponStatus == 5（待回滚）");
                            issueOfcModifyCoupon(profile, model, ticket);
                        }
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.error("回传优惠券处理消息处理失败！messageId:{}", dto.getMessageId(), e);
            Profiler.functionError(callerInfo);
            throw new JMQRetryException("回传优惠券处理消息处理失败！messageId:{}", e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    private RequestProfile convertRequestProfile(Long traceId) {
        RequestProfile requestProfile = new RequestProfile();
        requestProfile.setExt(null);
        requestProfile.setLocale("zh_CN");
        requestProfile.setTenantId("1000");
        requestProfile.setTimeZone("GMT+8");
        requestProfile.setTraceId(traceId.toString());
        return requestProfile;
    }

    private GetOrderFacadeRequest convertGetOrderRequest(String orderNo) {
        GetOrderFacadeRequest request = new GetOrderFacadeRequest();
        request.setOrderNo(orderNo);
        return request;
    }


    /**
     * 校验费用明细-是否使用折扣明细，如果存在discountType=12的优惠券，则代表使用过优惠券
     *
     * @param finance
     * @return
     */
    private static boolean existCoupon(Finance finance) {
        if (CollectionUtils.isNotEmpty(finance.getFinanceDetails())) {
            for (FinanceDetail financeDetail : finance.getFinanceDetails()) {
                if (!CollectionUtils.isEmpty(financeDetail.getDiscounts())) {
                    for (Discount discount : financeDetail.getDiscounts()) {
                        if (DiscountTypeEnum.TYPE_12.getCode().equals(discount.getDiscountType())) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * 修改下发OFC入参转换并调用
     *
     * @param model
     * @return
     */
    private void issueOfcModifyCoupon(RequestProfile profile, ExpressOrderModel model, Ticket ticket) {
        //下发OFC优惠券入参
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = new ModifyIssueFacadeRequest();
        List<TicketInfo> ticketInfoList = new ArrayList<>();

        TicketInfo ticketInfo = new TicketInfo();
        ticketInfo.setTicketNo(ticket.getTicketNo());
        ticketInfo.setTicketCategory(ticket.getTicketCategory());
        ticketInfo.setTicketType(ticket.getTicketType());
        ticketInfo.setTicketDiscountAmount(MoneyMapper.INSTANCE.toMoneyInfo(ticket.getTicketDiscountAmount()));
        ticketInfo.setTicketDiscountRate(ticket.getTicketDiscountRate());
        ticketInfo.setTicketDiscountUpperLimit(MoneyMapper.INSTANCE.toMoneyInfo(ticket.getTicketDiscountUpperLimit()));
        ticketInfo.setTicketDescription(ticket.getTicketDescription());
        ticketInfo.setTicketUseAmount(MoneyMapper.INSTANCE.toMoneyInfo(ticket.getTicketUseAmount()));
        Map<String, String> extendProps = new HashMap<>();

        extendProps.put("operateType", OperateTypeEnum.INSERT.getCode().toString());
        ticketInfo.setExtendProps(extendProps);
        ticketInfo.setTicketSource(ticket.getTicketSource());
        ticketInfoList.add(ticketInfo);

        PromotionInfo promotionInfo = new PromotionInfo();
        promotionInfo.setTicketInfos(ticketInfoList);
        modifyIssueFacadeRequest.setPromotionInfo(promotionInfo);

        modifyIssueFacadeRequest.setRequestProfile(profile);
        //渠道信息
        ChannelInfo channelInfo = new ChannelInfo();
        channelInfo.setChannelOperateTime(new Date());
        channelInfo.setSystemCaller(SystemCallerEnum.EXPRESS_OMS.getCode());
        channelInfo.setSystemSubCaller(SystemSubCallerEnum.JDL_OMS.getCode());
        modifyIssueFacadeRequest.setChannelInfo(channelInfo);
        //操作人
        modifyIssueFacadeRequest.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        modifyIssueFacadeRequest.setOrderNo(model.orderNo());
        modifyIssueFacadeRequest.setCustomOrderNo(model.getCustomOrderNo());

        Map<String, String> modifiedFields = new HashMap<>();

        modifiedFields.put(ModifiedFieldEnum.TICKET_INFOS.getCode(), ModifiedFieldValueEnum.ALL_COVER.getCode());

        modifyIssueFacadeRequest.setModifiedFields(modifiedFields);
        //业务身份
        modifyIssueFacadeRequest.setBusinessIdentity(model.getBusinessIdentity());

        try {
            modifyIssueFacade.modifyIssue(modifyIssueFacadeRequest, model.getOrderBusinessIdentity());
        } catch (Exception e) {
            log.error("CallbackCouponProcessHandler修改下发异常,执行PDQ重试", e);
            SchedulerMessage schedulerMessage = new SchedulerMessage();
            ModifyIssueMessageDto modifyIssueMessageDto = new ModifyIssueMessageDto();
            modifyIssueMessageDto.setBusinessIdentity(convertBusinessIdentity(model.getBusinessIdentity()));
            modifyIssueMessageDto.setOrderNo(model.orderNo());
            modifyIssueMessageDto.setCustomOrderNo(model.getCustomOrderNo());
            modifyIssueMessageDto.setRequestProfile(profile);
            modifyIssueMessageDto.setModifyIssueFacadeRequest(modifyIssueFacadeRequest);

            schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(modifyIssueMessageDto));
            schedulerMessage.setDtoClass(ModifyIssueMessageDto.class);
            schedulerService.addSchedulerTask(PDQTopicEnum.MODIFY_ISSUE_RETRY, schedulerMessage,
                    FlowConstants.EXPRESS_ORDER_MODIFY_ISSUE_FLOW_CODE);
        }
        log.info("CallbackCouponProcessHandler下发OFC修改优惠券完成");
    }

    private OrderBusinessIdentity convertBusinessIdentity(BusinessIdentity businessIdentity) {
        OrderBusinessIdentity orderBusinessIdentity = new OrderBusinessIdentity();
        orderBusinessIdentity.setBusinessUnit(businessIdentity.getBusinessUnit());
        orderBusinessIdentity.setBusinessType(businessIdentity.getBusinessType());
        orderBusinessIdentity.setBusinessScene(businessIdentity.getBusinessScene());
        orderBusinessIdentity.setBusinessStrategy(businessIdentity.getBusinessStrategy());
        orderBusinessIdentity.setFulfillmentUnit(businessIdentity.getFulfillmentUnit());
        return orderBusinessIdentity;
    }

    /**
     * 校验是否存在优惠券
     *
     * @param finance
     * @return
     */
    private static boolean hasDiscount(Finance finance) {
        if (CollectionUtils.isNotEmpty(finance.getFinanceDetails())) {
            for (FinanceDetail financeDetail : finance.getFinanceDetails()) {
                if (!CollectionUtils.isEmpty(financeDetail.getDiscounts())) {
                    for (Discount discount : financeDetail.getDiscounts()) {
                        if (DiscountTypeEnum.TYPE_12.getCode().equals(discount.getDiscountType())) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    /**
     * 获取始发流向
     *
     * @param orderModel
     * @return
     */
    private String getStartFlowDirection(ExpressOrderModel orderModel) {
        if (null == orderModel.getCustoms()) {
            return null;
        }
        if (null == orderModel.getCustoms().getStartFlowDirection()) {
            return null;
        }
        return orderModel.getCustoms().getStartFlowDirection().name();
    }

    /**
     * 获取目的流向
     *
     * @param orderModel
     * @return
     */
    private String getEndFlowDirection(ExpressOrderModel orderModel) {
        if (null == orderModel.getCustoms()) {
            return null;
        }
        if (null == orderModel.getCustoms().getEndFlowDirection()) {
            return null;
        }
        return orderModel.getCustoms().getEndFlowDirection().name();
    }

    /**
     * 查询优惠券
     */
    private CouponInfoResponse queryCouponInfo(CouponUseRequest couponUseRequest) {
        CouponInfoRequest request = new CouponInfoRequest();
        request.setTraceId(couponUseRequest.getTraceId());
        request.setCouponNo(couponUseRequest.getCouponNo());
        request.setOrderCreatorNo(couponUseRequest.getOrderCreatorNo());
        request.setCouponSource(couponUseRequest.getCouponSource());
        request.setPlatformOrderNo(couponUseRequest.getPlatformOrderNo());
        //流向
        request.setStartFlowDirection(couponUseRequest.getStartFlowDirection());
        request.setEndFlowDirection(couponUseRequest.getEndFlowDirection());
        if (StringUtils.isNotBlank(couponUseRequest.getFromUserTel())) {
            request.setFromUserTel(couponUseRequest.getFromUserTel());
        }
        if (StringUtils.isNotBlank(couponUseRequest.getToUserTel())) {
            request.setToUserTel(couponUseRequest.getToUserTel());
        }
        return couponFacade.queryInfo(request);
    }

    private ModifyOrderFacadeRequest toModifyOrderFacadeRequest(String orderNo, String ticketNo) {
        ModifyOrderFacadeRequest request = new ModifyOrderFacadeRequest();
        Map<String, String> modifiedFields = new HashMap<>();
        modifiedFields.put(ModifiedFieldEnum.TICKET_INFOS.getCode(), ModifiedFieldValueEnum.INCREMENT_UPDATE.getCode());
        request.setOrderNo(orderNo);
        PromotionFacade promotion = new PromotionFacade();
        List<TicketFacade> tickets = new ArrayList<>();
        TicketFacade ticketFacade = new TicketFacade();
        ticketFacade.setTicketNo(ticketNo);
        ticketFacade.setCouponStatus(CouponStatusEnum.ALLOW_ROLLBACK_YES.getCode());
        ticketFacade.setOperateType(OperateTypeEnum.DELETE);
        tickets.add(ticketFacade);
        promotion.setTickets(tickets);
        request.setPromotion(promotion);
        return request;
    }
}
