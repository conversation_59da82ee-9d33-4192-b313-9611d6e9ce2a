package cn.jdl.oms.express.worker.scheduler.pay;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.MoneyInfo;
import cn.jdl.oms.core.model.ReaddressRecordDetailInfo;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.dto.BusinessIdentityDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.flow.ordertrack.CommonOrderTrackFlowNode;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.ModifyOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.RetailOrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.pay.RefundFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderModelCreatorTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ProductFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.RefOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.QueryOrderBankPayDetailsResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.QueryOrderBankResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.MerchantUtils;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.OTSLedgerUtil;
import cn.jdl.oms.express.domain.infrs.acl.pl.pay.RefundRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.pay.RefundTranslator;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedBusinessIdentityUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.OrderStatusNotifyMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisClient;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisLock;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisLockFactory;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ModifyRepositoryMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.infrs.ohs.locals.thread.ThreadPoolExecutorService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.lock.LockEntry;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AdministrativeRegionEnum;
import cn.jdl.oms.express.domain.spec.dict.AfterSalesTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.BatchOrderSignEnum;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.ModifyRecordTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSignEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderExtendTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.RefundStatusEnum;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.RefundInfoVo;
import cn.jdl.oms.express.domain.vo.RefundRecord;
import cn.jdl.oms.express.domain.vo.record.ModifyRecord;
import cn.jdl.oms.express.domain.vo.record.ModifyRecordDelegate;
import cn.jdl.oms.express.domain.vo.record.ModifyRecordRefundDetail;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.FinanceConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.express.shared.common.dict.CancelTypeEnum;
import cn.jdl.oms.express.shared.common.dict.MerchantEnum;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.exception.ValidationRequestParamException;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.worker.scheduler.AbstractSchedulerHandler;
import com.jd.matrix.core.domain.flow.InputMessage;
import com.jd.paq.core.Message;
import com.jd.paq.plugin.IMessage;
import com.jd.paq.plugin.PDQClientException;
import com.jd.paq.plugin.Result;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * @Package: cn.jdl.oms.express.worker.scheduler.pay
 * @ClassName: RefundOrderHandler
 * @Description:
 * @Author: liufarui
 * @CreateDate: 2021/4/13
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version: V1.0
 */
public class RefundOrderHandler extends AbstractSchedulerHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(RefundOrderHandler.class);

    /**
     * 重试最大次数
     */
    private static final int MAX_RETRY_TIMES = 10;

    /**
     * 实收明细为空，重试最大次数
     */
    private static final int NO_PAY_MAX_RETRY_TIMES = 6;

    /**
     * 防并发锁过期时间，60分钟
     */
    private static final int CACHE_TIMEOUT = 60 * 60;

    /**
     * 防并发锁前缀
     * 商户号 + LOCK_PREFIX
     */
    private static final String LOCK_PREFIX = "REFUND_";

    //@Value("${jsf.OrderResource.o2oMerchantId}")
    //private String o2OMerchantId;

    /**
     * 账号类型(1-京东账号 2-erp账号)
     */
    private static final int ACCOUNT_TYPE_PIN = 1;
    private static final int ACCOUNT_TYPE_ERP = 2;

    private static final int REASON_MAX_LENGTH = 100;

    @Resource
    RetailOrderBankFacade retailOrderBankFacade;

    @Resource
    private GetOrderFacade getOrderFacade;

    @Resource
    private RefundFacade refundFacade;

    @Resource
    private IRedisLockFactory redisLockFactory;

    @Resource
    private IRedisClient redisClient;

    @Resource
    private RefundTranslator translator;

    /**
     * 生产者
     */
    @Resource
    private JMQMessageProducer orderStatusNotifyJmqProducer;

    /**
     * 线程池异步服务
     */
    @Resource
    private ThreadPoolExecutorService threadPoolExecutorService;


    /**
     * 订单模型的契约对象转换
     */
    @Resource
    private GetOrderModelCreatorTranslator orderModelCreatorTranslator;

    @Resource
    private CommonOrderTrackFlowNode commonOrderTrackFlowNode;

    /**
     * 订单信息修改防腐层转换器
     */
    @Resource
    private ModifyOrderFacadeTranslator modifyOrderFacadeTranslator;

    /**
     * 订单信息修改
     */
    @Resource
    private ModifyOrderFacade modifyOrderFacade;

    /**
     * pdq
     */
    @Resource
    SchedulerService schedulerService;

    /**
     * 报警
     */
    @Resource
    private UmpUtil umpUtil;

    /**
     * 云柜识别逻辑，站点子类型 48
     */
    private final static int YUNGUI_TYPE = 48;

    @Override
    public Result execute(IMessage iMessage) throws PDQClientException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_WORKER_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        // 任务调度执行结果对象根据实际的执行结果定义返回具体的业务结果
        Result result = new Result(Result.SUCCESS);
        result.setCode(1);
        try {
            if (iMessage instanceof Message) {
                String iMessageContent = iMessage.getMessageBody();
                SchedulerMessage schedulerMessage = JSONUtils.jsonToBean(iMessageContent, SchedulerMessage.class);
                if (null == schedulerMessage) {
                    LOGGER.info("退款任务调度【{}】,场景业务数据对象不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }

                RefundRequest refundRequest = (RefundRequest) JSONUtils.jsonToBean(schedulerMessage.getDtoJson(), schedulerMessage.getDtoClass());
                if (refundRequest == null) {
                    LOGGER.info("退款任务调度【{}】,场景业务数据对象不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                if (((Message) iMessage).getRedriveCount() >= MAX_RETRY_TIMES) {
                    //超过重试次数不能修改为失败
                    //refundFacade.modifyOrderRefund(refundRequest, RefundStatusEnum.REFUNDFAILED);
                    LOGGER.info("退款任务调度【{}】,重试次数超过" + MAX_RETRY_TIMES + "次,暂停重试,退款任务置为失败！", iMessage.getTopic());
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }

                PDQTopicEnum pdqTopicEnum = PDQTopicEnum.getByTopic(iMessage.getTopic());
                if (null == pdqTopicEnum) {
                    LOGGER.info("退款任务调度【{}】,未匹配到任务队列,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }

                GetOrderFacadeRequest getOrderFacadeRequest = new GetOrderFacadeRequest();
                getOrderFacadeRequest.setOrderNo(refundRequest.getOrderNo());

                RequestProfile requestProfile = new RequestProfile();
                requestProfile.setTimeZone(refundRequest.getTimeZone());
                requestProfile.setTenantId(refundRequest.getTenantId());
                requestProfile.setLocale(refundRequest.getLocale());
                requestProfile.setTraceId(refundRequest.getTraceId());

                // 获取订单状态和财务相关信息
                GetOrderFacadeResponse response = getOrderFacade.getOrder(requestProfile, getOrderFacadeRequest);

                // 融合身份转换
                UnitedBusinessIdentityUtil.convertOrderToReal(response);

                if(refundRequest.isReaddress1Order2End()){
                    readdress1Order2EndRefund(response,refundRequest, ((Message) iMessage).getRedriveCount());
                } else {
                    // 退款
                    refund(response,refundRequest,requestProfile, ((Message) iMessage).getRedriveCount());
                }
            }
        } catch (BusinessDomainException be) {
            //不用体现可用率
            LOGGER.error("退款业务异常,再次重试", be);
            result.setCode(Result.SYSTEMERROR);
            result.setReason(be.getMessage());
            return result;
        } catch (Exception e) {
            LOGGER.error("退款任务调度执行异常,再次重试", e);
            result.setCode(Result.SYSTEMERROR);
            result.setReason(e.getMessage());
            Profiler.functionError(callerInfo);
            return result;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        return result;
    }

    /**
     * 功能: 退款
     *
     * @param:
     * @return:
     * @throw:
     * @description: 包含六步：
     * 1. 入参合法性校验
     * 2. 获取订单状态和财务相关信息
     * 3. 订单合法性校验
     * 4. 调外单台账获取实收明细
     * 5. 查询外单台账的有效台账总账
     * 6. 调用外单台账退款接口进行退款
     * @author: liufarui
     * @date: 2021/6/1 9:56 下午
     */
    private void refund(GetOrderFacadeResponse order, RefundRequest refundRequest,RequestProfile requestProfile, int retryTimes) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".refund"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        IRedisLock redisLock = null;
        try {
            String refundRequestStr = JSONUtils.beanToJSONDefault(refundRequest);
            LOGGER.info("退款入参：refundRequest={}", refundRequestStr);
            //将订单详情转换成model
            ExpressOrderModelCreator modelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(order);
            ExpressOrderModel orderModel = new ExpressOrderModel(modelCreator);

            // 入参合法性校验
            validateRefundRequest(refundRequest);

            if (BusinessUnitEnum.CN_JDL_UEP_C2B.businessUnit().equals(order.getBusinessIdentity().getBusinessUnit())
                    || BusinessUnitEnum.CN_JDL_UEP_C2C.businessUnit().equals(order.getBusinessIdentity().getBusinessUnit())) {
                // 校验是否可以退款
                refundValid(order);
            }

            refundRequest = completeRefundRequest(refundRequest, order);

            //TODO 后续优化配置文件
            /*1.C2C特瞬送同城单独的商户号：10032
            2.白条代扣：10053
            4.默认商户ID：10024*/

            //外单退款业务类型
            /*1.C2C特瞬送同城：115
            2.白条代扣：134
            3.默认商户ID：111*/

            //默认商户ID：10024*
            String merchantId = "10024";
            //默认商户ID：111
            Integer outsideOrdersRefundBusinessType = 111;

            if (BusinessUnitEnum.CN_JDL_O2O.businessUnit().equals(order.getBusinessIdentity().getBusinessUnit())) {
                //1.C2C特瞬送同城单独的商户号：10032
                merchantId = "10032";
                //1.C2C特瞬送同城：115
                outsideOrdersRefundBusinessType = 115;
            }  else if (refundRequest.isDocumentSend()) {
                // 证件寄递
                merchantId = MerchantEnum.DOCUMENT_DELIVERY.getMerchantId();
                outsideOrdersRefundBusinessType = MerchantEnum.DOCUMENT_DELIVERY.getOutsideOrdersRefundBusinessType();
            } else if (refundRequest.isJiSong()) {
                // 跨城急送
                merchantId = MerchantEnum.O2O_C.getMerchantId();
                outsideOrdersRefundBusinessType = MerchantEnum.O2O_C.getOutsideOrdersRefundBusinessType();
            } else if (order.getFinance().getPayment() != null &&
                    (PaymentTypeEnum.BAITIAO_WITHHOLDING.getCode().intValue() == order.getFinance().getPayment()
                            || PaymentTypeEnum.ALIPAY_WITHHOLDING.getCode().intValue() == order.getFinance().getPayment())) {
                //白条 2.白条代扣：10053
                merchantId = "10053";
                //白条代扣：134
                outsideOrdersRefundBusinessType = 134;
            } else if (BusinessUnitEnum.CN_JDL_UEP_C2B.businessUnit().equals(order.getBusinessIdentity().getBusinessUnit())) {
                // uep c2b pop售后业务
                String customerInfoExtendProps = order.getExtendProps().get(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS);
                Map<String, String> customerInfoMap =  StringUtils.isBlank(customerInfoExtendProps) ? new HashMap<>() : JSONUtils.jsonToMap(customerInfoExtendProps);
                if (AfterSalesTypeEnum.SUPPLIER.getType().equals(MapUtils.getString(customerInfoMap, OrderConstants.AFTER_SALE_TYPE))) {
                    merchantId = MerchantEnum.FACTORY_DIRECT_AFTER_SALES.getMerchantId();
                    outsideOrdersRefundBusinessType = OTSLedgerUtil.OutsideOrdersRefundBusinessType.FACTORY_DIRECT_AFTER_SALES;
                } else {
                    merchantId = MerchantEnum.JDL_UEP_C2B.getMerchantId();
                    outsideOrdersRefundBusinessType = OTSLedgerUtil.OutsideOrdersRefundBusinessType.UEP_POP_AFTER_SALE;
                }
            } else if (BusinessUnitEnum.CN_JDL_INTL_C2C.businessUnit().equals(order.getBusinessIdentity().getBusinessUnit())) {
                // 国际 c2c 业务
                merchantId = MerchantEnum.INTL_C2C_CASH_ON_PICK.getMerchantId();
                outsideOrdersRefundBusinessType = OTSLedgerUtil.getOutsideOrdersRefundBusinessType(merchantId);
            } else if (BusinessUnitEnum.CN_JDL_CC_B2B.businessUnit().equals(order.getBusinessIdentity().getBusinessUnit())
                    && order.getOrderSign() != null && BatchOrderSignEnum.BATCH_ORDER.getCode().equals(order.getOrderSign().get(OrderSignEnum.BATCH_ORDER.getCode()))) {
                merchantId = MerchantEnum.COLD_CHAIN_PAYMENT.getMerchantId();
                outsideOrdersRefundBusinessType = 146;
                //冷链整车外单退款机构id为运输erpID
                if (order.getFinance() != null && order.getFinance().getExtendProps() != null) {
                    String transportErpId = order.getFinance().getExtendProps().get(AttachmentKeyEnum.TRANSPORT_ERP_ID.getKey());
                    if (StringUtils.isEmpty(transportErpId)) {
                        refundRequest.setOrgId(Integer.valueOf(transportErpId));
                    } else {
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL).withCustom("冷链整车-外单退款运输机构id不能为空");
                    }
                }
            } else if (BusinessUnitEnum.CN_JDL_UEP_C2C.businessUnit().equals(order.getBusinessIdentity().getBusinessUnit())){
                // uep c2c 退款业务
                merchantId = MerchantEnum.JDL_UEP.getMerchantId();
                outsideOrdersRefundBusinessType = 1028;
            } else if (isFreightBusinessUnit(order)) {
                // 快运揽收后改址，寄付现结，多退少补流程，退原单金额
                if (order.getFinance() != null
                        && (PaymentTypeEnum.ONLINE_PAY.getCode().equals(order.getFinance().getPayment())
                        || PaymentTypeEnum.PAY_BEFORE_PICKUP.getCode().equals(order.getFinance().getPayment()))) {
                    // 在线支付，10133
                    merchantId = MerchantEnum.FREIGHT_ONLINE_PAY_AFTER_PICKUP.getMerchantId();
                    // 1035
                    outsideOrdersRefundBusinessType = OTSLedgerUtil.getOutsideOrdersRefundBusinessType(merchantId);
                } else {
                    // 非在线支付，10033
                    merchantId = MerchantEnum.FREIGHT.getMerchantId();
                    // 114
                    outsideOrdersRefundBusinessType = OTSLedgerUtil.getOutsideOrdersRefundBusinessType(merchantId);
                }
            } else if (BusinessUnitEnum.CN_JDL_TMS_ZX.businessUnit().equals(order.getBusinessIdentity().getBusinessUnit())){
                // 专线包仓退款业务
                merchantId = MerchantEnum.TMS_ZXBC.getMerchantId();
                outsideOrdersRefundBusinessType = OTSLedgerUtil.getOutsideOrdersRefundBusinessType(merchantId);
            } else if (BusinessUnitEnum.CN_JDL_B2C.businessUnit().equals(order.getBusinessIdentity().getBusinessUnit())){
                if (order.isJG12123()) {
                    // B2C交管12123
                    merchantId = MerchantEnum.JG12123.getMerchantId();
                    outsideOrdersRefundBusinessType = MerchantEnum.JG12123.getOutsideOrdersRefundBusinessType();
                } else if(order.isCustomsServiceOrder()){
                    merchantId = MerchantEnum.HM_CUSTOMS_SERVICE.getMerchantId();
                    outsideOrdersRefundBusinessType = MerchantEnum.HM_CUSTOMS_SERVICE.getOutsideOrdersRefundBusinessType();
                }
            } else if (BusinessUnitEnum.CN_JDL_C2C.businessUnit().equals(order.getBusinessIdentity().getBusinessUnit())){
                if(order.isCustomsServiceOrder()){
                    merchantId = MerchantEnum.HM_CUSTOMS_SERVICE.getMerchantId();
                    outsideOrdersRefundBusinessType = MerchantEnum.HM_CUSTOMS_SERVICE.getOutsideOrdersRefundBusinessType();
                }
            } else if (BusinessUnitEnum.CN_JDL_C2B.businessUnit().equals(order.getBusinessIdentity().getBusinessUnit())) {
                MerchantEnum merchantEnum = MerchantUtils.getMerchantIdEnum(orderModel);
                if (merchantEnum != null) {
                    merchantId = merchantEnum.getMerchantId();
                }
                outsideOrdersRefundBusinessType = OTSLedgerUtil.getOutsideOrdersRefundBusinessType(merchantId);
            }

            String orderBankOrderNo = refundRequest.getWaybillCode();
            if(order.isJG12123()){
                // 交管12123支付单是用商家单号写账
                orderBankOrderNo = order.getChannel().getCustomerOrderNo();
            }

            if(order.isCustomsServiceOrder()){
                orderBankOrderNo = order.getCustomOrderNo();
            }

            LOGGER.info("orderNo={},orderBankOrderNo={},merchantId={},outsideOrdersRefundBusinessType={}",
                    refundRequest.getOrderNo(), orderBankOrderNo, merchantId, outsideOrdersRefundBusinessType);

            redisLock = tryLock(refundRequest.getOrderNo(), orderBankOrderNo, merchantId);
            if (null == redisLock) {
                LOGGER.error("退款-重复执行，直接返回，orderNo:{}，orderBankOrderNo:{}", refundRequest.getOrderNo(), orderBankOrderNo);
                return;
            }

            // 判断是否是云柜订单
            boolean isYunGui = isYunGui(order);

            // 是否需要退款
            if (!ifNeedRefund(refundRequest, order, isYunGui)) {
                LOGGER.info("订单{}不需要退款", refundRequest.getOrderNo());
                LOGGER.info("退款-释放锁: {}", redisLock.getRedisValue());
                redisLock.unlock();
                LOGGER.info("退款-释放锁成功");
                return;
            }

            // 订单合法性校验
            validateRefundOrder(refundRequest, order);

            // 重复消费校验
            Map<Long, RefundRecord> refundRecords = null;
            if (refundRequest.isSaveRefundInfo() && null != refundRequest.getRefId()) {
                Map<String, String> financeExt = order.getFinance().getExtendProps();
                if (null != financeExt && financeExt.containsKey(FinanceConstants.REFUND_INFO_LIST)) {
                    String refundInfoList = financeExt.get(FinanceConstants.REFUND_INFO_LIST);
                    refundRecords = JSONUtils.jsonToMap(refundInfoList, Long.class, RefundRecord.class);
                    if (refundRecords.containsKey(refundRequest.getRefId())) {
                        Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_REFUND_EXCEPTION_ALARM, "退款流水已存在，重复消费 " + refundRequest.getRefId());
                        return;
                    }
                } else {
                    refundRecords = new HashMap<>();
                }
            }

            QueryOrderBankPayDetailsResponse queryOrderBankPayDetailsResponse = null;
            // 部分退，查询当前可退明细 // 其他：全退，调外单台账获取实收明细
            // 涉及部分退流程：快运揽收后改址，寄付现结，多退少补流程
            queryOrderBankPayDetailsResponse = retailOrderBankFacade.getRefundableAmountAndPayDetails(orderBankOrderNo, merchantId);
            LOGGER.info("外单台账获取实收明细结果:{}", JSONUtils.beanToJSONDefault(queryOrderBankPayDetailsResponse));
            // 未获取到实收明细
            if (CollectionUtils.isEmpty(queryOrderBankPayDetailsResponse.getPayDetails())) {
                LOGGER.info("外单台账获取实收明细信息为空");
                //外单台账获取实收明细为空，则进行重试，超过重试最大次数，则
                if (retryTimes > NO_PAY_MAX_RETRY_TIMES) {
                    if (PaymentStatusEnum.COMPLETE_PAYMENT == refundRequest.getPaymentStatus()) {
                        LOGGER.error("外单台账获取实收明细为空，但是支付状态为已支付，退款入参：refundRequest={}", refundRequestStr);
                        Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_REFUND_EXCEPTION_ALARM
                                , System.currentTimeMillis()
                                        + "链路追踪ID:" + refundRequest.getTraceId()
                                        + ","
                                        + "申请租户:" + refundRequest.getTenantId()
                                        + ","
                                        + "业务身份:" + refundRequest.getOrderBusinessIdentity().getBusinessUnit()
                                        + ","
                                        + "业务类型:" + refundRequest.getOrderBusinessIdentity().getBusinessType()
                                        + ","
                                        + "外单台账获取实收明细为空，但是支付状态为已支付，退款订单号:" + refundRequest.getOrderNo());
                        //退款失败以接受退款结果为准
                        //refundFacade.modifyOrderRefund(refundRequest, RefundStatusEnum.REFUNDFAILED);
                    }
                    return;
                } else {
                    LOGGER.warn("运单{}外单台账未查到实收明细，进行业务重试，进行第{}次重试，最大重试次数{}", orderBankOrderNo, retryTimes, NO_PAY_MAX_RETRY_TIMES);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.EPT_REFUND_APPLY_FAIL).withCustom("外单台账未查到实收明细，且未达到重试次数，进行业务重试");
                }
            }

            // 商户收单号
            // 商户号+订单号生成对应的一个id号
            String merchantOrderNo = queryOrderBankPayDetailsResponse.getMerchantOrderNo();
            // 如果返回值里面缺少商户收单号，则从实收数据里面取
            if (StringUtils.isBlank(merchantOrderNo)
                    && CollectionUtils.isNotEmpty(queryOrderBankPayDetailsResponse.getPayDetails())) {
                merchantOrderNo = queryOrderBankPayDetailsResponse.getPayDetails().get(0).getMerchantOrderNo();
            }

            // 校验外单台账商户收单号
            if (StringUtils.isBlank(merchantOrderNo)) {
                LOGGER.error("外单台账获取实收明细，商户收单号为空");
                throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.QUERY_ORDER_BANK_FAIL_DETAIL).withCustom("商户收单号为空");
            }

            // 查询外单台账的有效台账总账
            QueryOrderBankResponse queryOrderBankResponse = retailOrderBankFacade.queryOrderBank(orderBankOrderNo, merchantId, merchantOrderNo);

            // 调外单台账退款申请接口申请退款
            Long refundId = refundFacade.refund(orderBankOrderNo, refundRequest, queryOrderBankPayDetailsResponse, queryOrderBankResponse, outsideOrdersRefundBusinessType);

            // 外单退款接口调用成功后，退款状态改为退款中
            if (null != refundId) {
                // 已经确认，支持幂等，所以在可能出现的多次发送的情况下，不影响实际运行结果
                Map<String, String> extendProps = new HashMap<>();
                MoneyInfo moneyInfo = new MoneyInfo();
                //退款金额
                moneyInfo.setAmount(queryOrderBankResponse.getRealPayPrice());
                CurrencyCodeEnum currency = CurrencyCodeEnum.ofOts(queryOrderBankResponse.getCurrency());
                if (null != currency) {
                    moneyInfo.setCurrencyCode(currency.getCode());
                } else {
                    moneyInfo.setCurrencyCode(CurrencyCodeEnum.CNY.getCode());
                }
                if (BusinessUnitEnum.CN_JDL_UEP_C2C.businessUnit().equals(order.getBusinessIdentity().getBusinessUnit())) {
                    extendProps.put(FinanceConstants.REFUND_MONEY, JSONUtils.beanToJSONDefault(moneyInfo));
                }

                //金额放在财务扩展字段
                boolean ifSuccessModify = false;
                // 部分退
                if (refundRequest.isPartRefund() && refundRequest.isSaveRefundInfo()) {
                    // 更新退款记录
                    updateRefundInfo(refundRecords, refundRequest, extendProps);
                    ifSuccessModify = refundFacade.modifyOrderRefund(refundRequest, null, extendProps);
                } else {
                    //金额放在财务扩展字段
                    ifSuccessModify = refundFacade.modifyOrderRefund(refundRequest, RefundStatusEnum.REFUNDING, extendProps);
                }

                if (ifSuccessModify) {
                    LOGGER.info("退款申请接口持久化数据成功：refundRequest={}", refundRequestStr);
                    //发送mq
                    if(BusinessUnitEnum.CN_JDL_UEP_C2C.businessUnit().equals(order.getBusinessIdentity().getBusinessUnit())){
                        OrderStatusNotifyMessageDto messageDto = refundFacade.buildStatusNoticeMq(orderModel, requestProfile, moneyInfo, RefundStatusEnum.REFUNDING);
                        //发送退款中mq
                        threadPoolExecutorService.submit(() -> {
                            orderStatusNotifyJmqProducer.send(order.getOrderNo(), JSONUtils.beanToJSONDefault(messageDto), null);
                            LOGGER.info("发送退款中状态变化消息：messageDto={}", JSONUtils.beanToJSONDefault(messageDto));
                        });
                    }
                    // TODO 暂时先不做本地持久化
                    //todo 持久化失败扔重试pdq里

                    // refundService.saveToDB(refundRequest);

                    // 云柜发退款申请全程跟踪
                    if (isYunGui || orderModel.isC2B()) {
                        InputMessage inputMessage = generateInputMessage(refundId, order, requestProfile);
                        commonOrderTrackFlowNode.call(inputMessage);
                    }

                    // 存在关联支付单，更新支付单退款状态
                    String payRefOrderNo = getPayRefOrderNo(order);
                    if (StringUtils.isNotBlank(payRefOrderNo)) {
                        updateRefundStatusForPayRefOrder(requestProfile, payRefOrderNo, order.getOrderNo());
                    }
                } else {
                    //todo持久化pdq
                    LOGGER.error("退款申请接口持久化数据失败：refundRequest={}", refundRequestStr);
                }
            } else {
                throw new InfrastructureException(UnifiedErrorSpec.BasisOrder.EPT_REFUND_APPLY_FAIL).withCustom("退款申请失败");
            }
        } catch (ValidationRequestParamException ve) {
            //需要体现可用率关注
            Profiler.functionError(callerInfo);
            LOGGER.error("退款校验异常,需排查订单信息是否异常", ve);
            throw ve;
        } catch (BusinessDomainException be) {
            if (be.getDooErrorSpec() == UnifiedErrorSpec.BasisOrder.NOT_NEED_RETRY) {
                LOGGER.error("退款业务异常, 无需重试", be);
                return;
            }
            //不用体现可用率
            LOGGER.error("退款业务异常", be);
            throw be;
        } catch (InfrastructureException ie) {
            //需要体现可用率关注
            Profiler.functionError(callerInfo);
            LOGGER.error("退款基础设施服务异常", ie);
            throw ie;
        } catch (Exception e) {
            //需要体现可用率关注
            LOGGER.error("退款未知异常", e);
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            if (null != redisLock) {
                LOGGER.info("退款-释放锁: {}", redisLock.getRedisValue());
                redisLock.unlock();
                LOGGER.info("退款-释放锁成功");
            }
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 获取关联的支付单单号
     * @param order
     * @return
     */
    private String getPayRefOrderNo(GetOrderFacadeResponse order) {
        if(MapUtils.isEmpty(order.getExtendRefOrder())){
            LOGGER.info("不存在关联的支付单");
            return null;
        }
        return order.getExtendRefOrder().get(RefOrderExtendTypeEnum.PAY_ORDER_NO.getCode());
    }

    /**
     * 更新退款信息
     * @param refundRecords
     * @param refundRequest
     * @param extendProps
     */
    private void updateRefundInfo(Map<Long, RefundRecord> refundRecords, RefundRequest refundRequest, Map<String, String> extendProps) {
        RefundRecord refundRecord = new RefundRecord();
        refundRecord.setRefundId(refundRequest.getRefId());
        refundRecord.setRefundStatus(RefundStatusEnum.REFUNDING.getStatus());
        refundRecord.setFeeType(refundRequest.getFeeType());
        refundRecords.put(refundRecord.getRefundId(), refundRecord);
        extendProps.put(FinanceConstants.REFUND_INFO_LIST, JSONUtils.beanToJSONDefault(refundRecords));
    }

    /**
     * 功能: 入参合法性校验
     *
     * @param:
     * @return:
     * @throw:
     * @description:
     * @author: liufarui
     * @date: 2021/6/1 9:56 下午
     */
    private void validateRefundRequest(RefundRequest refundRequest) {
        LOGGER.info("退款参数入参合法性校验,refundRequest:{}", refundRequest);
        if (StringUtils.isBlank(refundRequest.getWaybillCode())) {
            LOGGER.error("退款接口-运单号为空");
            throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("退款接口-运单号为空");
        }
        if (StringUtils.isBlank(refundRequest.getOrderNo())) {
            LOGGER.error("退款接口-订单号为空");
            throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("退款接口-订单号为空");
        }
        if (ACCOUNT_TYPE_PIN != refundRequest.getAccountType() && ACCOUNT_TYPE_ERP != refundRequest.getAccountType()) {
            LOGGER.error("退款接口-用户账号类型有误");
            throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("款接口-用户账号类型有误");
        }
        if (StringUtils.isBlank(refundRequest.getUserAccount())) {
            LOGGER.error("退款接口-用户账号为空");
            throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("退款接口-用户账号为空");
        }
        if (StringUtils.isBlank(refundRequest.getReason())) {
            refundRequest.setReason(CancelTypeEnum.CT_1006.getDesc());
            /*LOGGER.error("退款接口-退款原因为空");
            throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("退款接口-退款原因为空");*/
        }
        if (refundRequest.getReason().length() > REASON_MAX_LENGTH) {
            LOGGER.error("退款接口-退款原因长度不可超过100位");
            throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("退款接口-退款原因长度不可超过100位");
        }
    }

    private void refundValid(GetOrderFacadeResponse getOrderFacadeResponse) {
        if (!OrderStatusEnum.CANCELED.getCode().equals(getOrderFacadeResponse.getOrderStatus())) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL)
                    .withCustom("订单状态不为已取消");
        }

        if (PaymentStatusEnum.COMPLETE_PAYMENT != PaymentStatusEnum.of(getOrderFacadeResponse.getFinance().getPaymentStatus())) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL)
                    .withCustom("支付状态不为已支付");
        }

        RefundStatusEnum refundStatus = RefundStatusEnum.of(getOrderFacadeResponse.getFinance().getRefundStatus());
        if (RefundStatusEnum.REFUNDED == refundStatus || RefundStatusEnum.REFUNDING == refundStatus) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL)
                    .withCustom("退款状态不允许发起退款");
        }

        if (BigDecimal.ZERO.compareTo(getOrderFacadeResponse.getFinance().getDiscountAmount().getAmount()) >= 0) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                    .withCustom("支付金额不大于0");
        }

    }

    /**
     * 功能: 订单合法性校验
     *
     * @param:
     * @return:
     * @throw:
     * @description:
     * @author: liufarui
     * @date: 2021/6/1 9:55 下午
     */
    private void validateRefundOrder(RefundRequest refundRequest, GetOrderFacadeResponse order) {
        LOGGER.info("退款订单合法性校验, refundRequest: {}", refundRequest);

        // 交易业务单元：cn_jdl_c2c和cn_jdl_o2o（或用单据子类型orderSubType：C2C-C2C业务类型）
        if (null == refundRequest.getOrderBusinessIdentity()) {
            throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL)
                    .withCustom("退款接口-业务单元为空");
        }

        String businessType = refundRequest.getOrderBusinessIdentity().getBusinessType();
        if (StringUtils.isEmpty(businessType)) {
            LOGGER.error("退款接口-业务类型为空");
            throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL)
                    .withCustom("退款接口-业务类型为空");
        }

        if(isIntlHKMO(order)){
            LOGGER.warn("港澳拦截逆向退款不做状态校验卡控");
            return;
        }

        if (isFreightBusinessUnit(order)) {
            LOGGER.warn("快运寄付现结改址退款不做状态校验卡控");
            return;
        }

        String businessUnit = order.getBusinessIdentity().getBusinessUnit();
        if (BusinessUnitEnum.CN_JDL_TMS_ZX.businessUnit().equals(businessUnit)) {
            // TMS专线包仓支持已拦截的退款
            if (OrderStatusEnum.INTERCEPT != refundRequest.getOrderStatus()
                    && OrderStatusEnum.CANCELED != refundRequest.getOrderStatus()) {
                LOGGER.error("退款接口-订单状态非已取消/已拦截");
                throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL)
                        .withCustom("退款接口-订单状态非已取消/已拦截");
            }
        } else {
            // 订单未被取消，不做退款处理
            if (OrderStatusEnum.CANCELED != refundRequest.getOrderStatus()) {
                LOGGER.error("退款接口-订单状态非已取消");
                throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.ORDER_STATUS_VALIDATE_FAIL)
                        .withCustom("退款接口-订单状态非已取消");
            }
        }

        if (PaymentStatusEnum.COMPLETE_PAYMENT != refundRequest.getPaymentStatus()) {
            LOGGER.warn("退款接口-订单支付状态非已支付，退款不做订单支付状态校验，通过查实收台账明细去做校验");
            // 退款不做订单支付状态校验，通过查实收台账明细去做校验
        }
    }

    /**
     * 功能: 退款增加并发锁
     *
     * @param:
     * @return:
     * @throw:
     * @description:
     * @author: liufarui
     * @date: 2021/6/1 10:17 下午
     */
    private IRedisLock tryLock(String orderNo, String waybillCode, String merchantId) {
        try {
            String lockKey = orderNo + waybillCode;
            LockEntry lockEntry = new LockEntry(lockKey, CACHE_TIMEOUT, TimeUnit.SECONDS);
            lockEntry.withPrefix(LOCK_PREFIX + merchantId);
            IRedisLock redisLock = redisLockFactory.create(redisClient, lockEntry);
            if (redisLock.tryLock()) {
                LOGGER.info("退款-加锁成功，orderNo:{}，waybillCode:{}", orderNo, waybillCode);
                return redisLock;
            } else {
                LOGGER.error("退款-加锁失败，orderNo:{}，waybillCode:{}", orderNo, waybillCode);
                return null;
            }
        } catch (Exception e) {
            LOGGER.error("退款-并发锁写入异常:", e);
            throw e;
        }
    }

    /**
     * 功能: 改址记录-退款增加并发锁
     */
    private IRedisLock tryLockReaddress(String orderNo, String waybillCode) {
        try {
            String lockKey = orderNo + waybillCode;
            LockEntry lockEntry = new LockEntry(lockKey, CACHE_TIMEOUT, TimeUnit.SECONDS);
            lockEntry.withPrefix(LOCK_PREFIX);
            IRedisLock redisLock = redisLockFactory.create(redisClient, lockEntry);
            if (redisLock.tryLock()) {
                LOGGER.info("退款-加锁成功，lockKey:{}", lockEntry.lockKey());
                return redisLock;
            } else {
                LOGGER.error("退款-加锁失败，lockKey:{}", lockEntry.lockKey());
                return null;
            }
        } catch (Exception e) {
            LOGGER.error("退款-并发锁写入异常:", e);
            throw e;
        }
    }

    /**
     * 功能: 判断是否需要退款。
     *
     * @param:
     * @return:
     * @throw:
     * @description: 如果不需要退款，则直接返回true
     * @author: liufarui
     * @date: 2021/6/3 2:32 下午
     */
    private boolean ifNeedRefund(RefundRequest refundRequest, GetOrderFacadeResponse order, boolean isYunGui) {
        // 判断是否已经退款或者正在退款中
        RefundStatusEnum refundStatus = refundRequest.getRefundStatus();
        if (refundStatus == RefundStatusEnum.REFUNDED || refundStatus == RefundStatusEnum.REFUNDING) {
            LOGGER.info("退款接口-当前订单状态是：{}, 不需要退款", refundStatus.getDesc());
            return false;
        }

        if (refundRequest.isDocumentSend() || refundRequest.isJiSong()) {
            LOGGER.info("退款接口-支持退款");
            return true;
        }

        if (order.isJG12123()) {
            LOGGER.info("退款接口-交管12123");
            return true;
        }

        if (order.isC2BWeChatVideoAccount()) {
            LOGGER.info("退款接口-微信视频号取件");
            return true;
        }

        // O2O/uep-c2b订单和云柜订单和改址先款单可退款
        BusinessUnitEnum businessUnit = BusinessUnitEnum.fromCode(order.getBusinessIdentity().getBusinessUnit());
        if (BusinessUnitEnum.CN_JDL_O2O == businessUnit
                || BusinessUnitEnum.CN_JDL_UEP_C2B == businessUnit
                || BusinessUnitEnum.CN_JDL_INTL_C2C == businessUnit
                || isIntlHKMO(order)
                || BusinessUnitEnum.CN_JDL_UEP_C2C == businessUnit
                || isFreightBusinessUnit(order)
                || BusinessUnitEnum.CN_JDL_TMS_ZX == businessUnit
        ) {
            LOGGER.info("退款接口-业务类型o2o/uep-c2b/港澳/国际C2C/快运/tms");
        } else if (!isYunGui && !refundRequest.isReaddressOnline() && !PaymentTypeEnum.ifAliPay(refundRequest.getPaymentType())) {
            LOGGER.info("退款接口-业务类型非o2o/uep-c2b/港澳/国际C2C/快运/tms且非云柜且非改址先款订单且非芝麻代扣, 暂不支持退款");
            return false;
        }

        // 微信支付的情况不需要做处理
        if (PaymentTypeEnum.ifWechatPay(refundRequest.getPaymentType())) {
            LOGGER.info("退款接口-支付类型为微信支付, 暂不支持退款");
            return false;
        }

        return true;
    }

    /**
     * 功能:
     *
     * @param:
     * @return:
     * @throw:
     * @description:
     * @author: liufarui
     * @date: 2021/6/6 3:35 下午
     */
    public RefundRequest completeRefundRequest(RefundRequest refundRequest, GetOrderFacadeResponse order) {
        if (null != order.getOrderStatus()) {
            refundRequest.setOrderStatus(OrderStatusEnum.of(order.getOrderStatus()));
        }
        if (null != order.getFinance().getPayment()) {
            refundRequest.setPaymentType(PaymentTypeEnum.of(order.getFinance().getPayment()));
        }
        if (null != order.getFinance().getPaymentStatus()) {
            refundRequest.setPaymentStatus(PaymentStatusEnum.of(order.getFinance().getPaymentStatus()));
        }
        if (null != order.getFinance().getRefundStatus()) {
            refundRequest.setRefundStatus(RefundStatusEnum.of(order.getFinance().getRefundStatus()));
        }
        if (null != order.getFinance().getCollectionOrgNo()) {
            refundRequest.setOrgId(Integer.valueOf(order.getFinance().getCollectionOrgNo()));
        }
        // 客户外显单号，其实就是运单号
        if (CollectionUtils.isNotEmpty(order.getRefOrders())) {
            for (RefOrderFacade refOrder : order.getRefOrders()) {
                if (RefOrderTypeEnum.DELIVERY.getCode().equals(refOrder.getRefOrderType())) {
                    refundRequest.setWaybillCode(refOrder.getRefOrderNo());
                    break;
                }
            }
        }
        if (OrderConstants.YES_VAL.equals(MapUtils.getString(order.getOrderSign(), OrderSignEnum.DOCUMENT_SEND.getCode()))) {
            refundRequest.setDocumentSend(true);
        }
        if (null != order.getProducts()) {
            for (ProductFacade product : order.getProducts()) {
                if (ProductEnum.KCJS.getCode().equals(product.getProductNo())) {
                    refundRequest.setJiSong(true);
                    break;
                }
                if (ProductEnum.TSSTC.getCode().equals(product.getProductNo())) {
                    refundRequest.setJiSong(true);
                    break;
                }
            }
        }

        return refundRequest;
    }

    /**
     * 港澳订单
     * @param orderInfo
     * @return
     */
    public boolean isIntlHKMO(GetOrderFacadeResponse orderInfo) {
        // 获取跨境报关信息，始发目的流向存在与否，判断是否是港澳订单
        return Optional.ofNullable(orderInfo.getCustomsFacade())
                .filter(customsVo -> null != customsVo.getStartFlowDirection()
                        && null != customsVo.getEndFlowDirection()
                        && !(AdministrativeRegionEnum.CN == customsVo.getStartFlowDirection() && AdministrativeRegionEnum.CN == customsVo.getEndFlowDirection()))
                .isPresent();
    }

    /**
     * 判断是否快运
     */
    private boolean isFreightBusinessUnit(GetOrderFacadeResponse getOrderFacadeResponse) {
        String businessUnit = getOrderFacadeResponse.getBusinessIdentity().getBusinessUnit();
        return BusinessUnitEnum.CN_JDL_FREIGHT_SERVICE.businessUnit().equals(businessUnit)
                || BusinessUnitEnum.CN_JDL_FREIGHT_CONSUMER.businessUnit().equals(businessUnit)
                || UnitedB2CUtil.isUnitedFreightB2C(getOrderFacadeResponse);
    }

    /**
     * 改址一单到底退款处理逻辑
     * @param response
     * @param refundRequest
     */
    private void readdress1Order2EndRefund(GetOrderFacadeResponse response, RefundRequest refundRequest, int retryTimes) {
        LOGGER.info("改址一单到底-退款申请处理，orderNo:{},fromRecordNo:{},toRecordNo:{}",refundRequest.getOrderNo(),refundRequest.getFromModifyRecordNo(),refundRequest.getToModifyRecordNo());
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".readdress1Order2EndRefund"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        IRedisLock redisLock = null;
        try {
            ExpressOrderModelCreator orderModelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(response);
            ExpressOrderModel order = new ExpressOrderModel(orderModelCreator);
            ModifyRecordDelegate modifyRecordDelegate = order.getModifyRecordDelegate();
            String toModifyRecordNo = refundRequest.getToModifyRecordNo();
            String fromModifyRecordNo = refundRequest.getFromModifyRecordNo();
            //1 匹配to记录
            ModifyRecord toModifyRecord = Optional.ofNullable(modifyRecordDelegate.getModifyRecords())
                    .orElse(new ArrayList<>())
                    .stream().filter(modifyRecord -> toModifyRecordNo.equals(modifyRecord.getModifyRecordNo()))
                    .findFirst()
                    .orElse(null);
            //2 校验记录状态，记录退款状态，退款金额与可退金额对比
            if(null == toModifyRecord){
                LOGGER.warn("改址一单到底-待退款to记录不存在,orderNo:{},fromRecordNo:{},toRecordNo:{}",refundRequest.getOrderNo(),refundRequest.getFromModifyRecordNo(),refundRequest.getToModifyRecordNo());
                throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.REFUND_FAIL).withCustom("改址一单到底-待退款to记录不存在");
            }
            ReaddressRecordDetailInfo toReaddressRecordDetail = (ReaddressRecordDetailInfo)toModifyRecord.getModifyRecordDetail();
            FinanceInfo toRecordFinance = toReaddressRecordDetail.getFinance();
            /*if(null == toRecordFinance || RefundStatusEnum.REFUNDED.getStatus().equals(toRecordFinance.getRefundStatus())){
                LOGGER.warn("改址一单到底-待退款to记录,已退款完成，无需发起退款,orderNo:{},fromRecordNo:{},toRecordNo:{}",refundRequest.getOrderNo(),refundRequest.getFromModifyRecordNo(),refundRequest.getToModifyRecordNo());
                throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.REFUND_FAIL).withCustom("改址一单到底-待退款to记录,已退款完成，无需发起退款");
            }*/
            /*if(null != toRecordFinance.getRefundedAmount()
                    && null != toRecordFinance.getRefundedAmount().getAmount()
                    && toRecordFinance.getRefundedAmount().getAmount().add(refundRequest.getPartRefundAmount()).compareTo(toRecordFinance.getReceivableDifferenceAmount().getAmount().abs()) > 0){
                LOGGER.warn("改址一单到底-待退款to记录,退款金额异常，无需发起退款,orderNo:{},fromRecordNo:{},toRecordNo:{}",refundRequest.getOrderNo(),refundRequest.getFromModifyRecordNo(),refundRequest.getToModifyRecordNo());
                throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.REFUND_FAIL).withCustom("改址一单到底-待退款to记录,退款金额异常，无需发起退款");
            }*/
            //2.5 获取from记录，匹配from记录
            ModifyRecord fromModifyRecord = Optional.ofNullable(modifyRecordDelegate.getModifyRecords()).orElse(new ArrayList<>()).stream().filter(modifyRecord -> {
                return fromModifyRecordNo.equals(modifyRecord.getModifyRecordNo());
            }).findFirst().orElse(null);
            if(null == fromModifyRecord){
                LOGGER.warn("改址一单到底-退款from记录不存在,orderNo:{},fromRecordNo:{},toRecordNo:{}",refundRequest.getOrderNo(),refundRequest.getFromModifyRecordNo(),refundRequest.getToModifyRecordNo());
                throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.REFUND_FAIL).withCustom("改址一单到底-退款from记录不存在");
            }
            ReaddressRecordDetailInfo fromReaddressRecordDetail = (ReaddressRecordDetailInfo)fromModifyRecord.getModifyRecordDetail();
            FinanceInfo fromRecordFinance = fromReaddressRecordDetail.getFinance();
            if(null == fromRecordFinance){
                LOGGER.warn("改址一单到底-退款from记录财务信息为空，无法发起退款,orderNo:{},fromRecordNo:{},toRecordNo:{}",refundRequest.getOrderNo(),refundRequest.getFromModifyRecordNo(),refundRequest.getToModifyRecordNo());
                throw new ValidationRequestParamException(UnifiedErrorSpec.BasisOrder.REFUND_FAIL).withCustom("改址一单到底-退款from记录财务信息为空，无法发起退款");
            }
            String waybillCode = refundRequest.getWaybillCode();
            //改址默认Merchant
            MerchantEnum merchant = MerchantEnum.ONLINE_READDRESS;
            if(order.isFreight() || UnitedB2CUtil.isUnitedFreightB2C(order)){
                merchant = MerchantEnum.FREIGHT_READDRESS;
            }
            String orderBankOrderNo = fromModifyRecordNo;
            //3 匹配merchantId 及 外单台账退款业务类型【改址记录merchantId固定，原始记录得详细匹配】
            if (0 == fromModifyRecord.getModifyRecordSequence()) {
                orderBankOrderNo = waybillCode;
                merchant = MerchantUtils.getMerchantIdEnum(order,fromModifyRecord);
            } else if(ModifyRecordTypeEnum.RECHECK.getCode().equals(fromModifyRecord.getModifyRecordType())){
                merchant = MerchantUtils.getMerchantIdEnum(order,fromModifyRecord);
            }
            //分布式锁 fixme 从改址记录退款和从运单退款不公用同一个锁
            redisLock = tryLockReaddress(refundRequest.getOrderNo(), waybillCode);
            if (null == redisLock) {
                LOGGER.warn("退款-重复执行,直接返回,orderNo:{},waybillCode:{},fromRecordNo:{},toRecordNo:{}",
                        refundRequest.getOrderNo(), waybillCode,fromModifyRecordNo,toModifyRecordNo);
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.EPT_REFUND_APPLY_FAIL).withCustom("当前存在同一个记录的退款处理，后续重试");
            }


            //4 查外单台账实收总账及明细【改址记录通过记录号查，原始记录通过运单号查】
            QueryOrderBankPayDetailsResponse queryOrderBankPayDetailsResponse;
            // 部分退，查询当前可退明细
            // 涉及部分退流程：快运揽收后改址，寄付现结，多退少补流程
            queryOrderBankPayDetailsResponse = retailOrderBankFacade.getRefundableAmountAndPayDetails(orderBankOrderNo, merchant.getMerchantId());
            LOGGER.info("外单台账获取实收明细结果:{}", JSONUtils.beanToJSONDefault(queryOrderBankPayDetailsResponse));
            // 未获取到实收明细
            String refundJsonString = JSONUtils.beanToJSONDefault(refundRequest);
            if (CollectionUtils.isEmpty(queryOrderBankPayDetailsResponse.getPayDetails())) {
                LOGGER.info("外单台账获取实收明细信息为空,orderNo:{},fromRecordNo:{},toRecordNo:{}",refundRequest.getOrderNo(),refundRequest.getFromModifyRecordNo(),refundRequest.getToModifyRecordNo());
                //外单台账获取实收明细为空，则进行重试，超过重试最大次数，则
                if (retryTimes > NO_PAY_MAX_RETRY_TIMES) {
                    if (PaymentStatusEnum.COMPLETE_PAYMENT == refundRequest.getPaymentStatus()) {
                        LOGGER.error("外单台账获取实收明细为空，但是支付状态为已支付，退款入参：refundRequest={}", refundJsonString);
                        Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_REFUND_EXCEPTION_ALARM
                                , System.currentTimeMillis()
                                        + "链路追踪ID:" + refundRequest.getTraceId()
                                        + ","
                                        + "申请租户:" + refundRequest.getTenantId()
                                        + ","
                                        + "业务身份:" + refundRequest.getOrderBusinessIdentity().getBusinessUnit()
                                        + ","
                                        + "业务类型:" + refundRequest.getOrderBusinessIdentity().getBusinessType()
                                        + ","
                                        + "外单台账获取实收明细为空，但是支付状态为已支付，退款订单号:" + refundRequest.getOrderNo());
                    }
                    return;
                } else {
                    LOGGER.warn("订单{},运单{},fromRecordNo{},toRecordNo{},orderBankOrderNo{}" +
                                    ",外单台账未查到实收明细，进行业务重试，进行第{}次重试，最大重试次数{}",
                            refundRequest.getOrderNo(), waybillCode,refundRequest.getFromModifyRecordNo(),refundRequest.getToModifyRecordNo(),orderBankOrderNo
                            , retryTimes, NO_PAY_MAX_RETRY_TIMES);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.EPT_REFUND_APPLY_FAIL).withCustom("外单台账未查到实收明细，且未达到重试次数，进行业务重试");
                }
            }

            // 商户收单号
            // 商户号+订单号生成对应的一个id号
            String merchantOrderNo = queryOrderBankPayDetailsResponse.getMerchantOrderNo();
            // 如果返回值里面缺少商户收单号，则从实收数据里面取
            if (StringUtils.isBlank(merchantOrderNo)
                    && CollectionUtils.isNotEmpty(queryOrderBankPayDetailsResponse.getPayDetails())) {
                merchantOrderNo = queryOrderBankPayDetailsResponse.getPayDetails().get(0).getMerchantOrderNo();
            }

            // 校验外单台账商户收单号
            if (StringUtils.isBlank(merchantOrderNo)) {
                LOGGER.error("外单台账获取实收明细，商户收单号为空,orderNo:{},fromRecordNo:{},toRecordNo:{}",refundRequest.getOrderNo(),refundRequest.getFromModifyRecordNo(),refundRequest.getToModifyRecordNo());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.QUERY_ORDER_BANK_FAIL_DETAIL).withCustom("商户收单号为空");
            }

            // 查询外单台账的有效台账总账
            QueryOrderBankResponse queryOrderBankResponse = retailOrderBankFacade.queryOrderBank(orderBankOrderNo, merchant.getMerchantId(), merchantOrderNo);

            // 调外单台账退款申请接口申请退款
            Long refundId = refundFacade.refund(orderBankOrderNo, refundRequest, queryOrderBankPayDetailsResponse, queryOrderBankResponse, merchant.getOutsideOrdersRefundBusinessType());

            // 外单退款接口调用成功后，退款状态改为退款中
            if(null != refundId){
                LOGGER.info("退款申请成功处理，orderNo:{},fromRecordNo:{},toRecordNo:{}",refundRequest.getOrderNo(),refundRequest.getFromModifyRecordNo(),refundRequest.getToModifyRecordNo());
                //当前记录已退金额
                if(null == toRecordFinance.getRefundedAmount()){
                    toRecordFinance.setRefundedAmount(MoneyMapper.INSTANCE.toMoneyInfo(defMoneyZero()));
                }
                toRecordFinance.getRefundedAmount().setAmount(toRecordFinance.getRefundedAmount().getAmount().add(refundRequest.getPartRefundAmount()));
                //当前记录退款状态调整为退款中
                //toRecordFinance.setRefundStatus(RefundStatusEnum.REFUNDING.getStatus());


                //当前记录已退金额
                if(null == fromRecordFinance.getRefundedAmount()){
                    fromRecordFinance.setRefundedAmount(MoneyMapper.INSTANCE.toMoneyInfo(defMoneyZero()));
                }
                fromRecordFinance.getRefundedAmount().setAmount(fromRecordFinance.getRefundedAmount().getAmount().add(refundRequest.getPartRefundAmount()));

                //当前记录剩余待支付金额【先款的已经支付成功不更新待支付金额】
                //fromRecordFinance.getPendingMoney().setAmount(fromRecordFinance.getPendingMoney().getAmount().subtract(refundRequest.getPartRefundAmount()));

                //当前记录退款状态调整为退款中
                fromRecordFinance.setRefundStatus(RefundStatusEnum.REFUNDING.getStatus());

                readdress1Order2EndRecordRefundDetail(fromModifyRecord,toModifyRecord,refundRequest.getPartRefundAmount(),refundRequest.getRefId());

                if(null == order.getFinance().getRefundedAmount()){
                    order.getFinance().setRefundedAmount(defMoneyZero());
                }
                order.getFinance().getRefundedAmount().setAmount(order.getFinance().getRefundedAmount().getAmount().add(refundRequest.getPartRefundAmount()));
                order.getFinance().setRefundStatus(RefundStatusEnum.REFUNDING);
                LOGGER.info("退款成功持久化处理，orderNo:{},order:{},fromRecord:{},toRecord:{}", order.orderNo(),JSONUtils.beanToJSONDefault(order.getFinance()),JSONUtils.beanToJSONDefault(fromModifyRecord),JSONUtils.beanToJSONDefault(toModifyRecord));
                //退款发起持久化
                //todo liqiang262 后续优化；持久化失败场景，简单抛异常重试，会导致重复退款申请
                boolean refundDbSuccess = refundFacade.modifyReaddress1Order2EndOrderRefund(refundRequest, order, fromModifyRecord);
                if(!refundDbSuccess){
                    LOGGER.error("退款-申请持久化异常,后续重试,orderNo:{},waybillCode:{},fromRecordNo:{},toRecordNo:{}",
                            refundRequest.getOrderNo(), waybillCode,fromModifyRecordNo,toModifyRecordNo);
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.EPT_REFUND_APPLY_FAIL).withCustom("退款-申请持久化异常,后续重试");
                }
            }
            LOGGER.info("改址一单到底-退款申请成功，orderNo:{},fromRecordNo:{},toRecordNo:{}",refundRequest.getOrderNo(),refundRequest.getFromModifyRecordNo(),refundRequest.getToModifyRecordNo());
        } catch (ValidationRequestParamException ve) {
            // todo 稳定后去除报警
            refundExceptionAlarmC2BWeChatVideoAccount(response);
            //需要体现可用率关注
            Profiler.functionError(callerInfo);
            LOGGER.error("退款校验异常,需排查订单信息是否异常", ve);
            throw ve;
        } catch (BusinessDomainException be) {
            //不用体现可用率
            LOGGER.error("退款业务异常", be);
            throw be;
        } catch (InfrastructureException ie) {
            // todo 稳定后去除报警
            refundExceptionAlarmC2BWeChatVideoAccount(response);
            //需要体现可用率关注
            Profiler.functionError(callerInfo);
            LOGGER.error("退款基础设施服务异常", ie);
            throw ie;
        } catch (Exception e) {
            // todo 稳定后去除报警
            refundExceptionAlarmC2BWeChatVideoAccount(response);
            //需要体现可用率关注
            LOGGER.error("退款未知异常", e);
            Profiler.functionError(callerInfo);
            throw e;
        } finally {
            if (null != redisLock) {
                LOGGER.info("退款-释放锁: {}", redisLock.getRedisValue());
                redisLock.unlock();
                LOGGER.info("退款-释放锁成功");
            }
            Profiler.registerInfoEnd(callerInfo);
        }
    }


    /**
     * 构建新的退款明细
     * @param fromRecord
     * @param toRecord
     * @param refundAmount
     */
    private void readdress1Order2EndRecordRefundDetail(ModifyRecord fromRecord, ModifyRecord toRecord, BigDecimal refundAmount,Long refundOrderNo){
        //最新退款记录
        ModifyRecordRefundDetail modifyRecordRefundDetail = new ModifyRecordRefundDetail();
        modifyRecordRefundDetail.setFromRecordNo(fromRecord.getModifyRecordNo());
        modifyRecordRefundDetail.setToRecordNo(toRecord.getModifyRecordNo());
        modifyRecordRefundDetail.setRefundAmount(refundAmount);
        modifyRecordRefundDetail.setRefundDatetime(new Date());
        modifyRecordRefundDetail.setRefundStatus(RefundStatusEnum.REFUNDING.getStatus());
        modifyRecordRefundDetail.setRefundOrderNo(refundOrderNo);
        readdress1Order2EndAppendRefundRecord(fromRecord,modifyRecordRefundDetail);
        //readdress1Order2EndAppendRefundRecord(toRecord,modifyRecordRefundDetail);
    }

    /**
     * 追加退款记录
     * @param record
     * @param modifyRecordRefundDetail
     */
    private void readdress1Order2EndAppendRefundRecord(ModifyRecord record, ModifyRecordRefundDetail modifyRecordRefundDetail){
        if(MapUtils.isEmpty(record.getExtendProps())){
            record.setExtendProps(new HashMap<>());
        }
        List<ModifyRecordRefundDetail> modifyRecordRefundDetails;
        if(StringUtils.isEmpty(record.getExtendProps().get(ModifyRecordDelegate.REFUND_DETAILS))){
            modifyRecordRefundDetails = new ArrayList<>();
        } else {
            modifyRecordRefundDetails = JSONUtils.jsonToList(record.getExtendProps().get(ModifyRecordDelegate.REFUND_DETAILS), ModifyRecordRefundDetail.class);
        }
        modifyRecordRefundDetails.add(modifyRecordRefundDetail);
        record.getExtendProps().put(ModifyRecordDelegate.REFUND_DETAILS,JSONUtils.beanToJSONDefault(modifyRecordRefundDetails));
    }

    private static Money defMoneyZero(){
        Money money = new Money();
        money.setAmount(BigDecimal.ZERO);
        money.setCurrency(CurrencyCodeEnum.CNY);
        return money;
    }

    private boolean isYunGui(GetOrderFacadeResponse order) {
        boolean isYunGui = false;
        if (order.getShipment() != null && order.getShipment().getStartStationType() != null && YUNGUI_TYPE == order.getShipment().getStartStationType()) {
            LOGGER.info("是云柜订单");
            isYunGui = true;
        }
        return isYunGui;
    }

    private InputMessage generateInputMessage(Long refundId, GetOrderFacadeResponse order, RequestProfile requestProfile) {

        BusinessIdentityDto businessIdentity = new BusinessIdentityDto();
        businessIdentity.setBusinessUnit(order.getBusinessIdentity().getBusinessUnit());
        businessIdentity.setBusinessType(order.getBusinessIdentity().getBusinessType());
        businessIdentity.setBusinessScene(BusinessSceneEnum.REFUND.getCode());
        ExpressOrderModelCreator creator = new ExpressOrderModelCreator();
        creator.setBusinessIdentity(businessIdentity);

        ExpressOrderModel orderModel = new ExpressOrderModel(creator).withRequestProfile(requestProfile);
        RefundInfoVo refundInfoVo = new RefundInfoVo();
        refundInfoVo.setChannelRefundOrderNo(String.valueOf(refundId));
        orderModel.setRefundInfoVo(refundInfoVo);

        ExpressOrderModelCreator snapshotCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(order);
        ExpressOrderModel snapshot = new ExpressOrderModel(snapshotCreator);
        orderModel.assignSnapshot(snapshot);

        ExpressOrderContext orderContext = new ExpressOrderContext(orderModel.getOrderBusinessIdentity(), requestProfile, orderModel.getOrderBusinessIdentity().getBusinessScene());
        orderContext.setOrderModel(orderModel);
        return new InputMessage(orderContext);
    }

    /**
     * 持久化异常处理
     */
    private void produceRetryMq(RequestProfile requestProfile ,ModifyOrderFacadeRequest modifyOrderSomeDataRequest) {
        SchedulerMessage schedulerMessage = new SchedulerMessage();
        //持久化消息
        ModifyRepositoryMessageDto messageDto = new ModifyRepositoryMessageDto();
        messageDto.setRequestProfile(requestProfile);
        messageDto.setModifyOrderFacadeRequest(modifyOrderSomeDataRequest);
        schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(messageDto));
        schedulerMessage.setDtoClass(ModifyRepositoryMessageDto.class);
        schedulerService.addSchedulerTask(PDQTopicEnum.REVERSE_REPOSITORY_RETRY, schedulerMessage, this.getClass().getSimpleName());
    }

    /**
     * 更新关联支付单的退款状态（含异常重试机制）
     * @param requestProfile 请求上下文信息
     * @param payRefOrderNo 需要更新的支付关联单号
     */
    private void updateRefundStatusForPayRefOrder(RequestProfile requestProfile, String payRefOrderNo, String orderNo) {
        LOGGER.info("开始更新支付关联单退款状态，payRefOrderNo: {}", payRefOrderNo);

        // 构建查询请求并获取支付单信息
        GetOrderFacadeRequest getOrderRequest = new GetOrderFacadeRequest();
        getOrderRequest.setOrderNo(payRefOrderNo);
        GetOrderFacadeResponse response = getOrderFacade.getOrder(requestProfile, getOrderRequest);

        // 转换并更新退款状态
        ExpressOrderModelCreator modelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(response);
        ExpressOrderModel paymentOrder = new ExpressOrderModel(modelCreator);
        paymentOrder.getFinance().setRefundStatus(RefundStatusEnum.REFUNDING);

        // 构建修改请求
        ModifyOrderFacadeRequest modifyRequest = modifyOrderFacadeTranslator.toOnlyChangeFinanceRequest(paymentOrder);

        try {
            modifyOrderFacade.modifyOrder(requestProfile, modifyRequest);
            LOGGER.info("支付关联单退款状态更新成功，payRefOrderNo: {}", payRefOrderNo);
        } catch (Exception e) {
            LOGGER.error("支付关联单退款状态更新失败，将触发重试机制，payRefOrderNo: {}", payRefOrderNo, e);
            produceRetryMq(requestProfile, modifyRequest);
        }

        // 合并支付标识
        // 遍历所有合并支付的订单更新为退款中
        if (MapUtils.isNotEmpty(paymentOrder.getFinance().getExtendProps())
                && paymentOrder.getFinance().getExtendProps().containsKey(FinanceConstants.CUSTOMER_MERGE_PAY_ORDER_LIST)) {
            String customerMergePayOrderList = paymentOrder.getFinance().getExtendProps().get(FinanceConstants.CUSTOMER_MERGE_PAY_ORDER_LIST);
            if (StringUtils.isNotBlank(customerMergePayOrderList)) {
                Arrays.stream(customerMergePayOrderList.split(",")).forEach(mergePayOrder -> {
                    if (StringUtils.isNotBlank(mergePayOrder) && !mergePayOrder.equals(orderNo)) {
                        updateMergePayOrderRefundStatus(requestProfile, mergePayOrder);
                    }
                });
            }
        }
    }

    /**
     * 更新合并支付的配送单的退款状态
     * @param requestProfile
     * @param waybillNo
     */
    private void updateMergePayOrderRefundStatus(RequestProfile requestProfile, String waybillNo){
        // 构建查询请求并获取支付单信息
        GetOrderFacadeRequest getOrderRequest = new GetOrderFacadeRequest();
        getOrderRequest.setCustomOrderNo(waybillNo);
        GetOrderFacadeResponse response = getOrderFacade.getOrder(requestProfile, getOrderRequest);

        // 转换并更新退款状态
        ExpressOrderModelCreator modelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(response);
        ExpressOrderModel paymentOrder = new ExpressOrderModel(modelCreator);
        paymentOrder.getFinance().setRefundStatus(RefundStatusEnum.REFUNDING);

        // 构建修改请求
        ModifyOrderFacadeRequest modifyRequest = modifyOrderFacadeTranslator.toOnlyChangeFinanceRequest(paymentOrder);

        try {
            modifyOrderFacade.modifyOrder(requestProfile, modifyRequest);
            LOGGER.info("合并支付关联单退款状态更新成功，waybillNo: {}", waybillNo);
        } catch (Exception e) {
            LOGGER.error("合并支付关联单退款状态更新失败，将触发重试机制，waybillNo: {}", waybillNo, e);
            produceRetryMq(requestProfile, modifyRequest);
        }
    }

    /**
     * 微信视频号退款异常报警
     */
    private void refundExceptionAlarmC2BWeChatVideoAccount(GetOrderFacadeResponse order) {
        if (order != null
                && order.isC2BWeChatVideoAccount()
                && BatrixSwitch.applyByBoolean(BatrixSwitchKey.REFUND_EXCEPTION_ALARM_C2B_WE_CHAT_VIDEO_ACCOUNT_SWITCH)) {
            umpUtil.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_C2B_WE_CHAT_VIDEO_ACCOUNT_REFUND_EXCEPTION_ALARM, "C2B微信视频号退款异常", order.getOrderNo());
        }
    }
}
