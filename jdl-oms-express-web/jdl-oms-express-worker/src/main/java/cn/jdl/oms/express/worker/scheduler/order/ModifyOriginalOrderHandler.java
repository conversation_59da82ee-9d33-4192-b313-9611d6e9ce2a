package cn.jdl.oms.express.worker.scheduler.order;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.issue.ModifyIssueFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.ModifyOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderModelCreatorTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.product.ProductFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ModifyRepositoryMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ProductClearPdqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.InitiatorTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSignEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.ProductDelegate;
import cn.jdl.oms.express.shared.common.constant.FlowConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.worker.scheduler.AbstractSchedulerHandler;
import com.jd.paq.core.Message;
import com.jd.paq.plugin.IMessage;
import com.jd.paq.plugin.PDQClientException;
import com.jd.paq.plugin.Result;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 修改原单信息
 */
public class ModifyOriginalOrderHandler extends AbstractSchedulerHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ModifyOriginalOrderHandler.class);


    /**
     * 重试最大次数
     */
    private static final int MAX_RETRY_TIMES = 10;

    //查询订单防腐层
    @Resource
    private GetOrderFacade getOrderFacade;

    /**
     * 订单详情model转换
     */
    @Resource
    private GetOrderModelCreatorTranslator orderModelCreatorTranslator;

    /**
     * 修改下发服务
     */
    @Resource
    private ModifyIssueFacade modifyIssueFacade;

    @Resource
    private ModifyIssueFacadeTranslator modifyIssueFacadeTranslator;

    /**
     * 修改订单信息
     */
    @Resource
    private ModifyOrderFacade modifyOrderFacade;

    @Resource
    private ModifyOrderFacadeTranslator modifyOrderFacadeTranslator;

    /**
     * 任务调度
     */
    @Resource
    private SchedulerService schedulerService;

    /**
     * 补全产品123级
     */
    @Resource
    private ProductFacadeTranslator productFacadeTranslator;

    @Override
    public Result execute(IMessage iMessage) throws PDQClientException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_WORKER_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        //任务调度执行结果对象根据实际的执行结果定义返回具体的业务结果
        Result result = new Result(Result.SUCCESS);
        result.setCode(1);
        try {
            // 设置消息体为重试消息
            if (iMessage instanceof Message) {
                int retryTime = ((Message) iMessage).getRedriveCount();
                if (retryTime >= MAX_RETRY_TIMES) {
                    LOGGER.info("任务调度{},重试次数超过{}次,暂停重试",iMessage.getTopic(),MAX_RETRY_TIMES);
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }
                String iMessageContent = iMessage.getMessageBody();
                PDQTopicEnum pdqTopicEnum = PDQTopicEnum.getByTopic(iMessage.getTopic());
                if (null == pdqTopicEnum) {
                    LOGGER.info("任务调度{},未匹配到任务队列,暂停重试",iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                LOGGER.info("开始执行原单修改任务调度topic:{},消息体:{}", pdqTopicEnum, iMessageContent);
                // 重试消息体
                SchedulerMessage schedulerMessage = JSONUtils.jsonToBean(
                        iMessageContent, SchedulerMessage.class);
                if (null == schedulerMessage) {
                    LOGGER.info("任务调度{},场景业务数据对象不存在,暂停重试",iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                ProductClearPdqMessageDto messageDto = (ProductClearPdqMessageDto) JSONUtils.jsonToBean(schedulerMessage.getDtoJson(), schedulerMessage.getDtoClass());
                if (messageDto == null) {
                    LOGGER.info("任务调度{},场景业务数据对象不存在,暂停重试",iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }

                try {
                    //获取改址单详情
                    ExpressOrderModel readdressOrder = getOrderInfo(messageDto.getRequestProfile(), messageDto.getOrderNo());
                    //获取原单详情
                    ExpressOrderModel originalOrder = getOrderInfo(messageDto.getRequestProfile(), messageDto.getOriginalOrderNo());
                    if (readdressOrder == null || originalOrder == null) {
                        LOGGER.info("订单:{}详情查询为空", messageDto.getOrderNo());
                        result.setCode(Result.SYSTEMERROR);
                        return result;
                    }

                    // 原逻辑：只处理改址单
                    // 新逻辑：处理改址单、有待删除产品的退货单
                    List<String> clearProductNos = messageDto.getClearProductNos();
                    if (OrderTypeEnum.READDRESS != readdressOrder.getOrderType()
                            && !(OrderTypeEnum.RETURN_ORDER == readdressOrder.getOrderType() && CollectionUtils.isNotEmpty(clearProductNos))) {
                        LOGGER.info("订单:{}不是改址单、有待删除产品的退货单，不执行后续流程", messageDto.getOrderNo());
                        return result;
                    }

                    // 改址单特殊逻辑
                    if (OrderTypeEnum.READDRESS == readdressOrder.getOrderType()) {
                        // 修改原单结算方式 支付时机 & 支付状态
                        LOGGER.info("原单:{}结算方式为{},支付时机:{},改址单:{}发起人类型为{},原单为到付现结:{}，改址下单人类型为寄件人:{}",
                                originalOrder.orderNo(), originalOrder.getFinance().getSettlementType().getCode(), originalOrder.getFinance().getPaymentStage().getCode(),
                                readdressOrder.orderNo(), readdressOrder.getInitiatorType().getCode(), SettlementTypeEnum.CASH_ON_DELIVERY == originalOrder.getFinance().getSettlementType(), InitiatorTypeEnum.CONSIGNOR == readdressOrder.getInitiatorType());

                        Map<String, String> orderSign = readdressOrder.getOrderSign();
                        boolean readdressMode3 = "3".equals(MapUtils.getString(orderSign, OrderSignEnum.READDRESS_MODE.getCode()));
                        LOGGER.info("到付单的先款改址单原单改原单结算方式，原单号:{},新单号:{},readdressMode3:{}", originalOrder.orderNo(), readdressOrder.orderNo(), readdressMode3);
                        if (readdressMode3){

                            Map<String, String> orderNewFinanceExt = readdressOrder.getFinance().getExtendProps();//新单财务域扩展信息
                            // 指定原单的结算方式
                            String originSettlementType = MapUtils.isNotEmpty(orderNewFinanceExt) ? orderNewFinanceExt.get(AttachmentKeyEnum.ORIGIN_SETTLEMENT_TYPE.getKey()) : null;
                            // 指定原单的结算方式
                            Integer originSettlementTypeIntVal = StringUtils.isNotBlank(originSettlementType) ? Integer.parseInt(originSettlementType) : null;

                            // 改址原单到付，原单结算方式由端上指定 2024-03-05
                            // https://joyspace.jd.com/pages/0qSF3KDdpsQV8swRXKfd 3.3.7 @wangweijin3
                            // 如果原单到付，改址单先款，则修改原单支付方式和支付环节
                            if (SettlementTypeEnum.CASH_ON_DELIVERY == originalOrder.getFinance().getSettlementType()
                                    && PaymentStageEnum.ONLINEPAYMENT == readdressOrder.getFinance().getPaymentStage()) {
                                LOGGER.info("原单{}更新结算方式和支付环节", originalOrder.orderNo());

                                if (InitiatorTypeEnum.CONSIGNEE == readdressOrder.getInitiatorType()){
                                    originalOrder.getFinance().setPaymentStage(PaymentStageEnum.ONLINEPAYMENT);
                                }

                                if(InitiatorTypeEnum.CONSIGNOR == readdressOrder.getInitiatorType()
                                        && null != originSettlementTypeIntVal//且原单结算方式改为寄付现结
                                        && SettlementTypeEnum.CASH_ON_PICK == SettlementTypeEnum.of(originSettlementTypeIntVal)){
                                    originalOrder.getFinance().setPaymentStage(PaymentStageEnum.ONLINEPAYMENT);
                                    originalOrder.getFinance().setSettlementType(SettlementTypeEnum.CASH_ON_PICK);
                                }
                            } else if (SettlementTypeEnum.CASH_ON_DELIVERY == originalOrder.getFinance().getSettlementType()
                                    && PaymentStageEnum.CASHONDELIVERY == readdressOrder.getFinance().getPaymentStage()){
                                if(null != originSettlementTypeIntVal//且原单结算方式改为寄付月结
                                        && SettlementTypeEnum.MONTHLY_PAYMENT == SettlementTypeEnum.of(originSettlementTypeIntVal)){
                                    originalOrder.getFinance().setSettlementType(SettlementTypeEnum.MONTHLY_PAYMENT);
                                }
                            }
                        } else{
                            //原单为到付现结，支付状态=已支付，改址下单人类型：寄件人 则修改原单结算方式=寄付现结，支付时机=线上 -- 没有判断支付状态 是因为默认已支付
                            // 如果原单到付 修改人为寄件人 且 改址单是 先款，则修改原单支付方式为 寄付现结 & 先款 TODO
                            if (PaymentStageEnum.ONLINEPAYMENT == readdressOrder.getFinance().getPaymentStage()
                                    && SettlementTypeEnum.CASH_ON_DELIVERY == originalOrder.getFinance().getSettlementType()) {
                                LOGGER.info("原单{}更新结算方式和支付环节", originalOrder.orderNo());
                                originalOrder.getFinance().setPaymentStage(PaymentStageEnum.ONLINEPAYMENT);
                                if (InitiatorTypeEnum.CONSIGNOR == readdressOrder.getInitiatorType()){
                                    originalOrder.getFinance().setSettlementType(SettlementTypeEnum.CASH_ON_PICK);
                                }
                            }
                        }
                        //默认原单支付成功 TODO OFC优化后需要删除
                        originalOrder.getFinance().setPaymentStatus(PaymentStatusEnum.COMPLETE_PAYMENT);

                        //原单有cod且新单也有COD,则删除原单cod
                        if (CollectionUtils.isNotEmpty(readdressOrder.getProductDelegate().getCodProducts())
                                && CollectionUtils.isNotEmpty(originalOrder.getProductDelegate().getCodProducts())) {
                            originalOrder.getProductDelegate().getCodProduct().setOperateType(OperateTypeEnum.DELETE);
                        }
                    }

                    // 改址单和退货单公共逻辑：删除原单指定产品
                    if (CollectionUtils.isNotEmpty(clearProductNos)) {
                        LOGGER.info("原单删除指定增值产品clearProductNos="+ JSONUtils.beanToJSONDefault(clearProductNos));
                        ProductDelegate productDelegate = originalOrder.getProductDelegate();
                        if (productDelegate != null && !productDelegate.isEmpty()) {
                            for (Product product : productDelegate.getProductList()) {
                                if (clearProductNos.contains(product.getProductNo())) {
                                    product.setOperateType(OperateTypeEnum.DELETE);
                                }
                            }
                        }
                    }

                    //修改原单信息后下发
                    ModifyIssueFacadeRequest modifyIssueFacadeRequest = modifyIssueFacadeTranslator.toModifyOriginalOrderRequest(readdressOrder, originalOrder);
                    LOGGER.info("PDQ更改原单信息下发入参:{}", JSONUtils.beanToJSONDefault(modifyIssueFacadeRequest));
                    ModifyIssueFacadeResponse response = modifyIssueFacade.modifyIssue(modifyIssueFacadeRequest, messageDto.getBusinessIdentity());
                    LOGGER.info("PDQ更改原单信息下发出参:{}", JSONUtils.beanToJSONDefault(response));

                    //下发修改成功后修改数据库信息
                    if (response.isIssueResult()) {
                        ModifyOrderFacadeRequest modifyOrderFacadeRequest = modifyOrderFacadeTranslator.toCreateIssueSnapOrderSomeDataFacadeRequest(readdressOrder.getOrderType(), originalOrder);
                        LOGGER.info("PDQ更改原单信息下发成功后持久化入参:{}", JSONUtils.beanToJSONDefault(modifyOrderFacadeRequest));
                        try {
                            modifyOrderFacade.modifyOrder(messageDto.getRequestProfile(), modifyOrderFacadeRequest);
                        } catch (Exception e) {
                            LOGGER.error("原单修改数据库状态失败，发pdq重试", e);
                            SchedulerMessage scheduler = new SchedulerMessage();
                            //持久化消息
                            ModifyRepositoryMessageDto modifyRepositoryMessageDto = new ModifyRepositoryMessageDto();
                            modifyRepositoryMessageDto.setModifyOrderFacadeRequest(modifyOrderFacadeRequest);
                            modifyRepositoryMessageDto.setRequestProfile(readdressOrder.requestProfile());
                            modifyRepositoryMessageDto.setBusinessIdentity(readdressOrder.getOrderBusinessIdentity());
                            scheduler.setDtoJson(JSONUtils.beanToJSONDefault(modifyRepositoryMessageDto));
                            scheduler.setDtoClass(ModifyRepositoryMessageDto.class);
                            schedulerService.addSchedulerTask(PDQTopicEnum.MODIFY_REPOSITORY_RETRY, scheduler,
                                    FlowConstants.EXPRESS_ORDER_MODIFY_REPOSITORY_FLOW_CODE);
                            LOGGER.info("原单修改数据库状态失败，发pdq成功,modifyOrderFacadeRequest:{}", JSONUtils.beanToJSONDefault(modifyOrderFacadeRequest));
                        }

                        LOGGER.info("PDQ更改原单信息下发成功后持久化成功");
                    }
                } catch (Exception e) {
                    LOGGER.info("任务调度【" + iMessage.getTopic() + "】,清理COD失败,暂停重试", e);
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
            }
        } catch (Exception e) {
            result.setCode(Result.SYSTEMERROR);
            LOGGER.error("清理COD任务调度执行异常,再次重试", e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        return result;
    }

    private ExpressOrderModel getOrderInfo(RequestProfile requestProfile, String orderNo) {
        GetOrderFacadeRequest facadeRequest = new GetOrderFacadeRequest();
        facadeRequest.setOrderNo(orderNo);
        GetOrderFacadeResponse getOrderFacadeResponse = getOrderFacade.getOrder(requestProfile, facadeRequest);
        if (getOrderFacadeResponse == null) {
            LOGGER.error("orderNo:{}订单修改查询订单为空", orderNo);
            /*未获取到订单详情信息需继续重试*/
            return null;
        }
        ExpressOrderModelCreator modelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(getOrderFacadeResponse);
        return new ExpressOrderModel(modelCreator).withRequestProfile(requestProfile);
    }
}
