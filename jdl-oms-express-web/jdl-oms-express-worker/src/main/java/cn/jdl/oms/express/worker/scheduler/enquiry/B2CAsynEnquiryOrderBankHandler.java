package cn.jdl.oms.express.worker.scheduler.enquiry;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.core.model.ReaddressRecordDetailInfo;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CustomerConfig;
import cn.jdl.oms.express.domain.converter.FinanceDetailMapper;
import cn.jdl.oms.express.domain.dto.DiscountInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.enquiry.B2COrderBankOrgFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.enquiry.EnquiryFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.exchangecurrency.ExchangeCurrencyFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.issue.ModifyIssueFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.ModifyOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.GetMerchantOrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.orderbank.OrderBankFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.org.OrderbankOrgFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.BasicTraderResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.B2CCreateEnquiryFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.EnquiryFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.exchangecurrency.ExchangeCurrencyRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderModelCreatorTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.ModifyOrderFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.MoneyFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.B2COrderBankFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.BMerchantReceiveTypeEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.GetMerchantOrderBankFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeMiddleRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.OrderBankRedisOp;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.ReceivableDetailFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.bdue.PayModeEnum;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.FLowDirectionUtils;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.OTSLedgerUtil;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeRquest;
import cn.jdl.oms.express.domain.infrs.acl.pl.org.OrderbankOrgFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.util.ChannelUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.OriginalOrderModifyUtil;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ducc.ExpressDUCConfigCenter;
import cn.jdl.oms.express.domain.infrs.ohs.locals.promise.MakingDispatcherHandler;
import cn.jdl.oms.express.domain.infrs.ohs.locals.redis.IRedisLock;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.ModifyRepositoryMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.OrderBankPdqMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.impl.SchedulerService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.ModifyRecordTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.ServiceProductTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.model.IProduct;
import cn.jdl.oms.express.domain.spec.util.ReceiptCurrencyUtil;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.domain.vo.record.ModifyRecord;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.constant.FinanceConstants;
import cn.jdl.oms.express.shared.common.constant.FlowConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.mdc.MDCTraceConstants;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.worker.scheduler.AbstractSchedulerHandler;
import com.jd.paq.core.Message;
import com.jd.paq.plugin.IMessage;
import com.jd.paq.plugin.PDQClientException;
import com.jd.paq.plugin.Result;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.jdl.oms.express.shared.common.constant.EnquiryConstants.CALC_PRICE_ITEM_LIST;

/**
 * @ProjectName：jdl-oms-express-b2c-infrastructure
 * @Package： cn.jdl.oms.express.worker.scheduler.issue
 * @ClassName: ReverseBillHandler
 * @Description: 异步询价台账消息处理
 * @Author： caoxinyu16
 * @CreateDate 2021/5/20 14:05
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
public class B2CAsynEnquiryOrderBankHandler extends AbstractSchedulerHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(B2CAsynEnquiryOrderBankHandler.class);

    //查询订单防腐层
    @Resource
    private GetOrderFacade getOrderFacade;

    //修改防腐层
    @Resource
    private ModifyOrderFacade modifyOrderFacade;

    //修改防腐层对象转换器
    @Resource
    private ModifyOrderFacadeTranslator modifyOrderFacadeTranslator;

    /**
     * 订单详情model转换
     */
    @Resource
    private GetOrderModelCreatorTranslator orderModelCreatorTranslator;

    /**
     * 支付机构facade
     */
    @Resource
    private B2COrderBankOrgFacade b2COrderBankOrgFacade;

    /**
     * 询价facade
     */
    @Resource
    private EnquiryFacade enquiryFacade;

    /**
     * 机构信息获取防腐层转换器
     */
    @Resource
    private OrderbankOrgFacadeTranslator orderbankOrgFacadeTranslator;

    @Resource
    private B2CCreateEnquiryFacadeTranslator b2CCreateEnquiryFacadeTranslator;

    /**
     * 台账防腐层
     */
    @Resource
    private OrderBankFacade orderBankFacade;

    @Resource
    private B2COrderBankFacadeTranslator b2cOrderBankFacadeTranslator;

    /**
     * 修改下发服务
     */
    @Resource
    private ModifyIssueFacade modifyIssueFacade;

    @Resource
    private ModifyIssueFacadeTranslator modifyIssueFacadeTranslator;

    /**
     * 下发履约执行层达标逻辑
     */
    @Resource
    private MakingDispatcherHandler makingDispatcherHandler;

    /**
     * 任务调度
     */
    @Resource
    private SchedulerService schedulerService;

    /**
     * 单据台账锁
     */
    @Resource
    private OrderBankRedisOp orderBankRedisOp;

    /**
     * 商家基础信息
     */
    @Resource
    private CustomerConfigFacade customerConfigFacade;

    /**
     * B商家台账查询
     */
    @Resource
    private GetMerchantOrderBankFacade getMerchantOrderBankFacade;

    /**
     * 开关
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * DUCC
     */
    @Resource
    private ExpressDUCConfigCenter expressDUCConfigCenter;

    /**
     * 重试最大次数
     */
    private static final int MAX_RETRY_TIME = 10;

    @Resource
    private EnquiryFacadeTranslator enquiryFacadeTranslator;

    @Resource
    private ExchangeCurrencyFacade exchangeCurrencyFacade;

    /**
     * 支付机构facade
     */
    @Resource
    private OrderbankOrgFacade orderbankOrgFacade;

    /**
     * 原单修改工具类
     */
    @Resource
    private OriginalOrderModifyUtil originalOrderModifyUtil;

    /**
     * 异步询价台账消息处理处理
     *
     * @param iMessage
     * @return
     * @throws PDQClientException
     * <AUTHOR>
    @Override
    public Result execute(IMessage iMessage) throws PDQClientException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        //任务调度执行结果对象根据实际的执行结果定义返回具体的业务结果
        Result result = new Result(Result.SUCCESS);
        IRedisLock[] redisLock = {null};
        MDC.put(MDCTraceConstants.TRACEID, String.valueOf(System.nanoTime()));
        try {
            if (iMessage instanceof Message) {
                int retryTime = ((Message) iMessage).getRedriveCount();
                if (retryTime >= MAX_RETRY_TIME) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】,重试次数超过{}次,暂停重试", iMessage.getTopic(), MAX_RETRY_TIME);
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }
                String iMessageContent = iMessage.getMessageBody();
                PDQTopicEnum pdqTopicEnum = PDQTopicEnum.getByTopic(iMessage.getTopic());
                if (null == pdqTopicEnum) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】未匹配到任务队列,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                // 重试消息体
                SchedulerMessage schedulerMessage = JSONUtils.jsonToBean(
                        iMessageContent, SchedulerMessage.class);
                if (null == schedulerMessage) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】,重试消息体不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                OrderBankPdqMessageDto messageDto = JSONUtils.jsonToBean(schedulerMessage.getDtoJson(),
                        OrderBankPdqMessageDto.class);
                if (null == messageDto) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】,场景业务数据对象不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
                if (!this.asynEnquiryOrderbankHandle(messageDto, redisLock)) {
                    LOGGER.info("异步询价台账消息处理任务调度【{}】,场景业务数据对象不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.SYSTEMERROR);
                    return result;
                }
            }
        } catch (Exception e) {
            result.setCode(Result.SYSTEMERROR);
            result.setReason(e.getMessage());
            LOGGER.info("异步询价台账消息处理任务调度【{}】执行异常", iMessage.getTopic(), e);
            Profiler.functionError(callerInfo);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
            if (redisLock[0] != null) {
                redisLock[0].unlock();
            }
            MDC.remove(MDCTraceConstants.TRACEID);
        }
        return result;
    }

    /**
     * 异步询价-台账-持久化
     *
     * @param messageDto
     * @return
     */
    public boolean asynEnquiryOrderbankHandle(OrderBankPdqMessageDto messageDto, IRedisLock[] redisLock) throws ParseException {
        if (messageDto == null || StringUtils.isBlank(messageDto.getOrderNo())) {
            return true;
        }

        LOGGER.info("异步询价开始：orderNo:{}",messageDto.getOrderNo());
        //获取询价台账锁，获取不到锁重试. 需要做对应的释放功能
        redisLock[0] = orderBankRedisOp.getLock(messageDto.getRequestProfile(), messageDto.getBusinessIdentity(), messageDto.getOrderNo());
        if (!redisLock[0].tryLock()) {
            LOGGER.info("订单获取台账锁失败，需要重试");
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "订单orderNo:" + messageDto.getOrderNo() + "获取台账锁失败，需要重试");
            return false;
        }

        //获取订单详情
        GetOrderFacadeResponse orderFacadeResponse = toGetOrderFacadeResponse(messageDto.getRequestProfile(), messageDto.getOrderNo());
        if (orderFacadeResponse == null) {
            LOGGER.error("异步询价台账消息处理查询订单为空");
            /*未获取到订单详情信息需继续重试*/
            return false;
        }
        //将订单详情转换成model
        ExpressOrderContext orderContext = toExpressOrderContext(messageDto, orderFacadeResponse);
        //订单状态检查. 已取消的订单直接忽略
        ExpressOrderModel orderModel = orderContext.getOrderModel();
        if (OrderStatusEnum.CANCELED == orderModel.getOrderStatus().getOrderStatus()) {
            LOGGER.info("订单状态为已取消，忽略这个消息");
            return true;
        }

        // 已支付的不操作台账
        if (PaymentStatusEnum.COMPLETE_PAYMENT == orderModel.getFinance().getPaymentStatus()) {
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "已支付的不操作台账,orderNo:" + orderModel.orderNo());
            return true;
        }

        //结算方式是月结, 且无cod, 不写账
        if (SettlementTypeEnum.MONTHLY_PAYMENT == orderModel.getFinance().getSettlementType()
                && orderModel.getProductDelegate().getCodProducts().isEmpty()) {
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "结算方式是月结, 且无cod的不操作台账,orderNo:" + orderModel.orderNo());
            return true;
        }

        //判断台账是否初始化
        boolean init = orderBankRedisOp.haveInit(messageDto.getRequestProfile(), messageDto.getBusinessIdentity(), messageDto.getOrderNo(), true);
        if (!init) {
            Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL, "订单orderNo:" + orderModel.orderNo() + "尚未初始化台账，需要重试");
            LOGGER.info("订单尚未初始化台账，需要重试");
            return false;
        }

        //补全订单信息, 1.原单订单快照
        //原单信息
        GetOrderFacadeResponse refOrderFacadeResponse = toGetOrderFacadeResponse(messageDto.getRequestProfile(), messageDto.getRelateOrderNo());
        if (refOrderFacadeResponse == null) {
            LOGGER.error("异步询价台账消息处理查询原订单为空,原单号：{}", messageDto.getRelateOrderNo());
            return false;
        }
        ExpressOrderContext refOrderContext = toExpressOrderContext(messageDto, refOrderFacadeResponse);
        //将原单的快照信息存入当前单
        orderModel.assignSnapshot(refOrderContext.getOrderModel());

        // 当前单不需要询价
        if(!needCurrentEnquiry(orderModel)){
            LOGGER.info("当前单不需要询价,orderNo:{}",orderModel.orderNo());
            return true;
        }

        //当前单询价
        BillingEnquiryFacadeResponse billingEnquiryFacadeResponse = toBillingEnquiryFacadeResponse(orderContext, refOrderContext.getOrderModel(), null);

        /*
         * 淘天逆向建新单时（渠道.特殊来源=淘天），新单结算方式=到付现结、支付环节=后款。
         * 如果是淘天，新单的询价折后金额置 0。
         * - 淘天原单到付现结，将原单费用信息加到逆向新单写B商家和POS台账。后面原单询价逻辑已支持。
         * - 淘天原单其他场景（原单寄付现结、月结），逆向新单B商家和POS台账 折后金额写0
         */
        if (ChannelUtil.isTaoTianSpecial(orderModel.getOrderSnapshot())) {
            // 折后金额
            BigDecimal discountAmount = billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getAmount();
            if (discountAmount.compareTo(BigDecimal.ZERO) > 0) {
                LOGGER.info("淘天逆向建新单时，新单的询价 折后金额置0。discountAmount: {} -> 0。", discountAmount);
                billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().setAmount(BigDecimal.ZERO);
            }
        }

        // key:订单号  value:计费信息
        Map<String, BillingEnquiryFacadeResponse> billMap = new HashMap<>();
        billMap.put(orderModel.orderNo(), billingEnquiryFacadeResponse);
        boolean isHaveReaddress = false;
        FinanceDetailInfoDto originOrderFinance = null;
        // 原单询价时，无需询价的增值产品（未处理港澳，因为当前配置的ed-a-0109不涉及港澳，后续有需求再补充港澳分支逻辑）
        Set<String> originOrderEnquiryProductNoBlacklist = originalOrderModifyUtil.getOriginOrderEnquiryProductNoBlacklist();

        //逆向单询价(逆向次数原则上不受限，有大对象风险)
        if (OrderTypeEnum.RETURN_ORDER == orderModel.getOrderType()) {
            LOGGER.info("逆向单补全计费明细开始");
            if(refOrderContext.getOrderModel().isHKMO()) {
                LOGGER.info("B2C港澳逆向单原单询价-逆向单:{},原单:{}",orderModel.orderNo(),refOrderContext.getOrderModel().orderNo());
                // 税金处理
                dfOriTax(refOrderContext,orderContext,billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getCurrencyCode());
                // 判断是否有改址（目前只有港澳同城改址）
                if (CollectionUtils.isNotEmpty(refOrderContext.getOrderModel().getModifyRecordDelegate().getEnabledThroughOrderModifyRecords())) {
                    // 从改址记录获取原单应收总费用
                    isHaveReaddress = true;
                    originOrderFinance = getOriginOrderTotalAmountByModifyRecords(refOrderContext);
                } else {
                    // 原单询价
                    BillingEnquiryFacadeResponse response = crossBorderReverseOriEnquiry(refOrderContext, orderContext, billingEnquiryFacadeResponse);
                    if(null != response) {
                        billMap.put(refOrderContext.getOrderModel().orderNo(), response);
                    }
                }
            } else {
                //遍历获取到原单信息
                GetOrderFacadeResponse tempOrderFacadeResponse = refOrderFacadeResponse;
                do {
                    ExpressOrderContext tempContext = toExpressOrderContext(messageDto, tempOrderFacadeResponse);
                    if (OrderTypeEnum.DELIVERY.getCode().equals(tempOrderFacadeResponse.getOrderType())) {
                        if (CollectionUtils.isNotEmpty(tempContext.getOrderModel().getModifyRecordDelegate().getEnabledThroughOrderModifyRecords())) {
                            isHaveReaddress = true;
                        }
                    } else {
                        // 逆向单，查询原单信息
                        tempOrderFacadeResponse = toGetOrderFacadeResponse(messageDto.getRequestProfile(), tempContext.getOrderModel().getRefOrderInfoDelegate().getOriginalOrderNo());
                    }
                } while (OrderTypeEnum.RETURN_ORDER.getCode().equals(tempOrderFacadeResponse.getOrderType()));

                // 处理原单存在一单到底场景
                if (isHaveReaddress) {
                    BigDecimal totalAmount = BigDecimal.ZERO;

                    if(OrderTypeEnum.DELIVERY == refOrderContext.getOrderModel().getOrderType()){
                        //若原单是正向单，则需查询原单到付未支付的台账信息，金额合并继承到新单
                        List<ModifyRecord> modifyRecords = refOrderContext.getOrderModel().getModifyRecordDelegate().getEnabledModifyRecords();

                        for (ModifyRecord modifyRecord : modifyRecords) {
                            BigDecimal dfAmount = BigDecimal.ZERO;
                            if (ModifyRecordTypeEnum.READDRESS.getCode().equals(modifyRecord.getModifyRecordType()) // 改址记录
                                    || ModifyRecordTypeEnum.INTERCEPT.getCode().equals(modifyRecord.getModifyRecordType())) {// 拦截记录
                                // 不处理拒收改址记录 ModifyRecordTypeEnum.REJECT ，原因：拒收一单到底走不到逆向单接单异步询价
                                //改址记录
                                ReaddressRecordDetailInfo readdressDetail = (ReaddressRecordDetailInfo) modifyRecord.getModifyRecordDetail();
                                if (SettlementTypeEnum.CASH_ON_DELIVERY.getCode().equals(readdressDetail.getFinance().getSettlementType())
                                        && !PaymentStatusEnum.COMPLETE_PAYMENT.getPaymentStatus().equals(readdressDetail.getFinance().getPaymentStatus())
                                        && (null != readdressDetail.getFinance().getPendingMoney() && BigDecimal.ZERO.compareTo(readdressDetail.getFinance().getPendingMoney().getAmount()) < 0)) {
                                    //到付&&未支付&&待支付金额大于0的改址记录的台账金额，需继承到逆向新单上
                                    dfAmount = getBMerchantDfAmount(modifyRecord.getModifyRecordNo());
                                    LOGGER.info("到付&&未支付&&待支付金额大于0的改址记录号:{},dfAmount{}",modifyRecord.getModifyRecordNo(),dfAmount);
                                } else {
                                    continue;
                                }
                            } else if (ModifyRecordTypeEnum.RECHECK.getCode().equals(modifyRecord.getModifyRecordType())) {
                                //复重复量方询价记录记录
                                dfAmount = getBMerchantDfAmount(modifyRecord.getModifyRecordNo());
                                LOGGER.info("复重复量方询价记录号:{},dfAmount{}",modifyRecord.getModifyRecordNo(),dfAmount);
                            }
                            totalAmount = totalAmount.add(dfAmount);
                        }

                    } else {
                        //根据原单号查B商家到付运费
                        totalAmount = getBMerchantDfAmount(refOrderContext.getOrderModel().getCustomOrderNo());
                        LOGGER.info("根据原单号查B商家到付运费,totalAmount{}", totalAmount);
                    }

                    if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                        LOGGER.info("原单到付运费大于0，需继承到新单");
                        //组装费用：费用编码：9999、描述：原单应收总费用
                        originOrderFinance = new FinanceDetailInfoDto();
                        originOrderFinance.setCostNo("9999");
                        originOrderFinance.setCostName("原单应收总费用");
                        MoneyInfoDto moneyInfoDto = new MoneyInfoDto();
                        moneyInfoDto.setAmount(totalAmount);
                        moneyInfoDto.setCurrencyCode(CurrencyCodeEnum.CNY);
                        originOrderFinance.setPreAmount(moneyInfoDto);
                        originOrderFinance.setDiscountAmount(moneyInfoDto);
                    }

                } else {
                    // B2C存在多次逆向的场景，每一次逆向，当最新的逆向单是到付现结时，最新逆向单在接单后调计费接口询价，同时获取所有原单信息（取最原始的正向单及最新一次逆向之前的所有逆向单，
                    // 且只取结算方式为到付现结的单子）并依次调询价接口重新询价，新单和所有原单均询价成功后，需要加和所有原单运费（所有原单折后金额）并写在最新单运费（最新单折后金额）上落库，
                    // 且所有原单的费用明细均需落在新单的费用明细上（相同费用编码的费用需加和）。
                    // 所有单据询价完成后，在最新单的订单财务表的“备注”中落所有单（含所有原单及最新逆向单）的每一单的折后金额（形式为“订单号1：折后金额1；订单号2：折后金额2”；…），
                    // 在最新单的费用明细表的“备注”中落每一项费用下所有单（含所有原单及最新逆向单）的每一单的该费用的折后金额（形式为“订单号1：折后金额1；订单号2：折后金额2”；…
                    GetOrderFacadeResponse subOrderFacadeResponse = refOrderFacadeResponse;
                    ExpressOrderContext subOrderContext = null;
                    BillingEnquiryFacadeResponse subBillingEnquiryFacadeResponse = null;
                    do {
                        // 当前单(相对), 补全订单信息
                        subOrderContext = toExpressOrderContext(messageDto, subOrderFacadeResponse); // 当前逆向单对应的原单
                /*// 1.商家基础资料
                subOrderContext.setCustomerConfig(customerConfig);
                // 2.支付机构id和名称
                orderContext.getOrderModel().complement().complementFinanceCollectionOrg(this, parseCollectionOrgModelCreator(
                        orderbankOrg.getOrgId(), orderbankOrg.getOrgName()));*/
                        // 当前单关联单(相对)
                        GetOrderFacadeResponse subRefOrderFacadeResponse = null;
                        LOGGER.info("逆向单-中间过程询价, subOrderContext:{}", JSONUtils.beanToJSONDefault(subOrderContext));
                        if (subOrderContext.getOrderModel().isReceiverPayOnDelivery()) {//到付现结后款，的需要询原单合并支付，到付现结先款的已经支付，不需要询原单
                            if (OrderTypeEnum.DELIVERY == subOrderContext.getOrderModel().getOrderType()) {
                                // 回到第一单
                                subBillingEnquiryFacadeResponse = toBillingEnquiryFacadeResponse(subOrderContext, null, originOrderEnquiryProductNoBlacklist);
                            } else {
                                // 逆向单，查询原单信息
                                subRefOrderFacadeResponse = toGetOrderFacadeResponse(messageDto.getRequestProfile(), subOrderContext.getOrderModel().getRefOrderInfoDelegate().getOriginalOrderNo());
                                // 3.关联单快照存入当前单(相对)
                                ExpressOrderContext subRefOrderContext = toExpressOrderContext(messageDto, subRefOrderFacadeResponse);
                                subOrderContext.getOrderModel().assignSnapshot(subRefOrderContext.getOrderModel());
                                subBillingEnquiryFacadeResponse = toBillingEnquiryFacadeResponse(subOrderContext, subRefOrderContext.getOrderModel(), originOrderEnquiryProductNoBlacklist);
                            }
                            billMap.put(subOrderContext.getOrderModel().orderNo(), subBillingEnquiryFacadeResponse);
                        }
                        // 更新当前单(相对)subOrderFacadeResponse
                        subOrderFacadeResponse = subRefOrderFacadeResponse;
                    } while (OrderTypeEnum.RETURN_ORDER == subOrderContext.getOrderModel().getOrderType());
                }
            }
            LOGGER.info("逆向单完成全量计费获取. {}", JSONUtils.mapToJson(billMap));
        }

        // 更新补全财务信息
        if (isHaveReaddress && null != originOrderFinance) {
            complementBillingResult(orderContext, originOrderFinance, billingEnquiryFacadeResponse);
        } else {
            complementBillingResult(orderContext, billMap, billingEnquiryFacadeResponse);
        }

        LOGGER.info("补全计费明细信息结束.billing: {}", JSONUtils.beanToJSONDefault(orderModel.getFinance()));
        //查询商家基础资料
        BasicTraderResponse basicTraderResponse = customerConfigFacade.getCustomerConfig(orderModel.getCustomer().getAccountNo());
        CustomerConfig customerConfig = new CustomerConfig();
        customerConfig.setCustomerId(basicTraderResponse.getCustomerId());
        customerConfig.setCustomerName(basicTraderResponse.getCustomerName());
        orderContext.setCustomerConfig(customerConfig);

        //查询支付机构id和名称
        OrderbankOrgFacadeResponse orderbankOrg = null;
        //港澳 流向港澳到内地 结算方式到付现结
        if (orderModel.isHKMO() && orderModel.isCashOnDelivery()) {
            orderbankOrg = orderbankOrgFacade.getHMCashOnDeliveryOrgByAddress(orderContext);
        } else {
            //查询支付机构id和名称
            OrderbankOrgFacadeRquest orderbankOrgFacadeRquest = orderbankOrgFacadeTranslator.toOrderbankOrgFacadeRquest(orderContext);
            orderbankOrg = b2COrderBankOrgFacade.getOrderBankOrg(orderbankOrgFacadeRquest);
        }
        if (orderbankOrg == null) {
            LOGGER.error("获取机构信息失败");
            return false;
        }
        orderModel.complement().complementFinanceCollectionOrg(this, parseCollectionOrgModelCreator(
                orderbankOrg.getOrgId(), orderbankOrg.getOrgName()));
        //台账入参转换
        OrderBankFacadeMiddleRequest middleRequest = b2cOrderBankFacadeTranslator.toReverseReAddressOrderBankFacadeRequest(orderContext);
        modifyOrderBank(messageDto, orderContext, middleRequest);
        //下发ofc  仅有财务的
        issueOrder(orderContext);
        //持久化, 以及异常重试
        //仅有财务的
        try {
            ModifyOrderFacadeRequest facadeRequest = modifyOrderFacadeTranslator.toReverseOrChangeAddressOrderFacadeRequest(orderContext);
            modifyOrderFacade.modifyOrder(orderModel.requestProfile(), facadeRequest);
        } catch (Exception e) {
            LOGGER.error("单持久化防腐层异常", e);
            //触发重试
            produceRetryMq(orderModel.requestProfile(), orderContext);
        }
        return true;
    }

    /**
     * 获取B商家台账费用明细中的到付运费金额
     *
     * @param businessNo
     * @return
     */
    private BigDecimal getBMerchantDfAmount(String businessNo) {
        BigDecimal result = BigDecimal.ZERO;
        GetMerchantOrderBankFacadeResponse response = getMerchantOrderBankFacade.searchReceivableFullInfo(businessNo);
        if (null != response && CollectionUtils.isNotEmpty(response.getReceivableDetails())) {
            List<ReceivableDetailFacade> receivableDetails = response.getReceivableDetails();
            for (ReceivableDetailFacade detailFacade : receivableDetails) {
                if (BMerchantReceiveTypeEnum.DF.getCode().equals(detailFacade.getReceiveType())) {
                    result = detailFacade.getReceivableAmount();
                }
            }
        }
        return result;
    }
    /**
     * 调用台账接口修改台账
     *
     * @param messageDto
     * @param orderContext
     * @param middleRequest
     */
    private void modifyOrderBank(OrderBankPdqMessageDto messageDto, ExpressOrderContext orderContext, OrderBankFacadeMiddleRequest middleRequest) {
        // B商家到付修改
        if (middleRequest.getBMerchantDfModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = b2cOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantDfModify());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, B2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                b2cOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        // B商家代收货款修改
        if (middleRequest.getBMerchantCodModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = b2cOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantCodModify());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, B2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                b2cOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        // B商家寄付修改
        if (middleRequest.getBMerchantJfModify() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = b2cOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setBMerchantModify(middleRequest.getBMerchantJfModify());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, B2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                b2cOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        // pos寄付
        if (middleRequest.getPosJfYun() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = b2cOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setPosJfYun(middleRequest.getPosJfYun());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, B2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                b2cOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        // pos到付
        if (middleRequest.getPosYun() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = b2cOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setPosYun(middleRequest.getPosYun());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, B2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                b2cOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        //税金台账创建
        if (middleRequest.getBMerchantTaxCreate() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = b2cOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setOrgId(OTSLedgerUtil.OrgConstant.HKMO_TAX_ORG_ID);
            orderBankFacadeRequest.setOrgName(OTSLedgerUtil.OrgConstant.HKMO_TAX_ORG_NAME);
            orderBankFacadeRequest.setWaybillNo(middleRequest.getTaxBankServiceOrderNo());
            middleRequest.getBMerchantTaxCreate().setPayMode(PayModeEnum.COD);
            orderBankFacadeRequest.setBMerchantCreate(middleRequest.getBMerchantTaxCreate());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, C2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                b2cOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
        if (middleRequest.getPosYunTax() != null) {
            OrderBankFacadeRequest orderBankFacadeRequest = b2cOrderBankFacadeTranslator.toCommonOrderBankFacadeRequest(orderContext.getOrderModel(), messageDto.getRequestProfile().getTenantId());
            orderBankFacadeRequest.setOrgId(OTSLedgerUtil.OrgConstant.HKMO_TAX_ORG_ID);
            orderBankFacadeRequest.setOrgName(OTSLedgerUtil.OrgConstant.HKMO_TAX_ORG_NAME);
            orderBankFacadeRequest.setWaybillNo(middleRequest.getTaxBankServiceOrderNo());
            orderBankFacadeRequest.setPosYun(middleRequest.getPosYunTax());
            OrderBankFacadeResponse orderBankFacadeResponse = orderBankFacade.saveOrUpdate(orderBankFacadeRequest, C2CAsynEnquiryOrderBankHandler.class);
            if (orderBankFacadeResponse != null) {
                b2cOrderBankFacadeTranslator.complementPaymentNo(orderContext, orderBankFacadeResponse);
            }
        }
    }

    /**
     * 持久化异常处理
     *
     * @param orderContext
     */
    private void produceRetryMq(RequestProfile requestProfile, ExpressOrderContext orderContext) throws ParseException {
        SchedulerMessage schedulerMessage = new SchedulerMessage();
        //持久化消息
        ModifyRepositoryMessageDto modifyRepositoryMessageDto = this.toMessageDto(requestProfile, orderContext);
        schedulerMessage.setDtoJson(JSONUtils.beanToJSONDefault(modifyRepositoryMessageDto));
        schedulerMessage.setDtoClass(ModifyRepositoryMessageDto.class);
        schedulerService.addSchedulerTask(PDQTopicEnum.REVERSE_REPOSITORY_RETRY, schedulerMessage,
                FlowConstants.EXPRESS_ORDER_REVERSE_REPOSITORY_FLOW_CODE);
    }

    /**
     * 下发ofc
     *
     * @param orderContext
     * @throws ParseException
     */
    private void issueOrder(ExpressOrderContext orderContext) throws ParseException {
        Set<String> promiseUnits = makingDispatcherHandler.execute(orderContext);
        Optional.ofNullable(promiseUnits).orElseThrow(() ->
                new BusinessDomainException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("下发履约执行层打标无法识别")
        );
        //下发履约层复制给业务身份对象信息
        promiseUnits.forEach(promiseUnit -> orderContext.getOrderModel().getBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //订单领域模型
        promiseUnits.forEach(promiseUnit -> orderContext.getOrderModel().getOrderBusinessIdentity().setFulfillmentUnit(promiseUnit));
        //下发
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = modifyIssueFacadeTranslator.toReverseOrChangeAddressIssueFacadeRequest(
                orderContext);
        modifyIssueFacade.modifyIssue(modifyIssueFacadeRequest, orderContext.getOrderModel().getOrderBusinessIdentity());
    }

    /**
     * 询价，逆向单原单询价时，需要传入逆向单新单支付信息
     *
     * @param orderContext                         原单
     * @param reverseOrderModel                    当前单
     * @param originOrderEnquiryProductNoBlacklist 原单询价增值产品黑名单（无需询价）
     * @return
     */
    private BillingEnquiryFacadeResponse toBillingEnquiryFacadeResponse(ExpressOrderContext orderContext, ExpressOrderModel reverseOrderModel, Set<String> originOrderEnquiryProductNoBlacklist) {
        BillingEnquiryFacadeRequest billingEnquiryFacadeRequest = b2CCreateEnquiryFacadeTranslator.toReverseBillingEnquiryFacadeRequest(orderContext,reverseOrderModel);

        // 处理原单询价时，不需要计费的产品
        if (CollectionUtils.isNotEmpty(originOrderEnquiryProductNoBlacklist)
                && CollectionUtils.isNotEmpty(billingEnquiryFacadeRequest.getProductFacadeDtoList())) {
            List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtoList = billingEnquiryFacadeRequest.getProductFacadeDtoList();
            Iterator<BillingEnquiryFacadeRequest.ProductFacadeDto> iterator = productFacadeDtoList.iterator();
            while (iterator.hasNext()) {
                BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto = iterator.next();
                if (originOrderEnquiryProductNoBlacklist.contains(productFacadeDto.getProductNo())) {
                    LOGGER.info("原单不询价的产品：" + productFacadeDto.getProductNo());
                    iterator.remove();
                }
            }
        }

        return enquiryFacade.billingEnquiry(billingEnquiryFacadeRequest);
    }

    /**
     * 组装context
     *
     * @param messageDto
     * @param orderFacadeResponse
     * @return
     */
    private ExpressOrderContext toExpressOrderContext(OrderBankPdqMessageDto messageDto, GetOrderFacadeResponse orderFacadeResponse) {
        ExpressOrderModelCreator orderModelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(orderFacadeResponse);
        ExpressOrderModel orderModel = new ExpressOrderModel(orderModelCreator);
        orderModel.withRequestProfile(messageDto.getRequestProfile());
        ExpressOrderContext orderContext = new ExpressOrderContext(messageDto.getBusinessIdentity(), messageDto.getRequestProfile(), messageDto.getBusinessIdentity().getBusinessScene());
        orderContext.setOrderModel(orderModel);
        return orderContext;
    }

    /**
     * 查询订单详情
     *
     * @param profile
     * @param orderNo
     * @return
     */
    private GetOrderFacadeResponse toGetOrderFacadeResponse(RequestProfile profile, String orderNo) {
        GetOrderFacadeRequest facadeRequest = new GetOrderFacadeRequest();
        facadeRequest.setOrderNo(orderNo);
        return getOrderFacade.getOrder(profile, facadeRequest);
    }

    private ExpressOrderModelCreator parseCollectionOrgModelCreator(String orgId, String orgName) {
        ExpressOrderModelCreator creater = new ExpressOrderModelCreator();
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        financeInfoDto.setCollectionOrgNo(orgId);
        financeInfoDto.setCollectionOrgName(orgName);
        creater.setFinanceInfo(financeInfoDto);
        return creater;
    }

    /**
     * 原单财务数据根据计费结果重算
     *
     * @param orderContext
     * @param originOrderFinance 原单费用
     * @param currentOrder 当前单计费
     */
    public void complementBillingResult(ExpressOrderContext orderContext, FinanceDetailInfoDto originOrderFinance, BillingEnquiryFacadeResponse currentOrder) {
        enquiryFacadeTranslator.complementBillingResult(orderContext,currentOrder);
        if (null != originOrderFinance) {
            Finance finance = orderContext.getOrderModel().getFinance();
            //折前金额
            finance.getPreAmount().setAmount(finance.getPreAmount().getAmount().add(originOrderFinance.getPreAmount().getAmount()));
            //折后金额
            finance.getDiscountAmount().setAmount(finance.getDiscountAmount().getAmount().add(originOrderFinance.getDiscountAmount().getAmount()));
            //总优惠金额
            Money totalDiscountAmount = new Money();
            totalDiscountAmount.setAmount(finance.getPreAmount().getAmount().subtract(finance.getDiscountAmount().getAmount()));
            totalDiscountAmount.setCurrency(finance.getDiscountAmount().getCurrency());
            finance.setTotalDiscountAmount(totalDiscountAmount);
            //追加费用明细
            finance.getFinanceDetails().add(FinanceDetailMapper.INSTANCE.toFinanceDetail(originOrderFinance));
        }
    }

    /**
     * 原单财务数据根据计费结果重算
     *
     * @param orderContext
     * @param billMap      所有计费
     * @param currentOrder 当前单计费
     */
    public void complementBillingResult(ExpressOrderContext orderContext, Map<String, BillingEnquiryFacadeResponse> billMap, BillingEnquiryFacadeResponse currentOrder) {
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();

        BigDecimal preMoney = new BigDecimal(0);
        BigDecimal disMoney = new BigDecimal(0);
        CurrencyCodeEnum preCodeEnum = null;
        CurrencyCodeEnum disCodeEnum = null;
        //费用明细 key：CostNo
        Map<String,FinanceDetailInfoDto> detailInfoDtoMap =  new HashMap<>();
        StringBuilder financeRemark = new StringBuilder();
        for (Map.Entry<String, BillingEnquiryFacadeResponse> entry : billMap.entrySet()) {
            // 费用总额
            preMoney = preMoney.add(entry.getValue().getFinanceFacadeDto().getPreAmount().getAmount());
            preCodeEnum = entry.getValue().getFinanceFacadeDto().getPreAmount().getCurrencyCode();

            disMoney = disMoney.add(entry.getValue().getFinanceFacadeDto().getDiscountAmount().getAmount());
            disCodeEnum = entry.getValue().getFinanceFacadeDto().getDiscountAmount().getCurrencyCode();
            financeRemark.append(entry.getKey()).append(":").append(entry.getValue().getFinanceFacadeDto().getDiscountAmount().getAmount()).append(",");
            if(null != entry.getValue().getFinanceFacadeDto() && CollectionUtils.isNotEmpty(entry.getValue().getFinanceFacadeDto().getFinanceDetailFacadeDtoList())){
                // 费用明细
                for (BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : entry.getValue().getFinanceFacadeDto().getFinanceDetailFacadeDtoList()) {
                    //费用明细按CostNo分组
                    FinanceDetailInfoDto detailInfoDto = detailInfoDtoMap.get(detailFacadeDto.getCostNo());
                    if (detailInfoDto == null) {
                        detailInfoDto = new FinanceDetailInfoDto();
                    }
                    //费用明细整合
                    toFinanceDetailInfoDto(detailInfoDto,detailFacadeDto);
                    LOGGER.info("费用明细整合出参detailInfoDto：{}",JSONUtils.beanToJSONDefault(detailInfoDto));

                    if (CollectionUtils.isNotEmpty(detailFacadeDto.getDiscountInfoFacadeDtos())) {
                        // 费用折扣
                        Map<String,DiscountInfoDto> discountInfoDtoMap = new HashMap<>();
                        for (BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto : detailFacadeDto.getDiscountInfoFacadeDtos()) {
                            //费用折扣按DiscountNo分组
                            DiscountInfoDto discountInfoDto = discountInfoDtoMap.get(discountInfoFacadeDto.getDiscountNo());
                            if (discountInfoDto == null) {
                                discountInfoDto = new DiscountInfoDto();
                            }
                            //折扣整合
                            toDiscountInfoFacadeDto(discountInfoDto, discountInfoFacadeDto);
                            discountInfoDtoMap.put(discountInfoDto.getDiscountNo(), discountInfoDto);
                        }
                        List<DiscountInfoDto> discountInfoDtoList = new ArrayList<>(discountInfoDtoMap.values());
                        detailInfoDto.setDiscountInfoDtos(discountInfoDtoList);
                    }

                    //明细remark
                    StringBuilder detailRemark;
                    if (StringUtils.isNotBlank(detailInfoDto.getRemark())) {
                        detailRemark = new StringBuilder(detailInfoDto.getRemark());
                    } else {
                        detailRemark = new StringBuilder();
                    }
                    detailRemark.append(entry.getKey()).append(":").append(detailInfoDto.getDiscountAmount().getAmount()).append(",");
                    detailInfoDto.setRemark(detailRemark.toString());

                    detailInfoDto.setExtendProps(new HashMap<>());
                    // 价格项明细
                    if (detailFacadeDto.getExtendProps() != null && detailFacadeDto.getExtendProps().containsKey(CALC_PRICE_ITEM_LIST)) {
                        detailInfoDto.getExtendProps().put(CALC_PRICE_ITEM_LIST, JSONUtils.beanToJSONDefault(detailFacadeDto.getExtendProps().get(CALC_PRICE_ITEM_LIST)));
                    }

                    detailInfoDtoMap.put(detailInfoDto.getCostNo(),detailInfoDto);
                }
            }
        }
        LOGGER.info("费用明细整合出参detailInfoDtoMap：{}",JSONUtils.mapToJson(detailInfoDtoMap));
        List<FinanceDetailInfoDto> financeDetailInfoDtoList = new ArrayList<>(detailInfoDtoMap.values());
        //折前金额
        MoneyInfoDto preAmount = new MoneyInfoDto();
        preAmount.setAmount(preMoney);
        preAmount.setCurrencyCode(preCodeEnum);
        financeInfoDto.setPreAmount(preAmount);
        //折后金额
        MoneyInfoDto discountAmount = new MoneyInfoDto();
        discountAmount.setAmount(disMoney);
        discountAmount.setCurrencyCode(disCodeEnum);
        financeInfoDto.setDiscountAmount(discountAmount);
        //计费重量
        financeInfoDto.setBillingWeight(currentOrder.getFinanceFacadeDto().getBillingWeight());
        //计费体积
        financeInfoDto.setBillingVolume(currentOrder.getFinanceFacadeDto().getBillingVolume());
        financeInfoDto.setBillingMode(currentOrder.getFinanceFacadeDto().getBillingMode());
        //费用明细
        financeInfoDto.setFinanceDetailInfos(financeDetailInfoDtoList);
//        toFinanceDetailInfoDto(currentOrder.getFinanceFacadeDto().getFinanceDetailFacadeDtoList(),detailRemark.toString())
        //总优惠金额
        MoneyInfoDto totalDiscountAmount = new MoneyInfoDto();
        totalDiscountAmount.setAmount(preMoney.subtract(disMoney));
        totalDiscountAmount.setCurrencyCode(preCodeEnum);
        financeInfoDto.setTotalDiscountAmount(totalDiscountAmount);
        financeInfoDto.setRemark(financeRemark.toString());
        modelCreator.setFinanceInfo(financeInfoDto);
        orderContext.getOrderModel().complement().complementFinanceInfo(this, modelCreator);
    }

    private List<FinanceDetailInfoDto> toFinanceDetailInfoDto(List<BillingEnquiryFacadeResponse.FinanceDetailFacadeDto> detailFacadeDtos, String remark) {
        List<FinanceDetailInfoDto> financeDetailInfoDtos = new ArrayList<>();
        for (BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto : detailFacadeDtos) {
            FinanceDetailInfoDto financeDetailInfoDto = new FinanceDetailInfoDto();
            financeDetailInfoDto.setPointsInfoDto(detailFacadeDto.getPointsInfoDto());
            financeDetailInfoDto.setCostName(detailFacadeDto.getCostName());
            financeDetailInfoDto.setCostNo(detailFacadeDto.getCostNo());
            //折后金额
            MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
            detailDiscountAmount.setAmount(detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getAmount() : null);
            detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getCurrencyCode() : null);
            financeDetailInfoDto.setDiscountAmount(detailDiscountAmount);
            //折前金额
            MoneyInfoDto detailPreAmount = new MoneyInfoDto();
            detailPreAmount.setAmount(detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getAmount() : null);
            detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getCurrencyCode() : null);
            financeDetailInfoDto.setPreAmount(detailPreAmount);
            financeDetailInfoDto.setDiscountInfoDtos(toDiscountInfoFacadeDto(detailFacadeDto.getDiscountInfoFacadeDtos()));
            financeDetailInfoDto.setProductNo(detailFacadeDto.getProductNo());
            financeDetailInfoDto.setProductName(detailFacadeDto.getProductName());
            financeDetailInfoDto.setRemark(remark);
            financeDetailInfoDtos.add(financeDetailInfoDto);
        }
        return financeDetailInfoDtos;
    }

    private List<DiscountInfoDto> toDiscountInfoFacadeDto(List<BillingEnquiryFacadeResponse.DiscountInfoFacadeDto> discountInfoFacadeDto) {
        List<DiscountInfoDto> discountInfoDtos = new ArrayList<>();
        for (BillingEnquiryFacadeResponse.DiscountInfoFacadeDto facadeDto : discountInfoFacadeDto) {
            DiscountInfoDto discountInfoDto = new DiscountInfoDto();
            discountInfoDto.setDiscountNo(facadeDto.getDiscountNo());
            discountInfoDto.setDiscountType(facadeDto.getDiscountType());
            Money disAmount = new Money();
            disAmount.setAmount(facadeDto.getDiscountedAmount() != null ? facadeDto.getDiscountedAmount().getAmount() : null);
            disAmount.setCurrency(facadeDto.getDiscountedAmount() != null ? facadeDto.getDiscountedAmount().getCurrencyCode() : null);
            discountInfoDto.setDiscountedAmount(disAmount);
            discountInfoDtos.add(discountInfoDto);
        }
        return discountInfoDtos;
    }

    private void toFinanceDetailInfoDto(FinanceDetailInfoDto detailInfoDto,BillingEnquiryFacadeResponse.FinanceDetailFacadeDto detailFacadeDto) {
        //折前金额
        MoneyInfoDto detailPreAmount = new MoneyInfoDto();
        BigDecimal preNewMoney = detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getAmount() : null;
        BigDecimal preMoneySum = detailInfoDto.getPreAmount() != null ? detailInfoDto.getPreAmount().getAmount().add(preNewMoney != null ? preNewMoney : BigDecimal.ZERO) : preNewMoney;
        detailPreAmount.setAmount(preMoneySum);
        detailPreAmount.setCurrencyCode(detailFacadeDto.getPreAmount() != null ? detailFacadeDto.getPreAmount().getCurrencyCode() : null);
        detailInfoDto.setPreAmount(detailPreAmount);
        //折后金额
        MoneyInfoDto detailDiscountAmount = new MoneyInfoDto();
        BigDecimal disNewMoney = detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getAmount() : null;
        BigDecimal disMoneySum = detailInfoDto.getDiscountAmount() != null ? detailInfoDto.getDiscountAmount().getAmount().add(disNewMoney != null ? disNewMoney : BigDecimal.ZERO) : disNewMoney;
        detailDiscountAmount.setAmount(disMoneySum);
        detailDiscountAmount.setCurrencyCode(detailFacadeDto.getDiscountAmount() != null ? detailFacadeDto.getDiscountAmount().getCurrencyCode() : null);
        detailInfoDto.setDiscountAmount(detailDiscountAmount);
        detailInfoDto.setCostNo(detailFacadeDto.getCostNo());
        detailInfoDto.setCostName(detailFacadeDto.getCostName());
        detailInfoDto.setProductNo(detailFacadeDto.getProductNo());
    }

    private void toDiscountInfoFacadeDto(DiscountInfoDto discountInfoDto, BillingEnquiryFacadeResponse.DiscountInfoFacadeDto discountInfoFacadeDto) {
        discountInfoDto.setDiscountNo(discountInfoFacadeDto.getDiscountNo());
        discountInfoDto.setDiscountType(discountInfoFacadeDto.getDiscountType());

        Money disAmount = new Money();
        BigDecimal discountNewMoney = discountInfoFacadeDto.getDiscountedAmount() != null ? discountInfoFacadeDto.getDiscountedAmount().getAmount() : null;
        BigDecimal discountSumMoney = discountInfoDto.getDiscountedAmount() != null ? discountInfoDto.getDiscountedAmount().getAmount().add(discountNewMoney != null ? discountNewMoney : BigDecimal.ZERO) : discountNewMoney;
        disAmount.setAmount(discountSumMoney);
        disAmount.setCurrency(discountInfoFacadeDto.getDiscountedAmount().getCurrencyCode());
        discountInfoDto.setDiscountedAmount(disAmount);
    }

    /**
     * 功能: 转换消息, 大报文
     *
     * @param:
     * @return:
     * @throw:
     * @description: todo, 使用jss存储大报文
     * @author: liufarui
     * @date: 2021/6/22 14:36
     */
    private ModifyRepositoryMessageDto toMessageDto(RequestProfile requestProfile, ExpressOrderContext context) throws ParseException {
        ModifyRepositoryMessageDto messageDto = new ModifyRepositoryMessageDto();
        messageDto.setRequestProfile(requestProfile);
        messageDto.setModifyOrderFacadeRequest(modifyOrderFacadeTranslator.toModifyOrderFacadeRequest(context));
        return messageDto;
    }

    /**
     * 用于传输lock，避免在上下文中增加lock信息
     */
    private static class Lock {
        private IRedisLock redisLock;

        IRedisLock getRedisLock() {
            return redisLock;
        }

        void setRedisLock(IRedisLock redisLock) {
            this.redisLock = redisLock;
        }
    }

    /**
     * 当前单单是否需要询价
     * @param orderModel
     * @return
     */
    public boolean needCurrentEnquiry(ExpressOrderModel orderModel) {
        //港澳拦截逆向单
        if(orderModel.isHKMO() && orderModel.isInterceptReverse()){
            // 出口的逆向单需要询价
            if(FLowDirectionUtils.FLowDirection.CN2HKMO == FLowDirectionUtils.getFLowDirection(orderModel.getOrderSnapshot().getCustoms())){
                LOGGER.info("拦截逆向当前单询价-出口单-需要询价,当前单:{}",orderModel.orderNo());
                return true;
            }
            // 进口的逆向单-只有原单到付-才需要询价
            else if(FLowDirectionUtils.FLowDirection.HKMO2CN == FLowDirectionUtils.getFLowDirection(orderModel.getOrderSnapshot().getCustoms())
                    && orderModel.getOrderSnapshot().isCashOnDelivery()){
                LOGGER.info("拦截逆向当前单询价-进口原单到付-需要询价,当前单:{}",orderModel.orderNo());
                return true;
            } else {
                // 同城互寄不需要询价
                LOGGER.info("拦截逆向当前单询价-同城互寄或进口原单寄付-不需要询价,当前单:{}",orderModel.orderNo());
                return false;
            }
        }
        return true;
    }

    /**
     * 港澳国际跨境业务-逆向单-询原单场景
     * @param refOrderContext 原单上下文
     * @param orderContext 新单上下文
     * @param billingEnquiryFacadeResponse 新单询价结果
     */
    private BillingEnquiryFacadeResponse crossBorderReverseOriEnquiry(ExpressOrderContext refOrderContext, ExpressOrderContext orderContext, BillingEnquiryFacadeResponse billingEnquiryFacadeResponse){
        //当前订单币种
        CurrencyCodeEnum currencyCode = billingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getCurrencyCode();
        BillingEnquiryFacadeResponse oriBillingEnquiryFacadeResponse = null;

        //原单询价金额直接继承原单场景 不再重新询价获取场景
        //1 港澳 拒收｜清关逆向 原单到付 新单到付
        if(refOrderContext.getOrderModel().isHKMO()//原单港澳订单
                && (orderContext.getOrderModel().isRejectReverse()//新单为拒收逆向
                || orderContext.getOrderModel().isCustomsClearanceReverse())//新单为清关逆向
                && refOrderContext.getOrderModel().isCashOnDelivery()//原单到付现结
                && orderContext.getOrderModel().isCashOnDelivery()//新单到付现结
        ){
            LOGGER.info("国际港澳出口-原单询价金额直接继承原单场景,当前单:{},原单:{}",orderContext.getOrderModel().orderNo(),refOrderContext.getOrderModel().orderNo());
            //原单询价汇前金额-报价币种
            BigDecimal refOrderMoney;
            Map<String, String> extendProps = refOrderContext.getOrderModel().getFinance().getExtendProps();
            if(MapUtils.isNotEmpty(extendProps) && StringUtils.isNotBlank(extendProps.get(FinanceConstants.BEFORE_EXCHANGE_DISCOUNT_AMOUNT))){
                //原单询价汇前金额
                MoneyInfoDto beforeExchangeDiscountAmount = JSONUtils.jsonToBean(extendProps.get(FinanceConstants.BEFORE_EXCHANGE_DISCOUNT_AMOUNT), MoneyInfoDto.class);
                refOrderMoney = beforeExchangeDiscountAmount.getAmount();
                //原单询价汇前币种
                //如果金额>0 且币种和逆向单币种一致则加和处理
                if(null != refOrderMoney && refOrderMoney.compareTo(BigDecimal.ZERO) > 0
                        && beforeExchangeDiscountAmount.getCurrencyCode() == currencyCode){
                    oriBillingEnquiryFacadeResponse = new BillingEnquiryFacadeResponse();
                    BillingEnquiryFacadeResponse.FinanceFacadeDto financeFacadeDto = new BillingEnquiryFacadeResponse.FinanceFacadeDto();
                    financeFacadeDto.setPreAmount(MoneyFacadeMapper.INSTANCE.toEnquiryMoneyFacade(beforeExchangeDiscountAmount));
                    financeFacadeDto.setDiscountAmount(MoneyFacadeMapper.INSTANCE.toEnquiryMoneyFacade(beforeExchangeDiscountAmount));
                    oriBillingEnquiryFacadeResponse.setFinanceFacadeDto(financeFacadeDto);
                }
            }
        }
        //原单重新询价场景
        //港澳-出口-拦截逆向 || fixme 国际出口拦截逆向
        if(isHKMOExportInterceptReverse(orderContext.getOrderModel()) || isExportInterceptReverse(orderContext.getOrderModel())){
            LOGGER.info("国际港澳出口-原单重新询价场景-拦截逆向,当前单:{},原单:{}",orderContext.getOrderModel().orderNo(),refOrderContext.getOrderModel().orderNo());
            BillingEnquiryFacadeResponse refBillingEnquiryFacadeResponse = originOrderEnquiry(refOrderContext, orderContext);
            //原单、新单费用合并至新单
            BigDecimal refOrderMoney = BigDecimal.ZERO;
            if (refBillingEnquiryFacadeResponse != null && refBillingEnquiryFacadeResponse.getFinanceFacadeDto() != null
                    && refBillingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount() != null
                    && refBillingEnquiryFacadeResponse.getFinanceFacadeDto().getDiscountAmount().getAmount() != null) {
                //原单费用
                oriBillingEnquiryFacadeResponse = refBillingEnquiryFacadeResponse;
            }
        }
        return oriBillingEnquiryFacadeResponse;
    }
    /**
     * 港澳-出口-拦截逆向
     * @param orderModel
     * @return
     */
    private boolean isHKMOExportInterceptReverse(ExpressOrderModel orderModel) {
        return orderModel.isHKMO() //港澳
                && orderModel.isInterceptReverse() //拦截逆向
                && FLowDirectionUtils.FLowDirection.CN2HKMO == FLowDirectionUtils.getFLowDirection(orderModel.getOrderSnapshot().getCustoms()) //原单内地到港澳
                ;
    }

    /**
     * 出口-拦截逆向
     * @param orderModel
     * @return
     */
    private boolean isExportInterceptReverse(ExpressOrderModel orderModel) {
        return orderModel.getOrderSnapshot().isExport() //原单出口
                && orderModel.isInterceptReverse() //拦截逆向
                ;
    }

    /**
     * 逆向单原单询价
     * @param originContext  原单
     * @param reverseContext 逆向单
     * @return
     */
    private BillingEnquiryFacadeResponse originOrderEnquiry(ExpressOrderContext originContext, ExpressOrderContext reverseContext) {
        ExpressOrderModel reverseOrderModel = reverseContext.getOrderModel();
        LOGGER.info("拦截原单询价,当前单:{},原单:{}",JSONUtils.beanToJSONDefault(reverseOrderModel),JSONUtils.beanToJSONDefault(originContext.getOrderModel()));
        BillingEnquiryFacadeRequest facadeRequest = enquiryFacadeTranslator.toBillingEnquiryFacadeRequest(originContext);
        //收件人信息拦截逆向，需要替换收件人地址-为逆向单寄件人地址
        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = toConsigneeAddressFacadeDto(reverseOrderModel.getConsignor().getAddress());
        BillingEnquiryFacadeRequest.ConsigneeFacadeDto consigneeFacadeDto = new BillingEnquiryFacadeRequest.ConsigneeFacadeDto();
        consigneeFacadeDto.setAddressFacadeDto(addressFacadeDto);
        facadeRequest.setConsigneeFacadeDto(consigneeFacadeDto);
        //计费询价跨境 原单询价按逆向新单取反赋值
        String startRegionNo = reverseOrderModel.getConsignee().getAddress().getRegionNo();
        String endRegionNo = reverseOrderModel.getConsignor().getAddress().getRegionNo();
        if(StringUtils.isNotBlank(startRegionNo) && StringUtils.isNotBlank(endRegionNo)){
            facadeRequest.getExtendProps().put(EnquiryConstants.CROSS_BORDER_TYPE,startRegionNo + "_" + endRegionNo);
        } else {
            facadeRequest.getExtendProps().remove(EnquiryConstants.CROSS_BORDER_TYPE);
        }
        // 拦截都不应该传换汇币种；不会出现跨境后拦截
        facadeRequest.getExtendProps().remove(EnquiryConstants.EXCHANGE_CURRENCY);
        //国际拦截逆向原单询价 需要使用逆向单的主产品
        if(originContext.getOrderModel().isIntl()){
            intlReplaceProduct(facadeRequest, reverseContext);
        }
        return enquiryFacade.billingEnquiry(facadeRequest);
    }

    /**
     * 收件人信息 传过来的orderModel
     * @param consigneeAddress
     * @return
     */
    private BillingEnquiryFacadeRequest.AddressFacadeDto toConsigneeAddressFacadeDto(Address consigneeAddress) {

        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        if (consigneeAddress != null) {
            //收件人行政区｜国家
            addressFacadeDto.setRegionNo(consigneeAddress.getRegionNo());
            addressFacadeDto.setRegionName(consigneeAddress.getRegionName());
            //收件人省
            addressFacadeDto.setProvinceNoGis(consigneeAddress.getProvinceNoGis());
            addressFacadeDto.setProvinceNo(consigneeAddress.getProvinceNo());
            // 收件人市
            addressFacadeDto.setCityNoGis(consigneeAddress.getCityNoGis());
            addressFacadeDto.setCityNo(consigneeAddress.getCityNo());
            //收件人县
            addressFacadeDto.setCountyNoGis(consigneeAddress.getCountyNoGis());
            addressFacadeDto.setCountyNo(consigneeAddress.getCountyNo());
            addressFacadeDto.setTownNoGis(consigneeAddress.getTownNoGis());
            addressFacadeDto.setTownNo(consigneeAddress.getTownNo());
        }
        return addressFacadeDto;
    }

    /**
     * 国际拦截逆向原单询价 需要使用逆向单的主产品
     * fixme 现在收入归属是按产品区分的 所以新增国际产品，
     *  // 正向逆向新建两不同产品：国际特惠送(收件地址不包含国内，所以拦截逆向 国内到国内的正向单价格本无法配置，无法支持询价)  国际特惠送逆向
     *  // 且 正向单增值产品参与询价
     *  // 且正向单的附加服务费不参与询价，原因：
     * @param facadeRequest
     * @param reverseContext
     */
    private void intlReplaceProduct(BillingEnquiryFacadeRequest facadeRequest, ExpressOrderContext reverseContext){
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> productFacadeDtoList = facadeRequest.getProductFacadeDtoList().stream()
                .filter(productFacadeDto -> {return Boolean.FALSE.equals(productFacadeDto.isAttachFees());})
                .map(productFacadeDto -> {
                    if (ServiceProductTypeEnum.MAIN_PRODUCT.getCode().equals(productFacadeDto.getProductType())) {
                        IProduct mainProduct = reverseContext.getOrderModel().getProductDelegate().getMainProduct();
                        BillingEnquiryFacadeRequest.ProductFacadeDto reverseProductFacadeDto = new BillingEnquiryFacadeRequest.ProductFacadeDto();
                        reverseProductFacadeDto.setProductNo(mainProduct.getProductNo());
                        reverseProductFacadeDto.setProductType(mainProduct.getProductType());
                        reverseProductFacadeDto.setParentNo(mainProduct.getParentNo());
                        reverseProductFacadeDto.setProductAttrs(mainProduct.getProductAttrs());
                        return reverseProductFacadeDto;
                    } else {
                        return productFacadeDto;
                    }
                }).collect(Collectors.toList());
        facadeRequest.setProductFacadeDtoList(productFacadeDtoList);
        LOGGER.info("国际出口拦截逆向，原单询价,产品信息:{}",JSONUtils.beanToJSONDefault(productFacadeDtoList));
    }

    /**
     *
     * @param fromCurrency 换汇前币种
     * @param toCurrency 换汇后币种
     * @return rate 汇率
     */
    private BigDecimal getExchangeRate(String fromCurrency, String toCurrency){
        if(StringUtils.isBlank(fromCurrency) || StringUtils.isBlank(toCurrency)){
            return BigDecimal.ONE;
        }
        if(fromCurrency.equals(toCurrency)){
            return BigDecimal.ONE;
        }
        ExchangeCurrencyRequest request = new ExchangeCurrencyRequest();
        request.setBaseCurrency(fromCurrency);
        request.setToCurrency(toCurrency);
        return exchangeCurrencyFacade.exchangeCurrency(request).getExchangeRate();
    }

    /**
     * @param beforeExchangeAmt 换汇前金额
     * @param exchangeRate 汇率
     * @param currencyCode 换汇后币种
     * @return 换汇后金额
     */
    private Money exchangeAmt(Money beforeExchangeAmt, BigDecimal exchangeRate, CurrencyCodeEnum currencyCode){
        if(null == beforeExchangeAmt){
            return null;
        }
        Money afterExchangeAmt = new Money();
        afterExchangeAmt.setCurrency(currencyCode);
        //四舍五入保留两位小数
        afterExchangeAmt.setAmount(beforeExchangeAmt.getAmount().multiply(exchangeRate)
                .setScale(0, RoundingMode.HALF_UP));
        return afterExchangeAmt;
    }

    /**
     * 到付原单税金处理
     * @param refOrderContext
     * @param orderContext
     * @param currencyCode
     */
    private void dfOriTax(ExpressOrderContext refOrderContext, ExpressOrderContext orderContext, CurrencyCodeEnum currencyCode){
        //原单税金到付处理
        Finance finance = refOrderContext.getOrderModel().getFinance();
        if(null != finance && null != finance.getEstimatedTax()
                && null != finance.getEstimatedTax().getAmount()
                && finance.getEstimatedTax().getAmount().compareTo(BigDecimal.ZERO) > 0
                && null != finance.getTaxSettlementType()
                && SettlementTypeEnum.CASH_ON_DELIVERY.getCode().equals(finance.getTaxSettlementType())){
            LOGGER.info("国际港澳出口-税金处理,当前单:{},原单:{}",orderContext.getOrderModel().orderNo(),refOrderContext.getOrderModel().orderNo());
            //todo 税金换汇
            //汇率
            BigDecimal exchangeRate = getExchangeRate(finance.getEstimatedTax().getCurrency().getCode(), currencyCode.getCode());
            orderContext.getOrderModel().getFinance()
                    .complementTax(exchangeAmt(finance.getEstimatedTax(), exchangeRate, currencyCode)
                            , exchangeAmt(finance.getActualTax(), exchangeRate, currencyCode)
                            , finance.getTaxSettlementType());
        }
    }

    /**
     * 港澳同城改址从改址记录获取原单应收总费用
     * 参考非港澳的代码，不同之处是只从改址记录查费用
     * 备注：港澳当前只能逆向一次，并且改址只有港澳同城
     * @refOrderContext 正向单上下文
     */
    private FinanceDetailInfoDto getOriginOrderTotalAmountByModifyRecords(ExpressOrderContext refOrderContext) {
        BigDecimal totalAmount = BigDecimal.ZERO;

        //若原单是正向单，则需查询原单到付未支付的台账信息，金额合并继承到新单
        List<ModifyRecord> modifyRecords = refOrderContext.getOrderModel().getModifyRecordDelegate().getEnabledModifyRecords();

        for (ModifyRecord modifyRecord : modifyRecords) {
            BigDecimal dfAmount = BigDecimal.ZERO;
            if (ModifyRecordTypeEnum.READDRESS.getCode().equals(modifyRecord.getModifyRecordType()) // 改址记录
                    || ModifyRecordTypeEnum.INTERCEPT.getCode().equals(modifyRecord.getModifyRecordType())) {// 拦截记录
                //改址记录
                ReaddressRecordDetailInfo readdressDetail = (ReaddressRecordDetailInfo) modifyRecord.getModifyRecordDetail();
                if (SettlementTypeEnum.CASH_ON_DELIVERY.getCode().equals(readdressDetail.getFinance().getSettlementType())
                        && !PaymentStatusEnum.COMPLETE_PAYMENT.getPaymentStatus().equals(readdressDetail.getFinance().getPaymentStatus())
                        && (null != readdressDetail.getFinance().getPendingMoney() && BigDecimal.ZERO.compareTo(readdressDetail.getFinance().getPendingMoney().getAmount()) < 0)) {
                    //到付&&未支付&&待支付金额大于0的改址记录的台账金额，需继承到逆向新单上
                    dfAmount = getBMerchantDfAmount(modifyRecord.getModifyRecordNo());
                    LOGGER.info("到付&&未支付&&待支付金额大于0的改址记录号:{},dfAmount{}",modifyRecord.getModifyRecordNo(),dfAmount);
                } else {
                    continue;
                }
            } else if (ModifyRecordTypeEnum.RECHECK.getCode().equals(modifyRecord.getModifyRecordType())) {
                //复重复量方询价记录记录
                dfAmount = getBMerchantDfAmount(modifyRecord.getModifyRecordNo());
                LOGGER.info("复重复量方询价记录号:{},dfAmount{}",modifyRecord.getModifyRecordNo(),dfAmount);
            }
            totalAmount = totalAmount.add(dfAmount);
        }


        if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
            LOGGER.info("原单到付运费大于0，需继承到新单");
            //组装费用：费用编码：9999、描述：原单应收总费用
            // fixme 目前港澳同城改址，只会是港币或者澳门元，直接看原单即可。后续放开非同城需要优化
            CurrencyCodeEnum currencyCodeEnum = getCurrencyCodeEnum(refOrderContext.getOrderModel());
            FinanceDetailInfoDto originOrderFinance = new FinanceDetailInfoDto();
            originOrderFinance.setCostNo("9999");
            originOrderFinance.setCostName("原单应收总费用");
            MoneyInfoDto moneyInfoDto = new MoneyInfoDto();
            moneyInfoDto.setAmount(totalAmount);
            moneyInfoDto.setCurrencyCode(currencyCodeEnum);
            originOrderFinance.setPreAmount(moneyInfoDto);
            originOrderFinance.setDiscountAmount(moneyInfoDto);
            return originOrderFinance;
        }
        return null;
    }

    /**
     * 从快照获取币种
     */
    private CurrencyCodeEnum getCurrencyCodeEnum(ExpressOrderModel orderSnapshot) {
        CurrencyCodeEnum currencyCodeEnum = CurrencyCodeEnum.CNY;
        if(orderSnapshot.isHKMO() || orderSnapshot.isIntl()){
            if(null != orderSnapshot.getConsignor()
                    && null != orderSnapshot.getConsignor().getAddress()
                    && StringUtils.isNotBlank(orderSnapshot.getConsignor().getAddress().getRegionNo())
                    && null != orderSnapshot.getConsignee()
                    && null != orderSnapshot.getConsignee().getAddress()
                    && StringUtils.isNotBlank(orderSnapshot.getConsignee().getAddress().getRegionNo())
            ){
                String startRegionNo = orderSnapshot.getConsignor().getAddress().getRegionNo();
                String endRegionNo = orderSnapshot.getConsignee().getAddress().getRegionNo();
                if(null != orderSnapshot.getFinance() && null != orderSnapshot.getFinance().getSettlementType()){
                    //换汇币种
                    // fixme 当前港澳改址改派只有同城改址，原单结算方式和改址结算方式不同不影响换汇币种；后续放开非同城改址需要考虑传改址结算方式
                    return ReceiptCurrencyUtil.getCurrency(startRegionNo, endRegionNo, orderSnapshot.getFinance().getSettlementType().getCode());
                }
            }
        }
        return currencyCodeEnum;
    }
}
