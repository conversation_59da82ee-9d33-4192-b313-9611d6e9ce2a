package cn.jdl.oms.express.freight;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.PromotionInfo;
import cn.jdl.oms.core.model.TicketInfo;
import cn.jdl.oms.express.domain.infrs.acl.facade.issue.ModifyIssueFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.issue.ModifyIssueFacadeRequest;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import cn.jdl.oms.express.model.CallBackExpressOrderRequest;
import cn.jdl.oms.express.model.CallBackExpressOrderResponse;
import cn.jdl.oms.express.service.CallBackExpressOrderService;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldValueEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.SystemSubCallerEnum;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description:快运回传测试
 * @author:yangyusong1
 * @createdate:2023-03-23 10:44
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:./applicationContext.xml"})
@ActiveProfiles("test")
public class FreightCallbackOrderTest {

    @Resource
    private CallBackExpressOrderService callBackExpressOrderServiceImpl;

    /**
     * 修改下发服务
     */
    @Resource
    private ModifyIssueFacade modifyIssueFacade;

    @Test
    public void test() {
        RequestProfile requestProfile = JSON.parseObject("{\"ext\": {},\"locale\": \"zh_CN\",\"tenantId\": \"1000\",\"timeZone\": \"GMT+8\",\"traceId\": \"1612345234234560\"}", RequestProfile.class);

        String requestStr =
                "{\n" +
                "      \"businessIdentity\": {\n" +
                "            \"businessStrategy\": \"BFreight\",\n" +
                "            \"businessType\": \"transport\",\n" +
                "            \"businessUnit\": \"cn_jdl_freight-service\"\n" +
                "      },\n" +
                "      \"orderNo\":\"ECO1000010006422862\",\n" +
                "      \"channelInfo\": {\n" +
                "            \"channelOperateTime\": \"2021-01-12 12:00:00\",\n" +
                "            \"systemCaller\": \"Shop\",\n" +
                "            \"systemSubCaller\": \"Shop\"\n" +
                "      },\n" +
                "      \"operationType\": \"1\",\n" +
                "      \"operator\": \"EasyOne\",\n" +
                "      \"remark\": \"快运回传测试\"\n" +
                "}";

        CallBackExpressOrderRequest request = JSON.parseObject(requestStr,CallBackExpressOrderRequest.class);
        request.setExecutedStatus("150");
        CallBackExpressOrderResponse response = callBackExpressOrderServiceImpl.callBackOrder(requestProfile, request);
        System.out.println(JSONUtils.beanToJSONDefault(response));
    }

    @Test
    public void testReverse() {
        RequestProfile requestProfile = JSON.parseObject("{\"ext\": {},\"locale\": \"zh_CN\",\"tenantId\": \"1000\",\"timeZone\": \"GMT+8\",\"traceId\": \"1612345234234560\"}", RequestProfile.class);

        String requestStr =
                "{\n" +
                "      \"businessIdentity\": {\n" +
                "            \"businessStrategy\": \"BFreight\",\n" +
                "            \"businessType\": \"reverse_transport\",\n" +
                "            \"businessUnit\": \"cn_jdl_freight-consumer\"\n" +
                "      },\n" +
                "      \"orderNo\":\"FO0010002077684\",\n" +
                "      \"channelInfo\": {\n" +
                "            \"channelOperateTime\": \"2021-01-12 12:00:00\",\n" +
                "            \"systemCaller\": \"Shop\",\n" +
                "            \"systemSubCaller\": \"Shop\"\n" +
                "      },\n" +
                "      \"operationType\": \"1\",\n" +
                "      \"operator\": \"EasyOne\",\n" +
                "      \"remark\": \"快运回传测试\"\n" +
                "}";

        CallBackExpressOrderRequest request = JSON.parseObject(requestStr,CallBackExpressOrderRequest.class);
        request.setExecutedStatus("150");
        CallBackExpressOrderResponse response = callBackExpressOrderServiceImpl.callBackOrder(requestProfile, request);
        System.out.println(JSONUtils.beanToJSONDefault(response));
    }

    @Test
    public void issueOfc() {
        RequestProfile requestProfile = JSON.parseObject("{\"ext\":{},\"locale\":\"zh_CN\",\"tenantId\":\"1000\"," +
                "\"timeZone\":\"GMT+8\",\"traceId\":\"1111111111111111\"}", RequestProfile.class);

        //下发OFC优惠券入参
        ModifyIssueFacadeRequest modifyIssueFacadeRequest = new ModifyIssueFacadeRequest();
        List<TicketInfo> ticketInfoList = new ArrayList<>();

        TicketInfo ticketInfo = new TicketInfo();
        ticketInfo.setTicketNo("529809351745");
        Map<String, String> extendProps = new HashMap<>();
        extendProps.put("operateType", OperateTypeEnum.DELETE.getCode().toString());
        ticketInfo.setExtendProps(extendProps);
        ticketInfoList.add(ticketInfo);

        PromotionInfo promotionInfo = new PromotionInfo();
        promotionInfo.setTicketInfos(ticketInfoList);
        modifyIssueFacadeRequest.setPromotionInfo(promotionInfo);

        modifyIssueFacadeRequest.setRequestProfile(requestProfile);
        //渠道信息
        ChannelInfo channelInfo = new ChannelInfo();
        channelInfo.setChannelOperateTime(new Date());
        channelInfo.setSystemCaller(SystemCallerEnum.EXPRESS_OMS.getCode());
        channelInfo.setSystemSubCaller(SystemSubCallerEnum.JDL_OMS.getCode());
        modifyIssueFacadeRequest.setChannelInfo(channelInfo);
        //操作人
        modifyIssueFacadeRequest.setOperator(SystemCallerEnum.EXPRESS_OMS.getCode());
        modifyIssueFacadeRequest.setOrderNo("ECO1000020000131394");
        modifyIssueFacadeRequest.setCustomOrderNo("JDX036891589278");

        Map<String, String> modifiedFields = new HashMap<>();
        modifiedFields.put(ModifiedFieldEnum.TICKET_INFOS.getCode(), ModifiedFieldValueEnum.ALL_DELETE.getCode());

        modifyIssueFacadeRequest.setModifiedFields(modifiedFields);

        OrderBusinessIdentity orderBusinessIdentity = new OrderBusinessIdentity();
        orderBusinessIdentity.setBusinessUnit("cn_jdl_c2c");
        orderBusinessIdentity.setBusinessType("express");
        orderBusinessIdentity.setBusinessScene("callback");
        orderBusinessIdentity.setBusinessStrategy("CCommonStandardUnitedExpress");
        orderBusinessIdentity.setFulfillmentUnit("JDL.ORDER.UNITED.C2C");

        BusinessIdentity businessIdentity = new BusinessIdentity();
        businessIdentity.setBusinessUnit(orderBusinessIdentity.getBusinessUnit());
        businessIdentity.setBusinessType(orderBusinessIdentity.getBusinessType());
        businessIdentity.setBusinessScene(orderBusinessIdentity.getBusinessScene());
        businessIdentity.setFulfillmentUnit(orderBusinessIdentity.getFulfillmentUnit());
        businessIdentity.setBusinessStrategy(orderBusinessIdentity.getBusinessStrategy());

        modifyIssueFacadeRequest.setBusinessIdentity(businessIdentity);
        modifyIssueFacade.modifyIssue(modifyIssueFacadeRequest, orderBusinessIdentity);
        System.out.println("下发OFC修改优惠券完成");
    }

}
