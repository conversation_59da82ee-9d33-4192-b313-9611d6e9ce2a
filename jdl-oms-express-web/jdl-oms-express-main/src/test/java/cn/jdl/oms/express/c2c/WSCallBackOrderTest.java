package cn.jdl.oms.express.c2c;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.application.service.CallBackExpressOrderServiceImpl;
import cn.jdl.oms.express.application.service.CreateExpressOrderServiceImpl;
import cn.jdl.oms.express.domain.ability.coupon.CallbackCouponReleaseAbility;
import cn.jdl.oms.express.domain.infrs.ohs.locals.es.orderflow.ExpressOrderFlowService;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.CallbackCouponProcessMessageDto;
import cn.jdl.oms.express.model.CallBackExpressOrderRequest;
import cn.jdl.oms.express.model.CallBackExpressOrderResponse;
import cn.jdl.oms.express.model.CreateExpressOrderRequest;
import cn.jdl.oms.express.model.CreateExpressOrderResponse;
import cn.jdl.oms.express.model.ModifyExpressOrderRequest;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.text.ParseException;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:./applicationContext.xml"})
@ActiveProfiles("test")
public class WSCallBackOrderTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(WSCallBackOrderTest.class);

    @Resource
    private CallBackExpressOrderServiceImpl callBackExpressOrderServiceImpl;

    @Resource
    private CreateExpressOrderServiceImpl createExpressOrderServiceImpl;

    private static final String TENANT_ID = "1000";
    private static final String LOCALE = "zh_CN";
    private static final String TIME_ZONE = "GMT+8";

    /**
     * 纯配订单jmq服务生产者
     */
    @Resource
    private ExpressOrderFlowService expressOrderFlowService;

    @Test
    public void test() throws ParseException {

        CallBackExpressOrderResponse response = callBackExpressOrderServiceImpl.callBackOrder(getRequestProfile(), getCallBackExpressOrderRequest());
        LOGGER.info(JSONUtils.beanToJSONDefault(response));

    }

    @Test
    public void c2cTest() {
        // 揽收后回传状态的待回滚优惠券处理逻辑
        RequestProfile profile = new RequestProfile();
        profile.setLocale(LOCALE);
        profile.setTenantId(TENANT_ID);
        profile.setTimeZone(TIME_ZONE);
        profile.setTraceId(Long.toString(System.currentTimeMillis()));
        CallbackCouponProcessMessageDto callbackCouponProcessMessageDto = new CallbackCouponProcessMessageDto();
        callbackCouponProcessMessageDto.setRequestProfile(profile);
        callbackCouponProcessMessageDto.setOrderNo("1312");
        callbackCouponProcessMessageDto.setOperator("model.getOperator()");
        expressOrderFlowService.sendCallbackCouponProcessMq(callbackCouponProcessMessageDto);
        CreateExpressOrderResponse response = createExpressOrderServiceImpl.createOrder(getRequestProfile(), getCreateExpressOrderRequest());
        System.out.println("结果是" + JSONUtils.beanToJSONDefault(response));
    }

    private CallBackExpressOrderRequest getCallBackExpressOrderRequest() {
        String requestStr = "";

        return JSONUtils.jsonToBean(requestStr, CallBackExpressOrderRequest.class);
    }

    private CreateExpressOrderRequest getCreateExpressOrderRequest() {
        String requestStr = "{\n" +
                "    \"agreementInfos\": [\n" +
                "\n" +
                "    ],\n" +
                "    \"businessIdentity\": {\n" +
                "        \"businessStrategy\": \"StandardCargoExpress\",\n" +
                "        \"businessType\": \"express\",\n" +
                "        \"businessUnit\": \"cn_jdl_c2c\"\n" +
                "    },\n" +
                "    \"cargoInfos\": [\n" +
                "        {\n" +
                "            \"cargoName\": \"家装建材\",\n" +
                "            \"cargoQuantity\": {\n" +
                "                \"value\": 1\n" +
                "            },\n" +
                "            \"cargoRemark\": \"\",\n" +
                "            \"cargoSign\": {\n" +
                "                \"cargoFlag\": \"0\"\n" +
                "            },\n" +
                "            \"cargoType\": \"43\",\n" +
                "            \"cargoVolume\": {\n" +
                "                \"unit\": \"CM3\",\n" +
                "                \"value\": 1000\n" +
                "            },\n" +
                "            \"cargoVulnerable\": 2,\n" +
                "            \"cargoWeight\": {\n" +
                "                \"unit\": \"KG\",\n" +
                "                \"value\": 1\n" +
                "            },\n" +
                "            \"extendProps\": {\n" +
                "                \"userCargoName\": \"家装建材\"\n" +
                "            },\n" +
                "            \"privacyCargoName\": \"\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"channelInfo\": {\n" +
                "        \"channelNo\": \"0030001\",\n" +
                "        \"channelOperateTime\": 1754913145839,\n" +
                "        \"customerOrderNo\": \"d8bb2f79-24fc-4a90-86d4-5c33645e\",\n" +
                "        \"extendProps\": {\n" +
                "            \"dadaOperationMode\": \"1\"\n" +
                "        },\n" +
                "        \"systemCaller\": \"WeChat-MiniProgram\",\n" +
                "        \"systemSubCaller\": \"C2C\"\n" +
                "    },\n" +
                "    \"consigneeInfo\": {\n" +
                "        \"addressInfo\": {\n" +
                "            \"address\": \"京东总部2号楼B座\",\n" +
                "            \"chinaPostAddressCode\": \"N50J57237A13762000000\",\n" +
                "            \"cityName\": \"通州区\",\n" +
                "            \"cityNo\": \"2809\",\n" +
                "            \"countyName\": \"台湖镇\",\n" +
                "            \"countyNo\": \"51226\",\n" +
                "            \"extendProps\": {\n" +
                "                \"usePoi\": \"0\"\n" +
                "            },\n" +
                "            \"fenceInfos\": [\n" +
                "                {\n" +
                "                    \"fenceId\": \"F43A651939748282\",\n" +
                "                    \"fenceType\": \"F43\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"fenceId\": \"448283499202244608\",\n" +
                "                    \"fenceType\": \"F20\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"fenceId\": \"F35A663944704514\",\n" +
                "                    \"fenceType\": \"F35\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"fenceId\": \"SG202416000189\",\n" +
                "                    \"fenceType\": \"3\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"fenceTrusted\": 1,\n" +
                "            \"houseNumber\": \"\",\n" +
                "            \"poiCode\": \"\",\n" +
                "            \"poiName\": \"\",\n" +
                "            \"provinceName\": \"北京\",\n" +
                "            \"provinceNo\": \"1\"\n" +
                "        },\n" +
                "        \"consigneeCompany\": \"\",\n" +
                "        \"consigneeMobile\": \"***********\",\n" +
                "        \"consigneeName\": \"赵文彦\",\n" +
                "        \"consigneePhone\": \"***********\",\n" +
                "        \"extendProps\": {\n" +
                "\n" +
                "        }\n" +
                "    },\n" +
                "    \"consignorInfo\": {\n" +
                "        \"addressInfo\": {\n" +
                "            \"address\": \"京东总部1号楼\",\n" +
                "            \"chinaPostAddressCode\": \"N50J57237B31400400000\",\n" +
                "            \"cityName\": \"通州区\",\n" +
                "            \"cityNo\": \"2809\",\n" +
                "            \"countyName\": \"台湖镇\",\n" +
                "            \"countyNo\": \"51226\",\n" +
                "            \"extendProps\": {\n" +
                "                \"usePoi\": \"0\"\n" +
                "            },\n" +
                "            \"fenceInfos\": [\n" +
                "                {\n" +
                "                    \"fenceId\": \"F43A651939748282\",\n" +
                "                    \"fenceType\": \"F43\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"fenceId\": \"448283499202244608\",\n" +
                "                    \"fenceType\": \"F20\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"fenceId\": \"F35A663944704514\",\n" +
                "                    \"fenceType\": \"F35\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"fenceId\": \"SG202416000189\",\n" +
                "                    \"fenceType\": \"3\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"fenceTrusted\": 1,\n" +
                "            \"houseNumber\": \"\",\n" +
                "            \"poiCode\": \"\",\n" +
                "            \"poiName\": \"\",\n" +
                "            \"provinceName\": \"北京\",\n" +
                "            \"provinceNo\": \"1\"\n" +
                "        },\n" +
                "        \"consignorCompany\": \"\",\n" +
                "        \"consignorMobile\": \"***********\",\n" +
                "        \"consignorName\": \"赵文彦\",\n" +
                "        \"consignorPhone\": \"***********\"\n" +
                "    },\n" +
                "    \"customerInfo\": {\n" +
                "        \"accountNo\": \"010K239824\"\n" +
                "    },\n" +
                "    \"extendProps\": {\n" +
                "        \"extendInfos\": {\n" +
                "            \"wechatCode\": \"0c1V1l0w3dvpq53OI54w3gvjHl0V1l0u\"\n" +
                "        },\n" +
                "        \"orderMedium\": \"WX-XCX\",\n" +
                "        \"inputOrderType\": \"0\",\n" +
                "        \"trafficSource\": \"2\"\n" +
                "    },\n" +
                "    \"financeInfo\": {\n" +
                "        \"estimateAmount\": {\n" +
                "            \"amount\": 5,\n" +
                "            \"currencyCode\": \"CNY\"\n" +
                "        },\n" +
                "        \"estimateFinanceInfo\": {\n" +
                "            \"billingMode\": \"1\",\n" +
                "            \"billingVolume\": {\n" +
                "                \"unit\": \"CM3\",\n" +
                "                \"value\": 1000\n" +
                "            },\n" +
                "            \"billingWeight\": {\n" +
                "                \"unit\": \"KG\",\n" +
                "                \"value\": 1\n" +
                "            },\n" +
                "            \"discountAmount\": {\n" +
                "                \"amount\": 5,\n" +
                "                \"currencyCode\": \"CNY\"\n" +
                "            },\n" +
                "            \"financeDetailInfos\": [\n" +
                "                {\n" +
                "                    \"costName\": \"资源调节费\",\n" +
                "                    \"costNo\": \"KDGFQFJF\",\n" +
                "                    \"discountAmount\": {\n" +
                "                        \"amount\": 0\n" +
                "                    },\n" +
                "                    \"extendProps\": {\n" +
                "                        \"calcPriceItemList\": [\n" +
                "                            {\n" +
                "                                \"priceItemBeans\": [\n" +
                "                                    {\n" +
                "                                        \"price\": 0,\n" +
                "                                        \"priceItemName\": \"元/票\",\n" +
                "                                        \"priceItemNo\": \"104\"\n" +
                "                                    }\n" +
                "                                ],\n" +
                "                                \"startingValue\": 0\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    },\n" +
                "                    \"preAmount\": {\n" +
                "                        \"amount\": 0\n" +
                "                    },\n" +
                "                    \"productName\": \"资源调节费\",\n" +
                "                    \"productNo\": \"GF1004\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"costName\": \"快递运费\",\n" +
                "                    \"costNo\": \"QIPSF\",\n" +
                "                    \"discountAmount\": {\n" +
                "                        \"amount\": 5\n" +
                "                    },\n" +
                "                    \"discountInfos\": [\n" +
                "                        {\n" +
                "                            \"discountNo\": \"YHQ1229424054P-TZS-201810\",\n" +
                "                            \"discountType\": \"12\",\n" +
                "                            \"discountedAmount\": {\n" +
                "                                \"amount\": 10\n" +
                "                            }\n" +
                "                        }\n" +
                "                    ],\n" +
                "                    \"extendProps\": {\n" +
                "                        \"calcPriceItemList\": [\n" +
                "                            {\n" +
                "                                \"endValue\": 30,\n" +
                "                                \"ladderAmount\": 0,\n" +
                "                                \"priceItemBeans\": [\n" +
                "                                    {\n" +
                "                                        \"price\": 1,\n" +
                "                                        \"priceItemName\": \"首重公斤\",\n" +
                "                                        \"priceItemNo\": \"108\"\n" +
                "                                    },\n" +
                "                                    {\n" +
                "                                        \"price\": 15,\n" +
                "                                        \"priceItemName\": \"首重价格\",\n" +
                "                                        \"priceItemNo\": \"109\"\n" +
                "                                    },\n" +
                "                                    {\n" +
                "                                        \"price\": 1,\n" +
                "                                        \"priceItemName\": \"续重公斤\",\n" +
                "                                        \"priceItemNo\": \"111\"\n" +
                "                                    },\n" +
                "                                    {\n" +
                "                                        \"price\": 2,\n" +
                "                                        \"priceItemName\": \"续重价格\",\n" +
                "                                        \"priceItemNo\": \"110\"\n" +
                "                                    }\n" +
                "                                ],\n" +
                "                                \"startingValue\": 0\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    },\n" +
                "                    \"preAmount\": {\n" +
                "                        \"amount\": 15\n" +
                "                    },\n" +
                "                    \"productName\": \"京东特快\",\n" +
                "                    \"productNo\": \"ed-m-0002\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"preAmount\": {\n" +
                "                \"amount\": 15,\n" +
                "                \"currencyCode\": \"CNY\"\n" +
                "            }\n" +
                "        },\n" +
                "        \"paymentStage\": 2,\n" +
                "        \"settlementType\": 1\n" +
                "    },\n" +
                "    \"goodsInfos\": [\n" +
                "\n" +
                "    ],\n" +
                "    \"operator\": \"13191063599_p\",\n" +
                "    \"orderSign\": {\n" +
                "        \"consignorFenceTrusted\": \"1\",\n" +
                "        \"consigneeFenceTrusted\": \"1\"\n" +
                "    },\n" +
                "    \"orderSubType\": \"C2C\",\n" +
                "    \"orderType\": \"500\",\n" +
                "    \"productInfos\": [\n" +
                "        {\n" +
                "            \"extendProps\": {\n" +
                "                \"operationMode\": \"P14\"\n" +
                "            },\n" +
                "            \"productNo\": \"ed-m-0002\",\n" +
                "            \"productType\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"productAttrs\": {\n" +
                "                \"hiddenContent\": [\n" +
                "                    \"senderName\",\n" +
                "                    \"senderMobile\",\n" +
                "                    \"receiverName\",\n" +
                "                    \"receiverMobile\"\n" +
                "                ]\n" +
                "            },\n" +
                "            \"productNo\": \"ed-a-0032\",\n" +
                "            \"productType\": 2\n" +
                "        },\n" +
                "        {\n" +
                "            \"productAttrs\": {\n" +
                "                \"rejectAuditType\": \"once\",\n" +
                "                \"rejectAuditNumbers\": \"1\",\n" +
                "                \"realRejectAuditNumbers\": \"0\"\n" +
                "            },\n" +
                "            \"productNo\": \"ed-a-0005\",\n" +
                "            \"productType\": 2\n" +
                "        }\n" +
                "    ],\n" +
                "    \"promotionInfo\": {\n" +
                "        \"ticketInfos\": [\n" +
                "            {\n" +
                "                \"ticketNo\": \"642938777193\",\n" +
                "                \"ticketSource\": 0,\n" +
                "                \"ticketUseAmount\": {\n" +
                "                    \"amount\": 10,\n" +
                "                    \"currencyCode\": \"CNY\"\n" +
                "                }\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"shipmentInfo\": {\n" +
                "        \"expectPickupEndTime\": 1757748437000,\n" +
                "        \"expectPickupStartTime\": 1754913264662,\n" +
                "        \"extendProps\": {\n" +
                "            \"actualDeliveryTransportNetMode\": \"0\",\n" +
                "            \"actualPickupTransportNetMode\": \"0\",\n" +
                "            \"endRoadArea\": \"001\",\n" +
                "            \"rejectionMode\": \"2\",\n" +
                "            \"startRoadArea\": \"001\"\n" +
                "        },\n" +
                "        \"pickupCodeCreateType\": 2,\n" +
                "        \"pickupType\": 1,\n" +
                "        \"planDeliveryTime\": 1755230400000,\n" +
                "        \"serviceRequirements\": {\n" +
                "            \"tempStorageDay\": \"0\",\n" +
                "            \"discountCategories\": \"1\",\n" +
                "            \"specialGuarantee\": \"0\"\n" +
                "        },\n" +
                "        \"transportType\": 2\n" +
                "    }\n" +
                "}";

        return JSONUtils.jsonToBean(requestStr, CreateExpressOrderRequest.class);
    }

    private RequestProfile getRequestProfile() {
        return JSON.parseObject("{\n" +
                "\t\"ext\": {},\n" +
                "\t\"locale\": \"zh_CN\",\n" +
                "\t\"tenantId\": \"1000\",\n" +
                "\t\"timeZone\": \"GMT+8\",\n" +
                "\t\"traceId\": \"16122478551555561\"\n" +
                "}", RequestProfile.class);
    }
}