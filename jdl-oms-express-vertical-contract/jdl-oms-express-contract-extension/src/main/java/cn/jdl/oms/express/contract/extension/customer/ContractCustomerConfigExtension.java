package cn.jdl.oms.express.contract.extension.customer;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CustomerConfig;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.customer.ICustomerConfigExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.BasicTraderResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.DeptResponse;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.SystemCallerUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.TrustSellerUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.WarehouseModeUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.SupplyChainDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProductEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.TraderOperateStateEnum;
import cn.jdl.oms.express.shared.common.dict.TraderSignEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.dict.UnifiedSubErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.TraderSignUtils;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
*
*@Package: cn.jd.oms.express.contract.extension.customer
*@ClassName: ContractCustomerConfigExtension
*@Description: 合同物流客户配置信息校验扩展点
*@author: wengguoqi
*@date: 2023/8/11 11:52
*@Copyright: Copyright (c)2023 JDL.CN All Right Reserved
*@Since: JDK 1.8
*@Version: v1.0
*/
@Extension(code = ExpressOrderProduct.CODE)
public class ContractCustomerConfigExtension implements ICustomerConfigExtension {

    private static final Logger LOGGER = LoggerFactory.getLogger(ContractCustomerConfigExtension.class);

    /**
     * 客户配置信息
     */
    @Resource
    private CustomerConfigFacade customerConfigFacade;

    /**
     * 事业部信息
     */
    @Resource
    private CustomerFacade customerFacade;

    @Override
    public void execute(ExpressOrderContext expressOrderContext) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("合同物流客户配置信息校验开始");
            // 账号信息校验
            checkBasicTraderInfo(expressOrderContext);
            // 事业部信息校验
            checkEbuInfo(expressOrderContext);
            LOGGER.info("合同物流客户配置信息校验结束");
        } catch (BusinessDomainException e) {
            LOGGER.error("合同物流客户配置信息失败", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("合同物流客户配置信息校验异常", e);
            Profiler.functionError(callerInfo);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL, e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 账号信息校验
     */
    private void checkBasicTraderInfo(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel expressOrderModel = expressOrderContext.getOrderModel();

        CustomerConfig customerConfig = customerConfigFacade.getCustomerConfig(expressOrderContext);
        // 青龙业主号校验
        if (customerConfig == null) {
            LOGGER.error("客户配置信息校验失败，未查到相关客户信息");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0007.subCode())
                    .withCustom("客户配置信息校验失败，未查到相关客户信息");
        }

        //设置信任商家标示
        TrustSellerUtil.setTrustCustomerWeightVolume(expressOrderModel, customerConfig);
        LOGGER.info("客户配置信息是否信任商家：" + TrustSellerUtil.isTrustWeightVolume(expressOrderModel));

        // 补全账号id
        expressOrderModel.getComplementModel().complementAccountId(this, customerConfig.getCustomerId());
        // 补全账号名称
        expressOrderModel.getComplementModel().complementAccountName(this, customerConfig.getCustomerName());

        if (OrderTypeEnum.RETURN_ORDER == expressOrderModel.getOrderType()) {
            LOGGER.info("客户配置信息校验，逆向单不检验客户信息");
            return;
        }

        //修改场景仓出库发货，不检验客户信息
        if (BusinessSceneEnum.MODIFY.getCode().equals(expressOrderModel.getOrderBusinessIdentity().getBusinessScene())
                && ModifySceneRuleUtil.isOutboundDelivery(expressOrderModel)) {
            LOGGER.info("修改场景仓出库发货，不检验客户信息");
            return;
        }

        //青龙业主号校验
        if (!TraderOperateStateEnum.normal.getCode().equals(customerConfig.getTraderOperateState())) {
            LOGGER.error("青龙业主号:{}状态异常，TraderOperateState:{}"
                    , expressOrderModel.getCustomer().getAccountNo()
                    , customerConfig.getTraderOperateState());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withCustom("客户配置信息校验失败,商家青龙业主号状态异常")
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.desc());
        }

        //若结算方式不是月结，则不进行商家配置校验
        if (expressOrderModel.getFinance() == null || SettlementTypeEnum.MONTHLY_PAYMENT != expressOrderModel.getFinance().getSettlementType()) {
            LOGGER.info("结算方式不是月结，则不进行商家配置校验");
            return;
        }

        if (SystemCallerUtil.currentIsSupplyOFC(expressOrderModel) && SupplyChainDeliveryOrderSignUtil.currentFlag(expressOrderModel)) {
            LOGGER.info("仓配接配，不校验月结账号");
            return;
        }

        //结算账户不为空,则通过结算账号校验账户是否开通月结，否则校验配送履约账号是否开通月结
        if (StringUtils.isNotBlank(expressOrderModel.getFinance().getSettlementAccountNo())) {
            LOGGER.info("有结算账号，SettlementAccountNo={}", expressOrderModel.getFinance().getSettlementAccountNo());
            //快运结算账号传的是事业部，需要通过事业部查到履约账号再校验
            //获取事业部信息
            DeptResponse deptInfo = customerFacade.getDept(expressOrderModel.getFinance().getSettlementAccountNo());
            if (deptInfo == null) {
                LOGGER.error("客户配置信息校验失败，未查到相关事业部信息");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0020.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0020.desc());
            }
            if (StringUtils.isBlank(deptInfo.getBdOwnerNo())) {
                LOGGER.error("客户配置信息校验失败，未查到相关事业部的青龙业主号");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0021.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0021.desc());
            }
            BasicTraderResponse basicTraderResponse = customerConfigFacade.getCustomerConfig(deptInfo.getBdOwnerNo());
            //青龙业主号校验
            if (!TraderOperateStateEnum.normal.getCode().equals(basicTraderResponse.getTraderOperateState())) {
                LOGGER.error("青龙业主号:{}状态异常，TraderOperateState:{}"
                        , expressOrderModel.getFinance().getSettlementAccountNo()
                        , basicTraderResponse.getTraderOperateState());
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL)
                        .withCustom("客户配置信息校验失败,商家青龙业主号状态异常")
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.desc());
            }
            if (OrderTypeEnum.DELIVERY.equals(expressOrderModel.getOrderType()) && !SettlementTypeEnum.MONTHLY_PAYMENT.getTraderSignCode().contains(TraderSignUtils.getTraderSignCode(basicTraderResponse.getTraderSign(), TraderSignEnum.SETTLEMENT.getCode()))) {
                LOGGER.error("结算方式校验失败,商家结算账户未开通月结结算方式");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL).withCustom("客户配置信息校验失败,结算方式校验失败")
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.desc());
            }
        } else {
            if (OrderTypeEnum.DELIVERY.equals(expressOrderModel.getOrderType()) && !SettlementTypeEnum.MONTHLY_PAYMENT.getTraderSignCode().contains(TraderSignUtils.getTraderSignCode(customerConfig.getTraderSign(), TraderSignEnum.SETTLEMENT.getCode()))) {
                LOGGER.error("结算方式校验失败,商家结算账户未开通月结结算方式");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL).withCustom("客户配置信息校验失败,结算方式校验失败")
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0008.desc());
            }
        }
    }

    /**
     * 校验事业部信息
     */
    private void checkEbuInfo(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();

        if (SystemCallerUtil.currentIsSupplyOFC(orderModel) && SupplyChainDeliveryOrderSignUtil.currentFlag(orderModel)) {
            LOGGER.info("仓配接配，不校验事业部信息");
            return;
        }

        //获取事业部信息
        DeptResponse deptInfo = customerFacade.getDept(orderModel.getCustomer().getAccountNo2());
        if (StringUtils.isBlank(orderModel.getCustomer().getAccountName2())) {
            orderModel.getComplementModel().complementAccountName2(this, deptInfo.getDeptName());
        }
        // 正向单校验事业部逾期，逆向单不需要
        if (OrderTypeEnum.DELIVERY == orderModel.getOrderType()) {
            if (deptInfo == null) {
                LOGGER.error("客户配置信息校验失败，未查到相关事业部信息");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0020.subCode())
                        .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0020.desc());
            }
            if (deptInfo.getExpireStatus()) {
                if (BusinessSceneEnum.MODIFY.getCode().equals(orderModel.getOrderBusinessIdentity().getBusinessScene())
                        && ModifySceneRuleUtil.isOutboundDelivery(orderModel)) {
                    LOGGER.info("修改场景仓出库发货，不需要校验事业部逾期");
                } else {
                    LOGGER.error("客户配置信息校验失败，事业部逾期");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                            .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0022.subCode())
                            .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0022.desc());
                }
            }
        }
        //设置信任商家标示
        if(deptInfo != null && this.isNeedSetTrustCustomer(orderModel)){
            TrustSellerUtil.setTrustCustomerWeightVolume(orderModel, deptInfo);
            LOGGER.info("事业部信息是否信任商家：" + TrustSellerUtil.isTrustWeightVolume(orderModel));
        }
    }

    /**
     *
     * 根据产品编码判断是否需要执行信任商家打标逻辑
     *
     * @return
     */
    private boolean isNeedSetTrustCustomer(ExpressOrderModel orderModel) {
        if (null == orderModel.getProductDelegate()
                || CollectionUtils.isEmpty(orderModel.getProductDelegate().getProducts())) {
            return false;
        }
        List<String> productCodesList = orderModel.getProductDelegate().getProductCodes();
        if (CollectionUtils.isEmpty(productCodesList)) {
            return false;
        }
        List<String> needSetTrustCustomerProductList = Arrays.asList(ProductEnum.CONTRACT_LD.getCode(), ProductEnum.CONTRACT_DPZS.getCode());
        return productCodesList.stream().anyMatch(needSetTrustCustomerProductList::contains);
    }
}
