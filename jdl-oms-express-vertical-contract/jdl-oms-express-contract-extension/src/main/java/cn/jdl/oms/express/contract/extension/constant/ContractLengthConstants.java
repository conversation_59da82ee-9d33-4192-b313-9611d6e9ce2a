package cn.jdl.oms.express.contract.extension.constant;


/**
* 合同物流纯配
*<AUTHOR>
*@date 2023/8/9 22:08
*/
public class ContractLengthConstants {

    /**
     * 渠道信息中的渠道编码最大长度为50
     */
    public static final Integer CHANNEL_NO_LENGTH = 50;

    /**
     * 渠道信息中的客户订单号最大长度为100
     */
    public static final Integer CUSTOMER_ORDER_NO_LENGTH = 100;

    /**
     * 渠道信息中的渠道单号最大长度为50
     */
    public static final Integer SYSTEM_SUB_CALLER_LENGTH = 50;

    /**
     * 发货人姓名最大长度为50
     */
    public static final Integer CONSIGNOR_NAME_LENGTH = 50;

    /**
     * 发货人手机最大长度为30
     */
    public static final Integer CONSIGNOR_MOBILE_LENGTH = 30;

    /**
     * 发货人手机最大长度为30
     */
    public static final Integer CONSIGNOR_PHONE_LENGTH = 30;

    /**
     * 发货人公司最大长度为50
     */
    public static final Integer CONSIGNOR_COMPANY_LENGTH = 50;

    /**
     * 收货人姓名最大长度为50
     */
    public static final Integer CONSIGNEE_NAME_LENGTH = 50;

    /**
     * 收货人手机最大长度为30
     */
    public static final Integer CONSIGNEE_MOBILE_LENGTH = 30;

    /**
     * 收货人手机最大长度为30
     */
    public static final Integer CONSIGNEE_PHONE_LENGTH = 30;

    /**
     * 收货人公司最大长度为50
     */
    public static final Integer CONSIGNEE_COMPANY_LENGTH = 50;

    /**
     * 财务信息中的结算账号最大长度为30
     */
    public static final Integer SETTLEMENT_ACCOUNT_NO_LENGTH = 50;

    /**
     * 下单人唯一标识最大长度为30
     */
    public static final Integer OPERATOR_LENGTH = 50;

    /**
     * 订单备注最大长度为30
     */
    public static final Integer REMARK_LENGTH = 500;

    /**
     * 货品信息中的货品名称最大长度为200
     */
    public static final Integer CARGO_NAME_LENGTH = 200;

    /**
     * 货品信息中的货品编码最大长度为30
     */
    public static final Integer CARGO_NO_LENGTH = 30;

    /**
     * 货品信息中的货品类型最大长度为10
     */
    public static final Integer CARGO_TYPE_LENGTH = 10;

    /**
     * 货品信息中的货品备注最大长度为200
     */
    public static final Integer CARGO_REMARK_LENGTH = 200;

    /**
     * 商品信息中的商品名称最大长度为200
     */
    public static final Integer GOODS_NAME_LENGTH = 200;

    /**
     * 商品信息中的商品编码最大长度为50
     */
    public static final Integer GOODS_NO_LENGTH = 50;

    /**
     * 商品信息中的商品唯一编号最大长度为50
     */
    public static final Integer GOODS_UNIQUE_CODE_LENGTH = 50;

    /**
     * 发货人证件号码的最小长度
     */
    public static final Integer CONSIGNOR_ID_NO_LENGTH_MIN = 6;

    /**
     * 发货人证件号码的最大长度
     */
    public static final Integer CONSIGNOR_ID_NO_LENGTH_MAX = 20;

    /**
     * 户口薄号码长度
     */
    public static final Integer HOUSEHOLD_REGISTER_LENGTH = 15;

    /**
     * 身份证号码长度
     */
    public static final Integer ID_CARD_LENGTH = 18;

    /**
     * 收货人证件号码的最小长度
     */
    public static final Integer CONSIGNEE_ID_NO_LENGTH_MIN = 6;

    /**
     * 收货人证件号码的最大长度
     */
    public static final Integer CONSIGNEE_ID_NO_LENGTH_MAX = 20;

    /**
     * 地址信息中详细地址的长度
     */
    public static final Integer ADDRESS_LENGTH = 200;

    /**
     * 财务信息中预估费用最大值
     */
    public static final Integer ESTIMATE_AMOUNT_MAX = 1000000;

    /**
     * 仓库编码的最大长度
     */
    public static final Integer WAREHOUSE_NO_LENGTH = 50;

    /**
     * 仓库类型的最大长度
     */
    public static final Integer WAREHOUSE_SOURCE_LENGTH = 20;

    /**
     * 仓库名称的最大长度
     */
    public static final Integer WAREHOUSE_NAME_LENGTH = 100;

    /**
     * 配送信息中始发站编码的最大长度
     */
    public static final Integer START_STATION_NO_LENGTH = 50;

    /**
     * 配送信息中始发站编码的最大长度
     */
    public static final Integer END_STATION_NO_LENGTH = 50;

    /**
     * 配送信息中揽收网络类型的最大长度
     */
    public static final Integer PICKUP_TRANSPORT_NET_MODE_LENGTH = 10;

    /**
     * 配送信息中派送网络类型的最大长度
     */
    public static final Integer DELIVERY_TRANSPORT_NET_MODE_LENGTH = 10;

    /**
     * 交易客户信息中的青龙业主号最大长度为50
     */
    public static final Integer ACCOUNT_NO_LENGTH = 50;

    /**
     * 交易客户信息中的事业部编号最大长度为50
     */
    public static final Integer ACCOUNT_NO_2_LENGTH = 50;

    /**
     * 交易客户信息中的ECP编号最大长度为50
     */
    public static final Integer ACCOUNT_NO_3_LENGTH = 50;

    /**
     * 交易客户信息中的ECP名称最大长度为50
     */
    public static final Integer ACCOUNT_NAME_3_LENGTH = 100;

    /**
     * 产品服务信息中的产品编码最大长度为50
     */
    public static final Integer PRODUCT_NO_LENGTH = 50;

    /**
     * 订单号最大长度为50
     */
    public static final Integer ORDER_NO_LENGTH = 50;

    /**
     * 运单状态最大长度为50
     */
    public static final Integer EXECUTED_STATUS_LENGTH = 50;

    /**
     * 销售员（专属快递员）最大长度为30
     */
    public static final Integer SALES_NO_LENGTH = 50;

    /**
     * 销售员来源类型（专属快递员类型）最大长度为30
     */
    public static final Integer SALES_SOURCE_TYPE_LENGTH = 50;

    /**
     * 扩展信息最大长度为100000（订单主档，大报文扩展字段）
     */
    public static final Integer ORDER_EXTEND_INFOS_LENGTH = 100000;

    /**
     * 营业厅编码
     */
    public static final Integer BUSINESS_HALL_NO_LENGTH = 50;

    /**
     * 营业厅名称
     */
    public static final Integer BUSINESS_HALL_NAME_LENGTH = 100;

    /**
     * 车队ID
     */
    public static final Integer VEHICLE_TEAM_ID_LENGTH = 20;
}
