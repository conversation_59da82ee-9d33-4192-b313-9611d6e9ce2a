package cn.jdl.oms.express.cc.b2b.extension.customer;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CustomerConfig;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.customer.ICustomerConfigExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.DeptResponse;
import cn.jdl.oms.express.domain.infrs.acl.util.SystemCallerUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.WarehouseModeUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.SupplyChainDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.ProfessionTypeEnum;
import cn.jdl.oms.express.shared.common.constant.MagicCommonConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.TraderOperateStateEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.dict.UnifiedSubErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.InfrastructureException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * @ProjectName：jdl-oms-express
 * @Package： cn.jdl.oms.express.cc.b2b.extension.customer
 * @ClassName: CCB2BCustomerConfigExtension
 * @Description:
 * @Author： jiangwei279
 * @CreateDate 2023/5/29 11:49
 * @Copyright: Copyright (c)2023 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
@Extension(code = ExpressOrderProduct.CODE)
public class CCB2BCustomerConfigExtension implements ICustomerConfigExtension {

    /**
     * 客户配置信息
     */
    @Resource
    private CustomerConfigFacade customerConfigFacade;

    /**
     * 事业部查询API
     */
    @Resource
    private CustomerFacade customerFacade;

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(CCB2BCustomerConfigExtension.class);

    @Override
    public void execute(ExpressOrderContext expressOrderContext) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("冷链B2B客户配置信息校验开始：");
            if (StringUtils.isNotBlank(expressOrderContext.getOrderModel().getCustomer().getAccountNo2())) {
                checkDeptInfo(expressOrderContext);
            }
            if (StringUtils.isNotBlank(expressOrderContext.getOrderModel().getCustomer().getAccountNo())) {
                checkBasicTraderInfo(expressOrderContext);
            }
        } catch (InfrastructureException infrastructureException) {
            Profiler.functionError(callerInfo);
            LOGGER.error("冷链b2b客户配置信息校验异常", infrastructureException);
            throw infrastructureException;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("冷链B2B客户配置信息校验系统异常", exception);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL, exception);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 功能：事业部信息校验
     *
     * @param expressOrderContext
     */
    private void checkDeptInfo(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();

        if (SystemCallerUtil.currentIsSupplyOFC(orderModel) && SupplyChainDeliveryOrderSignUtil.currentFlag(orderModel)) {
            LOGGER.info("仓配接配，不校验事业部信息");
            return;
        }

        DeptResponse dept = customerFacade.getDept(orderModel.getCustomer().getAccountNo2());
        if (dept == null) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withCustom("冷链B2B客户配置信息校验-客户配置信息校验失败,未获取到商家事业部信息");
        }
        if (BusinessSceneEnum.MODIFY.getCode().equals(expressOrderContext.getOrderModel().getBusinessScene())) {
            LOGGER.info("冷链B2B客户配置信息校验-修改场景不校验事业部信息");
            return;
        }
        if (null != dept && null != dept.getStatus() && (dept.getStatus() == 1 || dept.getStatus() == 2)) {
            Map<String, String> extendProps = expressOrderContext.getOrderModel().getExtendProps();
            if (MapUtils.isNotEmpty(extendProps)) {
                String customerInfoExtendProps = extendProps.get(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS);
                Map<String, String> customerInfoMap;
                if (StringUtils.isNotBlank(customerInfoExtendProps)) {
                    customerInfoMap = JSONUtils.jsonToMap(customerInfoExtendProps);
                } else {
                    customerInfoMap = new HashMap();
                }
                if (ProfessionTypeEnum.FRESH.getCode().equals(customerInfoMap.get(OrderConstants.PROFESSION_TYPE))
                        && MagicCommonConstants.NUM_1.intValue() != dept.getColdChainType()) {
                    LOGGER.error("冷链B2B客户配置信息校验-冷链产品校验事业部配置上的行业类型不是冷链商家");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                            .withCustom("冷链B2B客户配置信息校验-冷链产品校验事业部配置上的行业类型不是冷链商家");
                }
                if (ProfessionTypeEnum.MEDICINE.getCode().equals(customerInfoMap.get(OrderConstants.PROFESSION_TYPE))
                        && MagicCommonConstants.NUM_3.intValue() != dept.getColdChainType()) {
                    LOGGER.error("冷链B2B客户配置信息校验-医药产品校验事业部配置上的行业类型不是医药商家");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                            .withCustom("冷链B2B客户配置信息校验-医药产品校验事业部配置上的行业类型不是医药商家");
                }
                if (MagicCommonConstants.NUM_3.equals(dept.getSendType()) && !ProfessionTypeEnum.MEDICINE.getCode().equals(customerInfoMap.get(OrderConstants.PROFESSION_TYPE))) {
                    LOGGER.error("冷链B2B客户配置信息校验-非医药事业部派送类型为强C，拒单");
                    throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                            .withCustom("冷链B2B客户配置信息校验-非医药事业部派送类型为强C，拒单");
                }
            }
            if (dept.getExpireStatus()) {
                LOGGER.error("冷链B2B客户配置信息校验-事业部为逾期状态，拒单");
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withCustom("冷链B2B客户配置信息校验-事业部为逾期状态，拒单");
            }
            CustomerConfig deptCustomerConfig = deptCustomerConfig(dept);
            expressOrderContext.setDeptCustomerConfig(deptCustomerConfig);
        } else {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withCustom("冷链B2B客户配置信息校验-客户配置信息校验失败,未获取到商家事业部信息");
        }
    }

    /**
     * 功能:商家状态校验
     *
     * @param:
     * @return:
     * @throw:
     * @description:
     * @author: liufarui
     * @date: 2021/6/11 10:37
     */
    private void checkBasicTraderInfo(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel expressOrderModel = expressOrderContext.getOrderModel();

        CustomerConfig customerConfig = customerConfigFacade.getCustomerConfig(expressOrderContext);

        // 青龙业主号校验
        if (customerConfig == null) {
            LOGGER.error("客户配置信息校验失败，未查到相关客户信息");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0007.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0007.desc());
        }
        // 补全账号id
        expressOrderModel.getComplementModel().complementAccountId(this, customerConfig.getCustomerId());
        if (OrderTypeEnum.RETURN_ORDER == expressOrderModel.getOrderType()) {
            LOGGER.info("客户配置信息校验，逆向单不检验客户信息");
            return;
        }
        if (BusinessSceneEnum.MODIFY.getCode().equals(expressOrderContext.getOrderModel().getBusinessScene())) {
            LOGGER.info("客户配置信息校验-修改场景不校验青龙客户信息");
            return;
        }
        //青龙业主号校验
        if (!TraderOperateStateEnum.normal.getCode().equals(customerConfig.getTraderOperateState())) {
            LOGGER.error("青龙业主号:{}状态异常，TraderOperateState:{}"
                    , expressOrderModel.getCustomer().getAccountNo()
                    , customerConfig.getTraderOperateState());
            TraderOperateStateEnum operateStateEnum = TraderOperateStateEnum.of(customerConfig.getTraderOperateState());
            if (operateStateEnum != null) {
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                        .withCustom("客户配置信息校验失败,商家青龙业主号" + operateStateEnum.getDesc() + "状态异常");
            }
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withCustom("客户配置信息校验失败,商家青龙业主号状态异常")
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0028.desc());
        }
    }

    /**
     * 事业部配置信息
     *
     * @return
     */
    private CustomerConfig deptCustomerConfig(DeptResponse deptResponse) {
        CustomerConfig customerConfig = new CustomerConfig();
        customerConfig.setSellerNo(deptResponse.getSellerNo());
        customerConfig.setSellerName(deptResponse.getSellerName());
        customerConfig.setRegionalSalesErp(deptResponse.getRegionalSalesErp());
        customerConfig.setRegionalSalesName(deptResponse.getRegionalSalesName());
        customerConfig.setBusinessChannel(deptResponse.getBusinessChannel());
        return customerConfig;
    }
}
