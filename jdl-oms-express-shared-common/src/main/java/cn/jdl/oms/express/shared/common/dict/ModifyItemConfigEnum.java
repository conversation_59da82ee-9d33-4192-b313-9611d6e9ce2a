package cn.jdl.oms.express.shared.common.dict;

import org.apache.commons.lang3.StringUtils;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * @ClassName ModifyItemConfigEnum
 * @Description 修改项配置
 * <AUTHOR>
 * @Date 2021/3/27 4:47 下午
 * @ModifyDate 2021/3/27 4:47 下午
 * @Version 1.0
 */
public enum ModifyItemConfigEnum {

    /**
     * 订单主档信息
     */
    ORDER_REMARK("orderRemark", "order", "remark", "订单备注", null),
    ORDER_NET_WEIGHT("orderNetWeight", "order", "orderNetWeight", "订单总净重", null),
    ORDER_NET_WEIGHT_UNIT("orderNetWeightUnit", "order", "orderNetWeightUnit", "订单总净重单位", null),
    ORDER_WEIGHT("orderWeight", "order", "orderWeight", "订单总毛重", null),
    ORDER_WEIGHT_UNIT("orderWeightUnit", "order", "orderWeightUnit", "订单总毛重单位", null),
    ORDER_VOLUME("orderVolume", "order", "orderVolume", "订单总体积", null),
    ORDER_VOLUME_UNIT("orderVolumeUnit", "order", "orderVolumeUnit", "订单总体积单位", null),
    RECHECK_WEIGHT("recheckWeight", "order", "recheckWeight", "复核重量", null),
    RECHECK_WEIGHT_UNIT("recheckWeightUnit", "order", "recheckWeightUnit", "复核重量单位", null),
    RECHECK_VOLUME("recheckVolume", "order", "recheckVolume", "复核体积", null),
    RECHECK_VOLUME_UNIT("recheckVolumeUnit", "order", "recheckVolumeUnit", "复核体积单位", null),

    /**
     * 主档扩展信息
     */
    ORDER_BOX_CODE("boxCodeList", "order", "ext", "箱号列表", null),
    CUSTOMER_INFO_EXTEND_PROPS("customerInfoExtendProps", "order", "ext", "扩展字段-客户扩展信息", null),
    CUSTOM_EXTEND_MSG("customExtendMsg", "order", "ext", "扩展字段-自定义扩展信息", null),
    ORDER_PRINT_TIMES("printTimes", "order", "ext.printTimes", "扩展字段-打印次数", null),
    ORDER_PRINT_STATUS("printStatus", "order", "ext.printStatus", "扩展字段-打印状态", null),
    ORDER_REMINDER_TIMES("reminderTimes", "order", "ext.reminderTimes", "扩展字段-催单次数", null),
    SINGLE_INSURANCE("singleInsurance", "order", "ext.singleInsurance", "扩展字段-单单保", null),
    INDIVIDUAL_MS_TYPE("individualMsType", "order", "ext.individualMsType", "扩展字段-是否散客挂月结", null),
    MODIFY_PACKAGE_RULE_TYPE("modifyPackageRuleType", "order", "ext.modifyPackageRuleType", "扩展字段-事业部是否在《可增加包裹数量商家白名单》和《可减少包裹数量商家白名单》中", null),
    MULTI_ADDRESS_VERIFY_STATUS("multiAddressVerifyStatus", "order", "ext.multiAddressVerifyStatus", "扩展字段-多地址审核状态", null),
    // 大件主档扩展信息--包装情况
    ORDER_PACKING_DAMAGE("packingDamage", "order", "ext.packingDamage", "扩展字段-包装情况", null),

    EXTEND_INFOS("extendInfos", "order", "ext.extendInfos", "扩展字段-扩展信息", null),
    EXHIBITION_NAME("exhibitionName", "order", "ext.extendInfos.exhibitionName", "扩展字段-扩展信息-展会名称", null),
    INVOICE_TYPE("invoiceType", "order", "ext.extendInfos.invoiceType", "扩展字段-扩展信息-开票类型", null),
    DEPARTMENT_NO("departmentNo", "order", "ext.departmentNo", "扩展字段-下单人的末级部门编码", null),
    DEPARTMENT_NAME("departmentName", "order", "ext.departmentName", "扩展字段-成本中心名称", null),
    EVALUATE_STATUS("evaluateStatus", "order", "ext.evaluateStatus", "扩展字段-是否已评价", null),
    RETURN_PLATFORM("returnPlatform","order", "ext.returnPlatform", "扩展字段-收货平台", null),
    WECHAT_C_MINI_OPENID("wechatCMiniOpenId", "order", "ext.wechatCMiniOpenId", "扩展字段-微信小程序OPenId", null),
    FEE_CHECK_STATUS("feeCheckStatus", "order", "ext.feeCheckStatus", "扩展字段-费用审核状态", null),
    //与上面的feeCheckStatus组合使用，刘琦确认一个订单上只能有一种审核状态，要不通过，要不都不通过。需要用户审核类型标识当前的审核类型：例子["1"]费用，["2"]主产品
    //["1","2"]主产品加费用审核
    USER_CHECK_TYPE("userCheckType", "order", "ext.userCheckType", "扩展字段-用户审核类型", null),
    MAIN_PRODUCT_CHANGED_SOURCE("mainProductChangedSource", "order", "ext.mainProductChangedSource", "扩展字段-主产品修改来源", null),
    RE_SIGN_FLAG("reSignFlag", "order", "ext.reSignFlag", "扩展字段-是否发起补签", null),
    FINAL_DEPARTMENT_NO("finalDepartmentNo", "order", "ext.finalDepartmentNo", "扩展字段-下单人的末级部门编码", null),
    FINAL_DEPARTMENT_NAME("finalDepartmentName", "order", "ext.finalDepartmentName", "扩展字段-下单人的末级部门名称", null),
    GOV_SUBSIDY_APPROVAL_STATUS("govSubsidyApprovalStatus", "order", "ext.govSubsidyApprovalStatus", "扩展字段-国补审核状态", null),
    COLLECTION_TERMINATION_CHECK_STATUS("collectionTerminationCheckStatus", "order", "ext.collectionTerminationCheckStatus" , "扩展字段-收款终止审核状态", null),
    HAND_SHAKE_HAND_OVER_INFO("handshakeHandoverInfo", "order", "ext.handshakeHandoverInfo", "扩展字段-握手交接信息", null),
    DELIVERY_BATCH_NUMBER("deliveryBatchNumber", "order", "ext.deliveryBatchNumber", "整车业务派送批次号", null),

    //渠道信息

    /**
     * 渠道信息
     */
    CHANNEL_CHANNEL_ORDER_NO("channelOrderNo", "channel", "channel.channelOrderNo", "渠道订单号", null),
    CHANNEL_EXTEND_PROPS("channel.extendProps", "channel", "channel.extendProps", "渠道扩展信息", null),
    /**
     * 主档扩展信息 todo ext待核实
     */
    ORDER_APPOINTMENT_TYPE("appointmentType", "order", "ext.appointmentType", "扩展字段-预约类型", null),

    /**
     * 订单标识
     * 是否允许到付转月结
     */
    ORDER_SIGN_ALLOW_DELIVERY_TO_MONTHLY("allowDeliveryToMonthly", "order", "orderSign.allowDeliveryToMonthly", "订单标识-是否允许到付转月结", null),
    ORDER_SIGN_DELIVERY_PICKUP_SYNC("deliveryPickupSync", "order", "orderSign.deliveryPickupSync", "订单标识-是否送取同步", null),

    /**
     * 发货人
     */
    CONSIGNOR_NAME("consignorName", "consignor", "consignor.consignorName", "发货人姓名", null),
    CONSIGNOR_MOBILE("consignorMobile", "consignor", "consignor.consignorMobile", "发货人手机", null),
    CONSIGNOR_ID_TYPE("consignorIdType", "consignor", "consignor.consignorIdType", "发货人证件类型", null),
    CONSIGNOR_ID_NO("consignorIdNo", "consignor", "consignor.consignorIdNo", "发货人证件号", null),
    CONSIGNOR_ID_NAME("consignorIdName", "consignor", "consignor.consignorIdName", "发货人证件姓名", null),
    CONSIGNOR_PROVINCE_NO_GIS("consignorProvinceNoGis", "consignor", "consignor.address.provinceNoGis", "发货人省", null),
    CONSIGNOR_CITY_NO_GIS("consignorCityNoGis", "consignor", "consignor.address.cityNoGis", "发货人市", null),
    CONSIGNOR_COUNTY_NO_GIS("consignorCountyNoGis", "consignor", "consignor.address.countyNoGis", "发货人区", null),
    CONSIGNOR_TOWN_NO_GIS("consignorTownNoGis", "consignor", "consignor.address.townNoGis", "发货人镇", null),
    CONSIGNOR_PROVINCE_NAME_GIS("consignorProvinceNameGis", "consignor", "consignor.address.provinceNameGis", "发货人省名称", null),
    CONSIGNOR_CITY_NAME_GIS("consignorCityNameGis", "consignor", "consignor.address.cityNameGis", "发货人市名称", null),
    CONSIGNOR_COUNTY_NAME_GIS("consignorCountyNameGis", "consignor", "consignor.address.countyNameGis", "发货人区名称", null),
    CONSIGNOR_TOWN_NAME_GIS("consignorTownNameGis", "consignor", "consignor.address.townNameGis", "发货人镇名称", null),
    CONSIGNOR_ADDRESS_GIS("consignorAddressGis", "consignor", "consignor.address.addressGis", "发货人详细地址", null),
    CONSIGNOR_ADDRESS("consignorAddress", "consignor", "consignor.address.address", "发货人详细地址", null),
    CONSIGNOR_CHINA_POST_ADDRESS_CODE("consignorChinaPostAddressCode", "consignor", "consignor.address.chinaPostAddressCode", "发货人邮编", null),
    CONSIGNOR_PHONE("consignorPhone", "consignor", "consignor.consignorPhone", "发货人电话", null),
    CONSIGNOR_COMPANY("consignorCompany", "consignor", "consignor.consignorCompany", "发货人公司", null),
    CONSIGNOR_NATION_NO("consignorNationNo", "consignor", "consignor.consignorNationNo", "发货人国家编码", null),
    CONSIGNOR_NATION("consignorNation", "consignor", "consignor.consignorNation", "发货人国家名称", null),
    CONSIGNOR_ZIP_CODE("consignorZipCode", "consignor", "consignor.consignorZipCode", "发货地邮编", null),
    CONSIGNOR_CONSIGNOR_EN_NAME("consignorEnName", "consignor", "consignor.consignorEnName", "英文发件人姓名", null),
    CUSTOMER_WAREHOUSE_NO("customerWarehouseNo", "consignor.customerWarehouse", "consignor.customerWarehouse.warehouseNo", "发货仓编码", null),
    CONSIGNOR_ADDRESS_REGION_NO("consignorAddressRegionNo", "consignor", "consignor.address.regionNo", "发货地行政区编码", null),
    CONSIGNOR_ADDRESS_REGION_NAME("consignorAddressRegionName", "consignor", "consignor.address.regionName", "发货地行政区名称", null),
    CONSIGNOR_ADDRESS_EN_CITY_NAME("enCityName", "consignor", "consignor.address.enCityName", "英文发件人城市", null),
    CONSIGNOR_ADDRESS_EN_ADDRESS("enAddress", "consignor", "consignor.address.enAddress", "英文发件人地址", null),
    CONSIGNOR_ADDRESS_POI_CODE("consignor.address.poiCode", "consignor", "consignor.address.poiCode", "寄件人poiCode", null),
    CONSIGNOR_ADDRESS_POI_NAME("consignor.address.poiName", "consignor", "consignor.address.poiName", "寄件人poiName", null),
    CONSIGNOR_ADDRESS_HOUSE_NUMBER("consignor.address.houseNumber", "consignor", "consignor.address.houseNumber", "寄件人门牌号", null),

    /**
     * 收货人
     */
    CONSIGNEE_NAME("consigneeName", "consignee", "consignee.consigneeName", "收货人姓名", null),
    CONSIGNEE_PHONE("consigneePhone", "consignee", "consignee.consigneePhone", "收货人电话", null),
    CONSIGNEE_MOBILE("consigneeMobile", "consignee", "consignee.consigneeMobile", "收货人手机", null),
    CONSIGNEE_PROVINCE_NO_GIS("consigneeProvinceNoGis", "consignee", "consignee.address.provinceNoGis", "收货人省", null),
    CONSIGNEE_CITY_NO_GIS("consigneeCityNoGis", "consignee", "consignee.address.cityNoGis", "收货人市", null),
    CONSIGNEE_COUNTY_NO_GIS("consigneeCountyNoGis", "consignee", "consignee.address.countyNoGis", "收货人区", null),
    CONSIGNEE_TOWN_NO_GIS("consigneeTownNoGis", "consignee", "consignee.address.townNoGis", "收货人镇", null),
    CONSIGNEE_PROVINCE_NAME_GIS("consigneeProvinceNameGis", "consignee", "consignee.address.provinceNameGis", "收货人省名称", null),
    CONSIGNEE_CITY_NAME_GIS("consigneeCityNameGis", "consignee", "consignee.address.cityNameGis", "收货人市名称", null),
    CONSIGNEE_COUNTY_NAME_GIS("consigneeCountyNameGis", "consignee", "consignee.address.countyNameGis", "收货人区名称", null),
    CONSIGNEE_TOWN_NAME_GIS("consigneeTownNameGis", "consignee", "consignee.address.townNameGis", "收货人镇名称", null),
    CONSIGNEE_ADDRESS_GIS("consigneeAddressGis", "consignee", "consignee.address.addressGis", "收货人详细地址", null),
    CONSIGNEE_ADDRESS("consigneeAddress", "consignee", "consignee.address.address", "收货人详细地址", null),
    CONSIGNEE_ID_TYPE("consigneeIdType", "consignee", "consignee.consigneeIdType", "收货人证件类型", null),
    CONSIGNEE_ID_NO("consigneeIdNo", "consignee", "consignee.consigneeIdNo", "收货人证件号码", null),
    CONSIGNEE_ID_NAME("consigneeIdName", "consignee", "consignee.consigneeIdName", "收货人证件姓名", null),
    CONSIGNEE_CHINA_POST_ADDRESS_CODE("consigneeChinaPostAddressCode", "consignee", "consignee.address.chinaPostAddressCode", "收货人邮编", null),
    CONSIGNEE_COMPANY("consigneeCompany", "consignee", "consignee.consigneeCompany", "收货人公司", null),
    CONSIGNEE_NATION_NO("consigneeNationNo", "consignee", "consignee.consigneeNationNo", "收货人国家编码", null),
    CONSIGNEE_NATION("consigneeNation", "consignee", "consignee.consigneeNation", "收货人国家名称", null),
    CONSIGNEE_ZIP_CODE("consigneeZipCode", "consignee", "consignee.consigneeZipCode", "收货地邮编", null),
    DELIVERY_PLACE_CODE("deliveryPlaceCode", "consignee", "consignee.deliveryPlaceCode", "收货地编码", null),
    RECEIVE_WAREHOUSE_NO("receiveWarehouseNo", "consignee.receiveWarehouse", "consignee.receiveWarehouse.warehouseNo", "仓库编码", null),
    RECEIVE_WAREHOUSE_NAME("receiveWarehouseName", "consignee.receiveWarehouse", "consignee.receiveWarehouse.warehouseName", "仓库名称", null),
    CONSIGNEE_ADDRESS_REGION_NO("consigneeAddressRegionNo", "consignee", "consignee.address.regionNo", "收货地行政区编码", null),
    CONSIGNEE_ADDRESS_REGION_NAME("consigneeAddressRegionName", "consignee", "consignee.address.regionName", "收货地行政区名称", null),
    CONSIGNEE_EMAIL("consigneeEmail", "consignee", "consignee.consigneeEmail", "收件人邮箱", null),
    CONSIGNEE_EXTENDPROPS_CONSIGNEE_CONTACTINFO_ID("consigneeContactInfoID", "consignee", "consignee.extendProps.consigneeContactInfoID", "收货人京东身份ID", null),

    CONSIGNEE_EXTENDPROPS_CONSIGNEE_CONTACTINFO_ID_REMARK("consigneeContactInfoIDRemark", "consignee", "consignee.extendProps.consigneeContactInfoIDRemark", "收货人京东身份ID备注", null),
    CONSIGNEE_ADDRESS_POI_CODE("consignee.address.poiCode", "consignee", "consignee.address.poiCode", "收件人poiCode", null),
    CONSIGNEE_ADDRESS_POI_NAME("consignee.address.poiName", "consignee", "consignee.address.poiName", "收件人poiName", null),
    CONSIGNEE_ADDRESS_HOUSE_NUMBER("consignee.address.houseNumber", "consignee", "consignee.address.houseNumber", "收件人门牌号", null),
    /**
     * 配送信息
     */
    PLAN_DELIVERY_TIME("planDeliveryTime", "shipment", "shipment.planDeliveryTime", "预计送达时间", null),
    EXPECT_PICKUP_START_TIME("expectPickupStartTime", "shipment", "shipment.expectPickupStartTime", "期望提货开始时间", null),
    EXPECT_PICKUP_END_TIME("expectPickupEndTime", "shipment", "shipment.expectPickupEndTime", "期望提货结束时间", null),
    EXPECT_DELIVERY_START_TIME("expectDeliveryStartTime", "shipment", "shipment.expectDeliveryStartTime", "期望送达开始时间", null),
    EXPECT_DELIVERY_END_TIME("expectDeliveryEndTime", "shipment", "shipment.expectDeliveryEndTime", "期望送达结束时间", null),
    PICKUP_TYPE("pickupType", "shipment", "shipment.pickupType", "揽收方式", null),
    DELIVERY_TYPE("deliveryType", "shipment", "shipment.deliveryType", "派送方式", null),
    WARM_LAYER("warmLayer", "shipment", "shipment.warmLayer", "温层", null),
    TRANSPORT_TYPE("transportType", "shipment", "shipment.transportType", "运输类型", null),
    PLAN_RECEIVE_TIME("planReceiveTime", "shipment", "shipment.planReceiveTime", "预计接单时间", null),
    EXPECT_DISPATCH_START_TIME("expectDispatchStartTime", "shipment", "shipment.expectDispatchStartTime", "期望派货开始时间", null),
    EXPECT_DISPATCH_END_TIME("expectDispatchEndTime", "shipment", "shipment.expectDispatchEndTime", "期望派货结束时间", null),
    CONTACTLESS_TYPE("contactlessType", "shipment", "shipment.contactlessType", "无接触收货方式", null),
    ASSIGNED_ADDRESS("assignedAddress", "shipment", "shipment.assignedAddress", "指定地点", null),
    START_STATION_NO("startStationNo", "shipment", "shipment.startStationNo", "始发站点", null),
    START_STATION_TYPE("startStationType", "shipment", "shipment.startStationType", "始发站点", null),
    END_STATION_NO("endStationNo", "shipment", "shipment.endStationNo", "目的站点", null),
    DELIVERY_TEMP_LAYER("deliveryTempLayer", "shipment", "shipment.deliveryTempLayer", "配送温层", null),
    RECEIVING_PREFERENCE("receivingPreference", "shipment", "shipment.receivingPreference", "收货偏好", null),
    SERVICE_REQUIREMENTS("serviceRequirements","shipment","shipment.serviceRequirements","物流服务要求",null),
    SHIPMENT_EXTENDPROPS("extendProps", "shipment", "shipment.extendProps", "配送扩展信息", null),
    SPECIAL_TEMPLATE("specialTemplate", "shipment", "shipment.extendProps.shipmentExtendProps.specialTemplate", "特殊面单模板要求", null),
    START_CENTER_NO("startCenterNo", "shipment", "shipment.startCenterNo", "始发配送中心编码", null),
    END_CENTER_NO("endCenterNo", "shipment", "shipment.endCenterNo", "目的配送中心编码", null),
    RECEIVE_PREFERENCE("receivePreference", "shipment", "shipment.extendProps.receivePreference", "收货偏好", null),
    SEND_PREFERENCE("sendPreference", "shipment", "shipment.extendProps.sendPreference", "发货偏好", null),
    PICKUP_BY_BOX_SERVICE("pickupByBoxService", "shipment", "shipment.serviceRequirements.pickupByBoxService", "按纸箱揽收服务要求", null),

    /**
     * 财务和结算信息
     */
    SETTLEMENT_TYPE("settlementType", "finance", "finance.settlementType", "结算方式", null),
    PAYMENT("payment", "finance", "finance.payment", "支付方式", null),
    PAYMENT_STAGE("paymentStage", "finance", "finance.paymentStage", "支付环节", null),
    PREEMPT_TYPE("preemptType", "finance", "finance.preemptType", "财务预占标识", null),
    PAYMENT_ACCOUNT_NO("paymentAccountNo", "finance", "finance.paymentAccountNo", "付款账号", null),
    SETTLEMENT_ACCOUNT_NO("settlementAccountNo", "finance", "finance.settlementAccountNo", "结算账号", null),
    ESTIMATE_AMOUNT("estimateAmount", "finance", "finance.estimateAmount", "预估费用", null),
    DISCOUNT_AMOUNT("discountAmount", "finance", "finance.discountAmount", "折后金额", null),
    REDEEM_POINTS_QUANTITY("redeemPointsQuantity", "finance", "finance.points", "积分使用数量", null),
    REDEEM_POINTS_AMOUNT("redeemPointsAmount", "finance", "finance.points", "积分使用金额", null),
    POINTS_AMOUNT("pointsAmount", "finance", "finance.pointsAmount", "积分支付金额", null),
    POINTS_QUANTITY("pointsQuantity", "finance", "finance.pointsQuantity", "积分支付数量", null),
    FINANCE_DETAILS("financeDetails", "finance", "finance.financeDetails", "费用明细", null),
    TAX_SETTLEMENT_TYPE("taxSettlementType", "finance", "finance.taxSettlementType", "税金结算方式", null),

    DEDUCTION_INFOS("deductions", "finance", "finance.deductions", "抵扣信息", null),
    COSTINFOS("costInfos", "finance", "finance.costInfos", "收费要求信息", null),
    //港澳相关
    ESTIMATED_TAX("estimatedTax", "finance", "finance.estimatedTax", "预估税金", null),
    ACTUAL_TAX("actualTax", "finance", "finance.actualTax", "真实税金", null),

    PAY_STATUS_MAP("payStatusMap", "finance", "finance.payStatusMap", "支付状态集合", null),

    ATTACH_FEES("attachFees", "finance", "finance.attachFees", "附加费用", null),

    CUSTOMER_BILLING_AMOUNT("customerBillingAmount", "finance", "finance.extendProps.customerBillingAmount", "商家自计费金额", null),

    /**
     * 产品信息
     */
    PRODUCT_NO("productNo", "product", "product.productNo", "产品编码", null),
    PRODUCT_SHOULD_PAY_MONEY("shouldPayMoney", "product", "product.productAttrs.shouldPayMoney", "代收货款金额", null),
    MAIN_PRODUCT("mainProduct", "product", "product.mainProduct", "主产品", null),

    /**
     * 营销信息
     */
    TICKET_NO("ticketNo", "ticket", "ticket.ticketNo", "券编码", null),
    DISCOUNT_NO("discountNo", "discount", "discount.discountNo", "折扣编码", null),
    ACTIVITY_NO("activityNo", "activity", "activity.activityNo", "活动编码", null),

    /**
     * 货品信息
     */
    CARGO("cargo", "cargo", "cargo", "货品信息", null),
    GOODS("goods", "goods", "goods", "商品信息", null),
    FENCE_INFOS("fenceInfos", "address", "address.fenceInfos", "围栏信息", null),

    /**
     * 协议信息
     */
    AGREEMENTS("agreements", "agreements", "agreements", "协议信息", null),

    /**
     * 退货信息
     */
    RETURN_INTO_RETURN_TYPE("returnInfoReturnType", "returnInfo", "returnInfo.returnType", "退货信息--退货类型", null),
    RETURN_INTO_CONSIGNEE_NAME("returnInfoConsigneeName", "returnInfo", "returnInfo.consignee.consigneeName", "退货信息--收货人姓名", null),
    RETURN_INTO_CONSIGNEE_PHONE("returnInfoConsigneePhone", "returnInfo", "returnInfo.consignee.consigneePhone", "退货信息--收货人电话", null),
    RETURN_INTO_CONSIGNEE_MOBILE("returnInfoConsigneeMobile", "returnInfo", "returnInfo.consignee.consigneeMobile", "退货信息--收货人手机", null),
    RETURN_INTO_CONSIGNEE_PROVINCE_NO("returnInfoConsigneeProvinceNo", "returnInfo", "returnInfo.consignee.address.provinceNo", "退货信息--收货人省", null),
    RETURN_INTO_CONSIGNEE_CITY_NO("returnInfoConsigneeCityNo", "returnInfo", "returnInfo.consignee.address.cityNo", "退货信息--收货人市", null),
    RETURN_INTO_CONSIGNEE_COUNTY_NO("returnInfoConsigneeCountyNo", "returnInfo", "returnInfo.consignee.address.countyNo", "退货信息--收货人区", null),
    RETURN_INTO_CONSIGNEE_TOWN_NO("returnInfoConsigneeTownNo", "returnInfo", "returnInfo.consignee.address.townNo", "退货信息--收货人镇", null),
    RETURN_INTO_CONSIGNEE_ADDRESS("returnInfoConsigneeAddress", "returnInfo", "returnInfo.consignee.address.address", "退货信息--收货人详细地址", null),
    RETURN_INTO_CONSIGNEE_ADDRESS_REGION_NO("returnInfoConsigneeAddressRegionNo", "returnInfo", "returnInfo.consignee.address.regionNo", "退货信息--行政区编码", null),
    RETURN_INTO_CONSIGNEE_ADDRESS_REGION_NAME("returnInfoConsigneeAddressRegionName", "returnInfo", "returnInfo.consignee.address.regionName", "退货信息--行政区名称", null),

    /**
     * 履约信息
     */
    FULFILLMENT_SIGN("fulfillmentSign","fulfillment","fulfillment.fulfillmentSign","履约标识",null),
    ACTUAL_RECEIVED_QUANTITY("actualReceivedQuantity","fulfillment","fulfillment.actualReceivedQuantity.value","实际揽收包裹数量",null),
    ACTUAL_RECEIVED_QUANTITY_UNIT("actualReceivedQuantityUnit","fulfillment","fulfillment.actualReceivedQuantity.unit","实际揽收包裹数量单位",null),
    PACKAGE_MAX_LEN("packageMaxLen","fulfillment","fulfillment.packageMaxLen.value","包裹最长边数量",null),
    PACKAGE_MAX_LEN_UNIT("packageMaxLenUnit","fulfillment","fulfillment.packageMaxLen.unit","包裹最长边单位",null),

    /**
     * 关务信息
     */
    CUSTOMS_START_FLOW_DIRECTION("startFlowDirection", "customs", "customs.startFlowDirection", "始发流向", null),
    CUSTOMS_END_FLOW_DIRECTION("endFlowDirection", "customs", "customs.endFlowDirection", "目的流向", null),
    CUSTOMS_CLEARANCE_MODE("clearanceMode", "customs", "customs.clearanceMode", "报关方式", null),
    CUSTOMS_CLEARANCE_TYPE("clearanceType", "customs", "customs.clearanceType", "报关类别", null),
    CUSTOMS_CUSTOMS_STATUS("customsStatus", "customs", "customs.customsStatus", "关务状态", null),
    CUSTOMS_FILE_TAG("fileTag", "customs", "customs.fileTag", "是否文件", null),
    CUSTOMS_SUPERVISION("supervision", "customs", "customs.supervision", "监管方式", null),
    CUSTOMS_TRANSACTION_METHOD("transactionMethod", "customs", "customs.transactionMethod", "成交方式", null),
    CUSTOMS_IMPORTER_VAT_NUMBER("importerVatNumber", "customs", "customs.importerVatNumber", "VAT号(增值税号)", null),
    CUSTOMS_IMPORTER_EORI_NUMBER("importerEoriNumber", "customs", "customs.importerEoriNumber", "EORI号(经营者注册和识别号码)", null),
    CUSTOMS_IMPORTER_IOSS_NUMBER("importerIossNumber", "customs", "customs.importerIossNumber", "IOSS号(IOSS增值税识别号)", null),


    /**
     *  附件信息
     */
    ATTACHMENTS("attachments", "attachments", "attachments", "附件信息", null),

    /**
     * 暂存天数
     */
    TEMP_STORAGE_DAY("tempStorageDay","shipment","shipment.serviceRequirements.tempStorageDay","暂存天数",null),

    /**
     * 物资周转
     */
    MATERIAL_TURNOVER("materialTurnover","shipment","shipment.extendProps.materialTurnover","物资周转",null),

    /**
     * 多方计费总金额
     */
    MULTI_PARTIES_TOTAL_AMOUNTS("finance.multiPartiesTotalAmounts", "finance", "finance.multiPartiesTotalAmounts", "多方计费总金额", null),

    /**
     * 签单返单运单号
     */
    SIGN_RETURN_WAYBILL_NO("signReturnWaybillNo", "refOrderInfoDelegate", "refOrderInfoDelegate.extendProps.signReturnWaybillNo", "签单返单运单号", null),

    /**
     * 解决方案编码
     */
    BUSINESS_SOLUTION_NO("businessSolutionNo", "businessSolution", "businessSolution.businessSolutionNo", "解决方案编码", null),

    ;
    private String code;
    private String group;
    private String field;
    private String desc;
    private Integer mark;

    ModifyItemConfigEnum(String code, String group, String field, String desc, Integer mark) {
        this.code = code;
        this.group = group;
        this.field = field;
        this.desc = desc;
        this.mark = mark;
    }

    private static final Map<String, ModifyItemConfigEnum> modifyItemConfigEnumMap = new HashMap<String, ModifyItemConfigEnum>();

    static {
        for (ModifyItemConfigEnum configEnum : ModifyItemConfigEnum.values()) {
            modifyItemConfigEnumMap.put(configEnum.getCode(), configEnum);
        }
    }

    public static ModifyItemConfigEnum getConfigEnumByCode(String code) {
        return modifyItemConfigEnumMap.get(code);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getMark() {
        return mark;
    }

    public void setMark(Integer mark) {
        this.mark = mark;
    }

    public static ModifyItemConfigEnum fromFiled(String field) {
        if (StringUtils.isBlank(field)) {
            return null;
        }
        return filedRegistry.get(field);
    }

    public static final Map<String, ModifyItemConfigEnum> filedRegistry = new HashMap<>();

    static {
        Iterator iterator = EnumSet.allOf(ModifyItemConfigEnum.class).iterator();
        while (iterator.hasNext()) {
            ModifyItemConfigEnum modifyItemConfigEnum = (ModifyItemConfigEnum) iterator.next();
            filedRegistry.put(modifyItemConfigEnum.getField(), modifyItemConfigEnum);
        }
    }
}
