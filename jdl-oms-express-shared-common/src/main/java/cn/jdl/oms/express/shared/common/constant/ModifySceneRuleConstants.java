package cn.jdl.oms.express.shared.common.constant;

/**
 * @ClassName ModifySceneRuleConstants
 * @Description 订单扩展信息-修改策略
 * @版权信息(@copyright Copyright 2014-XXXX JD.COM All Right Reserved)  与该类相关联类(@see);
 * <AUTHOR>
 * @Date 2022/6/16 10:51 上午
 * @Version 1.0
 * @复审人:。
 **/
public class ModifySceneRuleConstants {
    /**
     * 修改策略
     */
    public static final String MODIFY_SCENE_RULE = "modifySceneRule";
    /**
     * 揽收前
     */
    public static final String BEFORE_PICKUP = "beforePickUp";
    /**
     * 修改货品数量
     */
    public static final String MODIFY_QUANTITY = "modifyCargoQuantity";
    /**
     * 同城同站修改
     */
    public static final String MODIFY_SAME_SITE = "sameSite";
    /**
     * 揽收后
     */
    public static final String AFTER_PICKUP = "afterPickUp";
    /**
     * 揽收后改址
     */
    public static final String AFTER_PICKUP_READDRESS = "afterPickUpReaddress";

    /**
     * 仅修改订单，不下发OFC
     */
    public static final String ONLY_MODIFY_ORDER = "onlyModifyOrder";

    /**
     * 仅修改协议信息
     */
    public static final String ONLY_MODIFY_AGREEMENTS = "onlyModifyAgreements";
    /**
     * 仅修改报关信息
     */
    public static final String ONLY_MODIFY_CUSTOMS = "onlyModifyCustoms";

    /**
     * 仅修改收件人联系方式（姓名、电话、手机）
     */
    public static final String ONLY_MODIFY_CONSIGNEE_CONTACT_INFORMATION = "onlyModifyConsigneeContactInformation";

    /**
     * 仅修改财务信息
     */
    public static final String ONLY_MODIFY_FINANCE = "onlyModifyFinance";

    /**
     * 销售修改
     */
    public static final String MODIFY_BY_SALESPERSON = "modifyBySalesperson";

    /**
     * 新增压车费
     */
    public static final String INSERT_EXTRUDE_FEE = "insertExtrudeFee";

    /**
     * 营业厅修改
     */
    public static final String MODIFY_BY_BUSINESS_HALL = "modifyByBusinessHall";

    /**
     * 修改联系方式（姓名、电话、手机）
     */
    public static final String MODIFY_CONTACT_INFORMATION = "modifyContactInformation";

    /**
     * 仓出库发货
     */
    public static final String OUTBOUND_DELIVERY = "outboundDelivery";

    /**
     * 特殊修改策略
     */
    public static final String SPECIAL_MODIFY = "specialModify";

    /**
     * 仅修改费用审核状态
     */
    public static final String ONLY_MODIFY_FEE_CHECK_STATUS = "onlyModifyFeeCheckStatus";

    /**
     * 仅修改多地址审核状态
     */
    public static final String ONLY_MODIFY_MULTI_ADDRESS_VERIFY_STATUS = "onlyModifyMultiAddressVerifyStatus";

    /**
     * 仅限修改碳排量和碳减排
     */
    public static final String CARBON_EMISSION_CALCULATION = "carbonEmissionCalculation";

    /**
     * 内部修改前缀
     */
    public static final String INTERNAL_MODIFY_PREFIX = "internalModify";

    /**
     * 内部修改：修改/清除收发货人信息（姓名、电话、手机、地址），用 * 替换
     */
    public static final String INTERNAL_MODIFY_CONSIGNOR_AND_CONSIGNEE_INFO = "internalModifyConsignorAndConsigneeInfo";

    /**
     * 特殊仓出库发货：订单内部处理逻辑与outboundDelivery相同，区别是配运OFC识别此修改策略会更新运单包裹数
     */
    public static final String OUTBOUND_DELIVERY_CUSTOM = "outboundDeliveryCustom";

    /**
     * 允许"派送中"修改期望送达时间
     */
    public static final String MODIFY_EXPECT_DELIVERY_TIME_ON_DELIVERING = "modifyExpectDeliveryTimeOnDelivering";

    /**
     * 修改运营模式
     */
    public static final String MODIFY_OP_MODE = "modifyOpMode";

    /**
     * 内部修改：大件修改收货人信息（姓名、电话、手机、地址信息）
     */
    public static final String INTERNAL_MODIFY_LAS_CONSIGNEE_INFO = "internalModifyLasConsigneeInfo";

    /**
     * 仅修改补签标识
     */
    public static final String MODIFY_RE_SIGN_FLAG = "modifyReSignFlag";

    /**
     * 仅修改国补审核状态
     */
    public static final String MODIFY_GUO_BU_STATUS = "modifyGovSubsidyApprovalStatus";

    /**
     * 内部修改：修改逆向单信息，不做任何限制，完全信任
     */
    public static final String INTERNAL_MODIFY_RETURN_ORDER_INFO = "internalModifyReturnOrderInfo";

    /**
     * 修改国补采集信息（仅大件使用）
     */
    public static final String  MODIFY_GOV_SUBSIDY_COLLECTION_INFORMATION = "modifyGovSubsidyCollectionInformation";

    /**
     * 客服报关审核修改
     */
    public static final String CSS_CUSTOMS_DECLARATION_AUDIT = "cssCustomsDeclarationAudit";

    /**
     * 无任务揽收
     * 使用场景：终端使用，终端可以在揽收后24小时内，反复进行揽收修改，订单不能卡控状态校验
     */
    public static final String NO_TASK_FINISH_COLLECT = "noTaskFinishCollect";

    /**
     * 内部修改支持下发。通用内部修改
     * 使用场景：修改函速达url
     */
    public static final String INTERNAL_MODIFY = "internalModify";

    /**
     * 修改应收和实收
     */
    public static final String MODIFY_RECEIVABLE_AND_REAL_PAY = "modifyReceivableAndRealPay";

    /**
     * 优惠券逻辑删除标识
     */
    public static final String RELEASE_TICKET_PENDING = "releaseTicketPending";

    /**
     * 揽收转站、跨站截单策略
     */
    public static final String PICKUP_TRANSFER_STATION = "pickupTransferStation";

}
