package cn.jdl.oms.express.shared.common.dict;

import static cn.jdl.oms.express.shared.common.dict.ECardDisableReasonEnum.*;

/**
 * 计费折扣类型
 */
public enum DiscountTypeEnum {

//    TYPE_1("1", "产品折扣"),
    TYPE_2("2", "揽派同时"),
    TYPE_3("3", "专业市场"),
    TYPE_4("4", "渠道优惠"),
//    TYPE_5("5", "客户现结"),
    TYPE_6("6", "合同客户"),
    TYPE_10("10", "散单批量寄"),
    TYPE_11("11", "员工福利寄"),
    TYPE_12("12", "优惠券"),
    ;
    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String desc;

    DiscountTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
