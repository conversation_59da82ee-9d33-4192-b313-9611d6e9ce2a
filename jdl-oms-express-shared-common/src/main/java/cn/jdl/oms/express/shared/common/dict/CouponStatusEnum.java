package cn.jdl.oms.express.shared.common.dict;

public enum CouponStatusEnum {
    ALLOW_ROLLBACK_NO(1,"未回滚"),
    ALLOW_ROLLBACK_YES(4,"已回滚"),
    NOT_ALLOW_ROLLBACK(3,"不回滚"),
    INTERCEPT_ROLLBACK(2,"拦截回滚"),
    DELETE_PENDING(5,"待回滚")
    ;

    private Integer code;

    private String desc;

    CouponStatusEnum(Integer code, String desc){
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
