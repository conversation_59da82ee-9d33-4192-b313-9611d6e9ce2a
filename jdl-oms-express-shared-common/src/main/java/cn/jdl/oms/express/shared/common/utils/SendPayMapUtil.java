package cn.jdl.oms.express.shared.common.utils;

import cn.jdl.oms.express.shared.common.constant.MagicCommonConstants;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * sendPayMap工具类
 */
public class SendPayMapUtil {

    public static final String SEND_PAY_MAP = "sendPayMap";

    /**
     * 源头直发
     * @param sendPayMap
     * @return
     */
    public static boolean isSourceDeliver(String sendPayMap){
        if (StringUtils.isBlank(sendPayMap)) {
            return false;
        }
        JSONObject map = JSON.parseObject(sendPayMap);
        if(MapUtils.isEmpty(map)){
            return false;
        }
        return StringUtils.equals(map.getString("864"),"1");
    }

    /**
     * 是否中转模式
     *
     * @param sendPayMap
     * @return
     */
    public static boolean isTransitMode(String sendPayMap) {
        if (StringUtils.isBlank(sendPayMap)) {
            return false;
        }
        JSONObject map = JSON.parseObject(sendPayMap);
        if (MapUtils.isEmpty(map)) {
            return false;
        }
        return MagicCommonConstants.STRING_1.equals(map.getString("1086"));
    }

    /**
     * 京喜订单
     * @param sendPayMap
     * @return
     */
    public static boolean isJingXi(String sendPayMap){
        if (StringUtils.isBlank(sendPayMap)) {
            return false;
        }
        JSONObject map = JSON.parseObject(sendPayMap);
        if(MapUtils.isEmpty(map)){
            return false;
        }
        return StringUtils.equals(map.getString("997"),"1");
    }

    /**
     * SendPayMap[624==1]，判断是否为正品鉴定第一段发送到鉴定仓的单子
     *
     * @param sendPayMap
     * @return
     */
    public static boolean isZhengPinJianDingSendToJianDingCang(String sendPayMap) {
        if (StringUtils.isBlank(sendPayMap)) {
            return false;
        }
        JSONObject map = JSON.parseObject(sendPayMap);
        if (MapUtils.isEmpty(map)) {
            return false;
        }
        return StringUtils.equals(map.getString("624"), "1");
    }

    /**
     * 是否拍拍质检订单
     * SendPayMap[624==7]
     *
     * @param sendPayMap
     * @return
     */
    public static boolean isPaipaiQualityInspectionOrder(String sendPayMap) {
        if (StringUtils.isBlank(sendPayMap)) {
            return false;
        }
        JSONObject map = JSON.parseObject(sendPayMap);
        if (MapUtils.isEmpty(map)) {
            return false;
        }
        return StringUtils.equals(map.getString("624"), "7");
    }

    /**
     * 是否服饰定制二段物流
     *
     * @param sendPayMap
     * @return
     */
    public static boolean isPersonalCustomization(String sendPayMap) {
        if (StringUtils.isBlank(sendPayMap)) {
            return false;
        }
        JSONObject map = JSON.parseObject(sendPayMap);
        if (MapUtils.isEmpty(map)) {
            return false;
        }
        return MagicCommonConstants.STRING_5.equals(map.getString("819")) && MagicCommonConstants.STRING_1.equals(map.getString("1243"));
    }
}
