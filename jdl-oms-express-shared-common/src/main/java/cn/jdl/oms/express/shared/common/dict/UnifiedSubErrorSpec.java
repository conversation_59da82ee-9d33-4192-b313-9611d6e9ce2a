package cn.jdl.oms.express.shared.common.dict;

/**
 * @ProjectName：cn.jdl.oms.express.shared.commmon.dict
 * @Package： cn.jdl.oms.express.shared.commmon.dict
 * @ClassName: ExpressOrderErrorSpec
 * @Description:
 * @Author： wa<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate 2020/12/24  1:44 下午
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
public final class UnifiedSubErrorSpec {

    private UnifiedSubErrorSpec() {
    }

    /**
     * 错误码分段说明: 10000~~11000通用预留,错误码新增原则上不允许跳步,每次新增加一
     * 为避免多分支新增冲突，先到文档增加 https://joyspace.jd.com/pages/dq7S6DBKmbzvMRc3IyqZ
     */
    public enum BasisOrder implements ExpressOrderSubErrorSpec {

        /**
         * 沃尔玛0包裹修改需求校验失败
         */
        BASIC_INFO_VALIDATE_VALIDATE_FAIL_0001("11001", "11001_0001", "订单已完结，不支持修改包裹号、包裹数；"),
        /**
         * 客户配置信息校验失败
         */
        CUSTOMER_CONFIG_VALIDATE_FAIL_0001("11005", "11005_0001", "客户配置信息校验失败,商家未开通允许收件人改址"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0002("11005", "11005_0002", "客户配置信息校验失败,订单不为云仓VMI订单,商家类别不能是云仓VMI类型"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0003("11005", "11005_0003", "客户配置信息校验失败,现结余额查询异常"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0004("11005", "11005_0004", "客户配置信息校验失败,履约账号对应的商家信息不存在"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0005("11005", "11005_0005", "客户配置信息校验失败,结算账号对应的商家信息不存在"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0006("11005", "11005_0006", "客户配置信息校验失败,履约账号对应的商家扩展信息不存在"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0007("11005", "11005_0007", "客户配置信息校验失败,未查到相关客户信息"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0008("11005", "11005_0008", "客户配置信息校验失败,商家未开通当前结算方式"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0009("11005", "11005_0009", "客户配置信息校验失败,余额小于等于停用金额"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0010("11005", "11005_0010", "客户配置信息校验失败,未开通隐私通话服务"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0011("11005", "11005_0011", "客户配置信息校验失败,订单总重量非法"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0012("11005", "11005_0012", "客户配置信息校验失败,明细揽收校验失败"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0013("11005", "11005_0013", "客户配置信息校验失败,明细揽收校验失败,商品编码为空!"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0014("11005", "11005_0014", "客户配置信息校验失败,明细揽收校验失败,商品名称为空!"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0015("11005", "11005_0015", "客户配置信息校验失败,明细揽收校验失败,商品数量为空!"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0016("11005", "11005_0016", "客户配置信息校验失败,明细揽收校验失败,商品数量值或单位为空!"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0017("11005", "11005_0017", "客户配置信息校验失败,明细揽收校验失败,商品总数量大于最大阈值!"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0018("11005", "11005_0018", "客户配置信息校验失败,商家子类别为SOP类型且渠道编码不为售后单时,渠道编码必须为京东平台订单"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0019("11005", "11005_0019", "客户配置信息校验失败,商家子类别为SOP类型,渠道编码不为售后单且存在代收货款时,渠道订单号存在多个"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0020("11005", "11005_0020", "客户配置信息校验失败,查询事业部新信息接口异常,仓储履约账号不存在"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0021("11005", "11005_0021", "客户配置信息校验失败,查询事业部新信息接口异常,未查到相关事业部的青龙业主号"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0022("11005", "11005_0022", "客户配置信息校验失败,事业部逾期"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0023("11005", "11005_0023", "客户配置信息校验失败,事业部未启用"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0024("11005", "11005_0024", "客户配置信息校验失败,客户配置信息校验异常"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0025("11005", "11005_0025", "客户配置信息校验失败,客户配置渠道信息校验异常"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0026("11005", "11005_0026", "客户配置信息校验失败,您好,当前事业部对应的账户余额已不足,请充值后下单,或更换其他事业部"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0027("11005", "11005_0027", "客户配置信息校验失败,预收款停用金额校验异常：停用金额为空"),
        CUSTOMER_CONFIG_VALIDATE_FAIL_0028("11005", "11005_0028", "客户配置信息校验失败,商家青龙业主号状态异常"),
        ;

        /**
         * 错误码
         */
        private String code;
        /**
         * 错误子码
         */
        private String subCode;
        /**
         * 错误码描述
         */
        private String desc;

        BasisOrder(String code, String subCode, String desc) {
            this.code = code;
            this.subCode = subCode;
            this.desc = desc;
        }

        @Override
        public String code() {
            return code;
        }

        @Override
        public String subCode() {
            return subCode;
        }


        @Override
        public String desc() {
            return desc;
        }
    }
}
